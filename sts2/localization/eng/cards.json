{"ACCELERANT.description": "[gold]Poison[/gold] is triggered {Accelerant:diff()} additional {Accelerant:plural:time|times}.", "ACCELERANT.title": "Accelerant", "ACCURACY.description": "[gold]Shivs[/gold] deal {Accuracy:diff()} additional damage.", "ACCURACY.title": "Accuracy", "ACROBATICS.description": "Draw {Cards:diff()} cards.\nDiscard 1 card.", "ACROBATICS.title": "Acrobatics", "ADRENALINE.description": "Gain {Energy:energyIcons()}.\nDraw 2 cards.", "ADRENALINE.title": "Adrenaline", "AFTERLIFE.description": "[gold]Summon[/gold] {Summon:diff()}.\nNext turn, [gold]Summon[/gold] {Summon:diff()}.", "AFTERLIFE.title": "Afterlife", "AFTER_IMAGE.description": "Whenever you play a card, gain 1 [gold]Block[/gold].", "AFTER_IMAGE.title": "After Image", "AGGREGATE.description": "Gain {energyPrefix:energyIcons(1)} for every {Cards:diff()} cards in your [gold]Draw Pile[/gold].", "AGGREGATE.title": "Aggregate", "AGGRESSION.description": "At the start of your turn, put a random Attack from your [gold]Discard Pile[/gold] into your [gold]Hand[/gold] and [gold]Upgrade[/gold] it for the rest of combat.", "AGGRESSION.title": "Aggression", "ALCHEMIZE.description": "Procure a random potion.", "ALCHEMIZE.title": "Alchemize", "ALIGNMENT.description": "Gain {Energy:energyIcons()}.", "ALIGNMENT.title": "Alignment", "ALLEY_OOP.description": "Deal {Damage:diff()} damage.\n The enemy takes {IfUpgraded:show:triple|double} damage from other players this turn.", "ALLEY_OOP.title": "Alley-Oop", "ALL_FOR_ONE.description": "Deal {Damage:diff()} damage.\nPut all cost 0 cards from your [gold]Discard Pile[/gold] into your [gold]Hand[/gold].", "ALL_FOR_ONE.title": "All for One", "AMPLIFY.description": "This turn, your next {Powers:cond:>1?{Powers:diff()} Powers are|Power is} played an extra time.", "AMPLIFY.title": "Amplify", "ANGER.description": "Deal {Damage:diff()} damage.\nAdd a copy of this card into your [gold]Discard Pile[/gold].", "ANGER.title": "Anger", "ANOINTED.description": "Put every [gold]Rare[/gold] card from your [gold]Draw Pile[/gold] into your [gold]Hand[/gold].", "ANOINTED.title": "Anointed", "ANTICIPATE.description": "Gain {Dexterity:diff()} [gold]Dexterity[/gold] this turn.", "ANTICIPATE.title": "Anticipate", "APOTHEOSIS.description": "[gold]Upgrade[/gold] all your cards for the rest of combat.", "APOTHEOSIS.title": "Apotheosis", "ARMAMENTS.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Upgrade[/gold] {IfUpgraded:show:all cards|a card} in your [gold]Hand[/gold] for the rest of combat.", "ARMAMENTS.title": "Armaments", "ARRHYTHMIA.description": "Whenever this card is drawn, lose {Energy:energyIcons()}.", "ARRHYTHMIA.title": "Arrhythmia", "ARSENAL.description": "Whenever you play a Colorless card, gain {ArsenalPower:diff()} [gold]Strength[/gold].", "ARSENAL.title": "Arsenal", "ARTIFICIAL_WINTER.description": "[gold]Channel[/gold] {Channel:diff()} [gold]Frost[/gold]. Afflict all cards in your [gold]Hand[/gold] with [gold]Frozen[/gold].", "ARTIFICIAL_WINTER.title": "Artificial Winter", "ASCENDERS_BANE.description": "", "ASCENDERS_BANE.title": "Ascender's <PERSON><PERSON>", "ASHEN_STRIKE.description": "Deal {BaseDamage:diff()} damage, plus {DamagePerCard:diff()} additional damage for each card in your [gold]Exhaust Pile[/gold].{OnTable:\n(Deals {Damage:diff()} damage)|}", "ASHEN_STRIKE.title": "Ashen Strike", "ASSASSINATE.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "ASSASSINATE.title": "Assassinate", "ASTRAL_HARVEST.description": "Gain {Stars:starIcons()} X times.", "ASTRAL_HARVEST.title": "Astral Harvest", "ASTRAL_PULSE.description": "Deal {Damage:diff()} damage to all enemies.", "ASTRAL_PULSE.title": "Astral Pulse", "AUTOMATION.description": "Every 10 cards you draw, gain {Energy:energyIcons()}.", "AUTOMATION.title": "Automation", "AUTO_SHIELDS.description": "If you have no [gold]Block[/gold], gain {Block:diff()} [gold]Block[/gold].", "AUTO_SHIELDS.title": "Auto-Shields", "A_THOUSAND_CUTS.description": "Whenever you play a card, deal {Power:diff()} damage to all enemies.", "A_THOUSAND_CUTS.title": "A Thousand Cuts", "BACKFLIP.description": "Gain {Block:diff()} [gold]Block[/gold].\nDraw 2 cards.", "BACKFLIP.title": "Backflip", "BACKSTAB.description": "Deal {Damage:diff()} damage.", "BACKSTAB.title": "Backstab", "BALL_LIGHTNING.description": "Deal {Damage:diff()} damage.\n[gold]Channel[/gold] 1 [gold]Lightning[/gold].", "BALL_LIGHTNING.title": "Ball Lightning", "BANSHEES_CRY.description": "Deal {Damage:diff()} damage.\nCosts 1 less for each [gold]Ethereal[/gold] card played this combat.", "BANSHEES_CRY.title": "<PERSON><PERSON><PERSON>'s Cry", "BARRAGE.description": "Deal {Damage:diff()} damage for each [gold]Channeled[/gold] Orb. {OnTable:\n(Attacks {Repeat:diff()} {Repeat:plural:time|times})|}", "BARRAGE.title": "Barrage", "BARRICADE.description": "[gold]Block[/gold] is not removed at the start of your turn.", "BARRICADE.title": "Barricade", "BASH.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "BASH.title": "<PERSON><PERSON>", "BATTLE_TRANCE.description": "Draw {Cards:diff()} cards.\nYou cannot draw additional cards this turn.", "BATTLE_TRANCE.title": "Battle Trance", "BEACON_OF_HOPE.description": "Whenever you gain [gold]Block[/gold] from a card, other players gain half than much [gold]Block[/gold].", "BEACON_OF_HOPE.title": "Beacon Of Hope", "BEAM_CELL.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "BEAM_CELL.title": "Beam Cell", "BEAT_DOWN.description": "Play {Cards:diff()} random Attacks from your [gold]Discard Pile[/gold].", "BEAT_DOWN.title": "Beat Down", "BEAT_INTO_SHAPE.description": "Deal {Damage:diff()} damage.\n[gold]Forge[/gold] {Forge:diff()} for every time the enemy has taken attack damage this turn.", "BEAT_INTO_SHAPE.title": "Beat Into Shape", "BECKON.description": "At the end of your turn, if this is in your [gold]Hand[/gold], gain {Doom:diff()} [gold]Doom[/gold].", "BECKON.title": "Beckon", "BEGONE.description": "Deal {Damage:diff()} damage.\nChoose a card in your [gold]Hand[/gold] to [gold]Transform[/gold] into [gold]Minion Dive Bomb{IfUpgraded:show:+|}[/gold].", "BEGONE.title": "BEGONE!", "BELIEVE_IN_YOU.description": "Another player gains {Energy:energyIcons()}.", "BELIEVE_IN_YOU.title": "Believe In You", "BIASED_COGNITION.description": "Gain {Focus:diff()} [gold]Focus[/gold].\n At the start of your turn, lose {Bias:diff()} [gold]Focus[/gold].", "BIASED_COGNITION.title": "Biased Cognition", "BIG_BANG.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.\nGain {Energy:energyIcons()}.\nGain {Stars:starIcons()}.\n[gold]Forge[/gold] {Forge:diff()}.", "BIG_BANG.title": "Big Bang", "BIT_ROT.description": "Whenever this card is drawn, lose {Focus:diff()} [gold]Focus[/gold] this turn.", "BIT_ROT.title": "Bit Rot", "BLACK_HOLE.description": "At the start of your turn, deal {BlackHolePower:diff()} damage to all enemies.", "BLACK_HOLE.title": "Black Hole", "BLADE_DANCE.description": "Add {Cards:diff()} [gold]{Cards:plural:Shiv|Shivs}[/gold] into your [gold]Hand[/gold].", "BLADE_DANCE.title": "Blade Dance", "BLADE_OF_INK.description": "Whenever you create a [gold]Shiv[/gold] this turn, gain {BladeOfInkPower:diff()} [gold]Strength[/gold].", "BLADE_OF_INK.title": "Blade of Ink", "BLIGHT_STRIKE.description": "Deal {Damage:diff()} damage.\nApply [gold]Doom[/gold] equal to unblocked damage dealt.", "BLIGHT_STRIKE.title": "Blight Strike", "BLIZZARD.description": "Deal damage equal to {Multiplier:diff()} times the number of [gold]Frost Channeled[/gold] this combat to all enemies.{OnTable:\n(Deals {Damage:diff()} damage)|}", "BLIZZARD.title": "Blizzard", "BLOODLETTING.description": "Lose {HpLoss:diff()} HP.\nGain {Energy:energyIcons()}.", "BLOODLETTING.title": "Bloodletting", "BLOOD_WALL.description": "Lose {HpLoss:diff()} HP.\nGain {Block:diff()} [gold]Block[/gold].", "BLOOD_WALL.title": "Blood Wall", "BLUDGEON.description": "Deal {Damage:diff()} damage.", "BLUDGEON.title": "Bludgeon", "BLUR.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Block[/gold] is not removed at the start of your next turn.", "BLUR.title": "Blur", "BODYGUARD.description": "[gold]Summon[/gold] {Summon:diff()}.\n[gold]Osty[/gold] deals {OstyDamage:diff()} damage.", "BODYGUARD.title": "Bodyguard", "BODY_SLAM.description": "Deal damage equal to your [gold]Block[/gold].{OnTable:\n(Deals {Damage:diff()} damage)|}", "BODY_SLAM.title": "Body Slam", "BOLAS.description": "Deal {Damage:diff()} damage.\nAt the start of your next turn, return this to your [gold]Hand[/gold].", "BOLAS.title": "<PERSON><PERSON>", "BONE_SHARDS.description": "If [gold]<PERSON><PERSON>[/gold] is alive,\nhe dies and deals damage equal to his Max HP to all enemies.{OnTable:\n(Deals {OstyDamage:diff()} damage)|}", "BONE_SHARDS.title": "Bone Shards", "BONE_WALL.description": "[gold]Summon[/gold] {Summon:diff()} X times.", "BONE_WALL.title": "<PERSON>", "BOOST_AWAY.description": "Gain {Block:diff()} [gold]Block[/gold].\nShuffle a [gold]Dazed[/gold] into your [gold]Draw Pile[/gold].", "BOOST_AWAY.title": "Boost Away", "BOOT_SEQUENCE.description": "Gain {Block:diff()} [gold]Block[/gold].", "BOOT_SEQUENCE.title": "Boot Sequence", "BORROWED_TIME.description": "Apply {Doom:diff()} [gold]Doom[/gold] to yourself.\nGain {Energy:energyIcons()}.", "BORROWED_TIME.title": "Borrowed Time", "BOUNCING_FLASK.description": "Apply {Poison:diff()} [gold]Poison[/gold] to a random enemy {Repeat:diff()} times.", "BOUNCING_FLASK.title": "Bouncing Flask", "BREAK.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "BREAK.title": "Break", "BREAKTHROUGH.description": "Lose {HpLoss:diff()} HP.\nDeal {Damage:diff()} damage to all enemies.", "BREAKTHROUGH.title": "Breakthrough", "BRIGHTEST_FLAME.description": "Gain {Energy:energyIcons()}.\nDraw {Cards:diff()} {Cards:plural:card|cards}.\nLose {MaxHp:diff()} Max HP.", "BRIGHTEST_FLAME.title": "Brightest Flame", "BUBBLE_BUBBLE.description": "[gold]Poison[/gold] no longer reduces by 1 at the end of the turn.", "BUBBLE_BUBBLE.title": "Bubble Bubble", "BUFFER.description": "Prevent the next {Powers:plural:time|{Powers:diff()} times} you would lose HP.", "BUFFER.title": "<PERSON><PERSON><PERSON>", "BULLET_TIME.description": "You cannot draw additional cards this turn. Reduce the cost of all cards in your [gold]Hand[/gold] to 0 this turn.", "BULLET_TIME.title": "Bullet Time", "BULLY.description": "Deal {Damage:diff()} damage.\nDeal {ExtraDamage:diff()} additional damage for each [gold]Vulnerable[/gold] on the enemy.", "BULLY.title": "Bully", "BULWARK.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Forge[/gold] {Forge:diff()}.", "BULWARK.title": "Bulwark", "BUNDLE_OF_JOY.description": "Add {Cards:diff()} random Colorless {Cards:plural:card|cards} to your [gold]Hand[/gold]. They cost 0 this turn.", "BUNDLE_OF_JOY.title": "Bundle Of Joy", "BURN.description": "At the end of your turn, if this is in your [gold]Hand[/gold], take {Damage:diff()} damage.", "BURN.title": "Burn", "BURNING_PACT.description": "[gold]Exhaust[/gold] 1 card.\nDraw {Cards:diff()} cards.", "BURNING_PACT.title": "Burning Pact", "BURST.description": "This turn, your next{IfUpgraded:show: 2} {Skills:plural:Skill is|Skills are} played an extra time.", "BURST.title": "<PERSON><PERSON><PERSON>", "BURY.description": "Deal {BaseDamage:diff()} damage, plus {DamagePerCard:diff()} additional damage for each card in your [gold]Discard Pile[/gold].{OnTable:\n(Deals {Damage:diff()} damage)|}", "BURY.title": "Bury", "BYRDONIS_EGG.description": "Can be hatched at a Rest Site.", "BYRDONIS_EGG.title": "<PERSON><PERSON><PERSON>", "BYRD_SWOOP.description": "Deal {Damage:diff()} damage.", "BYRD_SWOOP.title": "<PERSON>op", "CALAMITY.description": "Whenever you play an Attack, add a random Attack to your hand.", "CALAMITY.title": "Calamity", "CALCULATED_GAMBLE.description": "Discard your [gold]Hand[/gold],\nthen draw that many cards.", "CALCULATED_GAMBLE.title": "Calculated Gamble", "CALL_OF_THE_VOID.description": "At the start of your turn, add {Cards:diff()} [gold]Ethereal[/gold] {Cards:plural:card|cards} to your hand.", "CALL_OF_THE_VOID.title": "Call of the Void", "CALTROPS.description": "Whenever you are attacked, deal {Thorns:diff()} damage back.", "CALTROPS.title": "Caltrops", "CAMARADERIE.description": "[gold]Summon[/gold] {Summon:diff()}.\n[gold]Osty[/gold] gains {Strength:diff()} [gold]Strength[/gold].", "CAMARADERIE.title": "Camaraderie", "CAPACITOR.description": "Gain {Repeat:diff()} Orb slots.", "CAPACITOR.title": "Capacitor", "CAPTURE_SPIRIT.description": "Enemy loses {Damage:diff()} HP.\nShuffle {Cards:diff()} [gold]Soul+[/gold] into your [gold]Draw Pile[/gold].", "CAPTURE_SPIRIT.title": "Capture Spirit", "CARVE_GHOST.description": "Deal {Damage:diff()} damage.\nAdd {Cards:diff()} [gold]{Cards:plural:Soul|Souls}[/gold] into your [gold]Hand[/gold].", "CARVE_GHOST.title": "<PERSON><PERSON>", "CATALYST.description": "Double the enemy's [gold]Poison[/gold].", "CATALYST.title": "Catalyst", "CATASTROPHE.description": "Deal {Damage:diff()} damage.\nPlay a random Attack from your [gold]Draw Pile[/gold].", "CATASTROPHE.title": "Catastrophe", "CELESTIAL_MIGHT.description": "Deal {Damage:diff()} damage {Repeat:diff()} {Repeat:plural:time|times}.", "CELESTIAL_MIGHT.title": "Celestial Might", "CHAAARGE.description": "Deal damage equal to your [gold]Block[/gold] to all enemies.{OnTable:\n(Deals {Damage:diff()} damage)|}", "CHAAARGE.title": "CHAAARGE", "CHAOS.description": "[gold]Channel[/gold] {Repeat:diff()} random {Repeat:plural:orb|orbs}.", "CHAOS.title": "Chaos", "CHARGE_BATTERY.description": "Gain {Block:diff()} [gold]Block[/gold].\nNext turn, gain {Energy:energyIcons()}.", "CHARGE_BATTERY.title": "Charge Battery", "CHEST_BEAM.description": "Deal {Damage:diff()} damage. Afflict this card with [gold]Cooldown[/gold] 2.", "CHEST_BEAM.title": "Chest Beam", "CHILD_OF_THE_STARS.description": "Whenever you spend {singleStarIcon}, gain {BlockForStars:diff()} [gold]Block[/gold] for each {singleStarIcon} spent.", "CHILD_OF_THE_STARS.title": "Child of the Stars", "CHRYSALIS.description": "Shuffle {Cards:diff()} random Skills into your [gold]Draw Pile[/gold]. They cost 0 {energyPrefix:energyIcons(1)} this combat.", "CHRYSALIS.title": "Ch<PERSON><PERSON><PERSON>", "CINDER.description": "Deal {Damage:diff()} damage.\n[gold]Exhaust[/gold] the top {CardsToExhaust:diff()} {CardsToExhaust:plural:card|cards} in your [gold]Draw Pile[/gold].", "CINDER.title": "Cinder", "CLASH.description": "Can only be played if every card in your [gold]Hand[/gold] is an Attack.\nDeal {Damage:diff()} damage.", "CLASH.title": "Clash", "CLAW.description": "Deal {Damage:diff()} damage.\nIncrease the damage of all Claw cards by {Increase:diff()} this combat.", "CLAW.title": "Claw", "CLEANSE.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Exhaust[/gold] 1 card from your [gold]Draw Pile[/gold].", "CLEANSE.title": "Cleanse", "CLOAK_AND_DAGGER.description": "Gain {Block:diff()} [gold]Block[/gold].\nAdd {Cards:diff()} [gold]{Cards:plural:Shiv|Shivs}[/gold] into your [gold]Hand[/gold].", "CLOAK_AND_DAGGER.title": "<PERSON><PERSON><PERSON> and Dagger", "CLOAK_OF_STARS.description": "Gain {Block:diff()} [gold]Block[/gold].", "CLOAK_OF_STARS.title": "Cloak of Stars", "CLUMSY.description": "", "CLUMSY.title": "Clumsy", "COLD_SNAP.description": "Deal {Damage:diff()} damage.\n[gold]Channel[/gold] 1 [gold]Frost[/gold].", "COLD_SNAP.title": "Cold Snap", "COLLISION_COURSE.description": "Deal {Damage:diff()} damage.\nAdd a [gold]Debris[/gold] into your [gold]Hand[/gold].", "COLLISION_COURSE.title": "Collision Course", "COLOSSUS.description": "Gain {Block:diff()} [gold]Block[/gold].\nYou receive 50% less damage from [gold]Vulnerable[/gold] enemies this turn.", "COLOSSUS.title": "Coloss<PERSON>", "COMBUST.description": "At the end of your turn, lose 1 HP and deal {EnemyDamage:diff()} damage to all enemies.", "COMBUST.title": "Combust", "COMET.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].\nApply {Weak:diff()} [gold]Weak[/gold].", "COMET.title": "Comet", "COMPILE_DRIVER.description": "Deal {Damage:diff()} damage.\nDraw 1 card for each unique Orb you have. {OnTable:\n(Draw {Cards:diff()} {Cards:plural:card|cards})|}", "COMPILE_DRIVER.title": "Compile Driver", "COMPUTE.description": "Gain {Focus:diff()} [gold]Focus[/gold].\nAdd a [gold]Bit Rot[/gold] into your [gold]Discard Pile[/gold].", "COMPUTE.title": "Compute", "CONFLAGRATION.description": "Deal {BaseDamage:diff()} damage to all enemies, plus {ExtraDamage:diff()} additional damage for each other Attack you've played this turn.{OnTable:\n(Deals {Damage:diff()} damage)|}", "CONFLAGRATION.title": "Conflagration", "CONQUEROR.description": "Deal {Damage:diff()} damage.\n[gold]Forge[/gold] {Forge:diff()} for every card in your hand.", "CONQUEROR.title": "Conqueror", "CONSTELLATION.description": "Gain {singleStarIcon} for each card in your hand.", "CONSTELLATION.title": "Constellation", "CONSUMING_SHADOW.description": "[gold]Channel[/gold] {Repeat:diff()} [gold]Dark[/gold].\nAt the end of your turn, [gold]Evoke[/gold] your leftmost Orb.", "CONSUMING_SHADOW.title": "Consuming Shadow", "CONTRACTILITY.description": "Next turn, gain [gold]Block[/gold] equal to your current [gold]Block[/gold].", "CONTRACTILITY.title": "Contractility", "CONVERGENCE.description": "Gain {Stars:starIcons()}.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "CONVERGENCE.title": "Convergence", "COOLHEADED.description": "[gold]Channel[/gold] 1 [gold]Frost[/gold].\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "COOLHEADED.title": "Coolheaded", "COORDINATE.description": "Give another player {Strength:diff()} [gold]Strength[/gold] this turn.", "COORDINATE.title": "Coordinate", "CORROSIVE_WAVE.description": "Whenever you draw a card this turn, apply {CorrosiveWave:diff()} [gold]Poison[/gold] to all enemies.", "CORROSIVE_WAVE.title": "Corrosive Wave", "CORRUPTION.description": "Skills cost 0 {energyPrefix:energyIcons(1)}.\nWhenever you play a Skill, [gold]Exhaust[/gold] it.", "CORRUPTION.title": "Corruption", "COSMIC_INDIFFERENCE.description": "Gain {Block:diff()} [gold]Block[/gold].\nPut a card from your [gold]Discard Pile[/gold] on top of your [gold]Draw Pile[/gold].", "COSMIC_INDIFFERENCE.selectionScreenPrompt": "Choose a Card to Put On Top of Your Draw Pile.", "COSMIC_INDIFFERENCE.title": "Cosmic Indifference", "COUNTDOWN.description": "At the start of your turn, apply {DoomPerTurn:diff()} [gold]Doom[/gold] to all enemies.", "COUNTDOWN.title": "Countdown", "COWER.description": "Apply {Weak:diff()} [gold]Weak[/gold].", "COWER.title": "Cower", "CREATIVE_AI.description": "At the start of your turn, add a random Power into your hand.", "CREATIVE_AI.title": "Creative AI", "CRESCENT_SPEAR.description": "Deal {Damage:diff()} damage.\nDeal {ExtraDamage:diff()} additional damage for every {singleStarIcon} you have.{OnTable:\n(Deals {TotalDamage:diff()} damage)|}", "CRESCENT_SPEAR.title": "Crescent Spear", "CRIMSON_MANTLE.description": "At the start of your turn, lose 1 HP and gain {CrimsonMantlePower:diff()} [gold]Block[/gold].", "CRIMSON_MANTLE.title": "Crimson Mantle", "CRUELTY.description": "[gold]Vulnerable[/gold] enemies take an additional {ExtraDamage:diff()}% damage.", "CRUELTY.title": "Cruelty", "CRUSHING_DEPTHS.description": "Deal {Damage:diff()} damage for each other card played this turn.{OnTable:\n(Hits {Repeat:diff()} {Repeat:plural:time|times})|}", "CRUSHING_DEPTHS.title": "Crushing Depths", "CRUSH_UNDER.description": "Deal {Damage:diff()} damage to all enemies. All enemies lose {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "CRUSH_UNDER.title": "Crush Under", "CURSE_OF_THE_BELL.description": "", "CURSE_OF_THE_BELL.title": "Curse of the Bell", "DAGGER_SPRAY.description": "Deal {Damage:diff()} damage to all enemies twice.", "DAGGER_SPRAY.title": "<PERSON><PERSON>", "DAGGER_THROW.description": "Deal {Damage:diff()} damage.\nDraw 1 card.\nDiscard 1 card.", "DAGGER_THROW.title": "<PERSON><PERSON>", "DANSE_MACABRE.description": "Whenever you play a card that costs {Energy:energyIcons()} or more, gain {DanseMacabrePower:diff()} [gold]Block[/gold]. ", "DANSE_MACABRE.title": "<PERSON><PERSON>", "DARKNESS.description": "[gold]Channel[/gold] 1 [gold]Dark[/gold].{IfUpgraded:show:\nTrigger the passive ability of all [gold]Dark[/gold] orbs.}", "DARKNESS.title": "Darkness", "DARK_EMBRACE.description": "Whenever a card is [gold]Exhausted[/gold],\ndraw 1 card.", "DARK_EMBRACE.title": "<PERSON> Embrace", "DARK_SHACKLES.description": "Enemy loses {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "DARK_SHACKLES.title": "<PERSON> Shackles", "DASH.description": "Gain {Block:diff()} [gold]Block[/gold].\nDeal {Damage:diff()} damage.", "DASH.title": "Dash", "DAZED.description": "", "DAZED.title": "<PERSON><PERSON>", "DEADLY_POISON.description": "Apply {Poison:diff()} [gold]Poison[/gold].", "DEADLY_POISON.title": "<PERSON>ly <PERSON>", "DEATHBRINGER.description": "Apply {Doom:diff()} [gold]Doom[/gold] to all enemies.", "DEATHBRINGER.title": "Deathbringer", "DEATHS_DOOR.description": "Gain {Block:diff()} [gold]Block[/gold].\nIf you applied [gold]Doom[/gold] this turn, gain [gold]Block[/gold] {Repeat:diff()} additional {Repeat:plural:time|times}.", "DEATHS_DOOR.title": "Death's Door", "DEATHS_VISAGE.description": "Apply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "DEATHS_VISAGE.title": "Death's Visage", "DEBRIS.description": "", "DEBRIS.title": "<PERSON><PERSON><PERSON>", "DECAY.description": "At the end of your turn, if this is in your [gold]Hand[/gold], take {Damage:diff()} damage.", "DECAY.title": "Decay", "DECISIONS_DECISIONS.description": "Draw {Cards:diff()} cards.\nChoose a Skill in your [gold]Hand[/gold] and play it {Repeat:diff()} times.", "DECISIONS_DECISIONS.selectionScreenPrompt": "Decide a Skill.", "DECISIONS_DECISIONS.title": "Decisions, Decisions", "DEFEND_DEFECT.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFEND_DEFECT.title": "Defend", "DEFEND_IRONCLAD.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFEND_IRONCLAD.title": "Defend", "DEFEND_NECROBINDER.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFEND_NECROBINDER.title": "Defend", "DEFEND_REGENT.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFEND_REGENT.title": "Defend", "DEFEND_SILENT.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFEND_SILENT.title": "Defend", "DEFILE.description": "Deal {Damage:diff()} damage.", "DEFILE.title": "<PERSON><PERSON><PERSON>", "DEFLECT.description": "Gain {Block:diff()} [gold]Block[/gold].", "DEFLECT.title": "Deflect", "DEFRAGMENT.description": "Gain {Focus:diff()} [gold]Focus[/gold].", "DEFRAGMENT.title": "Defragment", "DELAY.description": "Gain {Block:diff()} [gold]Block[/gold].\nNext turn,\ngain {Energy:energyIcons()}.", "DELAY.title": "Delay", "DEMESNE.description": "At the start of your turn, gain {Energy:energyIcons()} and draw {Cards:diff()} additional {Cards:plural:card|cards}.", "DEMESNE.title": "Demesne", "DEMON_FORM.description": "At the start of your turn, gain {Strength:diff()} [gold]Strength[/gold].", "DEMON_FORM.title": "Demon Form", "DEPRECATED_CARD.description": "This card has been removed from the game.", "DEPRECATED_CARD.title": "Deprecated Card", "DESPAIR.description": "For every 1 [gold]Doom[/gold] you apply, gain 1 [gold]Block[/gold]", "DESPAIR.title": "Despair", "DEVASTATE.description": "Deal {Damage:diff()} damage.", "DEVASTATE.title": "Devastate", "DEVOUR_LIFE.description": "[gold]Exhaust[/gold] all [gold]Souls[/gold].\nGain {Strength:diff()} [gold]Strength[/gold] for each [gold]Exhausted[/gold] card.", "DEVOUR_LIFE.title": "Devour Life", "DISCOVERY.description": "Choose 1 of 3 random cards to add into your [gold]Hand[/gold]. It costs 0 {energyPrefix:energyIcons(1)} this turn.", "DISCOVERY.title": "Discovery", "DISMANTLE.description": "Deal {Damage:diff()} damage.\nIf the enemy is [gold]Vulnerable[/gold], hits twice.", "DISMANTLE.title": "Dismantle", "DISTRACTION.description": "Add a random Skill into your [gold]Hand[/gold]. It costs 0 {energyPrefix:energyIcons(1)} this turn.", "DISTRACTION.title": "Distraction", "DODGE_AND_ROLL.description": "Gain {Block:diff()} [gold]Block[/gold].\nNext turn, gain {Block:diff()} [gold]Block[/gold].", "DODGE_AND_ROLL.title": "Dodge and Roll", "DOMINATE.description": "Gain {StrengthPerVulnerable:diff()} [gold]Strength[/gold] for each [gold]Vulnerable[/gold] on the enemy.", "DOMINATE.title": "Dominate", "DOOM_AND_GLOOM.description": "Deal {Damage:diff()} damage to all enemies.\n[gold]Channel[/gold] 1 [gold]Dark[/gold].", "DOOM_AND_GLOOM.title": "Doom and Gloom", "DOUBLE_ENERGY.description": "Double your Energy.", "DOUBLE_ENERGY.title": "Double Energy", "DOUBT.description": "At the end of your turn, if this is in your [gold]Hand[/gold], gain {Weak:diff()} [gold]Weak[/gold].", "DOUBT.title": "<PERSON><PERSON><PERSON>", "DRAIN_POWER.description": "Deal {Damage:diff()} damage.\n[gold]Upgrade[/gold] {Cards:diff()} random {Cards:plural:card|cards} in your [gold]Discard Pile[/gold] for the rest of combat.", "DRAIN_POWER.title": "Drain Power", "DRAMATIC_ENTRANCE.description": "Deal {Damage:diff()} damage to all enemies.", "DRAMATIC_ENTRANCE.title": "Dramatic Entrance", "DREDGE.description": "Return {Cards:diff()} random {Cards:plural:card|cards} from your [gold]Discard Pile[/gold] to your [gold]Hand[/gold].", "DREDGE.title": "Dredge", "DRUM_OF_BATTLE.description": "Draw {Cards:diff()} cards.\nAt the end of your turn, [gold]Exhaust[/gold] the top card of your [gold]Draw Pile[/gold].", "DRUM_OF_BATTLE.title": "Drum Of Battle", "DUALCAST.description": "[gold]Evoke[/gold] your rightmost Orb twice.", "DUALCAST.title": "Dualcast", "DUAL_WIELD.description": "Choose an Attack or Power card. Add {IfUpgraded:show:{Cards} copies|a copy} of that card into your [gold]Hand[/gold].", "DUAL_WIELD.selectionScreenPrompt": "Choose a Card to <PERSON><PERSON>", "DUAL_WIELD.title": "Dual Wield", "DYING_STAR.description": "All enemies lose {StrengthLoss:diff()} [gold]Strength[/gold] this turn.\nDeal {Damage:diff()} damage to all enemies.", "DYING_STAR.title": "Dying Star", "ECHO_FORM.description": "The first card you play each turn is played an extra time.", "ECHO_FORM.title": "Echo Form", "EIDOLON.description": "Apply {Doom:inverseDiff()} [gold]Doom[/gold] to YOURSELF.\nGain {Intangible:diff()} [gold]Intangible[/gold].", "EIDOLON.title": "Eidolon", "ELECTRODYNAMICS.description": "[gold]Channel[/gold] {Repeat:diff()} [gold]Lightning[/gold].\n[gold]Lightning[/gold] now hits all enemies.", "ELECTRODYNAMICS.title": "Electrodynamics", "EMPOWER.description": "At the start of your turn, give [gold]Osty[/gold] {EmpowerPower:diff()} [gold]Strength[/gold].", "EMPOWER.title": "Empower", "END_OF_DAYS.description": "Apply {Doom:diff()} [gold]Doom[/gold] to all enemies.\nKill enemies with more [gold]Doom[/gold] than HP.", "END_OF_DAYS.title": "End of Days", "ENFEEBLE.description": "Deal {Damage:diff()} damage.\n[gold]Vulnerable[/gold] and [gold]Weak[/gold] are twice as effective for the next {EnfeeblePower:diff()} turns.", "ENFEEBLE.title": "Enfeeble", "ENFEEBLING_TOUCH.description": "Enemy loses {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "ENFEEBLING_TOUCH.title": "Enfeebling Touch", "ENLIGHTENMENT.description": "Reduce the cost of all cards in your [gold]Hand[/gold] to 1 this {IfUpgraded:show:combat|turn}.", "ENLIGHTENMENT.title": "Enlightenment", "ENTHRALLED.description": "While in [gold]Hand[/gold], cards cost an additional {Energy:energyIcons()}.", "ENTHRALLED.title": "Enthralled", "ENTRENCH.description": "Double your [gold]Block[/gold].", "ENTRENCH.title": "Entrench", "ENTROPY.description": "At the start of your turn, [gold]Transform[/gold] {Cards:diff()} {Cards:plural:card|cards} in your [gold]Hand[/gold].", "ENTROPY.title": "Entropy", "ENVENOM.description": "Whenever an Attack deals unblocked damage, apply {Envenom:diff()} [gold]Poison[/gold].", "ENVENOM.title": "Envenom", "EQUILIBRIUM.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Retain[/gold] your [gold]Hand[/gold] this turn.", "EQUILIBRIUM.title": "Equilibrium", "ERADICATE.description": "Deal {Damage:diff()} damage X times.", "ERADICATE.title": "Eradicate", "ESCAPE_PLAN.description": "Draw 1 card.\nIf you draw a Skill, gain {Block:diff()} [gold]Block[/gold].", "ESCAPE_PLAN.title": "Escape Plan", "ETERNAL_ARMOR.description": "Gain {Block:diff()} [gold]Block[/gold] at the start of your next X turns.", "ETERNAL_ARMOR.title": "Eternal Armor", "EVIL_EYE.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain another {Block:diff()} [gold]Block[/gold] if you have [gold]Exhausted[/gold] a card this turn.", "EVIL_EYE.title": "Evil Eye", "EVOLVE.description": "Whenever you draw a [gold]Status[/gold] card, draw {EvolvePower:diff()} {EvolvePower:plural:card|cards}.", "EVOLVE.title": "Evolve", "EXECUTION.description": "Deal {Damage:diff()} damage.\n{IfUpgraded:show:Upgrade and play|Play} every [gold]Shiv[/gold] in your [gold]Exhaust Pile[/gold].", "EXECUTION.title": "Execution", "EXPECT_A_FIGHT.description": "Gain {energyPrefix:energyIcons(1)} for each Attack in your [gold]Hand[/gold].{OnTable:\n(Gain {Energy:energyIcons()}.)|}", "EXPECT_A_FIGHT.title": "Expect a Fight", "EXPERTISE.description": "Draw cards until you have {Cards:diff()} in your [gold]Hand[/gold].", "EXPERTISE.title": "Expertise", "EXPONENTIAL_GROWTH.description": "Whenever you play a cost X card, gain {Energy:energyIcons()}.", "EXPONENTIAL_GROWTH.title": "Exponential Growth", "EXPOSE.description": "Remove all [gold]Artifact[/gold] and [gold]Block[/gold] from the enemy.\nApply {Power:diff()} [gold]Vulnerable[/gold].", "EXPOSE.title": "Expose", "EXTERMINATE.description": "Deal {Damage:diff()} damage {Repeat:diff()} times to all enemies.", "EXTERMINATE.title": "Exterminate", "FALLING_STAR.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].\nApply {Weak:diff()} [gold]Weak[/gold].", "FALLING_STAR.title": "Falling Star", "FAN_OF_KNIVES.description": "[gold]Shivs[/gold] now hit all enemies.\nAdd {Shivs:diff()} [gold]Shivs[/gold] into your [gold]Hand[/gold].", "FAN_OF_KNIVES.title": "Fan of Knives", "FASTEN.description": "Defend cards gain {ExtraBlock:diff()} additional [gold]Block[/gold] when played.", "FASTEN.title": "<PERSON><PERSON>", "FAVORITE_STICK.description": "Deal {Damage:diff()} damage.\nWhen [gold]Osty[/gold] attacks, put this into your [gold]Hand[/gold] if it's in your [gold]Draw Pile[/gold].", "FAVORITE_STICK.title": "Favorite Stick", "FEAR.description": "Apply {Weak:diff()} [gold]Weak[/gold].", "FEAR.title": "Fear", "FEED.description": "Deal {Damage:diff()} damage.\nIf [gold]Fatal[/gold], raise your Max HP by {MaxHp:diff()}.", "FEED.title": "Feed", "FEEDING_FRENZY.description": "Gain {Strength:diff()} [gold]Strength[/gold] this turn.", "FEEDING_FRENZY.title": "Feeding Frenzy", "FEEL_NO_PAIN.description": "Whenever a card is [gold]Exhausted[/gold], gain {Power:diff()} [gold]Block[/gold].", "FEEL_NO_PAIN.title": "Feel No Pain", "FERAL.description": "Cards containing \"Strike\" cost {FeralPower:diff()} less {energyPrefix:energyIcons(1)}.", "FERAL.title": "Feral", "FESTER.description": "Apply {Doom:diff()} [gold]Doom[/gold] for each Skill already played this turn.{OnTable:\n(Apply {TotalDoom:diff()} [gold]Doom[/gold])|}", "FESTER.title": "Fester", "FETCH.description": "[gold]Osty[/gold] deals {OstyDamage:diff()} damage.\nIf the enemy has less HP than [gold]Osty[/gold], it is\n[gold]Stunned[/gold].", "FETCH.title": "<PERSON>tch", "FIEND_FIRE.description": "[gold]Exhaust[/gold] your [gold]Hand[/gold].\nDeal {Damage:diff()} damage for each card [gold]Exhausted[/gold].", "FIEND_FIRE.title": "Fiend Fire", "FIGHT_ME.description": "Deal {Damage:diff()} damage.\nGain {Strength:diff()} [gold]Strength[/gold].\nThe enemy gains {EnemyStrength:diff()} [gold]Strength[/gold].", "FIGHT_ME.title": "Fight Me!", "FINESSE.description": "Gain {Block:diff()} [gold]Block[/gold].\nDraw 1 card.", "FINESSE.title": "Finesse", "FINISHER.description": "Deal {Damage:diff()} damage for each Attack already played this turn.{OnTable:\n(Attacks {Repeat:diff()} {Repeat:plural:time|times})|}", "FINISHER.title": "Finisher", "FISTICUFFS.description": "Deal {Damage:diff()} damage.\nGain [gold]Block[/gold] equal to unblocked damage dealt.", "FISTICUFFS.title": "Fistic<PERSON>s", "FLAME_BARRIER.description": "Gain {Block:diff()} [gold]Block[/gold].\nWhenever you are attacked this turn, deal {DamageBack:diff()} damage back.", "FLAME_BARRIER.title": "<PERSON>", "FLAME_IN_THE_DARK.description": "Gain {Energy:energyIcons()}.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "FLAME_IN_THE_DARK.title": "Flame In The Dark", "FLASH_OF_STEEL.description": "Deal {Damage:diff()} damage.\nDraw 1 card.", "FLASH_OF_STEEL.title": "Flash of Steel", "FLATTEN.description": "[gold]Osty[/gold] deals {OstyDamage:diff()} damage {Repeat:diff()} {Repeat:plural:time|times}.", "FLATTEN.title": "<PERSON><PERSON>", "FLECHETTES.description": "Deal {Damage:diff()} damage for each Skill in your [gold]Hand[/gold].{OnTable:\n(Hits {Repeat:diff()} {Repeat:plural:time|times})|}", "FLECHETTES.title": "<PERSON><PERSON>chet<PERSON>", "FLEX.description": "Gain {Strength:diff()} [gold]Strength[/gold] this turn.", "FLEX.title": "Flex", "FLICK_FLACK.description": "Deal {Damage:diff()} damage to all enemies.", "FLICK_FLACK.title": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "FLYING_KNEE.description": "Deal {Damage:diff()} damage.\nNext turn, gain {Energy:energyIcons()}.", "FLYING_KNEE.title": "Flying Knee", "FOLLOW_THROUGH.description": "Can only be played\nafter a Skill.\nDeal {Damage:diff()} damage twice.", "FOLLOW_THROUGH.title": "Follow Through", "FOOTWORK.description": "Gain {Dexterity:diff()} [gold]Dexterity[/gold].", "FOOTWORK.title": "Footwork", "FORBIDDEN_GRIMOIRE.description": "At the end of combat, remove a card from your [gold]Deck[/gold].", "FORBIDDEN_GRIMOIRE.title": "Forbidden Grimoire", "FORCE_FIELD.description": "Gain {Block:diff()} [gold]Block[/gold].\nCost 1 less {energyPrefix:energyIcons(1)} for each Power card played this combat.", "FORCE_FIELD.title": "Force Field", "FORGOTTEN_RITUAL.description": "The first time you [gold]Exhaust[/gold] a card each turn, gain {Energy:energyIcons()}.", "FORGOTTEN_RITUAL.title": "Forgotten Ritual", "FRANTIC_ESCAPE.description": "Get farther away.\nIncrease [gold]Sandpit[/gold] by 1.\nIncrease the cost of this card by 1.", "FRANTIC_ESCAPE.title": "Frantic Escape", "FRIENDSHIP.description": "At the start of your turn, if [gold]Osty[/gold] is alive, gain {Energy:energyIcons()}.", "FRIENDSHIP.title": "Friendship", "FTL.description": "Deal {Damage:diff()} damage.\nIf you have played less than {PlayMax:diff()} cards this turn, draw 1 card.", "FTL.title": "FTL", "FUSION.description": "[gold]Channel[/gold] 1 [gold]Plasma[/gold].", "FUSION.title": "Fusion", "GAMMA_BEAM.description": "Deal {Damage:diff()} damage to all enemies.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "GAMMA_BEAM.title": "Gamma Beam", "GANG_UP.description": "Deal {Damage:diff()} damage for each times other allies have attacked the enemy this turn.{OnTable:\n(Deals {TrueDamage:diff()} damage)|}", "GANG_UP.title": "Gang Up", "GATHER_LIGHT.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain {Stars:starIcons()}.", "GATHER_LIGHT.title": "Gather Light", "GENESIS.description": "At the start of your turn, gain {StarsPerTurn:starIcons()}.", "GENESIS.title": "Genesis", "GENETIC_ALGORITHM.description": "Gain {Block:diff()} [gold]Block[/gold].\nPermanently increase this card's [gold]Block[/gold] by {Increase:diff()}.", "GENETIC_ALGORITHM.title": "Genetic Algorithm", "GHOST_EATER.description": "Remove all [gold]Doom[/gold].\nGain {Energy:energyIcons()} for every 6 [gold]Doom[/gold] removed.", "GHOST_EATER.title": "Ghost Eater", "GIANT_ROCK.description": "Deal {Damage:diff()} damage.", "GIANT_ROCK.title": "Giant Rock", "GLACIER.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Channel[/gold] 2 [gold]Frost[/gold].", "GLACIER.title": "Glacier", "GLASS_KNIFE.description": "Deal {Damage:diff()} damage twice. Decrease this card's damage by 2 this combat.", "GLASS_KNIFE.title": "Glass Knife", "GLIMMER.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.\nPut {PutBack:diff()} {PutBack:plural:card|cards} from your [gold]Hand[/gold] on top of your [gold]Draw Pile[/gold].", "GLIMMER.selectionScreenPrompt": "Choose Cards to put on top of your Draw Pile", "GLIMMER.title": "Glimmer", "GLITTERSTREAM.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain [gold]Block[/gold] twice if you gained {singleStarIcon} this turn.", "GLITTERSTREAM.title": "Glitterstream", "GLOW.description": "Gain {Stars:starIcons()}.", "GLOW.title": "Glow", "GOLD_AXE.description": "Deal damage equal to the number of cards played this combat.{OnTable:\\n(Deals {Damage:diff()} damage)|}", "GOLD_AXE.title": "Gold Axe", "GOOD_INSTINCTS.description": "Gain {Block:diff()} [gold]Block[/gold].", "GOOD_INSTINCTS.title": "Good Instincts", "GO_FOR_THE_EYES.description": "Deal {Damage:diff()} damage.\nIf the enemy intends to attack, apply {Weak:diff()} [gold]Weak[/gold].", "GO_FOR_THE_EYES.title": "Go for the Eyes", "GRACEFUL.description": "Every time you play {Cards:diff()} Skills in a single turn, gain {energyPrefix:energyIcons(1)}.", "GRACEFUL.title": "Graceful", "GRAND_FINALE.description": "Can only be played if there are no cards in your [gold]Draw Pile[/gold]. Deal {Damage:diff()} damage to all enemies.", "GRAND_FINALE.title": "Grand Finale", "GRAPPLE.description": "Apply {Vulnerable:diff()} [gold]Vulnerable[/gold].\nEnemy loses {StrengthLoss:diff()} [gold]Strength[/gold] this turn for each [gold]Vulnerable[/gold] it has.", "GRAPPLE.title": "<PERSON>rap<PERSON>", "GRAVEBLAST.description": "Deal {Damage:diff()} damage.\nPut the top card of your [gold]Discard Pile[/gold] into your [gold]Hand[/gold].", "GRAVEBLAST.title": "Graveblast", "GRAVE_WARDEN.description": "Gain {Block:diff()} [gold]Block[/gold].\nAdd {Cards:diff()} [gold]{Cards:plural:Soul|Souls}[/gold] into your [gold]Hand[/gold].", "GRAVE_WARDEN.title": "Grave Warden", "GRAVITY_WELL.description": "Deal {Damage:diff()} damage to all enemies. Whenever you play a card this turn, enemies take {Gravity:diff()} damage.", "GRAVITY_WELL.title": "Gravity Well", "GREED.description": "", "GREED.title": "Greed", "GUARDS.description": "[gold]Transform[/gold] any number of cards in your [gold]Hand[/gold] into [gold]Minion Sacrifice{IfUpgraded:show:+|}[/gold].", "GUARDS.selectionScreenPrompt": "Choose cards to [gold]Transform[/gold]", "GUARDS.title": "GUARDS!!", "GUIDING_STAR.description": "Deal {Damage:diff()} damage.\nNext turn, draw {Cards:diff()} {Cards:plural:card|cards}.", "GUIDING_STAR.title": "Guiding Star", "GUILTY.description": "Removed from your [gold]Deck[/gold] after {Combats:diff()} {Combats:plural:combat|combats}.", "GUILTY.title": "Guilty", "HAND_OF_GREED.description": "Deal {Damage:diff()} damage.\nIf [gold]Fatal[/gold], gain {Gold:diff()} gold.", "HAND_OF_GREED.title": "Hand of Greed", "HAND_TRICK.description": "Gain {Block:diff()} [gold]Block[/gold].\nAdd [gold]Sly[/gold] to a Skill in your [gold]Hand[/gold] for the rest of combat.", "HAND_TRICK.selectionScreenPrompt": "Choose a Card to add [gold]Sly[/gold] to.", "HAND_TRICK.title": "Hand Trick", "HANG.description": "Deal {Damage:diff()} damage.\nDouble the damage all [gold]Hangs[/gold] deal to this enemy.", "HANG.title": "Hang", "HAUNT.description": "Whenever you play a [gold]Soul[/gold], all enemies lose {HpLoss:diff()} HP.", "HAUNT.title": "Haunt", "HAVOC.description": "Play the top card of your [gold]Draw Pile[/gold] and [gold]Exhaust[/gold] it.", "HAVOC.title": "Havoc", "HAZE.description": "Apply {Poison:diff()} [gold]Poison[/gold] to all enemies.", "HAZE.title": "Haze", "HEADBUTT.description": "Deal {Damage:diff()} damage.\nPut a card from your [gold]Discard Pile[/gold] on top of your [gold]Draw Pile[/gold].", "HEADBUTT.selectionScreenPrompt": "Choose a Card to Put on Top of Your Draw Pile.", "HEADBUTT.title": "Headbutt", "HEATSINKS.description": "Whenever you play a Power, draw {Heatsinks:diff()} {Heatsinks:plural:card|cards}.", "HEATSINKS.title": "Heatsinks", "HEAVENLY_DRILL.description": "Deal {Damage:diff()} damage for each {singleStarIcon} spent this turn.{OnTable:\n(Repeat {Repeat:diff()} {Repeat:plural:times|time|times})|}", "HEAVENLY_DRILL.title": "<PERSON>ly Drill", "HEAVY_BLADE.description": "Deal {Damage:diff()} damage.\n[gold]Strength[/gold] affects this card {StrengthMultiplier:diff()} times.", "HEAVY_BLADE.title": "Heavy Blade", "HEGEMONY.description": "Deal {Damage:diff()} damage.\nNext turn, gain {Energy:energyIcons()}.", "HEGEMONY.title": "Hegemony", "HEIRLOOM_HAMMER.description": "Deal {Damage:diff()} damage.\nChoose a Colorless card in your hand. Add {IfUpgraded:show:{Repeat} copies|a copy} of that card into your [gold]Hand[/gold].", "HEIRLOOM_HAMMER.selectionScreenPrompt": "Choose a Colorless Card.", "HEIRLOOM_HAMMER.title": "<PERSON><PERSON><PERSON> Hammer", "HELLO_WORLD.description": "At the start of your turn, add a random Common card to your [gold]Hand[/gold].", "HELLO_WORLD.title": "Hello World", "HELLRAISER.description": "Whenever you draw a card containing \"Strike\", it is played against a random enemy.", "HELLRAISER.title": "Hellraiser", "HEMOKINESIS.description": "Lose {HpLoss:diff()} HP.\nDeal {Damage:diff()} damage.", "HEMOKINESIS.title": "Hemokinesis", "HIDDEN_CACHE.description": "Next turn, gain {Stars:starIcons()}\nand draw {Cards:diff()} {Cards:plural:card|cards}.", "HIDDEN_CACHE.title": "Hidden Cache", "HIDDEN_DAGGERS.description": "Discard {Cards:diff()} {Cards:plural:card|cards}.\nAdd {Shivs:diff()} [gold]{Cards:plural:Shiv|Shivs}[/gold] into your [gold]Hand[/gold].", "HIDDEN_DAGGERS.title": "Hidden Daggers", "HIDDEN_GEM.description": "Shuffle your [gold]Discard Pile[/gold] into your [gold]Draw Pile[/gold].\nA random shuffled card gains [gold]Replay[/gold] {Replay:diff()}.", "HIDDEN_GEM.title": "Hidden Gem", "HOLOGRAM.description": "Gain {Block:diff()} [gold]Block[/gold].\n Put a card from your [gold]Discard Pile[/gold] into your [gold]Hand[/gold].", "HOLOGRAM.selectionScreenPrompt": "Choose a Card to Put Back In Your Hand.", "HOLOGRAM.title": "Hologram", "HOTFIX.description": "Gain {Focus:diff()} [gold]Focus[/gold] this turn.", "HOTFIX.title": "Hotfix", "HOWL_FROM_BEYOND.description": "At the start of your turn, plays from the [gold]Exhaust[/gold] pile.\nDeal {Damage:diff()} damage to all enemies.", "HOWL_FROM_BEYOND.title": "Howl From Beyond", "HUDDLE_UP.description": "All allies draw {Cards:diff()} cards.", "HUDDLE_UP.title": "Huddle Up", "HYPERBEAM.description": "Deal {Damage:diff()} damage to all enemies.\nLose {Focus:diff()} [gold]Focus[/gold].", "HYPERBEAM.title": "Hyperbeam", "ILLUMINATE.description": "Deal {Damage:diff()} damage.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "ILLUMINATE.title": "Illuminate", "IMMORTALITY.description": "Lose {HpLoss:diff()} HP.\nGain {Intangible:diff()} [gold]Intangible[/gold].", "IMMORTALITY.title": "Immortality", "IMPALE.description": "Deal {Damage:diff()} damage.\nApply {Poison:diff()} [gold]Poison[/gold].", "IMPALE.title": "Impale", "IMPATIENCE.description": "If you have no Attacks in your [gold]Hand[/gold], draw {Cards:diff()} cards.", "IMPATIENCE.title": "Impatience", "IMPERVIOUS.description": "Gain {Block:diff()} [gold]Block[/gold].", "IMPERVIOUS.title": "<PERSON>mper<PERSON>", "INEVITABILITY.description": "At the end of your turn, deal damage equal to your {singleStarIcon} to all enemies.", "INEVITABILITY.title": "Inevitability", "INFECTION.description": "At the end of your turn, if this is in your [gold]Hand[/gold], take {Damage:diff()} damage.", "INFECTION.title": "Infection", "INFERNAL_BLADE.description": "Add a random Attack into your [gold]Hand[/gold]. It costs 0 {energyPrefix:energyIcons(1)} this turn.", "INFERNAL_BLADE.title": "Infernal Blade", "INFINITE_BLADES.description": "At the start of your turn, add a [gold]Shiv[/gold] into your [gold]Hand[/gold].", "INFINITE_BLADES.title": "Infinite Blades", "INFLAME.description": "Gain {Strength:diff()} [gold]Strength[/gold].", "INFLAME.title": "Inflame", "INJURY.description": "", "INJURY.title": "Injury", "INTERCEPT.description": "Gain {Block:diff()} [gold]Block[/gold].\nRedirect all incoming attacks that would be dealt to another ally this turn to you.", "INTERCEPT.title": "Intercept", "INVOKE.description": "Deal {Damage:diff()} damage.\nIf [gold]Osty[/gold] is dead, [gold]Summon[/gold] {Summon:diff()}.", "INVOKE.title": "Invoke", "IRON_WAVE.description": "Gain {Block:diff()} [gold]Block[/gold].\nDeal {Damage:diff()} damage.", "IRON_WAVE.title": "Iron Wave", "ITERATE.description": "Deal {Damage:diff()} damage.\nTrigger the passive ability of your rightmost Orb {Repeat:diff()} {Repeat:plural:time|times}.", "ITERATE.title": "Iterate", "I_AM_INVINCIBLE.description": "Gain {Block:diff()} [gold]Block[/gold].\nAt the end of your turn, if this is on top of your [gold]Draw Pile[/gold], play it.", "I_AM_INVINCIBLE.title": "I am Invincible", "JACKPOT.description": "Deal {Damage:diff()} damage.\nAdd {Cards:diff()} random{IfUpgraded:show: Upgraded} 0 cost {Cards:plural:card|cards} to your [gold]Hand[/gold].", "JACKPOT.title": "Jackpot", "JACK_OF_ALL_TRADES.description": "Add {Cards:diff()} random Colorless {Cards:plural:card|cards} into your [gold]Hand[/gold].", "JACK_OF_ALL_TRADES.title": "Jack of All Trades", "JUGGLING.description": "Whenever you play an Attack, gain {Strength:diff()} [gold]Strength[/gold] this turn.", "JUGGLING.title": "Juggling", "JUNK_KING.description": "Whenever you [gold]Channel Scrap[/gold], draw {JunkKingPower:diff()} {JunkKingPower:plural:card|cards}.", "JUNK_KING.title": "Junk King", "KINGLY_KICK.description": "Deal {Damage:diff()} damage.\nWhenever you draw this card, lower its cost by 1.", "KINGLY_KICK.title": "Kingly Kick", "KINGLY_PUNCH.description": "Deal {Damage:diff()} damage.\nWhenever you draw this card, increase its damage by {Increase:diff()} this combat.", "KINGLY_PUNCH.title": "Kingly Punch", "KNOCKOUT_BLOW.description": "Deal {Damage:diff()} damage.\nIf this kills an enemy, gain {Stars:starIcons()}.", "KNOCKOUT_BLOW.title": "Knockout Blow", "KNOW_THY_PLACE.description": "Apply {Weak:diff()} [gold]Weak[/gold].\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "KNOW_THY_PLACE.title": "<PERSON> Thy Place", "LANTERN_KEY.description": "Marks a unique event in the next Act.", "LANTERN_KEY.title": "Lantern Key", "LEADING_STRIKE.description": "Deal {Damage:diff()} damage.\nAdd {Shivs:diff()} [gold]Shiv[/gold] into your [gold]Hand[/gold].", "LEADING_STRIKE.title": "Leading Strike", "LEAP.description": "Gain {Block:diff()} [gold]Block[/gold].", "LEAP.title": "<PERSON><PERSON>", "LEG_SWEEP.description": "Apply {Weak:diff()} [gold]Weak[/gold].\nGain {Block:diff()} [gold]Block[/gold].", "LEG_SWEEP.title": "<PERSON><PERSON>", "LETHALITY.description": "Your first {LethalityPower:plural:Attack|{LethalityPower:diff()} Attacks} each turn {LethalityPower:plural:deals|deal} 50% additional damage.", "LETHALITY.title": "<PERSON><PERSON><PERSON>", "LOOP.description": "At the start of your turn, trigger the passive ability of your rightmost Orb{IfUpgraded:show: 2 times}.", "LOOP.title": "Loop", "LUMINESCE.description": "Gain {Energy:energyIcons()}.", "LUMINESCE.title": "Luminesce", "LUNAR_BLAST.description": "Deal {Damage:diff()} damage for each Skill already played this turn.{OnTable:\n(Attacks {Repeat:diff()} times)|}", "LUNAR_BLAST.title": "Lunar Blast", "MACHINE_LEARNING.description": "At the start of your turn, draw {Cards:diff()} additional {Cards:plural:card|cards}.", "MACHINE_LEARNING.title": "Machine Learning", "MADNESS.description": "Reduce the cost of a random card in your [gold]Hand[/gold] to 0 this combat.", "MADNESS.title": "Madness", "MAD_SCIENCE.description": "{CardType:choose(Attack|Skill|Power):Deal {Damage:diff()} damage|Gain {Block:diff()} [gold]Block[/gold]|Gain {PowerAmount:diff()} [gold]{PowerType}[/gold]}.{Body:\nGain {BodyStrength:diff()} [gold]Strength[/gold] this turn.|}{Flame:\nApply {FlameVulnerable:diff()} [gold]Vulnerable[/gold].|}{Heart:\nNext turn, gain {HeartEnergy:energyIcons()}.|}{Ice:\nApply {IceWeak:diff()} [gold]Weak[/gold].|}{Invulnerable:\nGain {InvulnerableBuffer:diff()} [gold]Buffer[/gold].|}{Mind:\nDraw {MindCards:diff()} cards.|}{Soul:\nAdd a random card to your hand. It costs 0 {energyPrefix:energyIcons(1)} this turn.|}{Stability:\nGain {StabilityDexterity:diff()} [gold]Dexterity[/gold] this turn.|}{Vengeance:\nGain {VengeanceThorns:diff()} [gold]Thorns[/gold].|}{Focus:\n[gold]Transform[/gold] a random card from your [gold]Draw Pile[/gold] into a copy of this card.|}", "MAD_SCIENCE.title": "Mad Science", "MAKE_IT_SO.description": "Deal {Damage:diff()} damage.\nEvery {Cards:diff()} {Cards:plural:card|cards} you play in a turn, put this into your [gold]Hand[/gold].", "MAKE_IT_SO.title": "Make It So", "MAKE_USE.description": "Deal {Damage:diff()} damage.\nIf this kills an enemy,\n[gold]Summon[/gold] {Summon:diff()}.", "MAKE_USE.title": "Make Use", "MALAISE.description": "Enemy loses X{IfUpgraded:show:+1} [gold]Strength[/gold]. Apply X{IfUpgraded:show:+1} [gold]Weak[/gold].", "MALAISE.title": "Malaise", "MANGLE.description": "Deal {Damage:diff()} damage.\nEnemy loses {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "MANGLE.title": "<PERSON>gle", "MANIFEST_AUTHORITY.description": "Gain {Block:diff()} [gold]Block[/gold].\nAdd 1 random{IfUpgraded:show: [gold]Upgraded[/gold]} Colorless card to your hand.", "MANIFEST_AUTHORITY.title": "Manifest Authority", "MASTER_OF_STRATEGY.description": "Draw {Cards:diff()} cards.", "MASTER_OF_STRATEGY.title": "Master of Strategy", "MASTER_PLANNER.description": "When you play a Skill, it gains [gold]Sly[/gold].", "MASTER_PLANNER.title": "Master Planner", "MAUL.description": "Deal {Damage:diff()} damage twice.\nIncrease the damage of all Maul cards by {Increase:diff()} this combat.", "MAUL.title": "<PERSON><PERSON>", "MAYHEM.description": "At the start of your turn, play the top card of your [gold]Draw Pile[/gold].", "MAYHEM.title": "Mayhem", "MELANCHOLY.description": "When this [gold]Exhausts[/gold], gain {Block:diff()} [gold]Block[/gold].", "MELANCHOLY.title": "<PERSON><PERSON><PERSON><PERSON>", "MEMENTO_MORI.description": "Deal {BaseDamage:diff()} damage.\nDeal {ExtraDamage:diff()} damage for each card discarded this turn.{OnTable:\n(Deals {Damage:diff()} damage)|}", "MEMENTO_MORI.title": "<PERSON><PERSON>", "METAMORPHOSIS.description": "Shuffle {Cards:diff()} random Attacks into your [gold]Draw Pile[/gold]. They cost 0 {energyPrefix:energyIcons(1)} this combat.", "METAMORPHOSIS.title": "Metamorphosis", "METEOR_SHOWER.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].\nApply {Weak:diff()} [gold]Weak[/gold].", "METEOR_SHOWER.title": "Meteor Shower", "METEOR_STRIKE.description": "Deal {Damage:diff()} damage.\n[gold]Channel[/gold] 3 [gold]Plasma[/gold].", "METEOR_STRIKE.title": "Meteor Strike", "MIMIC.description": "Gain [gold]Block[/gold] equal to the [gold]Block[/gold] on another player.{OnTable:\\n(Gain {Block:diff()} [gold]Block[/gold])|}", "MIMIC.title": "Mimic", "MIND_BLAST.description": "Deal damage equal to the number of cards in your [gold]Draw Pile[/gold].{OnTable:\\n(Deals {Damage:diff()} damage)|}", "MIND_BLAST.title": "Mind Blast", "MINION_DIVE_BOMB.description": "Deal {Damage:diff()} damage.", "MINION_DIVE_BOMB.title": "Minion Dive Bomb", "MINION_SACRIFICE.description": "Gain {Block:diff()} [gold]Block[/gold].", "MINION_SACRIFICE.title": "Minion Sacrifice", "MIRAGE.description": "Gain [gold]Block[/gold] equal to [gold]Poison[/gold] on all enemies.{OnTable:\\n(Gain {Block:diff()} [gold]Block[/gold])|}", "MIRAGE.title": "Mirage", "MISCHIEF.description": "Deal {Damage:diff()} damage.\nWhenever the enemy deals unblocked attack damage this turn, it takes that much damage.", "MISCHIEF.title": "Mischief", "MISERY.description": "Deal {Damage:diff()} damage.\nApply any debuffs on the enemy to all other enemies.", "MISERY.title": "Misery", "MOCK_ATTACK_CARD.description": "", "MOCK_ATTACK_CARD.title": "Mock Attack Card", "MOCK_CURSE_CARD.description": "", "MOCK_CURSE_CARD.title": "Mock Curse Card", "MOCK_POWER_CARD.description": "", "MOCK_POWER_CARD.title": "Mock Power Card", "MOCK_SKILL_CARD.description": "", "MOCK_SKILL_CARD.discardSelectionPrompt": "Select cards to discard", "MOCK_SKILL_CARD.title": "<PERSON><PERSON>", "MOLTEN_FIST.description": "Deal {Damage:diff()} damage.\nDouble the enemy's [gold]Vulnerable[/gold].", "MOLTEN_FIST.title": "Molten Fist", "MONARCHS_GAZE.description": "Whenever you attack an enemy, it loses {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "MONARCHS_GAZE.title": "Monarch's Gaze", "MONOLOGUE.description": "Whenever you play a card this turn, gain {Power:diff()} [gold]Strength[/gold] this turn.", "MONOLOGUE.title": "Monologue", "MULTI_CAST.description": "[gold]Evoke[/gold] your rightmost Orb {IfUpgraded:show:X+1|X} times.", "MULTI_CAST.title": "Multi-Cast", "NECRO_MASTERY.description": "Increase [gold]Summon[/gold] on cards by {SummonIncrease:diff()}.", "NECRO_MASTERY.title": "Necro Mastery", "NEGATIVE_PULSE.description": "Gain {Block:diff()} [gold]Block[/gold].\nApply {Doom:diff()} [gold]Doom[/gold] to all enemies.", "NEGATIVE_PULSE.title": "Negative Pulse", "NEUROSURGE.description": "Gain {Energy:energyIcons()}.\nDraw {Cards:diff()} {Cards:plural:card|cards}.\nAt the start of your turn, apply {NeurosurgePower:diff()} [gold]Doom[/gold] to yourself.", "NEUROSURGE.title": "Neurosurge", "NEUTRALIZE.description": "Deal {Damage:diff()} damage.\nApply {Weak:diff()} [gold]Weak[/gold].", "NEUTRALIZE.title": "Neutralize", "NEUTRON_AEGIS.description": "Gain {Plating:diff()} [gold]Plating[/gold].", "NEUTRON_AEGIS.title": "Neutron Aegis", "NIGHTMARE.description": "Choose a card.\nNext turn, add 3 copies of that card into your [gold]Hand[/gold].", "NIGHTMARE.selectionScreenPrompt": "Choose a Card.", "NIGHTMARE.title": "Nightmare", "NORMALITY.description": "You cannot play more than 3 cards this turn.{OnTable:\n({Cards:plural:{} card left|{} cards left})|}", "NORMALITY.title": "Normality", "NOSTALGIA.description": "The first Attack or Skill you play each turn is placed on top of your [gold]Draw Pile[/gold].", "NOSTALGIA.title": "Nostalgia", "NOXIOUS_FUMES.description": "At the start of your turn, apply {PoisonPerTurn:diff()} [gold]Poison[/gold] to all enemies.", "NOXIOUS_FUMES.title": "Noxious Fumes", "NO_ESCAPE.description": "Deal {Damage:diff()} damage.\nApply {Doom:diff()} [gold]Doom[/gold].", "NO_ESCAPE.title": "No Escape", "OBLIVION.description": "{IfUpgraded:show:Triple|Double} all [gold]Doom[/gold] you apply this turn.", "OBLIVION.title": "Oblivion", "OFFERING.description": "Lose {HpLoss:diff()} HP.\nGain {Energy:energyIcons()}.\nDraw {Cards:diff()} cards.", "OFFERING.title": "Offering", "OMNISLICE.description": "Deal {Damage:diff()} damage.\nDamage all other enemies equal to the damage dealt.", "OMNISLICE.title": "Omnislice", "ONE_TWO_PUNCH.description": "This turn, your next {Attacks:cond:>1?{Attacks:diff()} Attacks are|Attack is} played an extra time.", "ONE_TWO_PUNCH.title": "One-Two Punch", "ORBIT.description": "Gain {Block:diff()} [gold]Block[/gold].\nShuffle your [gold]Discard Pile[/gold] into your [gold]Draw Pile[/gold].", "ORBIT.title": "Orbit", "ORDER.description": "Deal {Damage:diff()} damage.\n[gold]Retain[/gold] your hand this turn.", "ORDER.title": "Order", "OUTMANEUVER.description": "Next turn, gain {Energy:energyIcons()}.", "OUTMANEUVER.title": "Outmaneuver", "OVERCLOCK.description": "Draw {Cards:diff()} cards.\nAdd a [gold]Burn[/gold] into your [gold]Discard Pile[/gold].", "OVERCLOCK.title": "Overclock", "OVERWHELM.description": "Deal {Damage:diff()} damage for each Power played this combat.{OnTable:\n(Hits {Repeat:diff()} times)|}", "OVERWHELM.title": "Overwhelm", "PACTS_END.description": "Can only be played if you have {Cards:diff()} or more {Cards:plural:card|cards} in your [gold]Exhaust Pile[/gold].\nDeal {Damage:diff()} damage to all enemies.", "PACTS_END.title": "Pact's End", "PAELS_STRIKE.description": "Deal damage equal to the number of cards in your [gold]Deck[/gold].{OnTable:\n(Deals {Damage:diff()} damage)|}", "PAELS_STRIKE.title": "Pa<PERSON>'s Strike", "PAGESTORM.description": "Whenever you draw an [gold]Ethereal[/gold] card, draw {Cards:diff()} {Cards:plural:card|cards}.", "PAGESTORM.title": "Pagestorm", "PALE_BLUE_DOT.description": "If you play {CardPlay} or more cards in a turn, draw {Cards:diff()} {Cards:plural:card|cards} at the start of your next turn.", "PALE_BLUE_DOT.title": "Pale <PERSON>", "PANACEA.description": "Gain {Artifact:diff()} [gold]Artifact[/gold].", "PANACEA.title": "Panacea", "PANACHE.description": "Every time you play 5 cards in a single turn, deal {PanacheDamage:diff()} damage to all enemies.", "PANACHE.title": "<PERSON><PERSON>", "PANIC_BUTTON.description": "Gain {Block:diff()} [gold]Block[/gold].\nYou cannot gain [gold]Block[/gold] from cards for {Turns:diff()} {Turns:plural:turn|turns}.", "PANIC_BUTTON.title": "Panic Button", "PARRY.description": "[gold]Forge[/gold] {Forge:diff()}.\nWhenever you play [gold]Sovereign Blade[/gold], gain {ParryPower:diff()} [gold]Block[/gold].", "PARRY.title": "<PERSON>", "PARSE.description": "Draw {Cards:diff()} cards.", "PARSE.title": "Parse", "PARTICLE_WALL.description": "Gain {Block:diff()} [gold]Block[/gold].\nReturn this card to your [gold]Hand[/gold].", "PARTICLE_WALL.title": "Particle Wall", "PATTER.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain {Vigor:diff()} [gold]Vigor[/gold].", "PATTER.title": "<PERSON><PERSON>", "PECK.description": "Deal {Damage:diff()} damage {Repeat:diff()} times.", "PECK.title": "<PERSON>", "PERFECTED_STRIKE.description": "Deal {BaseDamage:diff()} damage, plus {ExtraDamage:diff()} additional damage for all your cards containing \"Strike\".{OnTable:\n(Deals {Damage:diff()} damage)|}", "PERFECTED_STRIKE.title": "Perfected Strike", "PERFECT_REFLECTION.description": "Gain {Block:diff()} [gold]Block[/gold].\nBlocked attack damage is reflected to your attacker this turn.", "PERFECT_REFLECTION.title": "Perfect Reflection", "PHOTON_CUT.description": "Deal {Damage:diff()} damage.\nDraw {Cards:diff()} {Cards:plural:card|cards}.\nPut a card from your [gold]Hand[/gold] on top of your [gold]Draw Pile[/gold].", "PHOTON_CUT.selectionScreenPrompt": "Choose a Card to Put on Top of Your Draw Pile.", "PHOTON_CUT.title": "Photon <PERSON>", "PIERCING_WAIL.description": "All enemies lose {StrengthLoss:diff()} [gold]Strength[/gold] this turn.", "PIERCING_WAIL.title": "Piercing Wail", "PILLAR_OF_CREATION.description": "Whenever you create a card, gain {Block:diff()} [gold]Block[/gold].", "PILLAR_OF_CREATION.title": "Pillar of Creation", "PITY.description": "Fully heal an enemy.\nGain {singleStarIcon} for every {Hp:inverseDiff()} HP healed.", "PITY.title": "Pity", "POISONED_STAB.description": "Deal {Damage:diff()} damage.\nApply {Poison:diff()} [gold]Poison[/gold].", "POISONED_STAB.title": "Poisoned Stab", "POKE.description": "[gold]Summon[/gold] {Summon:diff()}.\n[gold]Osty[/gold] deals {OstyDamage:diff()} damage.", "POKE.title": "<PERSON><PERSON>", "POLISH.description": "Deal {Damage:diff()} damage.\nThe next Colorless card you play costs 0 {energyPrefix:energyIcons(1)}.", "POLISH.selectionScreenPrompt": "Choose a Card", "POLISH.title": "Polish", "POMMEL_STRIKE.description": "Deal {Damage:diff()} damage.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "POMMEL_STRIKE.title": "Pommel Strike", "POUNCE.description": "Deal {Damage:diff()} damage.\nThe next Skill you play costs 0 {energyPrefix:energyIcons(1)}.", "POUNCE.title": "<PERSON><PERSON><PERSON>", "PRECISE_CUT.description": "Deal {BaseDamage:diff()} damage.\nDeal 2 less damage for each other card in your [gold]Hand[/gold].{OnTable:\n(Deal {Damage:diff()} damage)|}", "PRECISE_CUT.title": "Precise Cut", "PREDATOR.description": "Deal {Damage:diff()} damage.\nNext turn, draw 2 additional cards.", "PREDATOR.title": "Predator", "PREPARED.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.\nDiscard {Cards:diff()} {Cards:plural:card|cards}.", "PREPARED.title": "Prepared", "PREP_TIME.description": "At the start of your turn, gain {PrepTimePower:diff()} [gold]Vigor[/gold].", "PREP_TIME.title": "Prep Time", "PRIMAL_FORCE.description": "Transform all Attacks in your [gold]Hand[/gold] into [gold]Giant Rock{IfUpgraded:show:+|}[/gold].", "PRIMAL_FORCE.title": "Primal Force", "PRODUCTION.description": "Gain {Energy:energyIcons()}.", "PRODUCTION.title": "Production", "PROPHESIZE.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.", "PROPHESIZE.title": "Prophesize", "PROTECTOR.description": "[gold]Summon[/gold] {Summon:diff()}.\n[gold]Osty[/gold] deals {OstyDamage:diff()} damage.", "PROTECTOR.title": "Protector", "PROWESS.description": "Gain {Strength:diff()} [gold]Strength[/gold].\nGain {Dexterity:diff()} [gold]Dexterity[/gold].", "PROWESS.title": "Prowess", "PULL_AGGRO.description": "[gold]Summon[/gold] {Summon:diff()}.\nGain {Block:diff()} [gold]Block[/gold].", "PULL_AGGRO.title": "<PERSON><PERSON> Aggro", "PULL_FROM_BELOW.description": "Whenever you draw a card this turn, the enemy loses {PullFromBelowPower:diff()} HP.", "PULL_FROM_BELOW.title": "Pull From Below", "PUMMEL.description": "Deal {Damage:diff()} damage {Repeat:diff()} times.", "PUMMEL.title": "<PERSON><PERSON><PERSON>", "PURITY.description": "Exhaust up to {Cards:diff()} cards in your [gold]Hand[/gold].", "PURITY.selectionScreenPrompt": "Choose up to {Cards:diff()} Cards to Exhaust", "PURITY.title": "Purity", "PUTREFY.description": "Apply {Power:diff()} [gold]Weak[/gold].\nApply {Power:diff()} [gold]Vulnerable[/gold].", "PUTREFY.title": "<PERSON><PERSON>fy", "QUASAR.description": "Choose 1 of 3 random{IfUpgraded:show: Upgraded|} Colorless cards to add into your [gold]Hand[/gold].", "QUASAR.title": "Quasar", "QUICK_SLASH.description": "Deal {Damage:diff()} damage.\nDraw 1 card.", "QUICK_SLASH.title": "Quick Slash", "RADIATE.description": "Deal {Damage:diff()} damage to all enemies for each {Stars:starIcons()} gained this turn.{OnTable:\n(Attacks {Repeat:diff()} {Repeat:plural:time|times})|}", "RADIATE.title": "Radiate", "RAGE.description": "Whenever you play an Attack this turn, gain {Power:diff()} [gold]Block[/gold].", "RAGE.title": "Rage", "RAINBOW.description": "[gold]Channel[/gold] 1 [gold]Lightning[/gold].\n[gold]Channel[/gold] 1 [gold]Frost[/gold].\n[gold]Channel[/gold] 1 [gold]Dark[/gold].", "RAINBOW.title": "Rainbow", "RALLY.description": "All players gain {Block:diff()} [gold]Block[/gold].", "RALLY.title": "Rally", "RAMPAGE.description": "Deal {Damage:diff()} damage.\nIncrease this card's damage by {Increase:diff()} this combat.", "RAMPAGE.title": "Rampage", "RATTLE.description": "[gold]Osty[/gold] deals {OstyDamage:diff()} damage {IfUpgraded:show:3 times|twice}.", "RATTLE.title": "Rattle", "REANIMATE.description": "If [gold]Osty[/gold] is dead, [gold]Summon[/gold] {Summon:diff()}.", "REANIMATE.title": "Reanimate", "REAP.description": "Deal {Damage:diff()} damage.", "REAP.title": "Reap", "REAPER_FORM.description": "At the end of your turn, deal damage to all enemies equal to {ReaperFormPower:diff()}% of their max HP.", "REAPER_FORM.title": "Reaper Form", "REBOOT.description": "Shuffle all your cards into your [gold]Draw Pile[/gold].\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "REBOOT.title": "Reboot", "REBOUND.description": "Deal {Damage:diff()} damage.\nPut the next card you play this turn on top of your [gold]Draw Pile[/gold].", "REBOUND.title": "Rebound", "RECONSTRUCTION.description": "[gold]Osty[/gold] gains {Regen:diff()} [gold]Regen[/gold].", "RECONSTRUCTION.title": "Reconstruction", "RECURSION.description": "[gold]Evoke[/gold] your rightmost Orb. [gold]Channel[/gold] the Orb that was just [gold]Evoked[/gold].", "RECURSION.title": "Recursion", "RECYCLE.description": "[gold]Exhaust[/gold] a card.\nGain {energyPrefix:energyIcons(1)} equal to its cost.", "RECYCLE.title": "Recycle", "REFINE_BLADE.description": "[gold]Forge[/gold] {Forge:diff()}.\nNext turn, gain {Energy:energyIcons()}.", "REFINE_BLADE.title": "Refine Blade", "REFLECTIVE_FORTRESS.description": "Whenever you fully [gold]Block[/gold] an attack, the blocked damage is reflected to the attacker.", "REFLECTIVE_FORTRESS.title": "Reflective Fortress", "REFLEX.description": "Draw {Cards:diff()} cards.", "REFLEX.title": "Reflex", "REGRET.description": "At the end of your turn, if this is in your [gold]Hand[/gold], lose 1 HP for each card in your [gold]Hand[/gold].", "REGRET.title": "<PERSON><PERSON>", "REINFORCED_BODY.description": "Gain {Block:diff()} [gold]Block[/gold] X times.", "REINFORCED_BODY.title": "Reinforced Body", "RELAX.description": "Gain {Block:diff()} [gold]Block[/gold].\nDraw {Cards:diff()} {Cards:plural:card|cards} and gain {Energy:energyIcons()} at the start of your next turn.", "RELAX.title": "Relax", "REND.description": "Deal {Damage:diff()} damage.\nDeal {ExtraDamage:diff()} additional damage for each unique debuff on the enemy.", "REND.title": "Rend", "REPROGRAM.description": "Lose {Focus:diff()} [gold]Focus[/gold].\nGain {Strength:diff()} [gold]Strength[/gold].\nGain {Dexterity:diff()} [gold]Dexterity[/gold].", "REPROGRAM.title": "Reprogram", "RESERVES.description": "If you don't have enough {energyPrefix:energyIcons(1)} for a card, 2{singleStarIcon} are used per {energyPrefix:energyIcons(1)} instead.", "RESERVES.title": "Reserves", "RESONANCE.description": "The first time you shuffle your [gold]Draw Pile[/gold] each turn, gain {Energy:energyIcons()}.", "RESONANCE.title": "Resonance", "RESTLESSNESS.description": "If your [gold]Hand[/gold] is empty, draw {Cards:diff()} cards and gain {Energy:energyIcons()}.", "RESTLESSNESS.title": "Restlessness", "RICOCHET.description": "Deal {Damage:diff()} damage to a random enemy {Repeat:diff()} times.", "RICOCHET.title": "Ricochet", "RIP_AND_TEAR.description": "Deal {Damage:diff()} damage to a random enemy twice.", "RIP_AND_TEAR.title": "Rip and Tear", "ROLLING_BOULDER.description": "At the start of your turn, deal {BaseDamage:diff()} damage to all enemies and increase this damage by {RollingBoulderPower:diff()}.", "ROLLING_BOULDER.title": "Rolling Boulder", "ROPE_REBOUND.description": "Deal {Damage:diff()} damage.\nThe next Attack another ally plays on the enemy is played an extra time.", "ROPE_REBOUND.title": "Rope Rebound", "ROYAL_GAMBLE.description": "Gain {Stars:diff()} {singleStarIcon}.", "ROYAL_GAMBLE.title": "Royal Gamble", "RUPTURE.description": "Whenever you lose HP from a card, gain {Strength:diff()} [gold]Strength[/gold].", "RUPTURE.title": "Rupture", "SACRIFICE.description": "If [gold]O<PERSON>[/gold] is alive, he dies and you gain [gold]Block[/gold] equal to double his Max HP.", "SACRIFICE.title": "Sacrifice", "SCAVENGE.description": "[gold]Osty[/gold] deals {OstyDamage:diff()} damage.\nIf [gold]Fatal[/gold], [gold]Osty[/gold] gains {MaxHp:diff()} Max HP.", "SCAVENGE.title": "Scavenge", "SCORNED_ASCENDERS_BANE.description": "", "SCORNED_ASCENDERS_BANE.title": "Ascender's <PERSON><PERSON>", "SCOURGE.description": "Apply {Doom:diff()} [gold]Doom[/gold].", "SCOURGE.title": "Scourge", "SCRAPE.description": "Deal {Damage:diff()} damage.\nDraw {Cards:diff()} cards.\nDiscard all cards drawn this way that do not cost 0 {energyPrefix:energyIcons(1)}", "SCRAPE.title": "Scrape", "SCRAPPY.description": "[gold]Channel[/gold] 1 [gold]Scrap[/gold].", "SCRAPPY.title": "<PERSON><PERSON><PERSON>", "SCRAWL.description": "Draw cards until your hand is full.", "SCRAWL.title": "Scrawl", "SCULPTING_STRIKE.description": "Deal {Damage:diff()} damage.\nCards in your [gold]Hand[/gold] become [gold]Ethereal[/gold] this turn.", "SCULPTING_STRIKE.title": "Sculpting Strike", "SEANCE.description": "Transform {Cards:diff()} {Cards:plural:card|cards} in your [gold]Draw Pile[/gold] into {Cards:plural:a [gold]Soul[/gold]|[gold]Souls[/gold]}.", "SEANCE.title": "<PERSON><PERSON>", "SECOND_WIND.description": "[gold]Exhaust[/gold] all non-Attack cards in your [gold]Hand[/gold]. Gain {Block:diff()} [gold]Block[/gold] for each [gold]Exhausted[/gold] card.", "SECOND_WIND.title": "Second Wind", "SECRET_TECHNIQUE.description": "Put a Skill from your [gold]Draw Pile[/gold] into your [gold]Hand[/gold].", "SECRET_TECHNIQUE.selectionScreenPrompt": "Select a Skill to put in your hand.", "SECRET_TECHNIQUE.title": "Secret Technique", "SECRET_WEAPON.description": "Put an Attack from your [gold]Draw Pile[/gold] into your [gold]Hand[/gold].", "SECRET_WEAPON.selectionScreenPrompt": "Select an Attack to put in your hand.", "SECRET_WEAPON.title": "Secret Weapon", "SEEKING_EDGE.description": "[gold]Forge[/gold] {Forge:diff()}.\n[gold]Sovereign Blade[/gold] now deals damage to all enemies.", "SEEKING_EDGE.title": "Seeking Edge", "SEEKING_STRIKE.description": "Deal {Damage:diff()} damage.\n Choose 1 of {Cards:diff()} cards in your [gold]Draw Pile[/gold] to add to your [gold]Hand[/gold].", "SEEKING_STRIKE.selectionScreenPrompt": "Select a card to put in your hand.", "SEEKING_STRIKE.title": "Seeking Strike", "SENTRY_MODE.description": "At the end of your turn, [gold]Osty[/gold] attacks a random enemy for {SentryModeDamage:diff()} damage.", "SENTRY_MODE.title": "Sentry Mode", "SETUP_STRIKE.description": "Deal {Damage:diff()} damage.\nGain {Strength:diff()} [gold]Strength[/gold] this turn.", "SETUP_STRIKE.title": "Setup Strike", "SEVEN_STARS.description": "Deal {Damage:diff()} damage to all enemies {Repeat:plural:{} time|{} times}.", "SEVEN_STARS.title": "Seven Stars", "SEVERANCE.description": "Deal {Damage:diff()} damage.\nAdd a [gold]Soul[/gold] to your [gold]Hand[/gold], [gold]Draw Pile[/gold], and [gold]Discard Pile[/gold].", "SEVERANCE.title": "Severance", "SHADOWMELD.description": "Double your [gold]Block[/gold] gain this turn.", "SHADOWMELD.title": "Shadowmeld", "SHADOW_STEP.description": "Discard your [gold]Hand[/gold].\nNext turn, Attacks deal double damage.", "SHADOW_STEP.title": "Shadow Step", "SHAME.description": "At the end of your turn, if this is in your [gold]Hand[/gold], gain {Frail:diff()} [gold]Frail[/gold].", "SHAME.title": "Shame", "SHARED_FATE.description": "Lose {PlayerStrengthLoss:diff()} [gold]Strength[/gold].\nEnemy loses {EnemyStrengthLoss:diff()} [gold]Strength[/gold].", "SHARED_FATE.title": "Shared Fate", "SHARP_EDGE.description": "Whenever you create a [gold]Shiv[/gold], [gold]Upgrade[/gold] it.", "SHARP_EDGE.title": "<PERSON> Edge", "SHINING_STRIKE.description": "Deal {Damage:diff()} damage.\nGain {Stars:starIcons()}.\nPut this card on top of your [gold]Draw Pile[/gold].", "SHINING_STRIKE.title": "Shining Strike", "SHINY_STRIKE.description": "Deal {Damage:diff()} damage.\nLower the cost of {Cards:diff()} Colorless {Cards:plural:card|cards} in your hand by {energyPrefix:energyIcons(1)} this turn.", "SHINY_STRIKE.selectionScreenPrompt": "Choose a Card", "SHINY_STRIKE.title": "Shiny Strike", "SHIV.description": "Deal {Damage:diff()} damage{HasFanOfKnives.BoolVal: to all enemies|}.", "SHIV.title": "Shiv", "SHOCKWAVE.description": "Apply {Power:diff()} [gold]Weak[/gold] and [gold]Vulnerable[/gold] to all enemies.", "SHOCKWAVE.title": "Shockwave", "SHREDDER.description": "Deal {Damage:diff()} damage to a random enemy {Repeat:diff()} times. For each unique enemy hit, [gold]Channel[/gold] 1 [gold]Scrap[/gold].", "SHREDDER.title": "Shredder", "SHROUD.description": "Whenever you apply [gold]Doom[/gold], gain {Block:diff()} [gold]Block[/gold].", "SHROUD.title": "<PERSON><PERSON><PERSON>", "SHRUG_IT_OFF.description": "Gain {Block:diff()} [gold]Block[/gold].\nDraw {Cards:diff()} card.", "SHRUG_IT_OFF.title": "Shrug it Off", "SKEWER.description": "Deal {Damage:diff()} damage X times.", "SKEWER.title": "Skewer", "SKIM.description": "Draw {Cards:diff()} cards.", "SKIM.title": "Skim", "SLEIGHT_OF_FLESH.description": "Whenever you apply a debuff to an enemy, they take {SleightOfFleshPower:diff()} damage.", "SLEIGHT_OF_FLESH.title": "Sleight Of Flesh", "SLICE.description": "Deal {Damage:diff()} damage.", "SLICE.title": "Slice", "SLIMED.description": "Draw 1 card.", "SLIMED.title": "Slimed", "SNAKEBITE.description": "Deal {Damage:diff()} damage.\nApply {Poison:diff()} [gold]Poison[/gold].", "SNAKEBITE.title": "Snakebite", "SNEAKY_STRIKE.description": "Deal {Damage:diff()} damage.\nIf you have discarded a card this turn,\ngain {Energy:energyIcons()}.", "SNEAKY_STRIKE.title": "Sneaky Strike", "SNOWFIELD.description": "Whenever you channel [gold]Frost[/gold], gain {Block:diff()} [gold]Block[/gold].", "SNOWFIELD.title": "Snowfield", "SOLAR_STRIKE.description": "Deal {Damage:diff()} damage.\nGain {Stars:starIcons()}.", "SOLAR_STRIKE.title": "Solar Strike", "SOOT.description": "", "SOOT.title": "<PERSON><PERSON>", "SOUL.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.", "SOUL.title": "Soul", "SOUL_STORM.description": "Add {Repeat:diff()} [gold]{Repeat:plural:Soul|Souls}[/gold] into your [gold]Hand[/gold].\nWhen Retained, increase [gold]Souls[/gold] created by {Cards:diff()}.", "SOUL_STORM.title": "Soul Storm", "SOVEREIGN_BLADE.description": "Deal {Damage:diff()} damage{SeekingEdge.BoolVal: to all enemies|}.", "SOVEREIGN_BLADE.title": "Sovereign Blade", "SOW.description": "Deal {Damage:diff()} damage to all enemies.", "SOW.title": "Sow", "SPECTRAL.description": "Gain {Block:diff()} [gold]Block[/gold].\nRemove all [gold]Weak[/gold] and [gold]Frail[/gold].", "SPECTRAL.title": "Spectral", "SPECTRUM_SHIFT.description": "At the start of your turn, add {Cards:diff()} random{IfUpgraded:show: [gold]Upgraded[/gold]} Colorless {Cards:plural:card|cards} to your hand.", "SPECTRUM_SHIFT.title": "Spectrum Shift", "SPEEDSTER.description": "Whenever you draw a card during your turn, deal {SpeedsterPower:diff()} damage to a random enemy.", "SPEEDSTER.title": "Speedster", "SPIKED.description": "At the end of your turn, take {Damage:diff()} damage.", "SPIKED.title": "Spiked", "SPIRIT_OF_ASH.description": "Whenever you play an [gold]Ethereal[/gold] card, gain {BlockOnExhaust:diff()} [gold]Block[/gold].", "SPIRIT_OF_ASH.title": "Spirit of Ash", "SPLASH.description": "Choose 1 of 3 random cards from another character to add into your [gold]Hand[/gold]. It costs 0 {energyPrefix:energyIcons(1)} this turn.", "SPLASH.title": "Splash", "SPOILS_MAP.description": "Marks a site of extra Relics and Gold in the next Act.", "SPOILS_MAP.title": "Spoils Map", "SPOILS_OF_BATTLE.description": "[gold]Forge[/gold] {Forge:diff()}.", "SPOILS_OF_BATTLE.title": "Spoils Of Battle", "SPORE_MIND.description": "While in [gold]Hand[/gold], lose {Damage} HP whenever you play a Power.", "SPORE_MIND.title": "Spore Mind", "SQUASH.description": "Deal {Damage:diff()} damage.\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "SQUASH.title": "Squash", "STACK.description": "Gain [gold]Block[/gold] equal to the number of cards in your [gold]Discard Pile[/gold] {IfUpgraded:show:+3|}.{OnTable:\n(Gain {Block:diff()} [gold]Block[/gold].)|}", "STACK.title": "<PERSON><PERSON>", "STAMPEDE.description": "At the end of your turn, {Power:diff()} random {Power:plural:Attack in your [gold]Hand[/gold] is played against a random enemy.|Attacks in your [gold]Hand[/gold] are played against random enemies.}", "STAMPEDE.title": "Stampede", "STARDUST.description": "Deal {Damage:diff()} damage to a random enemy X times.", "STARDUST.title": "Stardust", "STARS_IN_HIS_EYES.description": "Add a random card with a {singleStarIcon} cost to your [gold]Hand[/gold]. It costs 0 {singleStarIcon} this turn.", "STARS_IN_HIS_EYES.title": "Stars in His Eyes", "STAR_BLAST.description": "Gain {Block:diff()} [gold]Block[/gold].\nDeal {Damage:diff()} damage.", "STAR_BLAST.title": "Star Blast", "STAR_SHIELD.description": "Gain {Block:diff()} [gold]Block[/gold].\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "STAR_SHIELD.title": "Star Shield", "STOKE.description": "[gold]Exhaust[/gold] your [gold]Hand[/gold].\nDraw a card for each card [gold]Exhausted[/gold] this way.", "STOKE.title": "Stoke", "STOMP.description": "Deal {Damage:diff()} damage to all enemies.\nThis card costs 0 {energyPrefix:energyIcons(1)} if you have played at least {CardPlayThreshold:diff()} other {CardPlayThreshold:plural:attack|attacks} this turn.", "STOMP.title": "<PERSON><PERSON><PERSON>", "STONE_ARMOR.description": "Gain {Plating:diff()} [gold]Plating[/gold].\n<PERSON><PERSON> {Dexterity:diff()} [gold]Dexterity[/gold].", "STONE_ARMOR.title": "Stone Armor", "STORM.description": "Whenever you play a Power, [gold]Channel[/gold] 1 [gold]Lightning[/gold].", "STORM.title": "Storm", "STORM_OF_STEEL.description": "Discard your [gold]Hand[/gold]. \nAdd 1 [gold]Shiv{IfUpgraded:show:+|}[/gold] into your [gold]Hand[/gold] for each card discarded.", "STORM_OF_STEEL.title": "Storm of Steel", "STRANGLING_STRIKE.description": "Deal {Damage:diff()} damage.\nWhenever you play an Attack this turn, the enemy loses {StranglingStrikePower:diff()} HP.", "STRANGLING_STRIKE.title": "Strangling Strike", "STRATAGEM.description": "Whenever you shuffle your [gold]Draw Pile[/gold], choose a card from it to put into your hand.", "STRATAGEM.title": "Stratagem", "STREAMLINE.description": "Deal {Damage:diff()} damage.\nReduce this card's cost by 1 this combat.", "STREAMLINE.title": "Streamline", "STRIKE_DEFECT.description": "Deal {Damage:diff()} damage.", "STRIKE_DEFECT.title": "Strike", "STRIKE_IRONCLAD.description": "Deal {Damage:diff()} damage.", "STRIKE_IRONCLAD.title": "Strike", "STRIKE_NECROBINDER.description": "Deal {Damage:diff()} damage.", "STRIKE_NECROBINDER.title": "Strike", "STRIKE_REGENT.description": "Deal {Damage:diff()} damage.", "STRIKE_REGENT.title": "Strike", "STRIKE_SILENT.description": "Deal {Damage:diff()} damage.", "STRIKE_SILENT.title": "Strike", "SUCKER_PUNCH.description": "Deal {Damage:diff()} damage.\nApply {Weak:diff()} [gold]Weak[/gold].", "SUCKER_PUNCH.title": "Sucker Punch", "SUMMON_FORTH.description": "[gold]Forge[/gold] {Forge:diff()}.\nMove [gold]Sovereign Blade[/gold] to your [gold]Hand[/gold] from anywhere.", "SUMMON_FORTH.title": "Summon Forth", "SUNDER.description": "Deal {Damage:diff()} damage.\nIf this kills an enemy, gain {Energy:energyIcons()}.", "SUNDER.title": "<PERSON><PERSON>", "SUPERMASSIVE.description": "Deal {Damage:diff()} damage.\nDeals {Extra:diff()} additional damage for each card you created this combat.", "SUPERMASSIVE.title": "Supermassive", "SUPPORT.description": "Give another player {Block:diff()} [gold]Block[/gold].", "SUPPORT.title": "Support", "SUPPRESS.description": "Deal {Damage:diff()} damage.\nApply {Weak:diff()} [gold]Weak[/gold].", "SUPPRESS.title": "Suppress", "SURVIVOR.description": "Gain {Block:diff()} [gold]Block[/gold].\nDiscard 1 card.", "SURVIVOR.title": "Survivor", "SWEEPING_BEAM.description": "Deal {Damage:diff()} damage to all enemies.\nDraw {Cards:diff()} {Cards:plural:card|cards}.", "SWEEPING_BEAM.title": "Sweeping <PERSON>am", "SWIFT_STRIKE.description": "Deal {Damage:diff()} damage.", "SWIFT_STRIKE.title": "Swift Strike", "SWIPE.description": "[gold]Osty[/gold] deals {OstyDamage:diff()} damage to all enemies.", "SWIPE.title": "Swipe", "SWORD_BOOMERANG.description": "Deal {Damage:diff()} damage to a random enemy {Repeat:diff()} times.", "SWORD_BOOMERANG.title": "Sword Boomerang", "SYMBIOSIS.description": "Whenever you [gold]Summon[/gold], draw {Cards:diff()} {Cards:plural:card|cards}.", "SYMBIOSIS.title": "Symbiosis", "TACTICIAN.description": "Gain {Energy:energyIcons()}.", "TACTICIAN.title": "Tactician", "TAG_IN.description": "[gold]Osty[/gold] gains {Strength:diff()} [gold]Strength[/gold] this turn.", "TAG_IN.title": "Tag In", "TAUNT.description": "Gain {Block:diff()} [gold]Block[/gold].\nApply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "TAUNT.title": "<PERSON><PERSON>", "TEAR_ASUNDER.description": "Deal {Damage:diff()} damage.\nHits an additional time for each time you lost HP this combat.{OnTable:\n(Hits {Repeat:diff()} {Repeat:plural:time|times}.)|}", "TEAR_ASUNDER.title": "<PERSON>r <PERSON>", "TEMPEST.description": "[gold]Channel[/gold] {IfUpgraded:show:X+1|X} [gold]Lightning[/gold].", "TEMPEST.title": "Tempest", "TERRAFORMING.description": "At the end of your turn, increase the damage of Attacks in your [gold]Draw Pile[/gold] by {TerraformingPower:diff()} this combat.", "TERRAFORMING.title": "Terraforming", "TESLA_COIL.description": "Trigger all [gold]Lightning[/gold] orbs against the enemy.", "TESLA_COIL.title": "Tesla Coil", "TEZCATARAS_MIGHT.description": "Gain {Strength:diff()} [gold]Strength[/gold].\nAt the end of your turn, lose {StrengthLoss:diff()} [gold]Strength[/gold].", "TEZCATARAS_MIGHT.title": "Tezcatara's Might", "THE_BOMB.description": "At the end of {Turns:diff()} {Turns:plural:turn|turns}, deal {BombDamage:diff()} damage to all enemies.", "THE_BOMB.title": "The Bomb", "THE_GAMBIT.description": "Gain {Block:diff()} [gold]Block[/gold].\nIf you take attack damage this combat, die.", "THE_GAMBIT.title": "The Gambit", "THE_SEALED_THRONE.description": "Whenever you draw this card, ignore star cost this turn.", "THE_SEALED_THRONE.title": "The Sealed Throne", "THE_SMITH.description": "[gold]Forge[/gold] {Forge:diff()}.", "THE_SMITH.title": "<PERSON> Smith", "THE_TANK.description": "Take double damage from enemies.\nAllies take half damage from enemies.", "THE_TANK.title": "The Tank", "THINKING_AHEAD.description": "Draw {Cards:diff()} {Cards:plural:card|cards}.\nPut a card from your [gold]Hand[/gold] on top of your [gold]Draw Pile[/gold].", "THINKING_AHEAD.selectionScreenPrompt": "Choose a Card to put on top of your Draw Pile", "THINKING_AHEAD.title": "Thinking Ahead", "THRUMMING_HATCHET.description": "Deal {Damage:diff()} damage.\nAt the start of your next turn, return this to your [gold]Hand[/gold].", "THRUMMING_HATCHET.title": "Thrumming Hatchet", "THUNDER.description": "Whenever you [gold]Evoke Lightning[/gold], deal {ThunderPower:diff()} damage to each enemy hit.", "THUNDER.title": "Thunder", "THUNDERCLAP.description": "Deal {Damage:diff()} damage and apply {Vulnerable:diff()} [gold]Vulnerable[/gold] to all enemies.", "THUNDERCLAP.title": "Thunderclap", "TIMES_UP.description": "Deal damage equal to the enemy's [gold]Doom[/gold].", "TIMES_UP.title": "Time's Up!", "TOOLS_OF_THE_TRADE.description": "At the start of your turn, draw 1 card and discard 1 card.", "TOOLS_OF_THE_TRADE.title": "Tools of the Trade", "TORIC_TOUGHNESS.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain {Block:diff()} [gold]Block[/gold] at the start of the next {Turns:diff()} {Turns:plural:turn|turns}.", "TORIC_TOUGHNESS.title": "<PERSON><PERSON>", "TOXIC.description": "At the end of your turn, if this is in your [gold]Hand[/gold], take {Damage:diff()} damage.", "TOXIC.title": "Toxic", "TRACKING.description": "[gold]Weak[/gold] enemies take {TrackingPower:diff()}% additional damage from Attacks and [gold]Poison[/gold].", "TRACKING.title": "Tracking", "TRAINING_STRIKE.description": "Deal {Damage:diff()} damage.\nIf [gold]Fatal[/gold], Cocoon gains 1 experience.", "TRAINING_STRIKE.title": "Training Strike", "TRANSFIGURE.description": "Add [gold]Replay[/gold] to a card in your [gold]Hand[/gold] for the rest of combat.\nIt costs an extra {Energy:energyIcons()}.", "TRANSFIGURE.selectionScreenPrompt": "Choose a Card to Add [gold]Replay[/gold] To.", "TRANSFIGURE.title": "Transfigure", "TRANSFUSE.description": "[gold]Osty[/gold] heals {Heal:diff()} HP.", "TRANSFUSE.title": "Transfuse", "TREMBLE.description": "Apply {Vulnerable:diff()} [gold]Vulnerable[/gold].", "TREMBLE.title": "Tremble", "TRIPLECAST.description": "[gold]Evoke[/gold] your rightmost Orb 3 times.", "TRIPLECAST.title": "Triplecast", "TRUE_GRIT.description": "Gain {Block:diff()} [gold]Block[/gold].\n[gold]Exhaust[/gold] 1 card{IfUpgraded:show:| at random}.", "TRUE_GRIT.title": "True Grit", "TURBO.description": "Gain {Energy:energyIcons()}.\nAdd a [gold]Void[/gold] into your [gold]Discard Pile[/gold].", "TURBO.title": "TURBO", "TWIN_STRIKE.description": "Deal {Damage:diff()} damage twice.", "TWIN_STRIKE.title": "Twin Strike", "TWIST.description": "Enemy loses {Damage:diff()} HP.\nAdd {Cards:diff()} [gold]{Cards:plural:Soul|Souls}[/gold] into your [gold]Hand[/gold].", "TWIST.title": "Twist", "TYRANNY.description": "At the start of your turn, draw a card and [gold]Exhaust[/gold] a card from your [gold]Hand[/gold].", "TYRANNY.title": "Tyranny", "ULTIMATE_DEFEND.description": "Gain {Block:diff()} [gold]Block[/gold].", "ULTIMATE_DEFEND.title": "Ultimate Defend", "ULTIMATE_STRIKE.description": "Deal {Damage:diff()} damage.", "ULTIMATE_STRIKE.title": "Ultimate Strike", "UNCANNY_DODGE.description": "Gain {Block:diff()} [gold]Block[/gold].", "UNCANNY_DODGE.title": "Uncanny Dodge", "UNDEATH.description": "Gain {Block:diff()} [gold]Block[/gold].\nAdd a copy of this card into your [gold]Discard Pile[/gold].", "UNDEATH.title": "Undeath", "UNMOVABLE.description": "The first time you gain block each turn, gain {Unmovable:diff()} [gold]Block[/gold].", "UNMOVABLE.title": "Unmovable", "UNRELENTING.description": "Deal {Damage:diff()} damage.\nThe next Attack you play costs 0 {energyPrefix:energyIcons(1)}.", "UNRELENTING.title": "Unrelenting", "UPPERCUT.description": "Deal {Damage:diff()} damage.\nApply {Power:diff()} [gold]Weak[/gold].\nApply {Power:diff()} [gold]Vulnerable[/gold].", "UPPERCUT.title": "Uppercut", "UP_MY_SLEEVE.description": "Add {Cards:diff()} [gold]{Cards:plural:Shiv|Shivs}[/gold] into your [gold]Hand[/gold].\nReduce this card's cost by 1 this combat.", "UP_MY_SLEEVE.title": "Up My Sleeve", "VEILPIERCER.description": "Deal {Damage:diff()} damage.\nThe next [gold]Ethereal[/gold] card you play costs 0 {energyPrefix:energyIcons(1)}.", "VEILPIERCER.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VENERATE.description": "Gain {Stars:starIcons()}.", "VENERATE.title": "Venerate", "VETERAN.description": "Gain {Block:diff()} [gold]Block[/gold].\nGain {Strength:diff()} [gold]Strength[/gold].", "VETERAN.title": "Veteran", "VICIOUS.description": "Whenever you apply [gold]Vulnerable[/gold], draw {Cards:diff()} {Cards:plural:card|cards}.", "VICIOUS.title": "Vicious", "VISIONS_OF_GRANDEUR.description": "If you have {StarThreshold:diff()}{singleStarIcon}, draw {Cards:diff()} cards.", "VISIONS_OF_GRANDEUR.title": "Visions of Grandeur", "VOID.description": "Whenever this card is drawn, lose {Energy:energyIcons()}.", "VOID.title": "Void", "VOID_FORM.description": "End your turn.\nThe first {VoidFormPower:diff()} cards you play each turn {VoidFormPower:plural:costs|cost} 0.", "VOID_FORM.title": "Void Form", "WARP_SPACE.description": "Add {Cards:diff()} random cards to your [gold]Draw Pile[/gold].\nLower their costs by 1 until they are played.", "WARP_SPACE.title": "Warp Space", "WATERLOGGED.description": "If this card is [gold]Exhausted[/gold], lose {HpLoss} HP.", "WATERLOGGED.title": "Waterlogged", "WELL_LAID_PLANS.description": "At the end of your turn, [gold]Retain[/gold] up to {RetainAmount:diff()} {RetainAmount:plural:card|cards}.", "WELL_LAID_PLANS.title": "Well-Laid Plans", "WHIRLWIND.description": "Deal {Damage:diff()} damage to all enemies X times.", "WHIRLWIND.title": "Whirlwind", "WHITE_NOISE.description": "Add a random Power into your [gold]Hand[/gold]. It costs 0 {energyPrefix:energyIcons(1)} this turn.", "WHITE_NOISE.title": "White Noise", "WISP.description": "Gain {Energy:energyIcons()}.", "WISP.title": "Wisp", "WOUND.description": "", "WOUND.title": "Wound", "WRAITH_FORM.description": "Gain {Intangible:diff()} [gold]Intangible[/gold].\nAt the start of your turn, lose {WraithForm:diff()} [gold]Dexterity[/gold].", "WRAITH_FORM.title": "Wraith Form", "WRITHE.description": "", "WRITHE.title": "Writhe", "WROUGHT_IN_WAR.description": "Deal {Damage:diff()} damage.\n[gold]Forge[/gold] {Forge:diff()}.\nIncrease this card's damage and [gold]Forge[/gold] by {Increase:diff()}.", "WROUGHT_IN_WAR.title": "Wrought In War", "ZAP.description": "[gold]Channel[/gold] 1 [gold]Lightning[/gold].", "ZAP.title": "Zap"}