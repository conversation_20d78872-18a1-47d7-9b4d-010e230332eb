{"ACCELERANT_POWER.description": "[gold]Poison[/gold] is triggered an additional time.", "ACCELERANT_POWER.smartDescription": "[gold]Poison[/gold] is triggered [blue]{Amount}[/blue] additional {Amount:plural:time|times}.", "ACCELERANT_POWER.title": "Accelerant", "ACCURACY_POWER.description": "[gold]Shivs[/gold] deal additional damage.", "ACCURACY_POWER.smartDescription": "[gold]Shivs[/gold] deal [blue]{Amount}[/blue] additional damage.", "ACCURACY_POWER.title": "Accuracy", "ADAPTABLE.description": "When this creature would be defeated, it instead revives even stronger.", "ADAPTABLE.smartDescription": "When [gold]{OwnerName}[/gold] would be defeated, it instead revives even stronger.", "ADAPTABLE.title": "Adaptable", "ADAPTATION_POWER.description": "Whenever you gain [gold]Block[/gold], gain [blue]3[/blue] [gold]Dexterity[/gold] this turn.", "ADAPTATION_POWER.smartDescription": "Whenever you gain [gold]Block[/gold], gain [blue]{Amount}[/blue] [gold]Dexterity[/gold] this turn.", "ADAPTATION_POWER.title": "Adaptation", "AFTERLIFE_POWER.description": "Next turn, [gold]Su<PERSON>on[/gold] [blue]3[/blue].", "AFTERLIFE_POWER.smartDescription": "Next turn, [gold]Summon[/gold] [blue]{Amount}[/blue].", "AFTERLIFE_POWER.title": "Afterlife", "AFTER_IMAGE_POWER.description": "Whenever you play a card, gain 1 [gold]Block[/gold].", "AFTER_IMAGE_POWER.smartDescription": "Whenever you play a card, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "AFTER_IMAGE_POWER.title": "After Image", "AGGRESSION_POWER.description": "At the start of your turn, put a random [gold]Attack[/gold] from your [gold]Discard Pile[/gold] into your [gold]Hand[/gold] and [gold]Upgrade[/gold] it for the rest of combat.", "AGGRESSION_POWER.smartDescription": "At the start of your turn, put [blue]{Amount}[/blue] random [gold]{Amount:plural:Attack|Attacks}[/gold] from your [gold]Discard Pile[/gold] into your [gold]Hand[/gold] and [gold]Upgrade[/gold] {Amount:plural:it|them} for the rest of combat.", "AGGRESSION_POWER.title": "Aggression", "ALLEY_OOP_POWER.description": "This creature takes [blue]{Amount}[/blue] times the damage from other players this turn.", "ALLEY_OOP_POWER.remoteDescription": "You deal [blue]{Amount}[/blue] times the damage to this enemy because of [gold]{Applier}[/gold].", "ALLEY_OOP_POWER.smartDescription": "Other allies deal [blue]{Amount}x[/blue] more damage to this enemy this turn.", "ALLEY_OOP_POWER.title": "Alley-Oop", "AMPLIFY_POWER.description": "Your next [gold]Power[/gold] is played an extra time this turn.", "AMPLIFY_POWER.smartDescription": "Your next {Amount:plural:[gold]Power[/gold] is|[blue]{}[/blue] [gold]Powers[/gold] are} played an extra time this turn.", "AMPLIFY_POWER.title": "Amplify", "ARSENAL_POWER.description": "Whenever you play a Colorless card, gain [blue]1[/blue] [gold]Strength[/gold].", "ARSENAL_POWER.smartDescription": "Whenever you play a Colorless card, gain [blue]{Amount}[/blue] [gold]Strength[/gold].", "ARSENAL_POWER.title": "Arsenal", "ARTIFACT.description": "[gold]Negates[/gold] debuffs.", "ARTIFACT.smartDescription": "[gold]Negates[/gold] [blue]{Amount}[/blue] {Amount:plural:debuff|debuffs}.", "ARTIFACT.title": "Artifact", "ASLEEP.description": "TODO", "ASLEEP.smartDescription": "Awakens upon losing HP or {Amount:plural:next turn|after [blue]{}[/blue] turns}.", "ASLEEP.title": "<PERSON><PERSON><PERSON>", "AUTOMATION_POWER.description": "Every [blue]10[/blue] cards you draw, gain [gold]Energy[/gold].", "AUTOMATION_POWER.smartDescription": "Every [blue]{BaseCards}[/blue] cards you draw, gain {Amount:energyIcons()}.", "AUTOMATION_POWER.title": "Automation", "A_THOUSAND_CUTS_POWER.description": "Whenever you play a card, deal [blue]1[/blue] damage to all enemies.", "A_THOUSAND_CUTS_POWER.smartDescription": "Whenever you play a card, deal [blue]{Amount}[/blue] damage to all enemies.", "A_THOUSAND_CUTS_POWER.title": "A Thousand Cuts", "BACK_ATTACK_LEFT.description": "Deals [blue]50%[/blue] more damage when it is attacking you from behind.", "BACK_ATTACK_LEFT.title": "Back Attack", "BACK_ATTACK_RIGHT.description": "Deals [blue]50%[/blue] more damage when it is attacking you from behind.", "BACK_ATTACK_RIGHT.title": "Back Attack", "BARRICADE_POWER.description": "[gold]Block[/gold] is not removed at the start of this creature's turn.", "BARRICADE_POWER.smartDescription": "[gold]Block[/gold] is not removed at the start of {ApplierName.StringValue:cond:[gold]{OwnerName}'s[/gold]|your} turn.", "BARRICADE_POWER.title": "Barricade", "BATTLEWORN_DUMMY_TIME_LIMIT.description": "You have [blue]3[/blue] more turns to defeat the Battle Friend.", "BATTLEWORN_DUMMY_TIME_LIMIT.smartDescription": "You have [blue]{Amount}[/blue] more {Amount:plural:turn|turns} to defeat the Battle Friend.", "BATTLEWORN_DUMMY_TIME_LIMIT.title": "Time Limit", "BEACON_OF_HOPE_POWER.description": "Whenever you gain [gold]Block[/gold] from a card, other players gain half than much [gold]Block[/gold].", "BEACON_OF_HOPE_POWER.smartDescription": "Whenever you gain [gold]Block[/gold] from a card, other players gain half than much [gold]Block[/gold] {Amount:plural:|[blue]{}[/blue] times}.", "BEACON_OF_HOPE_POWER.title": "Beacon Of Hope", "BIAS.description": "At the start of your turn, lose [blue]1[/blue] [gold]Focus[/gold].", "BIAS.smartDescription": "At the start of your turn, lose [blue]{Amount}[/blue] [gold]Focus[/gold].", "BIAS.title": "Bias", "BLACK_HOLE_POWER.description": "At the start of your turn, deal [blue]9[/blue] damage to all enemies.", "BLACK_HOLE_POWER.smartDescription": "At the start of your turn, deal [blue]{Amount}[/blue] damage to all enemies.", "BLACK_HOLE_POWER.title": "Black Hole", "BLADE_OF_INK_POWER.description": "Whenever you create a [gold]Shiv[/gold] this turn, gain [blue]1[/blue] [gold]Strength[/gold].", "BLADE_OF_INK_POWER.smartDescription": "Whenever you create a [gold]Shiv[/gold] this turn, gain [blue]{Amount}[/blue] [gold]Strength[/gold].", "BLADE_OF_INK_POWER.title": "Blade of Ink", "BLOCK_NEXT_TURN.description": "At the start of your next turn, gain [blue]4[/blue] [gold]Block[/gold].", "BLOCK_NEXT_TURN.smartDescription": "At the start of your next turn, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "BLOCK_NEXT_TURN.title": "Block Next Turn", "BLUR_POWER.description": "[gold]Block[/gold] is not removed at the start of your next turn.", "BLUR_POWER.smartDescription": "[gold]Block[/gold] is not removed at the start of your next {Amount:plural:turn|{} turns}.", "BLUR_POWER.title": "Blur", "BUBBLE_BUBBLE_POWER.description": "[gold]Poison[/gold] no longer reduces by [blue]1[/blue] at the end of the turn.", "BUBBLE_BUBBLE_POWER.smartDescription": "[gold]Poison[/gold] no longer reduces by [blue]1[/blue] at the end of the turn.", "BUBBLE_BUBBLE_POWER.title": "Bubble Bubble", "BUFFER_POWER.description": "Prevent the next time you would lose HP.", "BUFFER_POWER.smartDescription": "Prevent the next {Amount:plural:time|{} times} you would lose HP.", "BUFFER_POWER.title": "<PERSON><PERSON><PERSON>", "BURROWED.description": "[gold]Block[/gold] is not removed at the start of this creature's turn. [gold]Stunned[/gold] if all [gold]Block[/gold] is removed.", "BURROWED.smartDescription": "[gold]Block[/gold] is not removed at the start of [gold]{OwnerName}[/gold]'s turn. [gold]Stunned[/gold] if all [gold]Block[/gold] is removed.", "BURROWED.title": "Burrowed", "BURST_POWER.description": "This turn, your next [gold]Skill[/gold] is played an extra time.", "BURST_POWER.smartDescription": "Your next {Amount:plural:[gold]Skill[/gold] is|[blue]{}[/blue] [gold]Skills[/gold] are} played an extra time this turn.", "BURST_POWER.title": "<PERSON><PERSON><PERSON>", "CALAMITY_POWER.description": "Whenever you play an [gold]Attack[/gold], add a random Attack to your hand.", "CALAMITY_POWER.smartDescription": "Whenever you play an [gold]Attack[/gold], add {Amount:plural:a random Attack|{} random Attacks} to your hand.", "CALAMITY_POWER.title": "Calamity", "CALL_OF_THE_VOID_POWER.description": "At the start of your turn, add [blue]1[/blue] [gold]Ethereal[/gold] card to your hand.", "CALL_OF_THE_VOID_POWER.smartDescription": "At the start of your turn, add [blue]{Amount}[/blue] [gold]Ethereal[/gold] {Amount:plural:card|cards} to your hand.", "CALL_OF_THE_VOID_POWER.title": "Call of the Void", "CEREMONIAL_BEAST_A1_REGEN.description": "", "CEREMONIAL_BEAST_A1_REGEN.smartDescription": "", "CEREMONIAL_BEAST_A1_REGEN.title": "", "CHAINS_OF_BINDING.description": "The first [blue]3[/blue] cards drawn each turn are Afflicted with [gold]Bound[/gold].", "CHAINS_OF_BINDING.smartDescription": "The first [blue]{Amount}[/blue] cards drawn each turn are Afflicted with [gold]Bound[/gold].", "CHAINS_OF_BINDING.title": "Chains of Binding", "CHILD_OF_THE_STARS_POWER.description": "Gain [blue]1[/blue] [gold]Block[/gold] for each {singleStarIcon} spent.", "CHILD_OF_THE_STARS_POWER.smartDescription": "Gain [blue]{Amount}[/blue] [gold]Block[/gold] for each {singleStarIcon} spent.", "CHILD_OF_THE_STARS_POWER.title": "Child of the Stars", "COLOSSUS_POWER.description": "You receive [blue]50%[/blue] less damage from [gold]Vulnerable[/gold] enemies this turn.", "COLOSSUS_POWER.smartDescription": "You receive [blue]{DamageDecrease:percentLess()}%[/blue] less damage from [gold]Vulnerable[/gold] enemies {Amount:plural:this turn|for [blue]{}[/blue] turns}.", "COLOSSUS_POWER.title": "Coloss<PERSON>", "COMBUST_POWER.description": "At the end of your turn, lose [blue]1[/blue] HP and deal [blue]5[/blue] damage to all enemies.", "COMBUST_POWER.smartDescription": "At the end of your turn, lose [blue]{SelfDamage}[/blue] HP and deal [blue]{Amount}[/blue] damage to all enemies.", "COMBUST_POWER.title": "Combust", "CONFUSED.description": "The costs of your cards are randomized on draw, from [blue]0[/blue] to [blue]3[/blue].", "CONFUSED.smartDescription": "The costs of your cards are randomized on draw, from [blue]0[/blue] to [blue]3[/blue].", "CONFUSED.title": "Confused", "CONQUEROR_POWER.description": "Whenever you deal unblocked attack damage, [gold]Forge[/gold] [blue]1[/blue].", "CONQUEROR_POWER.smartDescription": "Whenever you deal unblocked damage, [gold]Forge[/gold] [blue]{Amount}[/blue].", "CONQUEROR_POWER.title": "Conqueror", "CONSTELLATION_POWER.description": "Whenever you play a card this turn, gain {singleStarIcon}.", "CONSTELLATION_POWER.smartDescription": "Whenever you play a card this turn, gain {Amount:starIcons()}.", "CONSTELLATION_POWER.title": "Constellation", "CONSTRICT.description": "While the Slithering <PERSON><PERSON><PERSON> is alive, at the end of your turn, take [blue]1[/blue] damage.", "CONSTRICT.smartDescription": "While the Slithering <PERSON><PERSON><PERSON> is alive, at the end of your turn, take [blue]{Amount}[/blue] damage.", "CONSTRICT.title": "Constrict", "CONSUMING_SHADOW_POWER.description": "At the end of your turn, [gold]Evoke[/gold] your leftmost Orb.", "CONSUMING_SHADOW_POWER.smartDescription": "At the end of your turn, [gold]Evoke[/gold] your leftmost Orb.", "CONSUMING_SHADOW_POWER.title": "Consuming Shadow", "CONTRACTILITY_POWER.description": "Next turn, gain [blue]0[/blue] [gold]Block[/gold].", "CONTRACTILITY_POWER.smartDescription": "Next turn, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "CONTRACTILITY_POWER.title": "Contractility", "CORROSIVE_WAVE_POWER.description": "Whenever you draw a card this turn, apply [blue]2[/blue] [gold]Poison[/gold] to all enemies.", "CORROSIVE_WAVE_POWER.smartDescription": "Whenever you draw a card this turn, apply [blue]{Amount}[/blue] [gold]Poison[/gold] to all enemies.", "CORROSIVE_WAVE_POWER.title": "Corrosive Wave", "CORRUPTION_POWER.description": "[gold]Skills[/gold] cost [blue]0[/blue].\nWhenever you play a [gold]Skill[/gold], [gold]Exhaust[/gold] it.", "CORRUPTION_POWER.title": "Corruption", "COUNTDOWN_POWER.description": "At the start of your turn, apply [blue]3[/blue] [gold]Doom[/gold] to all enemies.", "COUNTDOWN_POWER.smartDescription": "At the start of your turn, apply [blue]{Amount}[/blue] [gold]Doom[/gold] to all enemies.", "COUNTDOWN_POWER.title": "Countdown", "COVERED.description": "An ally is covering you. All attacks that would be directed to you are redirected to them instead.", "COVERED.smartDescription": "[gold]{Applier}[/gold] is covering you. All attacks that would be directed to you are redirected to them instead.", "COVERED.title": "Covered", "CREATIVE_AI_POWER.description": "At the start of your turn, add a random [gold]Power[/gold] into your hand.", "CREATIVE_AI_POWER.smartDescription": "At the start of your turn, add {Amount:plural:a random [gold]Power[/gold]|[blue]{}[/blue] random [gold]Powers[/gold]} into your hand.", "CREATIVE_AI_POWER.title": "Creative AI", "CRIMSON_MANTLE_POWER.description": "At the start of your turn, lose HP and gain [gold]Block[/gold].", "CRIMSON_MANTLE_POWER.smartDescription": "At the start of your turn, lose [blue]{SelfDamage}[/blue] HP and gain [blue]{Amount}[/blue] [gold]Block[/gold].", "CRIMSON_MANTLE_POWER.title": "Crimson Mantle", "CRUELTY_POWER.description": "Vulnerable enemies take additional damage.", "CRUELTY_POWER.smartDescription": "[gold]Vulnerable[/gold] enemies take an additional [blue]{Amount}%[/blue] damage.", "CRUELTY_POWER.title": "Cruelty", "CURL_UP.description": "When damaged rolls up and gains block. (Once per combat)", "CURL_UP.smartDescription": "Gains [blue]{Amount}[/blue] [gold]Block[/gold] upon first receiving attack damage.", "CURL_UP.title": "Curl Up", "CURSE_OF_KNOWLEDGE.description": "Afflicts every third card drawn with [gold]Mind Rot[/gold].", "CURSE_OF_KNOWLEDGE.smartDescription": "Afflicts every third card drawn with [gold]Mind Rot[/gold].", "CURSE_OF_KNOWLEDGE.title": "Curse of Knowledge", "DAMPEN.banter": "Your upgrades are no more!!", "DAMPEN.description": "While <PERSON><PERSON> is alive, all your cards are [gold]Downgraded[/gold].", "DAMPEN.title": "<PERSON><PERSON>", "DANSE_MACABRE_POWER.description": "Whenever you play a card that costs 2 or more Energy, gain 7(10) Block.", "DANSE_MACABRE_POWER.smartDescription": "Whenever you play a card that costs {energyPrefix:energyIcons(2)} or more, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "DANSE_MACABRE_POWER.title": "<PERSON><PERSON>", "DARK_EMBRACE_POWER.description": "Whenever a card is [gold]Exhausted[/gold], draw [blue]1[/blue] card.", "DARK_EMBRACE_POWER.smartDescription": "Whenever a card is [gold]Exhausted[/gold], draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "DARK_EMBRACE_POWER.title": "<PERSON> Embrace", "DECREE_OF_ENTROPY_POWER.description": "At the start of your turn, add [blue]1[/blue] random upgraded card to your hand.", "DECREE_OF_ENTROPY_POWER.smartDescription": "At the start of your turn, add [blue]{Amount}[/blue] random upgraded {Amount:plural:card|cards} to your hand.", "DECREE_OF_ENTROPY_POWER.title": "Decree of Entropy", "DECREE_OF_UNMAKING_POWER.description": "Whenever you damage an enemy, apply a random debuff to it.", "DECREE_OF_UNMAKING_POWER.smartDescription": "Whenever you damage an enemy, apply [blue]{Amount}[/blue] random {Amount:plural:debuff|debuffs} to it.", "DECREE_OF_UNMAKING_POWER.title": "Decree of Unmaking", "DEMESNE_POWER.description": "At the start of your turn, gain [blue]1[/blue] Energy and draw [blue]1[/blue] additional card.", "DEMESNE_POWER.smartDescription": "At the start of your turn, gain {Amount:energyIcons()} and draw [blue]{Amount}[/blue] additional {Amount:plural:card|cards}.", "DEMESNE_POWER.title": "Demesne", "DEMISE.description": "At the end of its turn, this creature <PERSON> loses HP.", "DEMISE.smartDescription": "At the end of [gold]{OwnerName}[/gold] turn, it loses [blue]{Amount}[/blue] HP.", "DEMISE.title": "Demise", "DEMON_FORM_POWER.description": "At the start of your turn, gain [blue]2[/blue] [gold]Strength[/gold].", "DEMON_FORM_POWER.smartDescription": "At the start of your turn, gain [blue]{Amount}[/blue] [gold]Strength[/gold].", "DEMON_FORM_POWER.title": "Demon Form", "DESPAIR_POWER.description": "For every [blue]1[/blue] [gold]Doom[/gold] you apply, gain [blue]1[/blue] [gold]Block[/gold]", "DESPAIR_POWER.smartDescription": "For every [blue]1[/blue] [gold]Doom[/gold] you apply, gain [blue]{Amount}[/blue] [gold]Block[/gold]", "DESPAIR_POWER.title": "Despair", "DEXTERITY.description": "Dexterity improves [gold]Block[/gold] gained from cards.", "DEXTERITY.smartDescription": "{Amount:cond:<0?Decreases|Increases} [gold]Block[/gold] gained from cards by [blue]{Amount:abs()}[/blue].", "DEXTERITY.title": "Dexterity", "DEXTERITY_DOWN.description": "At the end of this turn, lose [gold]Dexterity[/gold].", "DEXTERITY_DOWN.smartDescription": "At the end of this turn, lose [blue]{Amount}[/blue] [gold]Dexterity[/gold].", "DEXTERITY_DOWN.title": "Dexterity Down", "DIE_FOR_YOU.description": "[gold]Osty[/gold] absorbs all unblocked attack damage.", "DIE_FOR_YOU.title": "Die For You", "DISPERSE.description": "On death, gives all allies its [gold]Strength[/gold].", "DISPERSE.title": "Disperse", "DISRUPT.description": "Dealing X damage to this enemy [gold]Stuns[/gold] it for one turn.", "DISRUPT.smartDescription": "Dealing [blue]{DisruptAmount}[/blue] damage to this enemy [gold]Stuns[/gold] it for one turn and resets this power.", "DISRUPT.title": "Disrupt", "DOOM.description": "At the end of a creature's turn, if it has more Doom than HP, it dies.", "DOOM.smartDescription": "At the end of [gold]{OwnerName}'s[/gold] turn, if it has [blue]{Amount}[/blue] or less HP, it dies.", "DOOM.title": "Doom", "DOUBLE_DAMAGE.description": "This turn, [gold]Attacks[/gold] deal double damage.", "DOUBLE_DAMAGE.smartDescription": "{Amount:plural:This turn|For the next [blue]{}[/blue] turns}, [gold]Attacks[/gold] deal double damage.", "DOUBLE_DAMAGE.title": "Double Damage", "DRAW_CARDS_NEXT_TURN.description": "At the start of your next turn, draw [blue]1[/blue] additional card.", "DRAW_CARDS_NEXT_TURN.smartDescription": "At the start of your next turn, draw [blue]{Amount}[/blue] additional {Amount:plural:card|cards}.", "DRAW_CARDS_NEXT_TURN.title": "Draw Cards", "DRAW_EXTRA_CARD.description": "At the start of your next turn, draw [blue]1[/blue] additional card.", "DRAW_EXTRA_CARD.smartDescription": "At the start of your next {Amount:plural:turn|[blue]{}[/blue] turns}, draw [blue]1[/blue] additional card.", "DRAW_EXTRA_CARD.title": "Draw Extra Card", "DRUM_OF_BATTLE_POWER.description": "At the end of your turn, [gold]Exhaust[/gold] the top {Amount:plural:card|[blue]{}[/blue] cards} of your [gold]Draw Pile[/gold].", "DRUM_OF_BATTLE_POWER.smartDescription": "At the end of your turn, [gold]Exhaust[/gold] the top {Amount:plural:card|[blue]{}[/blue] cards} of your [gold]Draw Pile[/gold].", "DRUM_OF_BATTLE_POWER.title": "Drum Of Battle", "DUPLICATION.description": "Your next card is played an extra time.", "DUPLICATION.smartDescription": "Your next {Amount:plural:card is|[blue]{}[/blue] cards are} played an extra time.", "DUPLICATION.title": "Duplication", "ECHO_FORM_POWER.description": "The first card you play each turn is played an extra time.", "ECHO_FORM_POWER.smartDescription": "The first {Amount:plural:card you play each turn is|[blue]{}[/blue] cards you play each turn are} played an extra time.", "ECHO_FORM_POWER.title": "Echo Form", "EGG_DROP.description": "After [blue]3[/blue] turns, hatches a new creature.", "EGG_DROP.smartDescription": "{Amount:plural:At the end of this turn|After [blue]{}[/blue] turns}, hatches a new creature.", "EGG_DROP.title": "Egg Drop", "ELECTRODYNAMICS_POWER.description": "[gold]Lightning[/gold] hit all enemies.", "ELECTRODYNAMICS_POWER.title": "Electrodynamics", "EMPOWER_POWER.description": "At the start of your turn, give [gold]Osty[/gold] [blue]2[/blue] [gold]Strength[/gold].", "EMPOWER_POWER.smartDescription": "At the start of your turn, give [gold]Osty[/gold] [blue]{Amount}[/blue] [gold]Strength[/gold].", "EMPOWER_POWER.title": "Empower", "ENERGIZED.description": "<PERSON>ain additional [gold]Energy[/gold] next turn.", "ENERGIZED.smartDescription": "Gain [blue]{Amount}[/blue] additional {energyPrefix:energyIcons(1)} next turn.", "ENERGIZED.title": "Energized", "ENFEEBLE_POWER.description": "[gold]Vulnerable[/gold] and [gold]Weak[/gold] are twice as effective for the next [blue]2[/blue] turns.", "ENFEEBLE_POWER.smartDescription": "[gold]Vulnerable[/gold] and [gold]Weak[/gold] are twice as effective for the next {Amount:plural:this turn|[blue]{}[/blue] turns}.", "ENFEEBLE_POWER.title": "Enfeeble", "ENRAGE.description": "Whenever you play a [gold]Skill[/gold], gains [blue]2[/blue] [gold]Strength[/gold].", "ENRAGE.smartDescription": "Whenever you play a [gold]Skill[/gold], gains [blue]{Amount}[/blue] [gold]Strength[/gold].", "ENRAGE.title": "Enrage", "ENTROPY_POWER.description": "At the start of your turn, [gold]Transform[/gold] 1 card in your [gold]Hand[/gold].", "ENTROPY_POWER.smartDescription": "At the start of your turn, [gold]Transform[/gold] {Amount} {Amount:plural:card|cards} in your [gold]Hand[/gold].", "ENTROPY_POWER.title": "Entropy", "ENVENOM_POWER.description": "Whenever you deal unblocked attack damage, apply [blue]1[/blue] [gold]Poison[/gold].", "ENVENOM_POWER.smartDescription": "Whenever you deal unblocked attack damage, apply [blue]{Amount}[/blue] [gold]Poison[/gold].", "ENVENOM_POWER.title": "Envenom", "ERADICATION_MODE.description": "It's Angry.", "ERADICATION_MODE.title": "Eradication Mode", "ESCAPE_ARTIST.description": "Exits the combat after 4 turns.", "ESCAPE_ARTIST.smartDescription": "Tries to escape the combat {Amount:plural:this turn|after [blue]{}[/blue] turns}.", "ESCAPE_ARTIST.title": "Escape Artist", "ETERNAL_ARMOR_POWER.description": "Gain [blue]10[/blue] [gold]Block[/gold] at the start of your next [blue]X[/blue] turns.", "ETERNAL_ARMOR_POWER.smartDescription": "Gain [blue]{Block}[/blue] [gold]Block[/gold] at the start of your next {Amount:plural:turn|[blue]{}[/blue] turns}.", "ETERNAL_ARMOR_POWER.title": "Eternal Armor", "EVOLVE_POWER.description": "Whenever you draw a [gold]Status[/gold] card, draw [blue]1[/blue] card.", "EVOLVE_POWER.smartDescription": "Whenever you draw a [gold]Status[/gold] card, draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "EVOLVE_POWER.title": "Evolve", "EXPONENTIAL_GROWTH_POWER.description": "Whenever you play a cost X card, gain [gold]Energy[/gold].", "EXPONENTIAL_GROWTH_POWER.smartDescription": "Whenever you play an cost X card, gain {Amount:energyIcons()}.", "EXPONENTIAL_GROWTH_POWER.title": "Exponential Growth", "FANATIC.description": "When an ally dies, heal [blue]3[/blue] HP.", "FANATIC.smartDescription": "When an ally dies, heal [blue]{Amount}[/blue] HP.", "FANATIC.title": "Fanatic", "FAN_OF_KNIVES_POWER.description": "[gold]Shivs[/gold] hit all enemies.", "FAN_OF_KNIVES_POWER.title": "Fan of Knives", "FASTEN_POWER.description": "Defend cards gain [blue]4[/blue] additional [gold]Block[/gold] when played.", "FASTEN_POWER.smartDescription": "Defend cards gain [blue]{Amount}[/blue] additional [gold]Block[/gold] when played.", "FASTEN_POWER.title": "<PERSON><PERSON>", "FEEL_NO_PAIN_POWER.description": "Whenever a card is Exhausted, gain [blue]3[/blue] [gold]Block[/gold].", "FEEL_NO_PAIN_POWER.smartDescription": "Whenever a card is [gold]Exhausted[/gold], gain [blue]{Amount}[/blue] [gold]Block[/gold].", "FEEL_NO_PAIN_POWER.title": "Feel No Pain", "FERAL_POWER.description": "Cards containing \"Strike\" cost [blue]1[/blue] less {energyPrefix:energyIcons(1)}.", "FERAL_POWER.smartDescription": "Cards containing \"Strike\" cost [blue]{Amount}[/blue] less {energyPrefix:energyIcons(1)}.", "FERAL_POWER.title": "Feral", "FLAME_BARRIER_POWER.description": "Whenever you are attacked this turn, deal [blue]4[/blue] damage back.", "FLAME_BARRIER_POWER.smartDescription": "Whenever you are attacked this turn, deal [blue]{Amount}[/blue] damage back.", "FLAME_BARRIER_POWER.title": "<PERSON>", "FLUTTER.description": "Receives [gold]50%[/gold] less damage.", "FLUTTER.smartDescription": "Receives [blue]{DamageDecrease}%[/blue] less damage. Deal damage [blue]{Amount}[/blue] {Amount:plural:time|times} to stun it.", "FLUTTER.title": "Flutter", "FOCUS.description": "Increases the effectiveness of Orbs.", "FOCUS.smartDescription": "Increases the effectiveness of Orbs by [blue]{Amount}[/blue].", "FOCUS.title": "Focus", "FOCUS_DOWN.description": "At the end of this turn, lose [blue]1[/blue] [gold]Focus[/gold].", "FOCUS_DOWN.smartDescription": "At the end of this turn, lose [blue]{Amount}[/blue] [gold]Focus[/gold].", "FOCUS_DOWN.title": "Focus Down", "FORBIDDEN_GRIMOIRE_POWER.description": "At the end of combat, remove a card from your [gold]Deck[/gold].", "FORBIDDEN_GRIMOIRE_POWER.smartDescription": "At the end of combat, remove [blue]{Amount}[/blue] {Amount:plural:card|cards} from your [gold]Deck[/gold].", "FORBIDDEN_GRIMOIRE_POWER.title": "Forbidden Grimoire", "FORGOTTEN_RITUAL_POWER.description": "The first time you [gold]Exhaust[/gold] a card each turn, gain Energy.", "FORGOTTEN_RITUAL_POWER.smartDescription": "The first time you [gold]Exhaust[/gold] a card each turn, gain {Amount:energyIcons()}.", "FORGOTTEN_RITUAL_POWER.title": "Forgotten Ritual", "FRAIL.description": "While Frail, gain [blue]25%[/blue] less [gold]Block[/gold] from cards.", "FRAIL.smartDescription": "Gain [blue]25%[/blue] less [gold]Block[/gold] from cards for [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "FRAIL.title": "Fr<PERSON>", "FREE_SKILL.description": "The next [gold]Skill[/gold] you play costs [blue]0[/blue].", "FREE_SKILL.smartDescription": "The next [blue]{Amount}[/blue] [gold]{Amount:plural:Skill|Skills}[/gold] you play costs [blue]0[/blue].", "FREE_SKILL.title": "Free Skill", "FRIENDSHIP_POWER.description": "At the start of your turn, if [gold]Osty[/gold] is alive, gain [blue]1[/blue] Energy.", "FRIENDSHIP_POWER.smartDescription": "At the start of your turn, if [gold]Osty[/gold] is alive, gain {Amount:energyIcons()}.", "FRIENDSHIP_POWER.title": "Friendship", "GALVANIC.description": "[gold]Powers[/gold] are afflicted with [gold]Galvanic[/gold].", "GALVANIC.smartDescription": "[gold]Powers[/gold] are afflicted with [gold]Galvanic[/gold].", "GALVANIC.title": "Galvanic", "GENESIS_POWER.description": "At the start of your turn, gain {singleStarIcon}.", "GENESIS_POWER.smartDescription": "At the start of your turn, gain {Amount:starIcons()}.", "GENESIS_POWER.title": "Genesis", "GRACEFUL_POWER.description": "If you play [blue]3[/blue] more Skills this turn, gain Energy.", "GRACEFUL_POWER.smartDescription": "If you play [blue]{CardsLeft}[/blue] more Skills this turn, gain {energyPrefix:energyIcons(1)}.", "GRACEFUL_POWER.title": "Graceful", "GRAVITY.description": "Whenever you play a card this turn, this creature takes [blue]2[/blue] damage.", "GRAVITY.smartDescription": "Whenever you play a card this turn, [gold]{OwnerName}[/gold] takes [blue]{Amount}[/blue] damage.", "GRAVITY.title": "Gravity", "GUARDED.description": "Take half damage from enemies.", "GUARDED.smartDescription": "Take [blue]1/{Amount}[/blue] as much damage from enemies. Remove if [gold]{Applier.StringValue}[/gold] dies.", "GUARDED.title": "Guarded", "HANG_POWER.description": "All [gold]Hangs[/gold] deal [blue]2[/blue] times more damage to this enemy.", "HANG_POWER.smartDescription": "All [gold]Hangs[/gold] deal [blue]{Amount}[/blue] times more damage to this enemy.", "HANG_POWER.title": "Hang", "HARDENED_SHELL.description": "This creature cannot lose more than [blue]25[/blue] HP each turn.", "HARDENED_SHELL.smartDescription": "[gold]{Owner<PERSON>ame}[/gold] cannot lose more than [blue]{Amount}[/blue] HP each turn.", "HARDENED_SHELL.title": "Hardened Shell", "HARD_TO_KILL.description": "The first time this creature would die, its [gold]HP[/gold] is instead set to [blue]9[/blue].", "HARD_TO_KILL.smartDescription": "The first time [gold]{OwnerName}[/gold] would die, its [gold]HP[/gold] is instead set to [blue]{Amount}[/blue].", "HARD_TO_KILL.title": "Hard to Kill", "HATCH.description": "Hatches after <PERSON> turns.", "HATCH.smartDescription": "Hatches after {Amount} {Amount:plural:turn|turns}.", "HATCH.title": "<PERSON>", "HAUNT_POWER.description": "Whenever you play a [gold]Soul[/gold], all enemies lose [blue]3[/blue] HP.", "HAUNT_POWER.smartDescription": "Whenever you play a [gold]Soul[/gold], all enemies lose [blue]{Amount}[/blue] HP.", "HAUNT_POWER.title": "Haunt", "HEATSINKS_POWER.description": "Whenever you play a [gold]Power[/gold], draw [blue]1[/blue] card.", "HEATSINKS_POWER.smartDescription": "Whenever you play a [gold]Power[/gold], draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "HEATSINKS_POWER.title": "Heatsink", "HEIST.description": "If killed, returns all the stolen gold.", "HEIST.smartDescription": "If killed, returns all the stolen gold.", "HEIST.title": "Heist", "HELLO_WORLD_POWER.description": "At the start of your turn, add [blue]1[/blue] random Common card to your [gold]Hand[/gold].", "HELLO_WORLD_POWER.smartDescription": "At the start of your turn, add [blue]{Amount}[/blue] random Common {Amount:plural:card|cards} to your [gold]Hand[/gold].", "HELLO_WORLD_POWER.title": "Hello World", "HELLRAISER_POWER.description": "Whenever you draw a card containing \"Strike\", it is played against a random enemy.", "HELLRAISER_POWER.smartDescription": "Whenever you draw a card containing \"Strike\", it is played against a random enemy.", "HELLRAISER_POWER.title": "Hellraiser", "HEX.description": "While <PERSON><PERSON><PERSON> is alive, all your cards are [gold]Ethereal[/gold].", "HEX.title": "Hex", "HIGH_VOLTAGE.description": "At the start of this creature's turn, it gains [blue]2[/blue] [gold]Strength[/gold].", "HIGH_VOLTAGE.smartDescription": "At the start of [gold]{OwnerName}'s[/gold] turn, it gains [blue]{Amount}[/blue] [gold]Strength[/gold].", "HIGH_VOLTAGE.title": "High Voltage", "ILLUSION.description": "Cannot be killed. Will disappear once all non-illusions are defeated.", "ILLUSION.smartDescription": "Cannot be killed. Will disappear once all non-illusions are defeated.", "ILLUSION.title": "Illusion", "IMBALANCED.description": "If this creature's attacks are fully blocked by any player, it becomes [gold]Stunned[/gold].", "IMBALANCED.smartDescription": "If [gold]{OwnerName}'s[/gold] attacks are fully blocked{IsMultiplayer: by any player|}, it becomes [gold]Stunned[/gold].", "IMBALANCED.title": "Imbalanced", "INEVITABILITY_POWER.description": "At the end of your turn, deal damage equal to your {singleStarIcon} to all enemies.", "INEVITABILITY_POWER.smartDescription": "At the end of your turn, deal damage equal to your {singleStarIcon} to all enemies [blue]{Amount}[/blue] {Amount:plural:time|times}.", "INEVITABILITY_POWER.title": "Inevitability", "INFESTED.description": "Upon dying, summons... something.", "INFESTED.smartDescription": "Upon dying, summons... something.", "INFESTED.title": "Infested", "INFINITE_BLADES_POWER.description": "At the start of your turn, add a [gold]Shiv[/gold] into your hand.", "INFINITE_BLADES_POWER.smartDescription": "At the start of your turn, add {Amount:plural:a [gold]Shiv[/gold]|[blue]{}[/blue] [gold]Shivs[/gold]} into your hand.", "INFINITE_BLADES_POWER.title": "Infinite Blades", "INFO_HAZARD.description": "Whenever you draw a card during your turn, Knowledge Demon gains [gold]Block[/gold].", "INFO_HAZARD.smartDescription": "Whenever you draw a card during your turn, gains [blue]{Amount}[/blue] [gold]Block[/gold].", "INFO_HAZARD.title": "Info Hazard", "INTANGIBLE.description": "Reduce all damage taken and HP loss to [blue]1[/blue] this turn.", "INTANGIBLE.smartDescription": "Reduce all damage taken and HP loss to [blue]1[/blue]. Lasts for [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "INTANGIBLE.title": "Intangible", "INTANGIBLE_INFINITE.description": "Reduce all damage taken and HP loss to [blue]1[/blue].", "INTANGIBLE_INFINITE.smartDescription": "Reduce all damage taken and HP loss to [blue]1[/blue].", "INTANGIBLE_INFINITE.title": "Intangible", "INTERCEPT_POWER.description": "You are covering another player. All attacks that would be directed towards them are redirected yourself.", "INTERCEPT_POWER.smartDescription": "You are covering [gold]{Covering}[/gold]. All attacks that would be directed towards them are redirected towards your.", "INTERCEPT_POWER.title": "Intercept", "JUGGLING_POWER.description": "Whenever you play an Attack, gain [blue]1[/blue] [gold]Strength[/gold] this turn.", "JUGGLING_POWER.smartDescription": "Whenever you play an Attack, gain [blue]{Amount}[/blue] [gold]Strength[/gold] this turn.", "JUGGLING_POWER.title": "Juggling", "JUNK_KING_POWER.description": "Whenever you [gold]Channel Scrap[/gold], draw [blue]1[/blue] card.", "JUNK_KING_POWER.smartDescription": "Whenever you [gold]Channel Scrap[/gold], draw {Amount:plural:[blue]{}[/blue] card|[blue]{}[/blue] cards}.", "JUNK_KING_POWER.title": "Junk King", "LEADERSHIP.description": "All other allies deal [blue]1[/blue] additional damage.", "LEADERSHIP.smartDescription": "All other allies deal [blue]{Amount}[/blue] additional damage.", "LEADERSHIP.title": "Leadership", "LETHALITY_POWER.description": "Your first [gold]Attack[/gold] each turn deals 50% additional damage.", "LETHALITY_POWER.smartDescription": "Your first {Amount:plural:[gold]Attack[/gold]|[blue]{}[/blue] [gold]Attacks[/gold]} each turn {Amount:plural:deals|deal} [blue]50%[/blue] additional damage.", "LETHALITY_POWER.title": "<PERSON><PERSON><PERSON>", "LOOP_POWER.description": "At the start of your turn, trigger the passive ability of your next Orb.", "LOOP_POWER.smartDescription": "At the start of your turn, trigger the passive ability of your next Orb [blue]{Amount}[/blue] {Amount:plural:time|times}.", "LOOP_POWER.title": "Loop", "MACHINE_LEARNING_POWER.description": "At the start of your turn, draw [blue]1[/blue] additional card.", "MACHINE_LEARNING_POWER.smartDescription": "At the start of your turn, draw [blue]{Amount}[/blue] additional {Amount:plural:card|cards}.", "MACHINE_LEARNING_POWER.title": "Machine Learning", "MAGIC_BOMB.description": "Take [blue]20[/blue] damage at the end of your turn. Is cleared if the <PERSON>gi Knight dies.", "MAGIC_BOMB.smartDescription": "Take [blue]{Amount}[/blue] damage at the end of your turn. Is cleared if the <PERSON>gi Knight dies.", "MAGIC_BOMB.title": "Magic Bomb", "MALLEABLE.description": "Upon receiving attack damage, gains [blue]1[/blue] [gold]Block[/gold]. [gold]Block[/gold] gain increases by [blue]1[/blue] each time [gold]Malleable[/gold] is triggered. Resets to [blue]3[/blue] at the start of your turn.", "MALLEABLE.smartDescription": "Upon receiving attack damage, gains [blue]{Block}[/blue] [gold]Block[/gold]. [gold]Block[/gold] gain increases by [blue]{BlockIncrease}[/blue] each time [gold]Malleable[/gold] is triggered. Resets to [blue]{BaseBlock}[/blue] at the start of your turn.", "MALLEABLE.title": "<PERSON>eable", "MASTER_PLANNER_POWER.description": "When you play a [gold]Skill[/gold], it gains [gold]Sly[/gold].", "MASTER_PLANNER_POWER.title": "Master Planner", "MAYHEM_POWER.description": "At the start of your turn, play the top card of your draw pile.", "MAYHEM_POWER.smartDescription": "At the start of your turn, play the {Amount:plural:top card|next [blue]{}[/blue] top cards} of your draw pile.", "MAYHEM_POWER.title": "Mayhem", "MELANCHOLY_POWER.description": "Whenever [gold]Doom[/gold] is applied, apply an additional [blue]2[/blue].", "MELANCHOLY_POWER.smartDescription": "Whenever [gold]Doom[/gold] is applied, apply an additional [blue]{Amount}[/blue].", "MELANCHOLY_POWER.title": "<PERSON><PERSON><PERSON><PERSON>", "METAL.description": "At the end of your turn, gain [gold]Block[/gold].", "METAL.smartDescription": "At the end of {OnPlayer:your|its} turn, {OnPlayer:gain|gains} [blue]{Amount}[/blue] [gold]Block[/gold].", "METAL.title": "Metal", "MINION.description": "Minions abandon combat without their leader.", "MINION.smartDescription": "Minions abandon combat without their leader.", "MINION.title": "Minion", "MISCHIEF_POWER.description": "Whenever this creature deals unblocked attack damage this turn, it takes that much damage.", "MISCHIEF_POWER.smartDescription": "Whenever [gold]{<PERSON>er<PERSON>ame}[/gold] deals unblocked attack damage this turn, it takes{Amount:plural:| [blue]{}[/blue] times} that much damage.", "MISCHIEF_POWER.title": "Mischief", "MOCK_FREE_CARDS_POWER.description": "", "MOCK_FREE_CARDS_POWER.title": "Mock Free Cards", "MOCK_MODIFY_ENERGY_COST_POWER.description": "", "MOCK_MODIFY_ENERGY_COST_POWER.title": "Mock Modify Energy Cost", "MOCK_PREVENT_DEATH_POWER.description": "", "MOCK_PREVENT_DEATH_POWER.title": "<PERSON><PERSON> Prevent Death", "MOCK_REVIVE_POWER.description": "", "MOCK_REVIVE_POWER.title": "<PERSON><PERSON>ive", "MONARCHS_GAZE_POWER.description": "Whenever you attack an enemy, it loses [blue]1[/blue] [gold]Strength[/gold] this turn.", "MONARCHS_GAZE_POWER.smartDescription": "Whenever you attack an enemy, it loses [blue]{Amount}[/blue] [gold]Strength[/gold] this turn.", "MONARCHS_GAZE_POWER.title": "Monarch's Gaze", "MONOLOGUE_POWER.description": "Whenever you play a card this turn, gain [blue]1[/blue] [gold]Strength[/gold] this turn.", "MONOLOGUE_POWER.smartDescription": "Whenever you play a card this turn, gain [blue]{Amount}[/blue] [gold]Strength[/gold] this turn.", "MONOLOGUE_POWER.title": "Monologue", "NECRO_MASTERY_POWER.description": "Increase [gold]Summon[/gold] on cards.", "NECRO_MASTERY_POWER.smartDescription": "Increase [gold]Summon[/gold] on cards by [blue]{Amount}[/blue].", "NECRO_MASTERY_POWER.title": "Necro Mastery", "NEOWS_NOTE.smartDescription": "The first time you visit the Merchant, all prices are reduced by [blue]75%[/blue].", "NEUROSURGE_POWER.description": "At the start of your turn, apply [blue]3[/blue] [gold]Doom[/gold] to yourself.", "NEUROSURGE_POWER.smartDescription": "At the start of your turn, apply [blue]{Amount}[/blue] [gold]Doom[/gold] to yourself.", "NEUROSURGE_POWER.title": "Neurosurge", "NIGHTMARE_POWER.description": "Add [blue]3[/blue] of a chosen card into your hand next turn.", "NIGHTMARE_POWER.smartDescription": "Add {Amount:plural:a|[blue]{}[/blue]} [gold]{Card}[/gold] {Amount:plural:card|cards} into your hand next turn.", "NIGHTMARE_POWER.title": "Nightmare", "NOSTALGIA_POWER.description": "The first [gold]Attack[/gold] or [gold]Skill[/gold] you play each turn is placed on top of your [gold]Draw Pile[/gold].", "NOSTALGIA_POWER.smartDescription": "The first {Amount:plural:[gold]Attack[/gold] or [gold]Skill[/gold]|{} [gold]Attacks[/gold] or [gold]Skills[/gold]} you play each turn {Amount:plural:is|are} placed on top of your [gold]Draw Pile[/gold].", "NOSTALGIA_POWER.title": "Nostalgia", "NOXIOUS_FUMES_POWER.description": "At the start of your turn, apply [blue]2[/blue] [gold]Poison[/gold] to all enemies.", "NOXIOUS_FUMES_POWER.smartDescription": "At the start of your turn, apply [blue]{Amount}[/blue] [gold]Poison[/gold] to all enemies.", "NOXIOUS_FUMES_POWER.title": "Noxious Fumes", "NO_BLOCK.description": "You cannot gain [gold]Block[/gold] from cards.", "NO_BLOCK.smartDescription": "You cannot gain [gold]Block[/gold] from cards for {Amount:plural:{} turn|{} turns}.", "NO_BLOCK.title": "No Block", "NO_DRAW.description": "You may not draw any more cards this turn.", "NO_DRAW.smartDescription": "You may not draw any more cards this turn.", "NO_DRAW.title": "No Draw", "NO_ESCAPE_POWER.description": "At the end of [blue]3[/blue] turns, gains [blue]50[/blue] [gold]Doom[/gold].", "NO_ESCAPE_POWER.smartDescription": "At the end of {Amount:plural:this turn|[blue]{}[/blue] turns}, gains [blue]{Doom}[/blue] [gold]Doom[/gold].", "NO_ESCAPE_POWER.title": "No Escape", "OBLIVION_POWER.description": "Whenever you apply [gold]Doom[/gold] this turn, double it.", "OBLIVION_POWER.smartDescription": "Whenever you apply [gold]Doom[/gold] this turn, apply [blue]{Amount}[/blue] {Amount:plural:time|times} as much.", "OBLIVION_POWER.title": "Oblivion", "ONE_TWO_PUNCH_POWER.description": "Your next [gold]Attack[/gold] is played an extra time this turn.", "ONE_TWO_PUNCH_POWER.smartDescription": "Your next {Amount:plural:[gold]Attack[/gold] is|[blue]{}[/blue] [gold]Attacks[/gold] are} played an extra time this turn.", "ONE_TWO_PUNCH_POWER.title": "One-Two Punch", "ORB_GENERATOR.description": "This enemy can harness the power of [gold]Orbs[/gold]. [gold]Orbs[/gold] will trigger at the end of your turn and are unaffected by buffs or debuffs.", "ORB_GENERATOR.title": "Orb Generator", "PAGESTORM_POWER.description": "Whenever you draw an [gold]Ethereal[/gold] card, draw [blue]1[/blue] card.", "PAGESTORM_POWER.smartDescription": "Whenever you draw an [gold]Ethereal[/gold] card, draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "PAGESTORM_POWER.title": "Pagestorm", "PALE_BLUE_DOT_POWER.description": "If you play [blue]4[/blue] or more cards in a turn, draw [blue]2[/blue] additional cards at the start of your next turn.", "PALE_BLUE_DOT_POWER.smartDescription": "If you play [blue]{CardPlay}[/blue] or more cards in a turn, draw [blue]{Amount}[/blue] additional {Amount:plural:card|cards} at the start of your next turn.", "PALE_BLUE_DOT_POWER.title": "Pale <PERSON>", "PANACHE_POWER.description": "If you play [blue]5[/blue] cards this turn, deal damage to all enemies.", "PANACHE_POWER.smartDescription": "If you play [blue]{CardsLeft}[/blue] more {CardsLeft:plural:card|cards} this turn, deal [blue]{Amount}[/blue] damage to all enemies.", "PANACHE_POWER.title": "<PERSON><PERSON>", "PAPER_CUTS.description": "Whenever this creature deals unblocked attack damage, you lose [blue]1[/blue] [gold]Max HP[/gold].", "PAPER_CUTS.smartDescription": "Whenever [gold]{OwnerName}[/gold] deals unblocked attack damage, you lose [blue]{Amount}[/blue] [gold]Max HP[/gold].", "PAPER_CUTS.title": "Paper Cuts", "PARRY_POWER.description": "Whenever you play [gold]Sovereign Blade[/gold], gain [blue]8[/blue] [gold]Block[/gold].", "PARRY_POWER.smartDescription": "Whenever you play [gold]Sovereign Blade[/gold], gain [blue]{Amount}[/blue] [gold]Block[/gold].", "PARRY_POWER.title": "<PERSON>", "PERFECT_REFLECTION_POWER.description": "Blocked damage is reflected to your attacker.", "PERFECT_REFLECTION_POWER.smartDescription": "Blocked damage is reflected to your attacker for [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "PERFECT_REFLECTION_POWER.title": "Perfect Reflection", "PERSONAL_HIVE.description": "Whenever this enemy takes attack damage, add [blue]X[/blue] [gold]Dazed[/gold] to your [gold]Draw Pile[/gold].", "PERSONAL_HIVE.smartDescription": "Whenever this enemy takes attack damage, add [blue]{Amount}[/blue] [gold]Dazed[/gold] to your [gold]Draw Pile[/gold].", "PERSONAL_HIVE.title": "Personal Hive", "PILLAR_OF_CREATION_POWER.description": "Whenever you create a card, gain [blue]5[/blue] [gold]Block[/gold].", "PILLAR_OF_CREATION_POWER.smartDescription": "Whenever you create a card, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "PILLAR_OF_CREATION_POWER.title": "Pillar of Creation", "PLATING.description": "At the end of your turn, gain [gold]Block[/gold]. [gold]Plating[/gold] is reduced by [blue]1[/blue] at the start of your turn.", "PLATING.smartDescription": "At the end of your turn, gain [blue]{Amount}[/blue] [gold]Block[/gold]. [gold]Plating[/gold] is reduced by [blue]1[/blue] at the start of your turn.", "PLATING.title": "Plating", "PLOW.description": "When this creature loses HP, its [gold]Attack[/gold] deals that much less damage. If [gold]Plow[/gold] reaches [blue]0[/blue], it becomes [gold]Stunned[/gold].", "PLOW.smartDescription": "When [gold]Ceremonial Beast[/gold] loses HP, its [gold]Attack[/gold] deals that much less damage. If [gold]Plow[/gold] reaches [blue]0[/blue], it becomes [gold]Stunned[/gold].", "PLOW.title": "<PERSON><PERSON>", "POISON.description": "Poisoned creatures lose HP at the start of their turn. Each turn, Poison is reduced by [blue]1[/blue].", "POISON.smartDescription": "At the start of {OnPlayer:your turn, lose|its turn, loses} [blue]{Amount}[/blue] HP, then reduce [gold]Poison[/gold] by [blue]1[/blue].", "POISON.title": "Poison", "POLISH_POWER.description": "The next Colorless card you play costs [blue]0[/blue].", "POLISH_POWER.smartDescription": "The next {Amount:plural:Colorless card|[blue]{}[/blue] Colorless cards} you play costs [blue]0[/blue].", "POLISH_POWER.title": "Polish", "POSSESS_POWER.description": "When this dies, return all stolen [gold]Strength[/gold] to the player.", "POSSESS_POWER.smartDescription": "When this dies, return all stolen [gold]Strength[/gold] to the player.", "POSSESS_POWER.title": "Possess Power", "POSSESS_SPEED.description": "When this dies, return all stolen [gold]Dexterity[/gold] to the player.", "POSSESS_SPEED.smartDescription": "When this dies, return all stolen [gold]Dexterity[/gold] to the player.", "POSSESS_SPEED.title": "Possess Speed", "PREP_TIME_POWER.description": "At the start of your turn, gain [blue]1[/blue] [gold]Vigor[/gold].", "PREP_TIME_POWER.smartDescription": "At the start of your turn, gain [blue]{Amount}[/blue] [gold]Vigor[/gold].", "PREP_TIME_POWER.title": "Prep Time", "PROTECTOR.description": "At the start of its turn, Torch Head Amalgam gives the Queen [blue]18[/blue] [gold]Block[/gold].", "PROTECTOR.smartDescription": "At the start of its turn, Torch Head Amalgam gives the Queen [blue]{Amount}[/blue] [gold]Block[/gold].", "PROTECTOR.title": "Protector", "PULL_FROM_BELOW_POWER.description": "Whenever you draw a card this turn, this enemy loses [blue]3[/blue] HP.", "PULL_FROM_BELOW_POWER.smartDescription": "Whenever you draw a card this turn, this enemy loses [blue]{Amount}[/blue] HP.", "PULL_FROM_BELOW_POWER.title": "Pull From Below", "RADIANCE.description": "Gain an additional [gold]Energy[/gold] next turn.", "RADIANCE.smartDescription": "Gain additional {Energy:energyIcons()} {Amount:plural:next turn|for the next [blue]{}[/blue] turns}.", "RADIANCE.title": "Radiance", "RAGE_POWER.description": "Whenever you play an [gold]Attack[/gold] this turn, gain [blue]3[/blue] [gold]Block[/gold]", "RAGE_POWER.smartDescription": "Whenever you play an [gold]Attack[/gold] this turn, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "RAGE_POWER.title": "Rage", "RAMPART.description": "At the start of the player's turn, Turret Operator gains [blue]25[/blue] [gold]Block[/gold].", "RAMPART.smartDescription": "At the start of the player's turn, Turret Operator gains [blue]{Amount}[/blue] [gold]Block[/gold].", "RAMPART.title": "<PERSON><PERSON><PERSON>", "RAVENOUS.description": "When an enemy dies, <PERSON>e Slug immediately eats it, becoming [gold]Stunned[/gold] and gaining [blue]1[/blue] [gold]Strength[/gold].", "RAVENOUS.smartDescription": "When an enemy dies, <PERSON>e Slug immediately eats it, becoming [gold]Stunned[/gold] and gaining [blue]{Amount}[/blue] [gold]Strength[/gold].", "RAVENOUS.title": "Ravenous", "READ_THE_BONES_POWER.description": "Whenever you [gold]Su<PERSON><PERSON>[/gold], draw 1 card.", "READ_THE_BONES_POWER.smartDescription": "Whenever you [gold]Summon[/gold], draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "READ_THE_BONES_POWER.title": "Read the Bones", "REAPER_FORM_POWER.description": "At the end of your turn, deal damage to all enemies equal to 10% of their max HP.", "REAPER_FORM_POWER.smartDescription": "At the end of your turn, deal damage to all enemies equal to [blue]{Amount}[/blue]% of their max HP.", "REAPER_FORM_POWER.title": "Reaper Form", "REATTACH.description": "If other segments are still alive, revives in [blue]2[/blue] turns with [blue]25[/blue] [gold]HP[/gold].", "REATTACH.smartDescription": "If other segments are still alive, revives in [blue]2[/blue] turns with [blue]{Amount}[/blue] [gold]HP[/gold].", "REATTACH.title": "Reattach", "REBOUND_POWER.description": "The next card you play this turn is placed on the top of your [gold]Draw Pile[/gold].", "REBOUND_POWER.smartDescription": "The next {Amount:plural:card|[blue]{}[/blue] cards} you play this turn {Amount:plural:is|are} placed on the top of your [gold]Draw Pile[/gold].", "REBOUND_POWER.title": "Rebound", "REFLECTIVE_FORTRESS_POWER.description": "Whenever you fully [gold]Block[/gold] an attack, the blocked damage is reflected to the attacker.", "REFLECTIVE_FORTRESS_POWER.smartDescription": "Whenever you fully [gold]Block[/gold] an attack, {Amount:plural:|[blue]{}x[/blue]} the blocked damage is reflected to the attacker.", "REFLECTIVE_FORTRESS_POWER.title": "Reflective Fortress", "REGEN.description": "<PERSON><PERSON> heals <PERSON> at the end of your turn. Each turn, <PERSON><PERSON> is reduced by [blue]1[/blue].", "REGEN.smartDescription": "At the end of your turn, heal [blue]{Amount}[/blue] [gold]HP[/gold], then reduce [gold]Regen[/gold] by [blue]1[/blue].", "REGEN.title": "Regen", "RESERVES_POWER.description": "If you don't have enough [gold]Energy[/gold] for a card, 2{singleStarIcon} are used per [gold]Energy[/gold] instead.", "RESERVES_POWER.smartDescription": "If you don't have enough {energyPrefix:energyIcons(1)} for a card, 2{singleStarIcon} are used per {energyPrefix:energyIcons(1)} instead.", "RESERVES_POWER.title": "Reserves", "RESONANCE_POWER.description": "The first time you shuffle your [gold]Draw Pile[/gold] each turn, gain [gold]Energy[/gold].", "RESONANCE_POWER.smartDescription": "The first time you shuffle your [gold]Draw Pile[/gold] each turn, gain {Amount:energyIcons()}.", "RESONANCE_POWER.title": "Resonance", "RESTORE_DEXTERITY.description": "At the end of its turn, gains [blue]9[/blue] [gold]Dexterity[/gold].", "RESTORE_DEXTERITY.smartDescription": "At the end of its turn, gains [blue]{Amount}[/blue] [gold]Dexterity[/gold].", "RESTORE_DEXTERITY.title": "<PERSON><PERSON>", "RESTORE_FOCUS.description": "At the end of its turn, gains [blue]9[/blue] [gold]Focus[/gold].", "RESTORE_FOCUS.smartDescription": "At the end of its turn, gains [blue]{Amount}[/blue] [gold]Focus[/gold].", "RESTORE_FOCUS.title": "Restore Focus", "RESTORE_STRENGTH.description": "At the end of its turn, gains [blue]9[/blue] [gold]Strength[/gold].", "RESTORE_STRENGTH.smartDescription": "At the end of its turn, gains [blue]{Amount}[/blue] [gold]Strength[/gold].", "RESTORE_STRENGTH.title": "Rest<PERSON> Strength", "RETAIN_HAND.description": "[gold]Retain[/gold] your hand for the next [blue]2[/blue] turns.", "RETAIN_HAND.smartDescription": "[gold]Retain[/gold] your hand {Amount:plural:this turn|for the next [blue]{}[/blue] turns}.", "RETAIN_HAND.title": "Retain Hand", "RITUAL.description": "Gain [gold]Strength[/gold] at the end of your turn.", "RITUAL.smartDescription": "At the end of {OnPlayer:your|its} turn, {OnPlayer:gain|gains} [blue]{Amount}[/blue] [gold]Strength[/gold].", "RITUAL.title": "Ritual", "ROLLING_BOULDER_POWER.description": "At the start of your turn, deal [blue]10[/blue] damage to all enemies and increase this damage by [blue]10[/blue].", "ROLLING_BOULDER_POWER.smartDescription": "At the start of your turn, deal [blue]{Damage}[/blue] damage to all enemies and increase this damage by [blue]{Amount}[/blue].", "ROLLING_BOULDER_POWER.title": "Rolling Boulder", "ROPE_REBOUND_POWER.description": "The next Attack another ally plays on the enemy is played {Amount:plural:an extra time|[blue]{}[/blue] extra times}.", "ROPE_REBOUND_POWER.remoteDescription": "The next Attack an ally plays on the enemy is played an extra time because of [gold]{Applier}[/gold].", "ROPE_REBOUND_POWER.smartDescription": "The next Attack another ally plays on the enemy is played an extra time.", "ROPE_REBOUND_POWER.title": "Rope Rebound", "RUPTURE_POWER.description": "Whenever you lose HP from a card, gain [blue]1[/blue] [gold]Strength[/gold].", "RUPTURE_POWER.smartDescription": "Whenever you lose HP from a card, gain [blue]{Amount}[/blue] [gold]Strength[/gold].", "RUPTURE_POWER.title": "Rupture", "SANDPIT.description": "A power used by The Insatiable.", "SANDPIT.smartDescription": "{Amount:plural:At the start of the next turn|In [blue]{}[/blue] turns}, you will be eaten and die.", "SANDPIT.title": "Sandpit", "SCRUTINY.description": "Cards are afflicted with [gold]Scrutinized[/gold] this turn.", "SCRUTINY.smartDescription": "Cards are afflicted with [gold]Scrutinized[/gold] this turn.", "SCRUTINY.title": "<PERSON><PERSON><PERSON><PERSON>", "SEEKING_EDGE_POWER.description": "[gold]Sovereign Blade[/gold] now hits all enemies.", "SEEKING_EDGE_POWER.smartDescription": "[gold]Sovereign Blade[/gold] now hits all enemies.", "SEEKING_EDGE_POWER.title": "Seeking Edge", "SELF_FORMING_CLAY_POWER.description": "Gain [blue]3[/blue] [gold]Block[/gold] next turn.", "SELF_FORMING_CLAY_POWER.smartDescription": "Gain [blue]{Amount}[/blue] [gold]Block[/gold] next turn.", "SELF_FORMING_CLAY_POWER.title": "Self-Forming Clay", "SENTRY_MODE_POWER.description": "At the end of your turn, [gold]Osty[/gold] deals [blue]10[/blue] damage to a random enemy.", "SENTRY_MODE_POWER.smartDescription": "At the end of your turn, [gold]Osty[/gold] deals [blue]{Amount}[/blue] damage to a random enemy.", "SENTRY_MODE_POWER.title": "Sentry Mode", "SHADOWMELD_POWER.description": "Double your [gold]Block[/gold] gain this turn.", "SHADOWMELD_POWER.smartDescription": "Double your [gold]Block[/gold] gain this turn{Amount:plural:| [blue]{}[/blue] times}.", "SHADOWMELD_POWER.title": "Shadowmeld", "SHADOW_STEP_POWER.description": "Next turn, [gold]Attacks[/gold] deal double damage.", "SHADOW_STEP_POWER.smartDescription": "{Amount:plural:Next turn|For the next [blue]{}[/blue] turns after this one}, [gold]Attacks[/gold] deal double damage.", "SHADOW_STEP_POWER.title": "Shadow Step", "SHARP_EDGE_POWER.description": "Whenever you create a [gold]Shiv[/gold], [gold]Upgrade[/gold] it.", "SHARP_EDGE_POWER.smartDescription": "Whenever you create a [gold]Shiv[/gold], [gold]Upgrade[/gold] it.", "SHARP_EDGE_POWER.title": "Sharp Edge Power", "SHRIEK.description": "The first time this creature's HP reaches [blue]50%[/blue] or below, it becomes [gold]Stunned[/gold].", "SHRIEK.smartDescription": "The first time [gold]{OwnerName}'s[/gold] HP reaches [blue]{Thresh<PERSON>}[/blue] or below, it becomes [gold]Stunned[/gold].", "SHRIEK.title": "<PERSON><PERSON>", "SHRINK.description": "This creature's [gold]Attacks[/gold] deal [blue]30%[/blue] less damage for the next 3 turns.", "SHRINK.smartDescription": "{ApplierName.StringValue:cond:While {} is alive, your|[gold]{OwnerName}'s[/gold]} [gold]Attacks[/gold] deal [blue]{DamageDecrease}%[/blue] less damage{Amount:cond:==1? next turn|>1? for the next [blue]{}[/blue] turns|}.", "SHRINK.title": "Shrink", "SHROUD_POWER.description": "Whenever you apply [gold]Doom[/gold], gain [blue]3[/blue] [gold]Block[/gold].", "SHROUD_POWER.smartDescription": "Whenever you apply [gold]Doom[/gold], gain [blue]{Amount}[/blue] [gold]Block[/gold].", "SHROUD_POWER.title": "<PERSON><PERSON><PERSON>", "SKITTISH.description": "The first time this creature takes attack damage each turn, it gains [blue]1[/blue] [gold]Block[/gold].", "SKITTISH.smartDescription": "The first time [gold]{OwnerName}[/gold] takes attack damage each turn, it gains [blue]{Amount}[/blue] [gold]Block[/gold].", "SKITTISH.title": "<PERSON><PERSON><PERSON>", "SLEIGHT_OF_FLESH_POWER.description": "Whenever you apply a debuff to an enemy, they take [blue]13[/blue] damage.", "SLEIGHT_OF_FLESH_POWER.smartDescription": "Whenever you apply a debuff to an enemy, they take [blue]{Amount}[/blue] damage.", "SLEIGHT_OF_FLESH_POWER.title": "Sleight Of Flesh Power", "SLIPPERY.description": "The next time this creature loses HP, it only loses [blue]1[/blue] HP instead.", "SLIPPERY.smartDescription": "The next {Amount:plural:time|[blue]{}[/blue] times} [gold]{OwnerName}[/gold] loses HP, it only loses [blue]1[/blue] HP instead.", "SLIPPERY.title": "<PERSON><PERSON><PERSON><PERSON>", "SLOW.description": "Whenever you play a card, this enemy receives [blue]10%[/blue] more damage from [gold]Attacks[/gold] this turn.", "SLOW.smartDescription": "Whenever you play a card, {OnPlayer:you receive|this enemy receives} [blue]10%[/blue] more damage from [gold]Attacks[/gold] this turn.{Amount:plural:|| (Receives [blue]{Amount}%[/blue] more damage)}", "SLOW.title": "Slow", "SLUMBER.description": "Awakens upon taking turns or losing HP 3 times.", "SLUMBER.smartDescription": "Awakens upon taking turns or losing HP [blue]{Amount}[/blue] times.", "SLUMBER.title": "Slumber", "SMOGGY.description": "You may only play [blue]1[/blue] [gold]Skill[/gold] per turn.", "SMOGGY.smartDescription": "You may only play [blue]{Amount}[/blue] [gold]Skill[/gold] per turn.", "SMOGGY.title": "<PERSON><PERSON><PERSON>", "SNOWFIELD_POWER.description": "Whenever you channel [gold]Frost[/gold], gain [blue]4[/blue] [gold]Block[/gold].", "SNOWFIELD_POWER.smartDescription": "Whenever you channel [gold]Frost[/gold], gain [blue]{Amount}[/blue] [gold]Block[/gold].", "SNOWFIELD_POWER.title": "Snowfield", "SOAR.description": "Receives [gold]50%[/gold] less attack damage for X turns.", "SOAR.smartDescription": "Receives [blue]{DamageDecrease}%[/blue] less attack damage for {Amount:plural:this turn|[blue]{}[/blue] turns}.", "SOAR.title": "Soar", "SOUL_WITHER.description": "After Soul Nexus deals unblocked attack damage to you 9 times, it deals 999 damage to you.", "SOUL_WITHER.smartDescription": "After Soul Nexus deals unblocked attack damage to you [blue]{Amount}[/blue] {Amount:plural:time|times}, it deals [blue]{Damage}[/blue] damage to you.", "SOUL_WITHER.title": "<PERSON>", "SOW_POWER.description": "[gold]Retain[/gold] [blue]1[/blue] random card in your [gold]Hand[/gold] this turn.", "SOW_POWER.smartDescription": "[gold]Retain[/gold] [blue]{Amount}[/blue] random {Amount:plural:card|cards} in your [gold]Hand[/gold] this turn.", "SOW_POWER.title": "Sow", "SPECTRUM_SHIFT_PLUS_POWER.description": "At the start of your turn, add [blue]2[/blue] random [gold]Upgraded[/gold] Colorless cards to your hand.", "SPECTRUM_SHIFT_PLUS_POWER.smartDescription": "At the start of your turn, add [blue]{Amount}[/blue] random [gold]Upgraded[/gold] Colorless {Amount:plural:card|cards} to your hand.", "SPECTRUM_SHIFT_PLUS_POWER.title": "Spectrum Shift+", "SPECTRUM_SHIFT_POWER.description": "At the start of your turn, add [blue]2[/blue] random Colorless cards to your hand.", "SPECTRUM_SHIFT_POWER.smartDescription": "At the start of your turn, add [blue]{Amount}[/blue] random Colorless {Amount:plural:card|cards} to your hand.", "SPECTRUM_SHIFT_POWER.title": "Spectrum Shift", "SPEEDSTER_POWER.description": "Whenever you draw a card during your turn, deal [blue]1[/blue] damage to a random enemy.", "SPEEDSTER_POWER.smartDescription": "Whenever you draw a card during your turn, deal [blue]{Amount}[/blue] damage to a random enemy.", "SPEEDSTER_POWER.title": "Speedster", "SPIRIT_OF_ASH_POWER.description": "Whenever you play an [gold]Ethereal[/gold] card, gain [blue]5[/blue] [gold]Block[/gold].", "SPIRIT_OF_ASH_POWER.smartDescription": "Whenever you play an [gold]Ethereal[/gold] card, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "SPIRIT_OF_ASH_POWER.title": "Spirit of Ash", "STAMPEDE_POWER.description": "At the end of your turn, [blue]1[/blue] random [gold]Attack[/gold] in your hand is played against a random enemy.", "STAMPEDE_POWER.smartDescription": "At the end of your turn, [blue]{Amount}[/blue] random [gold]{Amount:plural:Attack|Attacks}[/gold] in your hand {Amount:plural:is played against a random enemy.|are played against a random enemy.}", "STAMPEDE_POWER.title": "Stampede", "STAR_CHARGED.description": "Gain 1 {singleStarIcon} next turn.", "STAR_CHARGED.smartDescription": "Gain [blue]{Amount}[/blue] {singleStarIcon} next turn.", "STAR_CHARGED.title": "Star Charged", "STEAM_ERUPTION.description": "Upon dying, deals damage at the end of your next turn.", "STEAM_ERUPTION.smartDescription": "Upon dying, deals [blue]{Amount}[/blue] damage at the end of your next turn.", "STEAM_ERUPTION.title": "Steam Eruption", "STOCK.description": "When killed, a new Axebot is summoned in its place.", "STOCK.title": "Stock", "STORM_POWER.description": "Whenever you play a [gold]Power[/gold], [gold]Channel[/gold] [blue]1[/blue] [gold]Lightning[/gold].", "STORM_POWER.smartDescription": "Whenever you play a [gold]Power[/gold], [gold]Channel[/gold] [blue]{Amount}[/blue] [gold]Lightning[/gold].", "STORM_POWER.title": "Storm", "STRANGLING_STRIKE_POWER.description": "Whenever you play an [gold]Attack[/gold] this turn, this enemy loses [blue]2[/blue] HP.", "STRANGLING_STRIKE_POWER.smartDescription": "Whenever you play an [gold]Attack[/gold] this turn, {OnPlayer:you lose|this enemy loses} [blue]{Amount}[/blue] HP.", "STRANGLING_STRIKE_POWER.title": "Strangling Strike", "STRATAGEM_POWER.description": "Whenever you shuffle your [gold]Draw Pile[/gold], choose [blue]1[/blue] card from it to put into your hand.", "STRATAGEM_POWER.selectionScreenPrompt": "Choose {Amount:plural:a Card|{} Cards} to Add to Your Hand", "STRATAGEM_POWER.smartDescription": "Whenever you shuffle your [gold]Draw Pile[/gold], choose [blue]{Amount}[/blue] {Amount:plural:card|cards} from it to put into your hand.", "STRATAGEM_POWER.title": "Stratagem", "STRENGTH.description": "Strength adds additional damage to [gold]Attacks[/gold].", "STRENGTH.smartDescription": "{Amount:cond:<0?Decreases|Increases} attack damage by [blue]{Amount:abs()}[/blue].", "STRENGTH.title": "Strength", "STRENGTH_DOWN.description": "At the end of this turn, lose [gold]Strength[/gold].", "STRENGTH_DOWN.smartDescription": "At the end of this turn, lose [blue]{Amount}[/blue] [gold]Strength[/gold].", "STRENGTH_DOWN.title": "Strength Down", "SUCK.description": "Whenever this creature deals unblocked attack damage, it gains [blue]1[/blue] [gold]Strength[/gold].", "SUCK.smartDescription": "Whenever [gold]{OwnerName}[/gold] deals unblocked attack damage, it gains [blue]{Amount}[/blue] [gold]Strength[/gold].", "SUCK.title": "<PERSON><PERSON>", "SURPRISE.description": "Something is off about this creature...", "SURPRISE.smartDescription": "Something is off about this creature...", "SURPRISE.title": "Surprise", "SURROUNDED.description": "Receive [blue]50%[/blue] more damage if attacked from behind. Use targeting cards or potions to change your orientation.", "SURROUNDED.title": "Surrounded", "SWIPE.description": "Upon killing this enemy, a stolen card is returned to your hand.", "SWIPE.smartDescription": "Upon killing this enemy, the stolen card is returned to your hand.", "SWIPE.title": "Swipe", "SYMBIOSIS_POWER.description": "Whenever you [gold]Su<PERSON><PERSON>[/gold], draw [blue]1[/blue] cards.", "SYMBIOSIS_POWER.smartDescription": "Whenever you [gold]Summon[/gold], draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "SYMBIOSIS_POWER.title": "Symbiosis", "TANGLED.description": "[gold]Attacks[/gold] cost [blue]1[/blue] additional [gold]Energy[/gold] this turn.", "TANGLED.smartDescription": "[gold]Attacks[/gold] cost an additional {Amount:energyIcons()} {Amount:plural:this turn|for {} turns}.", "TANGLED.title": "Tangled", "TAUNT_POWER.description": "When an enemy attacks you, apply [blue]1[/blue] [gold]Vulnerable[/gold] to it.", "TAUNT_POWER.smartDescription": "When an enemy attacks you, apply [blue]{Amount}[/blue] [gold]Vulnerable[/gold] to it.", "TAUNT_POWER.title": "<PERSON><PERSON>", "TENDER.description": "Whenever you play a card, lose [blue]1[/blue] [gold]Strength[/gold] and [blue]1[/blue] [gold]Dexterity[/gold] this turn.", "TENDER.smartDescription": "Whenever you play a card, lose [blue]{Amount}[/blue] [gold]Strength[/gold] and [blue]{Amount}[/blue] [gold]Dexterity[/gold] this turn.", "TENDER.title": "<PERSON>der", "TERRAFORMING_POWER.description": "At the end of your turn, increase the damage of Attacks in your [gold]Draw Pile[/gold] by [blue]3[/blue] this combat.", "TERRAFORMING_POWER.smartDescription": "At the end of your turn, increase the damage of Attacks in your [gold]Draw Pile[/gold] by [blue]{Amount}[/blue] this combat.", "TERRAFORMING_POWER.title": "Terraforming", "TERRITORIAL.description": "At the end of its turn, gains [blue]1[/blue] [gold]Strength[/gold].", "TERRITORIAL.smartDescription": "At the end of its turn, gains [blue]{Amount}[/blue] [gold]Strength[/gold].", "TERRITORIAL.title": "Territorial", "TEZCATARAS_BLIGHT.description": "At the end of this turn, lose [blue]1[/blue] [gold]Strength[/gold].", "TEZCATARAS_BLIGHT.smartDescription": "At the end of this turn, lose [blue]{Amount}[/blue] [gold]Strength[/gold].", "TEZCATARAS_BLIGHT.title": "Tezcataras Blight", "THE_BOMB_POWER.description": "At the end of [blue]3[/blue] turns, deal [blue]40[/blue] damage to all enemies.", "THE_BOMB_POWER.smartDescription": "At the end of {Amount:plural:this turn|[blue]{}[/blue] turns}, deal [blue]{Damage}[/blue] damage to all enemies.", "THE_BOMB_POWER.title": "The Bomb", "THE_GAMBIT_POWER.description": "If you take attack damage this combat, die.", "THE_GAMBIT_POWER.smartDescription": "If you take attack damage this combat, die.", "THE_GAMBIT_POWER.title": "The Gambit", "THE_SEALED_THRONE_POWER.description": "All cards cost 0{singleStarIcon} this turn.", "THE_SEALED_THRONE_POWER.smartDescription": "All cards cost 0{singleStarIcon} this turn.", "THE_SEALED_THRONE_POWER.title": "The Sealed Throne Power", "THE_TANK_POWER.description": "Take double damage from enemies. Allies take half damage from enemies.", "THE_TANK_POWER.smartDescription": "Take [blue]{Amount}[/blue] times more damage from enemies. Allies take [blue]1/{Amount}[/blue] as much damage from enemies.", "THE_TANK_POWER.title": "The Tank", "THIEVERY.description": "Steals gold when Attacking.", "THIEVERY.smartDescription": "Steals [blue]{Amount}[/blue] gold when Attacking.", "THIEVERY.title": "<PERSON><PERSON><PERSON><PERSON>", "THORNS.description": "When hit by an attack, deal damage back.", "THORNS.smartDescription": "When hit by an attack, deal [blue]{Amount}[/blue] damage back.", "THORNS.title": "Thorns", "THUNDER_POWER.description": "Whenever you [gold]Evoke Lightning[/gold], deal [blue]8[/blue] damage to each enemy hit.", "THUNDER_POWER.smartDescription": "Whenever you [gold]Evoke Lightning[/gold], deal [blue]{Amount}[/blue] damage to each enemy hit.", "THUNDER_POWER.title": "Thunder", "TOOLS_OF_THE_TRADE_POWER.description": "At the start of your turn, draw [blue]1[/blue] card and discard [blue]1[/blue] card.", "TOOLS_OF_THE_TRADE_POWER.smartDescription": "At the start of your turn, draw [blue]{Amount}[/blue] {Amount:plural:card|cards} and discard [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "TOOLS_OF_THE_TRADE_POWER.title": "Tools of the Trade", "TORIC_TOUGHNESS_POWER.description": "Gain [blue]5[/blue] [gold]Block[/gold] at the start of the next [blue]2[/blue] turns.", "TORIC_TOUGHNESS_POWER.smartDescription": "Gain [blue]{Block:diff()}[/blue] [gold]Block[/gold] at the start of the next [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "TORIC_TOUGHNESS_POWER.title": "<PERSON><PERSON>", "TORN.description": "Draw [blue]1[/blue] less card every turn.", "TORN.smartDescription": "Draw [blue]{Amount}[/blue] less {Amount:plural:card|cards} every turn.", "TORN.title": "<PERSON><PERSON>", "TRACKING_POWER.description": "[gold]Weak[/gold] enemies take [blue]20%[/blue] additional damage from Attacks and [gold]Poison[/gold].", "TRACKING_POWER.smartDescription": "[gold]Weak[/gold] enemies take [blue]{Amount}%[/blue] additional damage from Attacks and [gold]Poison[/gold].", "TRACKING_POWER.title": "Tracking", "TYRANNY_POWER.description": "At the start of your turn, draw a card and [gold]Exhaust[/gold] a card from your [gold]Hand[/gold].", "TYRANNY_POWER.smartDescription": "At the start of your turn, draw {Amount:plural:[blue]{}[/blue] card|[blue]{}[/blue] cards} and [gold]Exhaust[/gold] {Amount:plural:[blue]{}[/blue] card|[blue]{}[/blue]cards} from your [gold]Hand[/gold].", "TYRANNY_POWER.title": "Tyranny", "UNFLINCHING.description": "Reduce all damage and HP loss taken by [blue]1[/blue].", "UNFLINCHING.smartDescription": "Reduce all damage and HP loss taken by [blue]{Amount}[/blue].", "UNFLINCHING.title": "Unflinching", "UNMOVABLE_POWER.description": "The first time you gain block each turn, gain [gold]Block[/gold].", "UNMOVABLE_POWER.smartDescription": "The first time you gain block each turn, gain [blue]{Amount}[/blue] [gold]Block[/gold].", "UNMOVABLE_POWER.title": "Unmovable", "UNRELENTING_POWER.description": "Your next [gold]Attack[/gold] costs [blue]0[/blue].", "UNRELENTING_POWER.smartDescription": "Your next {Amount:plural:[gold]Attack[/gold] costs|[blue]{}[/blue] [gold]Attacks[/gold] cost} [blue]0[/blue].", "UNRELENTING_POWER.title": "Unrelenting", "UNSTABLE.description": "Whenever you play an [gold]Attack[/gold], this enemy loses [blue]5[/blue] HP.", "UNSTABLE.smartDescription": "Whenever you play an [gold]Attack[/gold], {OnPlayer:you lose|this enemy loses} [blue]{Amount}[/blue] HP.", "UNSTABLE.title": "Unstable", "UNSTEADY.description": "When <PERSON><PERSON><PERSON> falls below [blue]50%[/blue] HP, becomes [gold]Stunned[/gold].", "UNSTEADY.smartDescription": "When <PERSON><PERSON><PERSON> falls below [blue]{HealthLimit}%[/blue] HP, becomes [gold]Stunned[/gold].", "UNSTEADY.title": "Unsteady", "VEILPIERCER_POWER.description": "The next [gold]Ethereal card[/gold] you play costs [blue]0[/blue].", "VEILPIERCER_POWER.smartDescription": "The next [blue]{Amount}[/blue] [gold]Ethereal[/gold] {Amount:plural:card|cards} you play costs [blue]0[/blue].", "VEILPIERCER_POWER.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VICIOUS_POWER.description": "Whenever you apply [gold]Vulnerable[/gold], draw [blue]1[/blue] card.", "VICIOUS_POWER.smartDescription": "Whenever you apply [gold]Vulnerable[/gold], draw [blue]{Amount}[/blue] {Amount:plural:card|cards}.", "VICIOUS_POWER.title": "Vicious", "VIGOR.description": "Your next [gold]Attack[/gold] deals additional damage.", "VIGOR.smartDescription": "{OnPlayer:Your|[gold]{OwnerName}'s[/gold]} next [gold]Attack[/gold] deals [blue]{Amount}[/blue] additional damage.", "VIGOR.title": "Vigor", "VISUAL_ONLY.description": "Visual Only", "VISUAL_ONLY.title": "Visual Only", "VOID_FORM_POWER.description": "The first [blue]2[/blue] cards you play each turn cost 0.", "VOID_FORM_POWER.smartDescription": "The first {Amount:plural:card|[blue]{}[/blue] cards} you play each turn {Amount:plural:costs|cost} 0.", "VOID_FORM_POWER.title": "Void Form", "VOLATILE.description": "Upon dying, deals [blue]1[/blue] damage at the end of your next turn.", "VOLATILE.smartDescription": "Upon dying, deals [blue]{Amount}[/blue] damage at the end of your next turn.", "VOLATILE.title": "Volatile", "VULNERABLE.description": "Vulnerable creatures take [blue]50%[/blue] more damage from [gold]Attacks[/gold].", "VULNERABLE.smartDescription": "Receive [blue]{DamageIncrease:percentMore()}%[/blue] more damage from [gold]Attacks[/gold] for [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "VULNERABLE.title": "Vulnerable", "WEAK.description": "Weakened creatures deal [blue]25%[/blue] less damage with [gold]Attacks[/gold].", "WEAK.smartDescription": "[gold]Attacks[/gold] deal [blue]25%[/blue] less damage for [blue]{Amount}[/blue] {Amount:plural:turn|turns}.", "WEAK.title": "Weak", "WELL_LAID_PLANS_POWER.description": "At the end of your turn, [gold]Retain[/gold] up to [blue]1[/blue] card.", "WELL_LAID_PLANS_POWER.selectionScreenPrompt": "Choose {Amount:plural:a Card|{} Cards} to Retain", "WELL_LAID_PLANS_POWER.smartDescription": "At the end of your turn, [gold]Retain[/gold] up to [gold]{Amount}[/gold] {Amount:plural:card|cards}.", "WELL_LAID_PLANS_POWER.title": "Well-Laid Plans", "WINGED_JUMP_POWER.description": "At the start of your turn, apply [blue]1[/blue] [gold]Vulnerable[/gold] to all enemies.", "WINGED_JUMP_POWER.smartDescription": "At the start of your turn, apply [blue]{Amount}[/blue] [gold]Vulnerable[/gold] to all enemies.", "WINGED_JUMP_POWER.title": "Winged Jump", "WRAITH_FORM_POWER.description": "At the start of your turn, lose [blue]1[/blue] [gold]Dexterity[/gold].", "WRAITH_FORM_POWER.smartDescription": "At the start of your turn, lose [blue]{Amount}[/blue] [gold]Dexterity[/gold].", "WRAITH_FORM_POWER.title": "Wraith Form"}