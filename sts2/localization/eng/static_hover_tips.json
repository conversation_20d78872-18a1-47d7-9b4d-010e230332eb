{"BLOCK.description": "Until next turn, prevents damage.", "BLOCK.title": "Block", "BLOOD_BANK_BUNDLE_OF_POTIONS.description": "Get [blue]3[/blue] random potions.", "BLOOD_BANK_BUNDLE_OF_POTIONS.title": "Bundle of Potions", "BLOOD_BANK_CARD_REMOVAL_SERVICE.description": "Remove a card from your deck.", "BLOOD_BANK_CARD_REMOVAL_SERVICE.title": "Card Removal Service", "BLOOD_BANK_TRANSFUSION.description": "Pay gold, receive health.", "BLOOD_BANK_TRANSFUSION.title": "Transfusion", "CARD_REWARD.description": "A pack of [blue]3[/blue] random cards. You may choose [blue]1[/blue] to add to your [gold]Deck[/gold].", "CARD_REWARD.title": "Card Reward", "CHANNELING.description": "Channeling an Orb puts it into your first empty slot. if you have no empty slots, your first Orb is automatically [gold]Evoked[/gold] to make room.", "CHANNELING.title": "Channel", "COMPENDIUM.description": "View all of the cards, potions, and relics you have encountered throughout the Spire.", "COMPENDIUM.title": "Compendium", "CURSED_VENDOR_BUNDLE_OF_POTIONS.description": "Get [blue]3[/blue] random potions.", "CURSED_VENDOR_BUNDLE_OF_POTIONS.title": "Bundle of Potions", "DECK.description": "View all of the cards in your deck.", "DECK.title": "Deck (D)", "DISCARD_PILE.description": "If your draw pile is empty, the discard pile is shuffled into the draw pile.\n\nClick to view the cards in your discard pile.", "DISCARD_PILE.title": "<PERSON><PERSON> (S)", "DRAW_PILE.description": "At the start of each turn,\n[blue]5[/blue] cards are drawn from here.\n\nClick to view the cards in your draw pile (shuffled).", "DRAW_PILE.title": "Draw Pile (A)", "END_TURN.description": "Pressing this button will end your turn.\n\nYou will discard your hand, enemies will take their turn, you will draw [blue]5[/blue] cards, then it will be your turn again.", "END_TURN.title": "End Turn (E)", "ENERGY.description": "Energy is used to play cards from your hand. ", "ENERGY.title": "Energy", "ENERGY_COUNT.description": "Your current Energy ({energyPrefix:energyIcons(1)}) count. Cards require {energyPrefix:energyIcons(1)} to play, indicated in the top left corner.", "ENERGY_COUNT.title": "Energy", "EVOKE.description": "Consume your rightmost Orb and use its Evoke effect.", "EVOKE.title": "Evoke", "EXHAUST_PILE.description": "Click to view cards Exhausted this combat.", "EXHAUST_PILE.title": "Exhausted Cards (X)", "FATAL.description": "Triggers whenever this card kills a non-minion enemy.", "FATAL.title": "Fatal", "FLOOR.description": "How far up you have climbed.", "FLOOR.title": "Floor", "FORGE.description": "The first time you [gold]Forge[/gold] each combat, add [gold]Sovereign Blade[/gold] to your hand. Adds additional damage to [gold]Sovereign Blade[/gold].", "FORGE.title": "Forge", "HIT_POINTS.description": "If you run out of HP, you die.", "HIT_POINTS.title": "Hit Points (HP)", "LINKED_REWARDS.description": "You can only select [blue]1[/blue] reward from this set.", "LINKED_REWARDS.title": "Linked Rewards", "MAP.description": "View this current Act's map.", "MAP.title": "Map (M)", "MONEY_POUCH.description": "How much Gold you have. Gold is the currency within the Spire.", "MONEY_POUCH.title": "Gold", "POTION_SLOT.description": "Use potions during combat to gain [green]bonuses[/green] or [red]hinder[/red] enemies.", "POTION_SLOT.title": "Potion Slot", "REPLAY.extraText": "[gold]Replay[/gold] {Times}.", "REPLAY_DYNAMIC.description": "Plays this card an additional {Times:plural:time|[blue]{}[/blue] times}.", "REPLAY_DYNAMIC.title": "Replay", "REPLAY_STATIC.description": "Plays this card an additional time.", "REPLAY_STATIC.title": "Replay", "ROOM_ANCIENT.description": "(PLACEHOLDER) Ancient", "ROOM_ANCIENT.title": "Ancient", "ROOM_BOSS.description": "The deadliest foe in the area.\n\nDefeating them will let you proceed to the next Act.", "ROOM_BOSS.title": "Boss", "ROOM_ELITE.description": "Powerful foes are in these rooms.\n\nDefeating them will reward you a [gold]Relic[/gold].", "ROOM_ELITE.title": "Elite", "ROOM_ENEMY.description": "Hostile enemies reside in these rooms.", "ROOM_ENEMY.title": "Enemy", "ROOM_EVENT.description": "Something unusual occurs in these rooms...", "ROOM_EVENT.title": "Event", "ROOM_MAP.description": "A place where you can plan your next move.", "ROOM_MAP.title": "Antechamber", "ROOM_MERCHANT.description": "The mysterious Merchant sells his wares in these rooms.\n\nSpend your well earned [gold]Gold[/gold] here!", "ROOM_MERCHANT.title": "Shop", "ROOM_REST.description": "Stop by these rooms to [green]Heal[/green] some HP or [gold]Upgrade[/gold] a card.", "ROOM_REST.title": "Rest Site", "ROOM_TREASURE.description": "[gold]Relics[/gold] and [gold]Gold[/gold] can be found in these coveted rooms.", "ROOM_TREASURE.title": "Treasure Room", "ROOM_UNKNOWN_ELITE.description": "A previously unknown room containing powerful foes.", "ROOM_UNKNOWN_ELITE.title": "Unknown - Elite", "ROOM_UNKNOWN_ENEMY.description": "A previously unknown room containing hostile enemies.", "ROOM_UNKNOWN_ENEMY.title": "Unknown - Enemy", "ROOM_UNKNOWN_EVENT.description": "A previously unknown room containing an event.\n\nSomething unusual occurs in these rooms...", "ROOM_UNKNOWN_EVENT.title": "Unknown - Event", "ROOM_UNKNOWN_MERCHANT.description": "A previously unknown room containing the mysterious merchant.\n\nSpent your well-earned [gold]Gold[/gold] here!", "ROOM_UNKNOWN_MERCHANT.title": "Unknown - Merchant", "ROOM_UNKNOWN_TREASURE.description": "A previously unknown room containing highly coveted treasure.", "ROOM_UNKNOWN_TREASURE.title": "Unknown - Treasure", "SETTINGS.description": "Opens the game menu.\n\nChange or update your graphics, audio, and gameplay preferences here.", "SETTINGS.title": "Settings (ESC)", "STAR_COUNT.description": "Your current Star ({singleStarIcon}) count. The Regent's cards may require {singleStarIcon} to play.", "STAR_COUNT.title": "Stars", "STUN.description": "Prevent the enemy from acting on its next turn.", "STUN.title": "<PERSON><PERSON>", "SUMMON_DYNAMIC.description": "Summon [gold]Osty[/gold] with [blue]{<PERSON><PERSON><PERSON>}[/blue] HP.\nIf already summoned, increase his Max HP by [blue]{<PERSON><PERSON><PERSON>}[/blue].", "SUMMON_DYNAMIC.title": "<PERSON><PERSON><PERSON> {<PERSON><PERSON><PERSON>}", "SUMMON_STATIC.description": "<PERSON><PERSON><PERSON> [gold]Osty[/gold].\nIf already summoned, increase his Max HP.", "SUMMON_STATIC.title": "<PERSON><PERSON><PERSON>", "TRANSFORM.description": "Transformed cards become a random card of any rarity.", "TRANSFORM.title": "Transform", "TURN_NUMBER.description": "Turn number for this combat"}