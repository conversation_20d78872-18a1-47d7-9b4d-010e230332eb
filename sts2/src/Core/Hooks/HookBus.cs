using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Models;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Hooks;

/// <summary>
/// A global class that bridges the gap between Commands and other classes to models.
/// Keeps a list of AbstractModels that have corresponding methods per each Hook,
/// and contain the logic on what happens when a hook is called.
/// </summary>
public class HookBus
{
    private static readonly DeterministicModelComparer _modelComparer = new();

    // Note: I tried to make this a SortedDictionary with a custom comparer. However, since the DeterministicModelComparer
    // uses Owner and Pile, which can change without the HookBus' knowledge, we can't keep the list sorted.
    // So instead, we sort the list every time we iterate.
    private readonly HashSet<AbstractModel> _models = [];
    private readonly List<AbstractModel> _orderedModels = [];

    private bool _modelsDirty;

    public static HookBus Instance { get; } = new();

    public void Subscribe(AbstractModel model)
    {
        ArgumentNullException.ThrowIfNull(model);
        _models.Add(model);
        model.ChangedSortingOrder += MarkDirty;
        MarkDirty();
    }

    public void Unsubscribe(AbstractModel model)
    {
        ArgumentNullException.ThrowIfNull(model);
        _models.Remove(model);
        model.ChangedSortingOrder -= MarkDirty;
        MarkDirty();
    }

    /// <summary>
    /// Notifies the HookBus that a model that is currently subscribed to it has changed sorting order.
    /// This must be called anytime a property used by DeterministicModelComparer is changed. Otherwise, state may
    /// diverge in multiplayer due to different iteration orders of models on different machines.
    /// </summary>
    public void MarkDirty()
    {
        _modelsDirty = true;
    }

    /// <summary>
    /// Copies the model list, sorts it deterministically, and iterates it, excluding models that are removed during the
    /// iteration.
    /// Executing hooks on models can cause them to be removed from the model list. Use this when you want to iterate a
    /// copy of the model list, but you still want to exclude models that get unsubscribed as part of hook execution.
    /// </summary>
    private IEnumerable<AbstractModel> IterateModels(bool combatModelsOnly = false)
    {
        if (_modelsDirty)
        {
            _orderedModels.Clear();
            _orderedModels.AddRange(_models);
            _orderedModels.Sort(_modelComparer);
            _modelsDirty = false;
        }

        foreach (AbstractModel model in _orderedModels.ToList())
        {
            if ((!combatModelsOnly || model.ShouldReceiveCombatHooks) && _models.Contains(model))
            {
                yield return model;
            }
        }
    }

    /// <summary>
    /// Like IterateModels, but only returns models that should have combat-only hooks (like AfterCardPlayed) run on them.
    /// See <see cref="AbstractModel.ShouldReceiveCombatHooks"/> for more details.
    /// </summary>
    private IEnumerable<AbstractModel> IterateCombatModels()
    {
        return IterateModels(true);
    }

    public void UnsubscribeAll()
    {
        _models.Clear();
    }

    /// <summary>
    /// Updates the dynamic variables of a card based on hooks (i.e damage, block, and powers).
    /// This is so powers and relic modifications to these values can be reflected in card descriptions.
    /// </summary>
    /// <param name="card">Card who we are updating the preview for</param>
    /// <param name="target">creature who this card is attacking, if it exists</param>
    /// <param name="dynamicVarSet">the dynamic variables for this card</param>
    public void UpdateDynamicVarPreview(CardModel card, Creature? target, DynamicVarSet dynamicVarSet)
    {
        // Skip dynamic var previews if the card exists outside of a climb (like in the Compendium or a HoverTip).
        if (card.ClimbState == null) return;

        foreach (DynamicVar dynamicVar in dynamicVarSet.Values)
        {
            if (dynamicVar is DamageVar damageVar)
            {
                damageVar.PreviewValue = ModifyDamageAmount(
                    target,
                    card.Owner.Creature,
                    damageVar.PreviewValue,
                    damageVar.Props,
                    card,
                    true,
                    out _
                );
            }
            else if (dynamicVar is OstyDamageVar ostyDamageVar)
            {
                ostyDamageVar.PreviewValue = ModifyDamageAmount(
                    target,
                    card.Owner.Osty,
                    ostyDamageVar.PreviewValue,
                    ostyDamageVar.Props,
                    card,
                    true,
                    out _
                );
            }
            else if (dynamicVar is BlockVar blockVar)
            {
                blockVar.PreviewValue = ModifyBlockReceived(
                    card.Owner.Creature,
                    blockVar.PreviewValue,
                    blockVar.Props,
                    card,
                    true,
                    out _
                );
            }
            else if (dynamicVar is SummonVar summonVar)
            {
                summonVar.PreviewValue = ModifySummonAmount(card.Owner, summonVar.PreviewValue, card);
            }
            else if (dynamicVar.GetType().IsGenericType && dynamicVar.GetType().GetGenericTypeDefinition() == typeof(PowerVar<>))
            {
                ModelId id = ModelDb.GetId(dynamicVar.GetType().GetGenericArguments()[0]);
                PowerModel power = ModelDb.GetById<PowerModel>(id);
                dynamicVar.PreviewValue = ModifyPowerAmountGiven(
                    power,
                    card.Owner.Creature,
                    dynamicVar.PreviewValue,
                    target,
                    card,
                    out _
                );
            }
        }
    }

    public void UpdateDynamicVarPreview(OrbModel orb, DynamicVarSet vars)
    {
        foreach (OrbVar orbVar in vars.Values.OfType<OrbVar>())
        {
            orbVar.PreviewValue = ModifyOrbValue(orb.Owner, orbVar.PreviewValue);
        }
    }

    public List<LocString>? AppendRestSiteOptionText()
    {
        List<LocString>? additionalText = null;

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryAppendAdditionalRestSiteHealOptionText(out LocString? locString))
            {
                additionalText ??= [];
                additionalText.Add(locString!);
            }
        }

        return additionalText;
    }

    #region Before/After Hooks

    public async Task AfterActEntered()
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterActEntered();
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeAttack(AttackCommand command)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.BeforeAttack(command);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterAttack(AttackCommand command)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterAttack(command);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterBlockCleared(Creature creature)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterBlockCleared(creature);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeBlockGained(Creature creature, decimal amount, ValueProp props, CardModel? cardSource)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.BeforeBlockGained(creature, amount, props, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterBlockGained(Creature creature, decimal amount, ValueProp props, CardModel? cardSource)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterBlockGained(creature, amount, props, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardChangedPiles(CardModel card, CardPileTarget oldPile, AbstractModel? source)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCardChangedPiles(card, oldPile, source);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCardChangedPilesLate(card, oldPile, source);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardDiscarded(PlayerChoiceContext choiceContext, CardModel card)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterCardDiscarded(choiceContext, card);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task AfterCardDrawn(PlayerChoiceContext choiceContext, CardModel card, bool fromHandDraw)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterCardDrawn(choiceContext, card, fromHandDraw);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task AfterCardEnteredCombat(CardModel card)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterCardEnteredCombat(card);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardGeneratedForCombat(CardModel card, bool addedByPlayer)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterCardGeneratedForCombat(card, addedByPlayer);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardExhausted(PlayerChoiceContext choiceContext, CardModel card, bool causedByEthereal)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterCardExhausted(choiceContext, card, causedByEthereal);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task BeforeCardRemoved(CardModel card)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeCardRemoved(card);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeCardAutoPlayed(CardModel card, Creature? target, AutoPlayType type)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.BeforeCardAutoPlayed(card, target, type);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeCardPlayed(CardModel card, Creature? target, int playCount)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.BeforeCardPlayed(card, target, playCount);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardPlayed(PlayerChoiceContext choiceContext, CardModel card, Creature? target, int playCount)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            // Unlike most cards/powers that listen in on this relics exist outside of the traditional combat flow,
            // so most of the time we still want them to ring (i.e. pen nib. ink bottle)
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            choiceContext.PushModel(model);
            await model.AfterCardPlayed(choiceContext, card, target, playCount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }

        foreach (AbstractModel model in IterateCombatModels())
        {
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            choiceContext.PushModel(model);
            await model.AfterCardPlayedLate(choiceContext, card, target, playCount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task BeforePotionUsed(PotionModel potion, Creature? target)
    {
        foreach (AbstractModel model in IterateModels())
        {
            // Unlike most cards/powers that listen in on this, relics exist outside of the traditional combat flow,
            // so most of the time we still want them to ring (i.e. Pen Nib, Ink Bottle).
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            await model.BeforePotionUsed(potion, target);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterPotionUsed(PotionModel potion, Creature? target)
    {
        foreach (AbstractModel model in IterateModels())
        {
            // Unlike most cards/powers that listen in on this, relics exist outside of the traditional combat flow,
            // so most of the time we still want them to ring (i.e. Pen Nib, Ink Bottle).
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            await model.AfterPotionUsed(potion, target);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterPotionProcured(PotionModel potion)
    {
        foreach (AbstractModel model in IterateModels())
        {
            // Unlike most cards/powers that listen in on this relics exist outside of the traditional combat flow,
            // so most of the time we still want them to ring (i.e. pen nib. ink bottle)
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            await model.AfterPotionProcured(potion);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterPotionDiscarded(PotionModel potion)
    {
        foreach (AbstractModel model in IterateModels())
        {
            // Unlike most cards/powers that listen in on this relics exist outside of the traditional combat flow,
            // so most of the time we still want them to ring (i.e. pen nib. ink bottle)
            if (!CombatManager.Instance.IsInProgress && model is not RelicModel) continue;

            await model.AfterPotionDiscarded(potion);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeCombatStart()
    {
        // Note: This may sound like a combat-only hook, but combat start is relevant to non-combat models (like a deck
        // card that transforms when you start your third combat).
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeCombatStart();
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCombatEnd(CombatRoom room)
    {
        // Note: This may sound like a combat-only hook, but combat end is relevant to non-combat models (like Guilty).
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCombatEnd(room);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCombatVictory(CombatRoom room)
    {
        // Note: This may sound like a combat-only hook, but combat victory is relevant to non-combat models (like a
        // deck card that transforms after 3 combat victories).
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCombatVictoryEarly(room);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCombatVictory(room);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterBlockBroken(Creature creature)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterBlockBroken(creature);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCreatureAddedToCombat(Creature creature)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterCreatureAddedToCombat(creature);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCurrentHpChanged(Creature creature, decimal delta)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCurrentHpChanged(creature, delta);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterDamageGiven(Creature? dealer, DamageResult results, ValueProp props, Creature target, CardModel? cardSource)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterDamageGiven(dealer, results, props, target, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeDamageReceived(target, amount, props, dealer, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterDamageReceived(target, result, props, dealer, cardSource);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeDeath(Creature creature)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeDeath(creature);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterDeath(Creature creature, float deathAnimLength)
    {
        foreach (AbstractModel model in IterateModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, creature.CombatState!);
            Task task = model.AfterDeath(playerChoiceContext, creature, deathAnimLength);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
        }
    }

    public async Task AfterDiedToDoom(Creature creature, CombatState combatState)
    {
        foreach (AbstractModel model in IterateModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterDiedToDoom(playerChoiceContext, creature);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
        }
    }

    public async Task AfterEnergyReset(Player player)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterEnergyReset(player);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeFlush(Player player)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, player.Creature.CombatState!);
            Task task = model.BeforeFlush(playerChoiceContext, player);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, player.Creature.CombatState!);
            Task task = model.BeforeFlushLate(playerChoiceContext, player);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                // Wait for the GameAction to complete, not just the underlying task. Otherwise, there is a chance that
                // checksums will be received out of order.
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        // Note that if late hooks ever depend on the player choices of non-late hooks, then this should probably
        // also happen between the non-late and late executions
        await Task.WhenAll(tasksToAwait);
    }

    // This takes a player choice context as an argument instead of creating one like other hooks because it needs to
    // block the entire player turn start if a player choice is encountered.
    public async Task BeforeHandDraw(Player player, HookPlayerChoiceContext playerChoiceContext, CombatState combatState)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            playerChoiceContext.PushModel(model);
            await model.BeforeHandDraw(player, playerChoiceContext, combatState);
            playerChoiceContext.PopModel(model);
        }
    }

    public async Task AfterGainedGold(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterGainedGold(player);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterHandEmptied(PlayerChoiceContext choiceContext, Player player)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterHandEmptied(choiceContext, player);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task AfterModifyingBlockAmount(decimal modifiedBlock, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingBlockAmount(modifiedBlock);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingCardPlayCount(CardModel card, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingCardPlayCount(card);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingOrbPassiveTriggerCount(OrbModel orb, IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingOrbPassiveTriggerCount(orb);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyCardRewardOptions(IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyCardRewardOptions();
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingDamageAmount(IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingDamageAmount();
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingHandDraw(IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingHandDraw();
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterPreventingDraw(AbstractModel modifier)
    {
        await modifier.AfterPreventingDraw();
        modifier.InvokeExecutionFinished();
    }

    public async Task AfterModifyingHpLost(IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingHpLost();
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingPowerAmountGiven(IEnumerable<AbstractModel> modifiers, PowerModel modifiedPower)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingPowerAmountGiven(modifiedPower);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingPowerAmountReceived(IEnumerable<AbstractModel> modifiers, PowerModel modifiedPower)
    {
        foreach (AbstractModel modifier in IterateCombatModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingPowerAmountReceived(modifiedPower);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterModifyingRewards(IEnumerable<AbstractModel> modifiers)
    {
        foreach (AbstractModel modifier in IterateModels().Intersect(modifiers).ToList())
        {
            await modifier.AfterModifyingRewards();
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterOrbChanneled(PlayerChoiceContext choiceContext, Player player, OrbModel orb)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterOrbChanneled(choiceContext, player, orb);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterOrbEvoked(OrbModel orb, IEnumerable<Creature> targets)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterOrbEvoked(orb, targets);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterOstyAttacked(Creature osty)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterOstyAttacked(osty);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforePowerAmountChanged(PowerModel power, decimal amount, Creature target, Creature? applier, CardModel? cardSource)
    {
        foreach (AbstractModel modifier in IterateCombatModels().ToList())
        {
            await modifier.BeforePowerAmountChanged(power, amount, target, applier, cardSource);
            modifier.InvokeExecutionFinished();
        }
    }

    public async Task AfterPowerAmountChanged(PowerModel power, decimal amount, Creature? applier)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterPowerAmountChanged(power, amount, applier);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterPreventingBlockClear(AbstractModel preventer, Creature creature)
    {
        await preventer.AfterPreventingBlockClear(preventer, creature);
        preventer.InvokeExecutionFinished();
    }

    public async Task AfterPreventingDeath(AbstractModel preventer, Creature creature)
    {
        await preventer.AfterPreventingDeath(creature);
        preventer.InvokeExecutionFinished();
    }

    public async Task BeforeRoomEntered(AbstractRoom room)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeRoomEntered(room);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterRoomEntered(AbstractRoom room)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterRoomEntered(room);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterMapGenerated(ActMap map, int actIndex)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterMapGenerated(map, actIndex);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterShuffle(PlayerChoiceContext choiceContext, Player shuffler)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterShuffle(choiceContext, shuffler);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task AfterStarsSpent(int amount, Player spender)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterStarsSpent(amount, spender);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterStarsGained(int amount, Player gainer)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterStarsGained(amount, gainer);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterForged(decimal amount, Player forger)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterForged(amount, forger);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterSummoning(PlayerChoiceContext choiceContext, Player summoner, decimal amount)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            choiceContext.PushModel(model);
            await model.AfterSummoning(choiceContext, summoner, amount);
            model.InvokeExecutionFinished();
            choiceContext.PopModel(model);
        }
    }

    public async Task AfterTakingExtraTurn(Player player)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            await model.AfterTakingExtraTurn(player);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterTurnStart(CombatSide side, CombatState combatState)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterTurnStartEarly(playerChoiceContext, side);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
        }

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterTurnStart(playerChoiceContext, side);
            await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
        }
    }

    public async Task AfterOstyRevived(Creature osty)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterOstyRevived(osty);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeTurnEnd(CombatSide side, CombatState combatState)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEndVeryEarly(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEndEarly(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.BeforeTurnEnd(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        // Note that if late hooks ever depend on the player choices of non-late hooks, then this should probably
        // also happen between the non-late and late executions
        await Task.WhenAll(tasksToAwait);
    }

    public async Task BeforeTurnStart(CombatSide side, CombatState combatState)
    {
        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            await model.BeforeTurnStart(playerChoiceContext, side);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterTurnEnd(CombatSide side, CombatState combatState)
    {
        // Unlike other hooks, where we can run player choice during the turn while other things are executing, callers
        // of this hook expect the entirety of all hooks to be complete before this returns and end of turn occurs
        List<Task> tasksToAwait = [];

        foreach (AbstractModel model in IterateCombatModels())
        {
            HookPlayerChoiceContext playerChoiceContext = new(model, LocalContext.NetId!.Value, combatState);
            Task task = model.AfterTurnEnd(playerChoiceContext, side);
            bool ranToCompletion = await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

            if (!ranToCompletion)
            {
                tasksToAwait.Add(playerChoiceContext.GameAction!.CompletionTask);
            }
        }

        await Task.WhenAll(tasksToAwait);
    }

    public async Task AfterPlayerRested(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterPlayerRested(player);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterPlayerSmithed(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterPlayerSmithed(player);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterItemPurchased(Player player, MerchantEntry itemPurchased, int cost)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterItemPurchased(player, itemPurchased, cost);
            model.InvokeExecutionFinished();
        }
    }

    public async Task BeforeOfferingRewards(Player player, IReadOnlyList<Reward> rewards)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.BeforeOfferingRewards(player, rewards);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterRewardTaken(Player player, Reward reward)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterRewardTaken(player, reward);
            model.InvokeExecutionFinished();
        }
    }

    public async Task AfterCardRetained(CardModel card)
    {
        foreach (AbstractModel model in IterateModels())
        {
            await model.AfterCardRetained(card);
            model.InvokeExecutionFinished();
        }
    }

    #endregion

    #region Modify Hooks

    public decimal ModifyBlockReceived(Creature target, decimal block, ValueProp props, CardModel? cardSource, bool isPreview, out IEnumerable<AbstractModel> modifiers)
    {
        List<AbstractModel> modifyingModels = [];
        bool isCardDescription = cardSource != null && isPreview;

        // if this card is the upgrade preview for another card
        bool isCombatCardUpgradePreview = cardSource is { UpgradePreviewType: CardUpgradePreviewType.Combat, Pile: null };

        List<AbstractModel> validModels = IterateModels().ToList();

        if (isCardDescription && cardSource?.Pile?.Type is not (CardPileTarget.Hand or CardPileTarget.Play) && !isCombatCardUpgradePreview)
        {
            // If we are modifying block for a card preview and the card is not in the player's hand or play area,
            // skip models that shouldn't preview outside of combat
            validModels.RemoveAll(l => !l.PreviewOutsideOfCombat);
        }

        decimal finalBlock = block;

        foreach (AbstractModel model in validModels)
        {
            bool modified = model.TryModifyBlockReceived(target, finalBlock, props, cardSource, out finalBlock);

            if (modified)
            {
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            bool modified = model.TryModifyBlockReceivedLate(target, finalBlock, props, cardSource, out finalBlock);

            if (modified)
            {
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;

        return Math.Max(0, finalBlock);
    }

    public CardModel ModifyCardBeingAddedToDeck(CardModel card, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];

        foreach (AbstractModel model in IterateModels())
        {
            bool modified = model.TryModifyCardBeingAddedToDeck(card, out CardModel? newCard);

            if (modified && newCard != null)
            {
                modifyingModels.Add(model);
                card = newCard;
            }

            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in IterateModels())
        {
            bool modified = model.TryModifyCardBeingAddedToDeckLate(card, out CardModel? newCard);

            if (modified && newCard != null)
            {
                modifyingModels.Add(model);
                card = newCard;
            }

            model.InvokeExecutionFinished();
        }

        return card;
    }

    public int ModifyCardPlayCount(CardModel card, int playCount, Creature? target, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];
        int finalPlayCount = playCount;

        foreach (AbstractModel model in IterateModels())
        {
            int prevPlayCount = finalPlayCount;
            finalPlayCount = model.ModifyCardPlayCount(card, target, finalPlayCount);

            if (finalPlayCount != prevPlayCount)
            {
                modifyingModels.Add(model);
            }
        }

        return finalPlayCount;
    }

    public int ModifyOrbPassiveTriggerCounts(OrbModel orb, int triggerCount, out List<AbstractModel> modifyingModels)
    {
        modifyingModels = [];
        int finalTriggerCount = triggerCount;

        foreach (AbstractModel model in IterateModels())
        {
            int prevTriggerCount = finalTriggerCount;
            finalTriggerCount = model.ModifyOrbPassiveTriggerCounts(orb, finalTriggerCount);

            if (finalTriggerCount != prevTriggerCount)
            {
                modifyingModels.Add(model);
            }
        }

        return finalTriggerCount;
    }

    public decimal ModifyCardRewardUpgradeOdds(Player player, CardModel card, decimal originalOdds)
    {
        decimal finalOdds = originalOdds;

        foreach (AbstractModel model in IterateModels())
        {
            finalOdds = model.ModifyCardRewardUpgradeOdds(player, card, finalOdds);
        }

        return finalOdds;
    }

    public decimal ModifyPotionRewardOdds(Player player, RoomType roomType, decimal originalOdds)
    {
        decimal finalOdds = originalOdds;

        foreach (AbstractModel model in IterateModels())
        {
            finalOdds = model.ModifyPotionRewardOdds(player, roomType, finalOdds);
        }

        return finalOdds;
    }

    public IEnumerable<AbstractModel> ModifyCardRewardAlternatives(Player player, CardReward cardReward, List<CardRewardAlternative> alternatives)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryModifyCardRewardAlternatives(player, cardReward, alternatives))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    public ActMap ModifyGeneratedMap(ClimbState climbState, ActMap map, int actIndex)
    {
        foreach (AbstractModel model in IterateModels())
        {
            map = model.ModifyGeneratedMap(climbState, map, actIndex);
            model.InvokeExecutionFinished();
        }

        foreach (AbstractModel model in IterateModels())
        {
            map = model.ModifyGeneratedMapLate(climbState, map, actIndex);
            model.InvokeExecutionFinished();
        }

        return map;
    }

    public CardRarity ModifyMerchantCardRarity(Player player, CardRarity rarity)
    {
        foreach (AbstractModel model in IterateModels())
        {
            rarity = model.ModifyMerchantCardRarity(player, rarity);
        }

        return rarity;
    }

    public void ModifyMerchantCardCreationResults(Player player, List<CardCreationResult> cards)
    {
        foreach (AbstractModel model in IterateModels())
        {
            model.ModifyMerchantCardCreationResults(player, cards);
        }
    }

    public decimal ModifyMerchantPrice(Player player, MerchantEntry entry, decimal result)
    {
        foreach (AbstractModel model in IterateModels())
        {
            result = model.ModifyMerchantPrice(player, entry, result);
        }

        return result;
    }

    public decimal ModifyDamageAmount(Creature? receiver, Creature? dealer, decimal damage, ValueProp props, CardModel? cardSource, bool isPreview, out IEnumerable<AbstractModel> modifiers)
    {
        List<AbstractModel> modifyingModels = [];

        decimal modifiedDamage = damage;

        modifiedDamage = ModifyDamageGiven(dealer, modifiedDamage, props, receiver, cardSource, modifyingModels, isPreview);

        if (receiver == null)
        {
            // If we're doing a damage preview with an AOE/randomly-targeted card, run the ModifyDamageReceived hooks
            // for powers that every enemy has.
            // For example, if all enemies have Vulnerable, Breakthrough will preview it.
            bool shouldPreviewSharedPowers =
                isPreview &&
                // Note: We have to separate the owner check from the other checks so it short-circuits properly.
                cardSource is { Owner: not null } and
                {
                    TargetEnemy: UiTargetEnemy.All or UiTargetEnemy.Random,
                    Pile.Type: CardPileTarget.Hand or CardPileTarget.Play
                };

            if (shouldPreviewSharedPowers)
            {
                Creature owner = cardSource!.Owner!.Creature;
                IEnumerable<Creature> enemies = owner.CombatState?.GetOpponentsOf(owner).Where(c => c.IsHittable) ?? [];
                Creature? firstEnemy = enemies.FirstOrDefault();
                List<PowerModel> sharedPowers = [];

                if (firstEnemy != null)
                {
                    // Build up a list of powers that every enemy has.
                    foreach (PowerModel power in firstEnemy.Powers)
                    {
                        // Only preview powers that explicitly should be previewed when every enemy has them.
                        if (!power.ShouldPreviewWhenSharedByAllEnemies) continue;

                        // Only preview powers that every enemy has.
                        if (!enemies.All(e => e.HasPower(power.Id))) continue;

                        sharedPowers.Add(power);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceivedEarly(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceived(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }

                    foreach (PowerModel power in sharedPowers)
                    {
                        modifiedDamage = power.ModifyDamageReceivedLate(firstEnemy, modifiedDamage, props, dealer, cardSource);
                    }
                }
            }
        }
        else
        {
            modifiedDamage = ModifyDamageReceived(receiver, modifiedDamage, props, dealer, cardSource, modifyingModels, isPreview);
        }

        modifiers = modifyingModels;

        return Math.Max(0, modifiedDamage);
    }

    private decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource, ICollection<AbstractModel> modifiers, bool isPreview)
    {
        bool isCardDescription = cardSource != null && isPreview;

        // if this card is the upgrade preview for another card
        bool isCombatCardUpgradePreview = cardSource is { UpgradePreviewType: CardUpgradePreviewType.Combat, Pile: null };
        List<AbstractModel> validModels = IterateModels().ToList();

        if (isCardDescription && cardSource?.Pile?.Type is not (CardPileTarget.Hand or CardPileTarget.Play) && !isCombatCardUpgradePreview)
        {
            // If we are modifying damage for a card preview and the card is not in the player's hand or play area,
            // skip models that shouldn't preview outside of combat
            validModels.RemoveAll(l => !l.PreviewOutsideOfCombat);
        }

        decimal finalAmount = amount;

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenEarly(dealer, finalAmount, props, target, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGiven(dealer, finalAmount, props, target, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenLate(dealer, finalAmount, props, target, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageGivenVeryLate(dealer, finalAmount, props, target, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        return finalAmount;
    }

    private decimal ModifyDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource, ICollection<AbstractModel> modifiers, bool isPreview)
    {
        bool isCardPreview = cardSource != null && isPreview;
        List<AbstractModel> validModels = IterateModels().ToList();

        if (isCardPreview && cardSource?.Pile?.Type is not (CardPileTarget.Hand or CardPileTarget.Play))
        {
            // If we are modifying damage for a card preview and the card is not in the player's hand or play area,
            // skip models that shouldn't preview outside of combat
            validModels.RemoveAll(l => !l.PreviewOutsideOfCombat);
        }

        decimal finalAmount = amount;

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceivedEarly(target, finalAmount, props, dealer, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceived(target, finalAmount, props, dealer, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in validModels)
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyDamageReceivedLate(target, finalAmount, props, dealer, cardSource);

            if (prevAmount != finalAmount)
            {
                modifiers.Add(model);
            }
        }

        return finalAmount;
    }

    public decimal ModifyEnergyCostInCombat(CardModel card, decimal originalCost)
    {
        // In combat, all models are listening.
        return ModifyEnergyCost(card, originalCost, IterateModels());
    }

    public decimal ModifyEnergyCostOutOfCombat(CardModel card, decimal originalCost)
    {
        // Since the card is in a non-combat pile (Deck, etc.), don't run Power hooks on it, as Powers are combat-only.
        return ModifyEnergyCost(card, originalCost, IterateModels().Where(m => m is not PowerModel));
    }

    private static decimal ModifyEnergyCost(CardModel card, decimal originalCost, IEnumerable<AbstractModel> listeningModels)
    {
        // If the cost is < 0, it means it probably was already unplayable.
        if (originalCost < 0) return originalCost;

        decimal finalCost = originalCost;

        foreach (AbstractModel model in listeningModels)
        {
            model.TryModifyEnergyCost(card, finalCost, out finalCost);
        }

        return finalCost;
    }

    public decimal ModifyStarCost(CardModel card, decimal originalCost)
    {
        // If the cost is < 0, it means it probably was already unplayable.
        if (originalCost < 0) return originalCost;

        decimal finalCost = originalCost;

        foreach (AbstractModel model in IterateModels())
        {
            model.TryModifyStarCost(card, finalCost, out finalCost);
        }

        return finalCost;
    }

    public decimal ModifyHandDraw(Player player, decimal originalCardCount, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalCardCount = originalCardCount;
        List<AbstractModel>? modifyingModels = null;

        foreach (AbstractModel model in IterateModels())
        {
            decimal prevCount = finalCardCount;
            finalCardCount = model.ModifyHandDraw(player, finalCardCount);

            if (prevCount != finalCardCount)
            {
                modifyingModels ??= [];
                modifyingModels.Add(model);
            }
        }

        foreach (AbstractModel model in IterateModels())
        {
            decimal prevCount = finalCardCount;
            finalCardCount = model.ModifyHandDrawLate(player, finalCardCount);

            if (prevCount != finalCardCount)
            {
                modifyingModels ??= [];
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels ?? Enumerable.Empty<AbstractModel>();
        return finalCardCount;
    }

    public decimal ModifyHpLost(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel>? modifyingModels = null;

        foreach (AbstractModel model in IterateModels())
        {
            decimal prevAmount = finalAmount;
            finalAmount = model.ModifyHpLost(target, finalAmount, props, dealer, cardSource);

            if (prevAmount != finalAmount)
            {
                modifyingModels ??= [];
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels ?? Enumerable.Empty<AbstractModel>();
        return finalAmount;
    }

    public decimal ModifyMaxEnergy(Player player, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifyMaxEnergy(player, finalAmount);
        }

        return finalAmount;
    }

    public decimal ModifyPowerAmountGiven(PowerModel power, Creature giver, decimal amount, Creature? target, CardModel? cardSource, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel> modifyingModels = [];

        foreach (AbstractModel model in IterateModels())
        {
            decimal newAmount = model.ModifyPowerAmountGiven(power, giver, finalAmount, target, cardSource);

            if (newAmount != finalAmount)
            {
                finalAmount = newAmount;
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels;
        return finalAmount;
    }

    public decimal ModifyPowerAmountReceived(PowerModel canonicalPower, Creature target, decimal amount, Creature? giver, out IEnumerable<AbstractModel> modifiers)
    {
        decimal finalAmount = amount;
        List<AbstractModel>? modifyingModels = null;

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryModifyPowerAmountReceived(canonicalPower, target, finalAmount, giver, out decimal modifiedAmount))
            {
                finalAmount = modifiedAmount;
                modifyingModels ??= [];
                modifyingModels.Add(model);
            }
        }

        modifiers = modifyingModels ?? [];
        return finalAmount;
    }

    public decimal ModifySummonAmount(Player summoner, decimal amount, AbstractModel? source)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifySummonAmount(summoner, finalAmount, source);
        }

        return finalAmount;
    }

    public Creature ModifyUnblockedDamageTarget(Creature originalTarget, decimal amount, ValueProp props, Creature? dealer)
    {
        Creature finalTarget = originalTarget;

        foreach (AbstractModel model in IterateModels())
        {
            finalTarget = model.ModifyUnblockedDamageTarget(finalTarget, amount, props, dealer);
        }

        return finalTarget;
    }

    public EventModel ModifyNextEvent(EventModel currentEvent)
    {
        EventModel modifiedEvent = currentEvent;
        foreach (AbstractModel model in IterateModels())
        {
            modifiedEvent = model.ModifyNextEvent(modifiedEvent);
        }

        return modifiedEvent;
    }

    public IReadOnlySet<RoomType> ModifyUnknownMapPointRoomTypes(IReadOnlySet<RoomType> roomTypes)
    {
        IReadOnlySet<RoomType> modifiedRoomTypes = new HashSet<RoomType>(roomTypes);

        foreach (AbstractModel model in IterateModels())
        {
            modifiedRoomTypes = model.ModifyUnknownMapPointRoomTypes(modifiedRoomTypes);
        }

        return modifiedRoomTypes;
    }

    public float ModifyOddsIncreaseForUnrolledRoomType(RoomType roomType, float oddsIncrease)
    {
        foreach (AbstractModel model in IterateModels())
        {
            oddsIncrease = model.ModifyOddsIncreaseForUnrolledRoomType(roomType, oddsIncrease);
        }

        return oddsIncrease;
    }

    public IEnumerable<AbstractModel> ModifyRestSiteOptions(Player player, ICollection<RestSiteOption> options)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryModifyRestSiteOptions(player, options))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    public decimal ModifyHealAmount(Creature creature, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifyHealAmount(creature, finalAmount);
        }

        return finalAmount;
    }

    public decimal ModifyRestSiteHealAmount(Creature creature, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifyRestSiteHealAmount(creature, finalAmount);
        }

        return ModifyHealAmount(creature, finalAmount);
    }

    public IEnumerable<AbstractModel> ModifyRewards(Player player, List<Reward> rewards, AbstractRoom room)
    {
        List<AbstractModel> modifiers = [];

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryModifyRewards(player, rewards, room))
            {
                modifiers.Add(model);
            }
        }

        foreach (AbstractModel model in IterateModels())
        {
            if (model.TryModifyRewardsLate(player, rewards, room))
            {
                modifiers.Add(model);
            }
        }

        return modifiers;
    }

    public void TryModifyDrawShuffleOrder(Player player, List<CardModel> cards)
    {
        foreach (AbstractModel model in IterateModels())
        {
            model.TryModifyDrawShuffleOrder(player, cards);
        }
    }

    public bool TryModifyCardRewardOptions(Player player, List<CardCreationResult> options, CardCreationSource source, out List<AbstractModel> modifiers)
    {
        bool modified = false;
        modifiers = [];

        foreach (AbstractModel model in IterateModels())
        {
            bool didModify = model.TryModifyCardRewardOptions(player, options, source);
            modified = modified || didModify;
            modifiers.Add(model);
        }

        foreach (AbstractModel model in IterateModels())
        {
            bool didModify = model.TryModifyCardRewardOptionsLate(player, options, source);
            modified = modified || didModify;
            modifiers.Add(model);
        }

        return modified;
    }

    public decimal ModifyOrbValue(Player player, decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifyOrbValue(player, finalAmount);
        }

        return finalAmount;
    }

    public decimal ModifyOstyAttackCount(decimal amount)
    {
        decimal finalAmount = amount;

        foreach (AbstractModel model in IterateModels())
        {
            finalAmount = model.ModifyOstyAttackCount(finalAmount);
        }

        return finalAmount;
    }

    #endregion

    #region Validation Hooks

    public bool ShouldAddToDeck(CardModel card, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAddToDeck(card))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    public bool ShouldAfflict(CardModel card, AfflictionModel affliction)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAfflict(card, affliction))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldAllowAncient(Player player, AncientEventModel ancient)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAllowAncient(player, ancient))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldAllowHitting(Creature creature)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAllowHitting(creature))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldAllowTargeting(Creature target, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAllowTargeting(target))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    public bool ShouldAllowSelectingMoreCardRewards(Player player, CardReward reward)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (model.ShouldAllowSelectingMoreRewards(player, reward))
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// If a creature's block is about to be cleared, should it still be cleared?
    /// </summary>
    /// <param name="creature">Creature whose block is about to be cleared.</param>
    /// <param name="preventer">Model that prevented the block clear, if any.</param>
    /// <returns>Whether the creature's block should still be cleared.</returns>
    public bool ShouldClearBlock(Creature creature, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldClearBlock(creature))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    /// <summary>
    /// If a creature is about to die, should they still?
    /// </summary>
    /// <param name="creature">Creature who is about to die.</param>
    /// <param name="preventer">Model that prevented the death, if any.</param>
    /// <returns>Whether or not the creature should still die.</returns>
    public bool ShouldDie(Creature creature, out AbstractModel? preventer)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldDie(creature))
            {
                preventer = model;
                return false;
            }
        }

        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldDieLate(creature))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    public bool ShouldDisableRemainingRestSiteOptions(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldDisableRemainingRestSiteOptions(player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldDraw(Player player, bool fromHandDraw, out AbstractModel? modifier)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldDraw(player, fromHandDraw))
            {
                modifier = model;
                return false;
            }
        }

        modifier = null;
        return true;
    }

    public bool ShouldEtherealTrigger(CardModel card)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldEtherealTrigger(card))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldFlush(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldFlush(player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldGainGold(decimal amount, Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldGainGold(amount, player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldGainStars(decimal amount, Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldGainStars(amount, player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldPayExcessEnergyCostWithStars(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (model.ShouldPayExcessEnergyCostWithStars(player))
            {
                return true;
            }
        }

        return false;
    }

    public bool ShouldPlay(CardModel card, out AbstractModel? preventer, AutoPlayType autoPlayType)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldPlay(card, autoPlayType))
            {
                preventer = model;
                return false;
            }
        }

        preventer = null;
        return true;
    }

    public bool ShouldPlayerResetEnergy(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldPlayerResetEnergy(player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldProceedToNextMapPoint()
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldProceedToNextMapPoint())
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldRefillMerchantEntry(MerchantEntry entry, Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (model.ShouldRefillMerchantEntry(entry, player))
            {
                return true;
            }
        }

        return false;
    }

    public bool ShouldAllowMerchantCardRemoval(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldAllowMerchantCardRemoval(player))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldCreatureBeRemovedFromCombatAfterDeath(Creature creature)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.ShouldCreatureBeRemovedFromCombatAfterDeath(creature))
            {
                return false;
            }
        }

        return true;
    }

    public bool ShouldStopCombatFromEnding()
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (model.ShouldStopCombatFromEnding())
            {
                return true;
            }
        }

        return false;
    }

    public bool ShouldTakeExtraTurn(Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (model.ShouldTakeExtraTurn(player))
            {
                return true;
            }
        }

        return false;
    }

    public bool CanUpgrade(CardModel card)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.CanUpgrade(card))
            {
                return false;
            }
        }

        return true;
    }

    public bool CanProcurePotions(PotionModel potion, Player player)
    {
        foreach (AbstractModel model in IterateModels())
        {
            if (!model.CanProcurePotions(potion, player))
            {
                return false;
            }
        }

        return true;
    }

    public IEnumerable<CardModel> ModifyMerchantCardPool(Player player, IEnumerable<CardModel> options)
    {
        foreach (AbstractModel model in IterateModels())
        {
            options = model.ModifyMerchantCardPool(player, options);
        }

        return options;
    }


    public IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source)
    {
        foreach (AbstractModel model in IterateModels())
        {
            options = model.ModifyCardRewardCardPool(player, options, source);
        }

        foreach (AbstractModel model in IterateModels())
        {
            options = model.ModifyCardRewardCardPoolLate(player, options, source);
        }

        return options;
    }

    #endregion
}
