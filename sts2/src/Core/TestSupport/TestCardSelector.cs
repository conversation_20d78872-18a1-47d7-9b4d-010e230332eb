using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.TestSupport;

public class TestCardSelector
{
    public delegate CardModel? CardRewardSelectionDelegate(CardReward reward, IReadOnlyList<CardRewardAlternative> alternatives);

    private IEnumerable<CardModel>? _cardsToSelect;
    private IEnumerable<int>? _indicesToSelect;
    private CardRewardSelectionDelegate? _cardRewardSelectionDelegate;

    public static TestCardSelector? Instance { get; private set; }

    private TestCardSelector() { }

    public static void InitForTests()
    {
        Instance = new TestCardSelector();
    }

    public void Cleanup()
    {
        _cardsToSelect = null;
        _indicesToSelect = null;
    }

    public void PrepareToSelect(IEnumerable<CardModel> cards)
    {
        _cardsToSelect = cards;
    }

    public void PrepareToSelect(IEnumerable<int> indices)
    {
        _indicesToSelect = indices;
    }

    public void PrepareToSelectCardReward(CardRewardSelectionDelegate del)
    {
        _cardRewardSelectionDelegate = del;
    }

    public CardModel? GetSelectedCardReward(CardReward cardReward, IReadOnlyList<CardRewardAlternative> alternatives)
    {
        if (_cardRewardSelectionDelegate != null)
        {
            return _cardRewardSelectionDelegate?.Invoke(cardReward, alternatives);
        }

        return cardReward.Cards.FirstOrDefault();
    }

    public IEnumerable<CardModel> GetSelectedCards(IEnumerable<CardModel> options)
    {
        if (_cardsToSelect != null)
        {
            if (_cardsToSelect.Any(c => !options.Contains(c)))
            {
                throw new InvalidOperationException("Selected card missing from options.");
            }

            return _cardsToSelect;
        }

        if (_indicesToSelect != null)
        {
            return _indicesToSelect.Select(options.ElementAt);
        }

        return Enumerable.Empty<CardModel>();
    }
}
