using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Platform.Null;

public class NullAchievementStrategy : IAchievementStrategy
{
    public static readonly JsonSerializerOptions serializerOptions = new(JsonSerializationUtility.serializerOptions)
    {
        Converters = { new JsonStringEnumConverter(JsonNamingPolicy.SnakeCaseLower) }
    };

    private readonly GodotFileIo _fileIo = new ("user://saves");
    private List<Achievement> _achievements;

    public NullAchievementStrategy()
    {
        _achievements = Read();
    }

    public void Unlock(Achievement achievement)
    {
        if (_achievements.Contains(achievement)) return;
        _achievements.Add(achievement);
        Write(_achievements);
    }

    public void Revoke(Achievement achievement)
    {
        _achievements.Remove(achievement);
        Write(_achievements);
    }

    public bool IsUnlocked(Achievement achievement)
    {
        return _achievements.Contains(achievement);
    }

    private List<Achievement> Read()
    {
        if (TestMode.IsOn) return [];

        string? achievementsFile = _fileIo.ReadFile(_fileIo.GetFullPath("achievements.save"));
        if (achievementsFile == null)
        {
            return [];
        }

        ReadSaveResult<AchievementsFile> result = JsonSerializationUtility.FromJson<AchievementsFile>(achievementsFile, serializerOptions);
        if (result.Status != ReadSaveStatus.Success)
        {
            return [];
        }

        return result.SaveData!.Unlocked;
    }

    private void Write(List<Achievement> achievements)
    {
        if (TestMode.IsOn) return;
        string serialized = JsonSerializer.Serialize(new AchievementsFile { Unlocked = achievements }, serializerOptions);
        _fileIo.WriteFile(_fileIo.GetFullPath("achievements.save"), serialized);
    }
}
