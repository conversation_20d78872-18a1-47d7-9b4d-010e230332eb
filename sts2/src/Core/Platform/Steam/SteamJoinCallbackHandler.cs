using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Connection;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Debug.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using Steamworks;

namespace MegaCrit.Sts2.Core.Platform.Steam;

/// <summary>
/// Responsible for handling Steam-related requests to join multiplayer sessions coming from outside the game.
/// </summary>
public class SteamJoinCallbackHandler : IDisposable
{
    private readonly Callback<GameLobbyJoinRequested_t> _steamJoinCallback;

    public SteamJoinCallbackHandler()
    {
        _steamJoinCallback = new Callback<GameLobbyJoinRequested_t>(OnSteamLobbyJoinRequested);
    }

    /// <summary>
    /// If the game is not yet launched and the player accepts an invite, <PERSON> launches the game with a specific command
    /// line argument. This method handles joining the multiplayer session. It should be called relatively early after
    /// the game is launched.
    /// </summary>
    public void CheckForCommandLineJoin()
    {
        Log.Info($"Command line: {string.Join(" ", OS.GetCmdlineArgs())}");

        if (CommandLineHelper.TryGetValue("+connect_lobby", out string? lobbyIdString))
        {
            ulong lobbyId = ulong.Parse(lobbyIdString!);
            TaskHelper.RunSafely(JoinToHost(lobbyId, null));
        }
    }

    public void Dispose()
    {
        _steamJoinCallback.Dispose();
    }

    private void OnSteamLobbyJoinRequested(GameLobbyJoinRequested_t lobbyJoinRequest)
    {
        TaskHelper.RunSafely(JoinToHost(lobbyJoinRequest.m_steamIDLobby.m_SteamID, lobbyJoinRequest.m_steamIDFriend.m_SteamID));
    }

    private async Task JoinToHost(ulong lobbyId, ulong? playerId)
    {
        if (NGame.Instance!.RootSceneContainer.CurrentScene is NMultiplayerTest multiplayerTest)
        {
            SteamClientConnectionInitializer connectionInitializer = SteamClientConnectionInitializer.FromLobby(lobbyId);
            await multiplayerTest.JoinToHost(connectionInitializer);
        }
        else
        {
            // Climb is in progress, confirm that the player wants to quit
            if (ClimbManager.Instance.IsInProgress)
            {
                LocString body = new("gameplay_ui", "QUIT_AND_JOIN_CONFIRMATION.body");
                playerId ??= SteamMatchmaking.GetLobbyOwner(new CSteamID(lobbyId)).m_SteamID;
                body.Add("host", PlatformUtil.GetPlayerName(PlatformType.Steam, playerId.Value));

                NGenericMultiplayerConfirmationPopup confirmation = NGenericMultiplayerConfirmationPopup.Create()!;
                NModalContainer.Instance!.Add(confirmation);
                bool confirmed = await confirmation.WaitForConfirmation(
                    body,
                    new LocString("gameplay_ui", "QUIT_AND_JOIN_CONFIRMATION.header"),
                    new LocString("gameplay_ui", "QUIT_AND_JOIN_CONFIRMATION.cancel"),
                    new LocString("gameplay_ui", "QUIT_AND_JOIN_CONFIRMATION.confirm"));

                // If player cancelled, do nothing
                if (!confirmed) return;
            }

            if (NGame.Instance.MainMenu == null)
            {
                await NGame.Instance.ReturnToMainMenu();
            }

            // If we were already at the main menu, then clear all submenus (e.g. settings)
            while (NGame.Instance.MainMenu?.SubmenuStack.Peek() != null)
            {
                NGame.Instance.MainMenu?.SubmenuStack.Pop();
            }

            // Join game from main menu
            await NGame.Instance.MainMenu!.JoinGame(SteamClientConnectionInitializer.FromLobby(lobbyId));
        }
    }
}
