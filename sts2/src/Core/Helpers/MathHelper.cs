using <PERSON>ot;

namespace MegaCrit.Sts2.Core.Helpers;

/// <summary>
/// A set of static math functions which are missing from <PERSON><PERSON>'s Mathf.
/// </summary>
public static class MathHelper
{
    /// <summary>
    /// Linearly scales the input value from the source range [from1, to1] to the target range [from2, to2].
    /// </summary>
    /// <param name="value"></param>
    /// <param name="from1"></param>
    /// <param name="to1"></param>
    /// <param name="from2"></param>
    /// <param name="to2"></param>
    /// <returns></returns>
    public static float Remap(float value, float from1, float to1, float from2, float to2)
    {
        return (value - from1) / (to1 - from1) * (to2 - from2) + from2;
    }

    /// <summary>
    /// Bezier Curve curve function.
    /// </summary>
    /// <param name="v0"></param>
    /// <param name="v1"></param>
    /// <param name="c0"></param>
    /// <param name="t"></param>
    /// <returns></returns>
    public static Vector2 BezierCurve(Vector2 v0, Vector2 v1, Vector2 c0, float t)
    {
        Vector2 ret = Mathf.Pow(1f - t, 2f) * v0 + 2f * (1f - t) * t * c0 + Mathf.Pow(t, 2f) * v1;
        return ret;
    }

    public static float GetAngle(Vector2 vector)
    {
        return Mathf.Atan2(vector.Y, vector.X);
    }

    public const float degToRad = 0.0174533f;

    /// <summary>
    /// Clamps a Vector2, only if both X and Y are equal values.
    /// </summary>
    /// <param name="input"></param>
    /// <param name="min"></param>
    /// <param name="max"></param>
    /// <returns></returns>
    public static Vector2 Clamp(Vector2 input, float min, float max)
    {
        float clampedValue = Mathf.Clamp(input.X, min, max);
        return new Vector2(clampedValue, clampedValue);
    }

    /// <summary>
    /// Gradually changes a value towards a desired goal over time.
    /// </summary>
    /// <param name="current">The current value</param>
    /// <param name="target">The target value</param>
    /// <param name="currentVelocity">The current velocity. This should start at zero, be stored somewhere, and passed to
    /// this method every frame.</param>
    /// <param name="smoothTime">The approximate amount of time it will take to reach the target. Smaller values mean that
    /// we'll reach the target value faster.</param>
    /// <param name="deltaTime">The last process time.</param>
    /// <param name="maxSpeed">Maximum speed we'll move towards the target.</param>
    /// <returns>New value after interpolation.</returns>
    public static float SmoothDamp(float current, float target, ref float currentVelocity, float smoothTime, float deltaTime, float maxSpeed = Mathf.Inf)
    {
        // Based on Game Programming Gems 4 Chapter 1.10
        smoothTime = Mathf.Max(0.0001f, smoothTime);
        float omega = 2f / smoothTime;

        float y = omega * deltaTime;
        float exp = 1f / (1f + y + 0.48f * y * y + 0.235f * y * y * y);
        float change = current - target;
        float originalTo = target;

        // Clamp maximum speed
        float maxChange = maxSpeed * smoothTime;
        change = Mathf.Clamp(change, -maxChange, maxChange);
        target = current - change;

        float temp = (currentVelocity + omega * change) * deltaTime;
        currentVelocity = (currentVelocity - omega * temp) * exp;
        float output = target + (change + temp) * exp;

        // Prevent overshooting
        if (originalTo - current > 0f == output > originalTo)
        {
            output = originalTo;
            currentVelocity = (output - originalTo) / deltaTime;
        }

        return output;
    }

    /// <summary>
    /// Gradually changes a value towards a desired goal over time.
    /// </summary>
    /// <param name="current">The current value</param>
    /// <param name="target">The target value</param>
    /// <param name="currentVelocity">The current velocity. This should start at zero, be stored somewhere, and passed to
    /// this method every frame.</param>
    /// <param name="smoothTime">The approximate amount of time it will take to reach the target. Smaller values mean that
    /// we'll reach the target value faster.</param>
    /// <param name="deltaTime">The last process time.</param>
    /// <param name="maxSpeed">Maximum speed we'll move towards the target.</param>
    /// <returns>New value after interpolation.</returns>
    public static Vector2 SmoothDamp(Vector2 current, Vector2 target, ref Vector2 currentVelocity, float smoothTime, float deltaTime, float maxSpeed = Mathf.Inf)
    {
        // Based on Game Programming Gems 4 Chapter 1.10
        smoothTime = Mathf.Max(0.0001F, smoothTime);
        float omega = 2F / smoothTime;

        float x = omega * deltaTime;
        float exp = 1F / (1F + x + 0.48F * x * x + 0.235F * x * x * x);

        float changeX = current.X - target.X;
        float changeY = current.Y - target.Y;
        Vector2 originalTo = target;

        // Clamp maximum speed
        float maxChange = maxSpeed * smoothTime;

        float maxChangeSq = maxChange * maxChange;
        float sqDist = changeX * changeX + changeY * changeY;
        if (sqDist > maxChangeSq)
        {
            float mag = Mathf.Sqrt(sqDist);
            changeX = changeX / mag * maxChange;
            changeY = changeY / mag * maxChange;
        }

        target.X = current.X - changeX;
        target.Y = current.Y - changeY;

        float tempX = (currentVelocity.X + omega * changeX) * deltaTime;
        float tempY = (currentVelocity.Y + omega * changeY) * deltaTime;

        currentVelocity.X = (currentVelocity.X - omega * tempX) * exp;
        currentVelocity.Y = (currentVelocity.Y - omega * tempY) * exp;

        float outputX = target.X + (changeX + tempX) * exp;
        float outputY = target.Y + (changeY + tempY) * exp;

        // Prevent overshooting
        float origMinusCurrentX = originalTo.X - current.X;
        float origMinusCurrentY = originalTo.Y - current.Y;
        float outMinusOrigX = outputX - originalTo.X;
        float outMinusOrigY = outputY - originalTo.Y;

        if (origMinusCurrentX * outMinusOrigX + origMinusCurrentY * outMinusOrigY > 0)
        {
            outputX = originalTo.X;
            outputY = originalTo.Y;

            currentVelocity.X = (outputX - originalTo.X) / deltaTime;
            currentVelocity.Y = (outputY - originalTo.Y) / deltaTime;
        }
        return new Vector2(outputX, outputY);
    }
}
