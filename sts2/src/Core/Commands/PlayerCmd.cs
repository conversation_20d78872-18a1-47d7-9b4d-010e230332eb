using System;
using System.Diagnostics.CodeAnalysis;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Commands;

public static class PlayerCmd
{
    public const string goldSmallSfx = "event:/sfx/ui/gold/gold_1";
    public const string goldMediumSfx = "event:/sfx/ui/gold/gold_2";
    public const string goldLargeSfx = "event:/sfx/ui/gold/gold_3";

    /// <summary>
    /// Increase the current amount of energy that the player has.
    /// </summary>
    /// <param name="amount">Amount of energy to give.</param>
    /// <param name="player">Player to give the energy to.</param>
    public static Task GainEnergy(decimal amount, Player player)
    {
        if (amount <= 0) return Task.CompletedTask;
        if (CombatManager.Instance.IsAboutToEnd) return Task.CompletedTask;

        player.PlayerCombatState!.GainEnergy(amount);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Decrease the current amount of energy that the player has.
    /// </summary>
    /// <param name="amount">Amount of energy to remove.</param>
    /// <param name="player">Player to remove the energy from.</param>
    public static Task LoseEnergy(decimal amount, Player player)
    {
        if (amount <= 0) return Task.CompletedTask;
        if (CombatManager.Instance.IsAboutToEnd) return Task.CompletedTask;

        player.PlayerCombatState!.LoseEnergy(amount);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Set the player to have a specific amount of energy.
    /// </summary>
    /// <param name="amount">New amount of energy.</param>
    /// <param name="player">Player whose energy we're setting.</param>
    public static async Task SetEnergy(decimal amount, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        int oldAmount = player.PlayerCombatState!.Energy;

        if (oldAmount < amount)
        {
            await GainEnergy(amount - oldAmount, player);
        }
        else if (oldAmount > amount)
        {
            await LoseEnergy(oldAmount - amount, player);
        }
    }

    /// <summary>
    /// Increase the current amount of stars that the player has.
    /// </summary>
    /// <param name="amount">Amount of stars to give.</param>
    /// <param name="player">Player to give the stars to.</param>
    public static async Task GainStars(decimal amount, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;
        if (!HookBus.Instance.ShouldGainStars(amount, player)) return;

        player.PlayerCombatState!.GainStars(amount);
        await HookBus.Instance.AfterStarsGained((int)amount, player);
    }

    /// <summary>
    /// Decrease the current amount of stars that the player has.
    /// </summary>
    /// <param name="amount">Amount of stars to remove.</param>
    /// <param name="player">Player to remove the stars from.</param>
    [SuppressMessage(
        "ReSharper",
        "MemberCanBePrivate.Global",
        Justification = "Part of API."
    )]
    public static async Task LoseStars(decimal amount, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        player.PlayerCombatState!.LoseStars(amount);
        await HookBus.Instance.AfterStarsSpent((int)amount, player);
    }

    /// <summary>
    /// Set the player to have a specific amount of stars.
    /// </summary>
    /// <param name="amount">New amount of stars.</param>
    /// <param name="player">Player whose stars we're setting.</param>
    public static async Task SetStars(decimal amount, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        int oldAmount = player.PlayerCombatState!.Stars;

        if (oldAmount < amount)
        {
            await GainStars(amount - oldAmount, player);
        }
        else if (oldAmount > amount)
        {
            await LoseStars(oldAmount - amount, player);
        }
    }

    /// <summary>
    /// Increase the current amount of gold that the player has.
    /// </summary>
    /// <param name="amount">Amount of gold to give.</param>
    /// <param name="player">Player to give the gold to.</param>
    public static async Task GainGold(decimal amount, Player player)
    {
        if (!HookBus.Instance.ShouldGainGold(amount, player)) return;

        if (player == LocalContext.GetMe(player.ClimbState))
        {
            if (amount >= 100)
            {
                SfxCmd.Play(goldLargeSfx);
            }
            else if (amount > 30)
            {
                SfxCmd.Play(goldMediumSfx);
            }
            else
            {
                SfxCmd.Play(goldSmallSfx);
            }
        }

        PlayerMapPointHistoryEntry? entry = player.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(player.NetId);

        if (entry != null)
        {
            entry.Gold += (int)amount;
        }

        player.Gold += (int)amount;
        await HookBus.Instance.AfterGainedGold(player);
    }

    /// <summary>
    /// Decrease the current amount of gold that the player has.
    /// </summary>
    /// <param name="amount">Amount of gold to lose.</param>
    /// <param name="player">Player to take the gold from.</param>
    public static Task LoseGold(decimal amount, Player player)
    {
        SfxCmd.Play(goldSmallSfx);
        PlayerMapPointHistoryEntry? entry = player.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(player.NetId);

        if (entry != null)
        {
            entry.Gold -= (int)amount;
        }

        player.Gold = int.Max(0, player.Gold - (int)amount);

        return Task.CompletedTask;
    }

    /// <summary>
    /// Set the player to have a specific amount of gold.
    /// </summary>
    /// <param name="amount">New amount of gold.</param>
    /// <param name="player">Player whose gold we're setting.</param>
    public static async Task SetGold(decimal amount, Player player)
    {
        int oldAmount = player.Gold;

        if (oldAmount < amount)
        {
            await GainGold(amount - oldAmount, player);
        }
        else if (oldAmount > amount)
        {
            await LoseGold(oldAmount - amount, player);
        }
    }

    public static Task GainMaxPotionCount(int amount, Player player)
    {
        player.AddToMaxPotionCount(amount);
        return Task.CompletedTask;
    }

    public static Task LoseMaxPotionCount(int amount, Player player)
    {
        player.SubtractFromMaxPotionCount(amount);
        return Task.CompletedTask;
    }

    /// <summary>
    /// Give the player a new pet (aww).
    /// </summary>
    /// <param name="player">Player to give the pet to.</param>
    /// <typeparam name="T">Type of pet to give them.</typeparam>
    public static async Task<Creature> AddPet<T>(Player player) where T : MonsterModel
    {
        Creature pet = player.Creature.CombatState!.CreateCreature((T)ModelDb.Monster<T>().ToMutable(), player.Creature.Side, null);
        await AddPet(pet, player);

        return pet;
    }

    /// <summary>
    /// Give the player a new pet (aww).
    /// </summary>
    /// <param name="pet">Pet creature to give to the player.</param>
    /// <param name="player">Player to give the pet to.</param>
    public static async Task AddPet(Creature pet, Player player)
    {
        if (pet.CombatState == null) throw new InvalidOperationException("Pet must already be added to a combat state.");

        player.PlayerCombatState!.AddPetInternal(pet);
        await CreatureCmd.Add(pet);
    }

    /// <summary>
    /// Heal the player as if they were resting at a rest site.
    /// </summary>
    /// <param name="player">Player to heal.</param>
    public static async Task MimicRestSiteHeal(Player player)
    {
        HealRestSiteOption.PlayRestSiteHealSfx();
        await CreatureCmd.Heal(player.Creature, HealRestSiteOption.GetHealAmount(player.Creature));
        await HookBus.Instance.AfterPlayerRested(player);
    }

    /// <summary>
    /// Ends the turn for a given player.
    /// </summary>
    /// <param name="player">Player who is ending their turn</param>
    /// <param name="canBackOut">If the player is allowed to un-end their turn, particularly in multiplayer.</param>
    public static void EndTurn(Player player, bool canBackOut)
    {
        if (LocalContext.IsMe(player))
        {
            CombatManager.Instance.OnEndedTurnLocally();
        }

        CombatManager.Instance.SetReadyToEndTurn(player, canBackOut);
    }
}
