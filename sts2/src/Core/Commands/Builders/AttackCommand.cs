using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Commands.Builders;

/// <summary>
/// A "builder" command that allows you to chain method calls to build up the property of an attack.
/// This pattern is very similar to how Godot Tweens are built up.
/// </summary>
public class AttackCommand
{
    /// <summary>
    /// The amount of damage that this attack will deal on each hit.
    /// </summary>
    public decimal DamagePerHit { get; }

    /// <summary>
    /// The number of hits this attack should do.
    /// </summary>
    public int Hits { get; }

    /// <summary>
    /// The creature performing this attack.
    /// It's safe to assume this is non-null once the attack is being executed.
    /// </summary>
    public Creature? Attacker { get; private set; }

    /// <summary>
    /// The model that this attack comes from, such as an attack card.
    /// </summary>
    public AbstractModel? ModelSource { get; private set; }

    // Note that this is nullable internally to keep track of whether Targeting was called or not. It should not default
    // to an empty list. An empty list is a valid list of targets, e.g. when an AOE card is played while there are no
    // hittable enemies.
    private List<Creature>? _targets;

    /// <summary>
    /// The creatures being targeted by this attack.
    /// For random attacks, this contains all possible targets. After the attack has been executed, you can check
    /// <see cref="Results"/> to see which targets were actually hit.
    /// </summary>
    public IReadOnlyList<Creature> Targets => _targets ?? throw new InvalidOperationException("No targets set, the Targeting method must be called before Execute");

    /// <summary>
    /// The ValueProps of the damage that this attack deals.
    /// </summary>
    public ValueProp DamageProps { get; private set; } = ValueProp.Move;

    /// <summary>
    /// Whether this attack is randomly targeted.
    /// If false, all targets in <see cref="Targets"/> will be hit.
    /// If true, one will be chosen at random for each hit, and the results can be found in <see cref="Results"/>.
    /// </summary>
    public bool IsRandomlyTargeted { get; private set; }

    private bool _doesRandomTargetingAllowDuplicates = true;

    private readonly List<DamageResult> _results = [];

    /// <summary>
    /// The resulting instances of damage from this attack.
    /// This will start out empty, and will be populated after <see cref="Execute"/> finishes running.
    /// </summary>
    public IEnumerable<DamageResult> Results => _results;

    private string? _attackerAnimName;
    private float _attackerAnimDelay;
    private Creature? _visualAttacker;
    private bool _playOnEveryHit = true;

    private string? _attackerVfx;
    private string? _attackerSfx;
    private string? _tmpAttackerSfx;

    private string? _hitVfx;
    private string? _hitSfx;
    private string? _tmpHitSfx;

    private Func<Node2D?>? _createHitVfxNode;
    private Func<Task>? _beforeDamage;

    /// <summary>
    /// Create a new attack command.
    /// </summary>
    /// <param name="damagePerHit">The amount of damage this attack should deal on each hit.</param>
    /// <param name="hits">The number of times this attack should hit.</param>
    public AttackCommand(decimal damagePerHit, int hits)
    {
        DamagePerHit = damagePerHit;
        Hits = hits;
    }

    /// <summary>
    /// Set the attack to come from the specified card.
    /// This also automatically sets the attacker as the card's owner, and the attacker animation name/delay to the card
    /// owner's defaults.
    /// </summary>
    /// <param name="card">Card that the attack came from.</param>
    public AttackCommand FromCard(CardModel card)
    {
        if (Attacker != null) throw new InvalidOperationException("Attacker has already been set.");
        if (ModelSource != null) throw new InvalidOperationException("ModelSource has already been set.");

        Player owner = card.Owner;
        Attacker = owner.Creature;
        _attackerAnimName = SpineAnimator.attackTrigger;
        _attackerAnimDelay = owner.Character.AttackAnimDelay;
        ModelSource = card;

        return this;
    }

    /// <summary>
    /// Set the attack to come from the specified monster.
    /// </summary>
    /// <param name="monster">Monster that the attack came from.</param>
    public AttackCommand FromMonster(MonsterModel monster)
    {
        if (Attacker != null) throw new InvalidOperationException("Attacker has already been set.");

        Attacker = monster.Creature;
        _attackerAnimName = SpineAnimator.attackTrigger;

        return this;
    }

    /// <summary>
    /// Set the attack to target the specified creature.
    /// </summary>
    /// <param name="target">Creature for the attack to target.</param>
    public AttackCommand Targeting(Creature target)
    {
        return TargetingAll([target]);
    }

    /// <summary>
    /// Set the attack to target the specified set of creatures.
    /// </summary>
    /// <param name="targets">Creatures for the attack to target.</param>
    public AttackCommand TargetingAll(IEnumerable<Creature> targets)
    {
        if (_targets != null) throw new InvalidOperationException("Targets already set.");

        _targets = targets.ToList();
        return this;
    }

    /// <summary>
    /// Sets the attack to target random creatures from the specified set.
    /// A new random target will be chosen on each hit.
    /// </summary>
    /// <param name="targets">Creatures for the attack to target.</param>
    /// <param name="allowDuplicates">
    /// Whether the same target can be hit multiple times.
    /// In real gameplay, we pretty much always want this to be true, but we pass false sometimes for testing.
    /// </param>
    public AttackCommand TargetingRandom(IEnumerable<Creature> targets, bool allowDuplicates = true)
    {
        if (_targets != null) throw new InvalidOperationException("Targets already set.");

        _targets = targets.ToList();
        IsRandomlyTargeted = true;
        _doesRandomTargetingAllowDuplicates = allowDuplicates;
        return this;
    }

    /// <summary>
    /// Set the attack to deal unpowered damage.
    ///
    /// WARNING: 99% of the time, when unpowered damage is dealt, it shouldn't be considered an
    /// attack (like via <see cref="Burn"/>). However, occasionally we want to do a real attack
    /// that skips powers (like <see cref="Omnislice"/>), so this is useful there.
    /// </summary>
    public AttackCommand Unpowered()
    {
        DamageProps |= ValueProp.Unpowered;
        return this;
    }

    /// <summary>
    /// Set the attack to play the specified animation on the attacker.
    /// </summary>
    /// <param name="animName">Name of the animation trigger.</param>
    /// <param name="delay">Amount of time to wait for the animation to complete before proceeding.</param>
    /// <param name="visualAttacker">
    /// Optional custom creature to visually display as the attacker in this animation.
    /// Good for attacks like <see cref="ByrdSwoop"/> where we want a pet to animate for the attack, even though the
    /// actual attack is coming from the attack's owner.
    /// If this is left null, the attack's owning creature will play the animation.
    /// </param>
    public AttackCommand WithAttackerAnim(string? animName, float delay, Creature? visualAttacker = null)
    {
        if (_attackerAnimName == null)
        {
            throw new InvalidOperationException(
                $"{nameof(WithAttackerAnim)} was called before {nameof(FromCard)}/{nameof(FromMonster)}, " +
                "should be called after."
            );
        }

        _attackerAnimName = animName;
        _attackerAnimDelay = delay;
        _visualAttacker = visualAttacker;

        return this;
    }

    /// <summary>
    /// Set the attack to play the specified VFX/SFX on attacker.
    /// </summary>
    /// <param name="vfx">File path of the VFX.</param>
    /// <param name="sfx">File path of the SFX.</param>
    /// <param name="tmpSfx">
    /// Temporary SFX file path. If the attack uses temporary non-FMOD SFX, pass this instead of sfx using keyword args.
    /// </param>
    public AttackCommand WithAttackerFx(string? vfx = null, string? sfx = null, string? tmpSfx = null)
    {
        _attackerVfx = vfx;
        _attackerSfx = sfx;
        _tmpAttackerSfx = tmpSfx;

        return this;
    }

    /// <summary>
    /// Set the attack to play the specified VFX/SFX on the target(s) when they're hit.
    /// </summary>
    /// <param name="vfx">File path of the VFX.</param>
    /// <param name="sfx">File path of the SFX.</param>
    /// <param name="tmpSfx">
    /// Temporary SFX file path. If the attack uses temporary non-FMOD SFX, pass this instead of sfx using keyword args.
    /// </param>
    public AttackCommand WithHitFx(string? vfx = null, string? sfx = null, string? tmpSfx = null)
    {
        _hitVfx = vfx;
        _hitSfx = sfx;
        _tmpHitSfx = tmpSfx;

        return this;
    }

    /// <summary>
    /// Set the attack to add the specified custom VFX node to the combat VFX container when the target(s) are hit.
    /// </summary>
    public AttackCommand WithHitVfxNode(Func<Node2D?> createHitVfxNode)
    {
        _createHitVfxNode = createHitVfxNode;
        return this;
    }

    /// <summary>
    /// Set the attack to only play animations/VFX/SFX once, even on a multi-hit.
    /// Good for situations where a single bespoke attack animation illustrates multiple hits.
    /// </summary>
    public AttackCommand OnlyPlayAnimOnce()
    {
        _playOnEveryHit = false;
        return this;
    }

    /// <summary>
    /// Logic to execute before each instance of damage is dealt. Good for one-offs.
    /// Note: If you find yourself calling this with the same logic in many different attacks, consider making a
    /// first-class builder method for the logic instead.
    /// </summary>
    public AttackCommand BeforeDamage(Func<Task> beforeDamage)
    {
        _beforeDamage = beforeDamage;
        return this;
    }

    /// <summary>
    /// Execute this attack.
    /// If you forget to set some required values (like attacker or target), this will error.
    /// </summary>
    public async Task<AttackCommand> Execute()
    {
        if (Attacker == null) throw new InvalidOperationException("No attacker set.");
        if (_targets == null) throw new InvalidOperationException("No targets set.");
        if (CombatManager.Instance.IsAboutToEnd) return this;

        await HookBus.Instance.BeforeAttack(this);

        // TODO: ModifyAttackCount(dealer, times)
        // decimal attackCount = HookBus.Instance.ModifyOstyAttackCount(times);
        decimal attackCount = Hits;

        // TODO: CreatureAttacked(attacker, targets)
        // CombatManager.Instance.History.OstyAttacked(osty, targets);

        // TODO: Figure out how to make poke/slam attack anim work nicely for Osty.
        // the poke animation can repeat. the slam animation is to slow to happen multiple times
        // bool playAnimOnce = attackCount > 1 && triggerName != Osty.pokeAnim;

        for (int i = 0; i < attackCount; i++)
        {
            // Exit early if the attacker died.
            if (Attacker.IsDead) break;

            List<Creature> validTargets = Targets.Where(c => c.IsAlive).ToList();

            // Exit early if all targets are dead.
            if (validTargets.Count == 0) break;

            // TODO: Figure out how to make poke/slam attack anim work nicely for Osty.
            // if (i == 0 || !playAnimOnce)
            // {
            //     await CreatureCmd.TriggerAnim(dealer, triggerName, 0.3f);
            // }

            if (_playOnEveryHit || i == 0)
            {
                if (_attackerVfx != null)
                {
                    VfxCmd.PlayOnCreatureCenter(Attacker, _attackerVfx);
                }

                if (_attackerSfx != null)
                {
                    SfxCmd.Play(_attackerSfx);
                }
                else if (_tmpAttackerSfx != null)
                {
                    NDebugAudioManager.Instance?.Play(_tmpAttackerSfx);
                }

                if (_attackerAnimName != null)
                {
                    await CreatureCmd.TriggerAnim(_visualAttacker ?? Attacker, _attackerAnimName, _attackerAnimDelay);
                }
            }

            if (_hitSfx != null)
            {
                SfxCmd.Play(_hitSfx);
            }
            else if (_tmpHitSfx != null)
            {
                NDebugAudioManager.Instance?.Play(_tmpHitSfx);
            }

            NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(_createHitVfxNode?.Invoke());

            Creature? singleTarget;

            if (IsRandomlyTargeted)
            {
                if (!_doesRandomTargetingAllowDuplicates)
                {
                    validTargets = validTargets.Where(c => _results.All(r => r.Receiver != c)).ToList();

                    if (validTargets.Count == 0)
                    {
                        throw new InvalidOperationException(
                            "No valid targets for attack with duplicates disallowed. " +
                            "If you're in a test, you probably need to add more enemies. " +
                            "If you're in real gameplay, something is wrong."
                        );
                    }
                }

                singleTarget = Attacker.Player!.ClimbState.Rng.CombatTargets.NextItem(validTargets);
            }
            else if (validTargets.Count == 1)
            {
                singleTarget = validTargets[0];
            }
            else
            {
                singleTarget = null;
            }

            if (_hitVfx != null)
            {
                if (singleTarget != null)
                {
                    VfxCmd.PlayOnCreatureCenter(singleTarget, _hitVfx);
                }
                else
                {
                    VfxCmd.PlayOnSide(Attacker.Side.GetOppositeSide(), _hitVfx, Attacker.CombatState!);
                }
            }

            if (_beforeDamage != null)
            {
                await _beforeDamage.Invoke();
            }

            _results.AddRange(
                await CreatureCmd.Damage(
                    // If we have a single target (due either to random targeting or to only a single target being
                    // specified/alive), pass that target.
                    // Otherwise, pass all living targets.
                    singleTarget != null ? [singleTarget] : validTargets,
                    DamagePerHit,
                    DamageProps,
                    Attacker,
                    ModelSource as CardModel
                )
            );
        }

        await HookBus.Instance.AfterAttack(this);

        return this;
    }
}
