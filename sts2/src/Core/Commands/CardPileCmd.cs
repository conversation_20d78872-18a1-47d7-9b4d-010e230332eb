using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Cards;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Commands;

public static class CardPileCmd
{
    /// <summary>
    /// Remove a card from the deck and play an animation.
    /// </summary>
    /// <param name="card">Card to remove.</param>
    public static async Task RemoveFromDeck(CardModel card)
    {
        await RemoveFromDeck([card]);
    }

    /// <summary>
    /// Remove cards from the deck and play an animation.
    /// </summary>
    /// <param name="cards">Cards to remove.</param>
    public static async Task RemoveFromDeck(IReadOnlyList<CardModel> cards)
    {
        foreach (CardModel card in cards)
        {
            if (card.Pile!.Type != CardPileTarget.Deck)
            {
                throw new InvalidOperationException("You cannot remove a card that is not in the deck.");
            }

            card.Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(card.Owner.NetId).CardsRemoved.Add(card.Id);
            await HookBus.Instance.BeforeCardRemoved(card);
            card.RemoveFromCurrentPile();

            if (LocalContext.IsMine(card))
            {
                NCard? cardNode = NCard.Create(card);

                if (cardNode != null)
                {
                    NClimb.Instance!.GlobalUi.CardPreviewContainer.AddChildSafely(cardNode);

                    cardNode.UpdateVisuals(CardPileTarget.None);

                    Tween tween = cardNode.CreateTween();

                    // First scale tween
                    tween.TweenProperty(cardNode, "scale", Vector2.One * 1f, 0.25f)
                        .From(Vector2.Zero)
                        .SetEase(Tween.EaseType.Out)
                        .SetTrans(Tween.TransitionType.Cubic);

                    tween.TweenProperty(cardNode, "scale:y", 0, 0.3f).SetDelay(1.5f);
                    tween.Parallel().TweenProperty(cardNode, "scale:x", 1.5f, 0.3).SetDelay(1.5f);
                    tween.Parallel().TweenProperty(cardNode, "modulate", Colors.Black, 0.2).SetDelay(1.5f);
                    tween.TweenCallback(Callable.From(cardNode.QueueFree));
                }
            }
        }
    }

    /// <summary>
    /// Remove a card from combat and play an animation.
    /// This will remove the card from any combat pile (<see cref="CardPileTargetExtensions.IsCombatPile"/>) it's in,
    /// but not from the player's deck.
    /// </summary>
    /// <param name="card">Card to remove.</param>
    public static async Task RemoveFromCombat(CardModel card)
    {
        await RemoveFromCombat([card]);
    }

    /// <summary>
    /// Remove cards from combat and play an animation.
    /// This will remove the cards from any combat pile (<see cref="CardPileTargetExtensions.IsCombatPile"/>) they're
    /// in, but not from the player's deck.
    /// </summary>
    /// <param name="cards">Cards to remove.</param>
    public static async Task RemoveFromCombat(IEnumerable<CardModel> cards)
    {
        // Also keep track of all the card nodes. Note that there might not be a node for each CardModel! Only cards
        // being moved to/from the Hand or Play piles will have nodes, since they're the only cards that are visible
        // during normal combat.
        List<NCard> cardNodes = [];
        Dictionary<CardModel, CardPile> oldPiles = new();

        foreach (CardModel card in cards)
        {
            if (card.Pile is not { IsCombatPile: true }) throw new InvalidOperationException("card must be in a combat pile for it to be removed");

            if (LocalContext.IsMine(card))
            {
                // Try to find an existing node for the card.
                NCard? node = NCard.FindOnTable(card);

                // Keep track of the card's node so we can tween/free it later.
                // If the card is a power, then we are not responsible for freeing the node - NFlyPowerVfx is.
                if (node != null && card.Type != CardType.Power)
                {
                    cardNodes.Add(node);
                }
            }

            oldPiles.Add(card, card.Pile);
            card.RemoveFromCurrentPile();
        }

        if (cardNodes.Count != 0)
        {
            NPlayerHand handNode = NCombatRoom.Instance!.Ui.Hand;
            Control playContainer = NCombatRoom.Instance.Ui.PlayContainer;
            Tween? tween = null;

            for (int i = 0; i < cardNodes.Count; i++)
            {
                NCard node = cardNodes[i];
                Vector2 nodeGlobalPos = node.GlobalPosition;
                CardModel card = node.Model!;
                CardPile oldPile = oldPiles[card];

                // Sometimes, cards in hand can look like they are being played, but they're not actually in the play
                // pile. See Regret/Doubt/Shame.
                if (oldPile.Type == CardPileTarget.Hand && !NodeUtil.IsDescendant(playContainer, node))
                {
                    // If the card was in the player's hand, we have to give the hand container a chance to run its
                    // special logic.
                    // Note: If we ever need to do this for any other piles, we should create an interface for this.
                    handNode.Remove(card);
                }
                else
                {
                    // If the card was in any other pile, just remove its node from the tree.
                    node.GetParent()?.RemoveChildSafely(node);
                }

                NCombatRoom.Instance.Ui.AddChildSafely(node);

                node.GlobalPosition = nodeGlobalPos;

                // Set up a single tween to be used for all the cards in parallel.
                // We have to do this lazily because for some reason an empty tween will start running and never
                // emit the finished signal.
                if (tween == null)
                {
                    tween = NCombatRoom.Instance.CreateTween();
                    tween.SetParallel();
                }

                card.Pile?.InvokeCardAddFinished();

                if (oldPile.Type != CardPileTarget.Hand && oldPile.Type != CardPileTarget.Play)
                {
                    AppendPileLerpTween(tween, node, CardPileTarget.Play, oldPile);
                }

                // Special VFX for cards being exhausted.
                tween.Chain().TweenCallback(Callable.From(() => { NCombatRoom.Instance.Ui.AddChildSafely(NExhaustVfx.Create(node)!); }));
                tween.Parallel().TweenProperty(node, "modulate", StsColors.exhaustGray, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
            }

            // Play the tween and wait for it to finish.
            if (tween != null)
            {
                tween.Play();

                if (tween.IsValid() && tween.IsRunning())
                {
                    await NCombatRoom.Instance.ToSignal(tween, Tween.SignalName.Finished);
                }
            }

            // Now that the tween is done, clean up all the nodes.
            foreach (NCard node in cardNodes)
            {
                node.QueueFreeSafely();
            }

            foreach (KeyValuePair<CardModel, CardPile> kvp in oldPiles)
            {
                await HookBus.Instance.AfterCardChangedPiles(kvp.Key, kvp.Value.Type, null);
            }
        }
    }

    /// <summary>
    /// Adds a new card into one of the combat piles.
    /// Card must have just been generated (ie shivs, infernal blade generation, attack potion).
    /// We do this, instead of a regular add, because this adds the generated card entry to the combat history.
    /// </summary>
    /// <param name="card">Card to add.</param>
    /// <param name="newPileType">Type of pile to add the card to.</param>
    /// <param name="addedByPlayer">
    /// True if this card is being added by an effect from the player (like <see cref="InfiniteBlades"/>).
    /// False if an enemy is adding it (like a monster giving you a curse).
    /// </param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    public static async Task<CardPileAddResult> AddGeneratedCardToCombat(CardModel card, CardPileTarget newPileType, bool addedByPlayer, CardPilePosition position = CardPilePosition.Bottom)
    {
        return (await AddGeneratedCardToCombat([card], newPileType, addedByPlayer, position))[0];
    }

    /// <summary>
    /// Adds a new card into one of the combat piles.
    /// Card must have just been generated (ie shivs, infernal blade generation, attack potion).
    /// We do this, instead of a regular add, because this adds the generated card entry to the combat history.
    /// </summary>
    /// <param name="cards">Cards to add.</param>
    /// <param name="newPileType">Type of pile to add the card to.</param>
    /// <param name="addedByPlayer">if this card is being added by a player</param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    public static async Task<IReadOnlyList<CardPileAddResult>> AddGeneratedCardToCombat(IEnumerable<CardModel> cards, CardPileTarget newPileType, bool addedByPlayer, CardPilePosition position = CardPilePosition.Bottom)
    {
        if (!cards.Any()) return [];
        if (!CombatManager.Instance.IsInProgress) throw new InvalidOperationException("Can only be added when in combat");
        if (cards.Any(c => c.Pile != null)) throw new InvalidOperationException("You are not allowed to generate cards that already have a pile");
        if (!newPileType.IsCombatPile()) throw new InvalidOperationException("You are not allowed to added generated cards to a non combat pile");

        List<CardPileAddResult> results = [];

        foreach (CardModel cardModel in cards)
        {
            // We log this event before adding the card to the pile, because a card being generated is a different event
            // than a card being added to a pile.
            CombatManager.Instance.History.CardGenerated(cardModel, addedByPlayer);

            results.Add(await Add(cardModel, newPileType.GetPile(cardModel.Owner), position));
            await HookBus.Instance.AfterCardGeneratedForCombat(cardModel, addedByPlayer);
        }

        return results;
    }

    /// <summary>
    /// Add a card to a pile.
    /// </summary>
    /// <param name="card">Card to add.</param>
    /// <param name="newPileType">Type of pile to add the card to.</param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public static async Task<CardPileAddResult> Add(CardModel card, CardPileTarget newPileType, CardPilePosition position = CardPilePosition.Bottom, AbstractModel? source = null)
    {
        if (card.Owner == null) throw new InvalidOperationException($"Attempted to add card {card} to pile, but it has no owner!");
        return await Add(card, newPileType.GetPile(card.Owner), position, source);
    }

    /// <summary>
    /// Add a card to a pile.
    /// </summary>
    /// <param name="card">Card to add.</param>
    /// <param name="newPile">Pile to add the card to.</param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public static async Task<CardPileAddResult> Add(CardModel card, CardPile newPile, CardPilePosition position = CardPilePosition.Bottom, AbstractModel? source = null)
    {
        return (await Add([card], newPile, position, source))[0];
    }

    /// <summary>
    /// Add multiple cards to a pile.
    /// </summary>
    /// <param name="cards">Cards to add.</param>
    /// <param name="newPileType">Type of pile to add the cards to.</param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public static async Task<IReadOnlyList<CardPileAddResult>> Add(IEnumerable<CardModel> cards, CardPileTarget newPileType, CardPilePosition position = CardPilePosition.Bottom, AbstractModel? source = null)
    {
        if (!cards.Any()) return [];

        return await Add(cards, newPileType.GetPile(cards.First().Owner), position, source);
    }

    /// <summary>
    /// Add multiple cards to a pile.
    /// </summary>
    /// <param name="cards">Cards to add.</param>
    /// <param name="newPile">Pile to add the cards to.</param>
    /// <param name="position">Optional position in the pile to add the cards to. Defaults to bottom.</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public static async Task<IReadOnlyList<CardPileAddResult>> Add(IEnumerable<CardModel> cards, CardPile newPile, CardPilePosition position = CardPilePosition.Bottom, AbstractModel? source = null)
    {
        if (!cards.Any()) return [];
        if (newPile.IsCombatPile && CombatManager.Instance.IsAboutToEnd) return cards.Select(c => new CardPileAddResult { cardAdded = c, success = false }).ToList();

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // WARNING! This is a really important method, and by necessity, there's a lot going on in it!                //
        //                                                                                                            //
        // If you're going to make changes to this method, PLEASE read it carefully and make sure you understand how  //
        // everything in it works. None of it is overly complex, but it's fragile and hard to debug.                  //
        //                                                                                                            //
        // Please do this even if you've already read it in the past! It may have changed since last time.            //
        //                                                                                                            //
        // If you try to make quick one-off changes to it without fully understanding it, you might be making a lot   //
        // more work in the long-term for yourself and others.                                                        //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Keep track of whether or not each card was successfully added to the pile.
        // We start them all out as true here, and we can toggle a card to false later if it's blocked.
        List<CardPileAddResult> results = [];
        Player? owningPlayer = null;

        foreach (CardModel card in cards)
        {
            if (card.Owner == null)
            {
                throw new InvalidOperationException($"{card.Id.Entry} has no owner.");
            }

            if (newPile.Type == CardPileTarget.Deck)
            {
                if (!card.Owner.ClimbState.ContainsCard(card))
                {
                    throw new InvalidOperationException($"{card.Id.Entry} must be added to a ClimbState before adding it to your deck.");
                }
            }
            else if (card.CombatState == null)
            {
                throw new InvalidOperationException($"{card.Id.Entry} must be added to a CombatState before adding it to your deck.");
            }

            if (card.UpgradePreviewType.IsPreview())
            {
                throw new InvalidOperationException("A card preview cannot be added to a pile.");
            }

            CardPileAddResult result = new()
            {
                success = true,
                cardAdded = card,
                oldPile = card.Pile,
                modifyingModels = null
            };

            results.Add(result);

            owningPlayer ??= card.Owner;
            if (owningPlayer != card.Owner)
            {
                throw new InvalidOperationException("Tried to add cards with different owners to the same pile!");
            }
        }

        bool owningPlayerIsLocal = LocalContext.IsMe(owningPlayer);

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // This method works in 4 stages:                                                                             //
        //                                                                                                            //
        // 1. If we're adding the cards to your deck, give the ShouldAddToDeck hook the opportunity to block adds to  //
        //    the deck.                                                                                               //
        // 2. Add the cards to their new piles on the back-end. If necessary, create nodes for them and set up their  //
        //    initial positions on the front-end.                                                                     //
        // 3. Tween all card nodes to their new piles.                                                                //
        // 4. Run "After" hooks on cards that changed piles.                                                          //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // This is Stage 1, where we allow the ShouldAddToDeck hook to block adds to deck.                            //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        if (newPile.Type == CardPileTarget.Deck)
        {
            for (int i = 0; i < results.Count; i++)
            {
                CardPileAddResult result = results[i];

                if (HookBus.Instance.ShouldAddToDeck(result.cardAdded, out AbstractModel? preventer))
                {
                    IClimbState climbState = owningPlayer!.ClimbState;
                    climbState.CurrentMapPointHistoryEntry?.GetEntry(owningPlayer.NetId)
                        .CardsGained
                        .Add(result.cardAdded.Id);
                    result.cardAdded.FloorAddedToDeck = climbState.TotalFloor;
                }
                else
                {
                    await preventer!.AfterAddToDeckPrevented(result.cardAdded);

                    result.success = false;
                    results[i] = result;
                }
            }
        }

        // Exit early if we are trying to add it to a combat pile, but combat was completed.
        if (newPile.IsCombatPile && !CombatManager.Instance.IsInProgress) return results;

        // Exit early if no cards were passed or they all got invalidated by a hook.
        if (results.Count(r => r.success) <= 0) return results;

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // This is Stage 2, where we add all the cards to their correct piles on the back-end.                        //
        // If necessary, we also create NCard nodes in the correct starting positions on the front-end in here.       //
        // If necessary, we also create CardSoulVfx for cards traveling between piles.                                //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////

        // Also keep track of all the card nodes. Note that there might not be a node for each CardModel! Only cards
        // being moved to/from the Hand or Play piles will have nodes, since they're the only cards that are visible
        // during normal combat.
        List<NCard> cardNodes = [];

        for (int i = 0; i < results.Count; i++)
        {
            CardPileAddResult result = results[i];

            if (!result.success) continue;

            NCard? node = null;
            CardPile? oldPile = result.oldPile;
            CardModel card = result.cardAdded;

            // we use a new reference to the pile just in case it ends up changing for some reason
            CardPile targetPile = newPile;

            // In the most common cases (i.e. drawing a full hand at the start of your turn), we check the hand size manually as to not overflow.
            // However, there are some cases where we end up having to do this during cases where a card/power specifically tries to add a card to your hand
            // Ex:
            // - Upgraded cloak and dagger tries to add 2 shivs to a hand that already has 9+ cards
            // - A bunch of Nightmares end up adding 10+ cards to your hand
            bool isFullHandAdd = targetPile is { Type: CardPileTarget.Hand, Cards.Count: >= CardPile.maxCardsInHand };

            if (isFullHandAdd)
            {
                targetPile = CardPile.Get(CardPileTarget.Discard, card.Owner)!;
            }

            // We want to show tweens for:
            // - All local player cards
            // - Remote player cards that are entering play or exiting play
            bool shouldFindCard = owningPlayerIsLocal || targetPile.Type == CardPileTarget.Play || oldPile?.Type == CardPileTarget.Play;

            if (TestMode.IsOff && shouldFindCard)
            {
                // Try to find an existing node for the card.
                node = NCard.FindOnTable(card);

                // NOTE: if oldPile null, that means that it was a newly generated card.
                // If it is, and it is not going into the hand, let the caller handle it via PreviewCardPileAdd.
                // TODO: this is starting to feel a bit clunky. Maybe revisit to see if we can make it cleaner
                bool createCardNode =
                    node == null &&
                    targetPile.Type.IsCombatPile() &&
                    (isFullHandAdd || oldPile != null || targetPile.Type == CardPileTarget.Hand);

                if (createCardNode)
                {
                    // If we don't have one (meaning the card was either just created or is coming from a pile other
                    // than Hand or Play), create one for it.
                    node = NCard.Create(card)!;
                    NCombatRoom.Instance!.Ui.AddChildSafely(node);
                    node.UpdateVisuals(targetPile.Type);

                    if (!owningPlayerIsLocal)
                    {
                        // If this is a remote player and the card doesn't exist (can happen for things like infection),
                        // spawn the card at the creature node's intent position
                        node.Position = NCombatRoom.Instance.GetCreatureNode(card.Owner.Creature)!.IntentContainer.GlobalPosition;
                    }
                    else if (card.Pile != null)
                    {
                        // If the card is starting out in an existing pile, position the node in the appropriate place
                        // for that pile.
                        node.Position = card.Pile.Type.GetTargetPosition(node);
                    }
                    else
                    {
                        // If the card is starting out with no pile (probably meaning it was just created)
                        // start it out where it should be positioned for the new pile.
                        node.Position = targetPile.Type.GetTargetPosition(node);
                    }
                }

                // Keep track of the card's node so we can tween/free it later.
                if (node != null)
                {
                    cardNodes.Add(node);
                }
            }

            CardModel cardToAdd = card;

            if (card.Pile != null)
            {
                if (owningPlayerIsLocal && node == null)
                {
                    NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(
                        NCardFlyShuffleVfx.Create(card.Pile, targetPile, owningPlayer!.Character.TrailPath)
                    );
                }

                // If the card is currently in a pile, remove it in preparation to be added to the new one.
                card.RemoveFromCurrentPile();
            }
            else if (targetPile.IsCombatPile)
            {
                // If the card was just created and is being added to a combat pile (anything other than Deck), run the
                // AfterCardEnteredCombat hook.
                await HookBus.Instance.AfterCardEnteredCombat(card);
            }
            else if (targetPile.Type == CardPileTarget.Deck)
            {
                // If the card was just created and is being added to the deck, run the ModifyCardBeingAddedToDeck hook.
                CardModel newCard = HookBus.Instance.ModifyCardBeingAddedToDeck(card, out List<AbstractModel> modifyingModels);
                cardToAdd = newCard;

                if (modifyingModels is { Count: > 0 })
                {
                    result.cardAdded = newCard;
                    result.modifyingModels = modifyingModels;
                    results[i] = result;
                }
            }

            int index = position switch
            {
                CardPilePosition.Bottom => -1,
                CardPilePosition.Top => 0,
                CardPilePosition.Random => card.Owner.ClimbState.Rng.Shuffle.NextInt(targetPile.Cards.Count + 1),
                _ => throw new ArgumentOutOfRangeException(nameof(position), position, null)
            };

            // Add the card on the back-end.
            targetPile.AddInternal(cardToAdd, index);

            if (isFullHandAdd && owningPlayerIsLocal)
            {
                ThinkCmd.Play(new LocString("combat_messages", "HAND_FULL"), owningPlayer!.Creature, 2f);
            }

            // If the card is transitioning from play to a non-hand pile, don't update the text. The card is going away
            if (oldPile?.Type != CardPileTarget.Play || newPile.Type == CardPileTarget.Hand || card.IsDupe)
            {
                node?.UpdateVisuals(targetPile.Type);
            }
        }

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // This is Stage 3, where we take all our relevant NCard nodes and tween them to the correct position for     //
        // their destination pile.                                                                                    //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        if (cardNodes.Count != 0)
        {
            NPlayerHand handNode = NCombatRoom.Instance!.Ui.Hand;
            Control playContainer = NCombatRoom.Instance.Ui.PlayContainer;

            // Set up a single tween to be used for all the cards in parallel.
            Tween tween = NCombatRoom.Instance.CreateTween();
            tween.SetParallel();

            foreach (NCard node in cardNodes)
            {
                Vector2 nodeGlobalPos = node.GlobalPosition;
                CardModel card = node.Model!;
                CardPileAddResult addResult = results.Find(r => r.cardAdded == card);
                CardPile? oldPile = addResult.oldPile;

                if (owningPlayerIsLocal &&
                    oldPile?.Type == CardPileTarget.Hand &&
                    // Sometimes, cards in hand can look like they are being played, but they're not actually in the
                    // play pile. See Regret/Doubt/Shame
                    !NodeUtil.IsDescendant(playContainer, node))
                {
                    // If the card was in the local player's hand, we have to give the hand container a chance to run
                    // its special logic.
                    // Note: If we ever need to do this for any other piles, we should create an interface for this.
                    handNode.Remove(card);
                }
                else if (node.GetParent() is NRemoteCardPlayHolder remoteHolder)
                {
                    // If the card is part of a remote card play, free the holder entirely and reuse the card.
                    remoteHolder.RemoveChildSafely(node);
                    remoteHolder.QueueFreeSafely();
                }
                else
                {
                    // If the card was in any other pile, just remove its node from the tree.
                    node.GetParent()?.RemoveChildSafely(node);
                }

                // Parent the card to the appropriate container, and position it where it was before it was removed from the tree.
                if (card.Pile!.Type == CardPileTarget.Play)
                {
                    playContainer.AddChildSafely(node);
                }
                else
                {
                    NCombatRoom.Instance.Ui.AddChildSafely(node);
                }

                node.GlobalPosition = nodeGlobalPos;

                // For non-local players and the specified piles, we just do a generic "go away" tween because there's no real pile to move the card to.
                if (!owningPlayerIsLocal && card.Pile.Type is CardPileTarget.Deck or CardPileTarget.Discard or CardPileTarget.Draw or CardPileTarget.Hand)
                {
                    // HACK: This is set in multiplayer when the card is tweening back to the play pile from the player's intent position
                    node.PlayPileTween?.Kill();
                    tween.Parallel().TweenProperty(node, "position", node.Position + Vector2.Down * 25f, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
                    tween.Parallel().TweenProperty(node, "modulate", StsColors.exhaustGray, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
                    tween.Chain().TweenCallback(Callable.From(node.QueueFreeSafely));
                    continue;
                }

                switch (card.Pile.Type)
                {
                    case CardPileTarget.Exhaust:
                    {
                        card.Pile.InvokeCardAddFinished();

                        if (oldPile != null && oldPile.Type != CardPileTarget.Hand && oldPile.Type != CardPileTarget.Play)
                        {
                            AppendPileLerpTween(tween, node, CardPileTarget.Play, oldPile);
                        }

                        // Special VFX for cards being exhausted.
                        tween.Chain().TweenCallback(Callable.From(() => { NCombatRoom.Instance.Ui.AddChildSafely(NExhaustVfx.Create(node)!); }));
                        tween.Parallel().TweenProperty(node, "modulate", StsColors.exhaustGray, SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.2f : 0.3f);
                        tween.Chain().TweenCallback(Callable.From(node.QueueFreeSafely));
                        break;
                    }
                    case CardPileTarget.Hand:
                        AppendPileLerpTween(tween, node, card.Pile.Type, oldPile!);
                        tween.Parallel().TweenCallback(Callable.From(() => { handNode.Add(node); }));
                        break;
                    case CardPileTarget.Play:
                        AppendPileLerpTween(tween, node, card.Pile.Type, oldPile!);
                        tween.Parallel().TweenCallback(Callable.From(() => NCombatRoom.Instance.Ui.AddToPlayContainer(node)));
                        break;
                    default:
                        // Draw, Discard, Deck
                        tween.TweenCallback(Callable.From(() =>
                        {
                            // NOTE: It's never the deck...
                            Node vfxContainer = card.Pile.Type != CardPileTarget.Deck
                                ? NCombatRoom.Instance.CombatVfxContainer
                                : NClimb.Instance!.GlobalUi.TopBar.TrailContainer;

                            // Re-parents the Card Node so it's rendered below the draw, discard, or exhaust pile when the card flies into it.
                            node.Reparent(vfxContainer);

                            // Create the trail vfx and attach it to the card and place it on the same layer as our card node.
                            Vector2 targetPos = card.Pile!.Type.GetTargetPosition(node);
                            NCardFlyVfx? vfx = NCardFlyVfx.Create(node, targetPos, true, card.Owner.Character.TrailPath);
                            vfxContainer.AddChildSafely(vfx);
                        }));
                        break;
                }
            }

            // Play the tween and wait for it to finish.
            tween.Play();

            if (tween.IsValid() && tween.IsRunning())
            {
                await NCombatRoom.Instance.ToSignal(tween, Tween.SignalName.Finished);
            }
        }

        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        // This is Stage 4, where we run any After hooks on cards that changed piles.                                 //
        ////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        foreach (CardPileAddResult result in results)
        {
            if (!result.success) continue;
            await HookBus.Instance.AfterCardChangedPiles(result.cardAdded, result.oldPile?.Type ?? CardPileTarget.None, source);
        }

        return results;
    }

    private static void AppendPileLerpTween(Tween tween, NCard cardNode, CardPileTarget targetPile, CardPile? oldPile)
    {
        // Normal VFX for cards being moved to a new pile.
        Vector2 targetPos = targetPile.GetTargetPosition(cardNode);
        float duration = SaveManager.Instance.SettingsSave.FastMode switch
        {
            FastModeType.Instant => 0.01f,
            FastModeType.Fast => 0.1f,
            _ => 0.25f
        };

        // the Hand Pile is in charge of animating in its own cards
        if (targetPile != CardPileTarget.Hand)
        {
            tween.TweenProperty(cardNode, "position", targetPos, duration)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }

        // we scale the card down a little bit when transitioning to the play pile so that it takes less space
        if (targetPile == CardPileTarget.Play)
        {
            tween.TweenProperty(cardNode, "scale", Vector2.One * 0.8f, 0.15f)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }
        else if (oldPile == null)
        {
            tween.TweenProperty(cardNode, "scale", Vector2.One, duration)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic)
                .From(Vector2.Zero);
        }
        else
        {
            // In most cases card node should already be scaled at one. The current exception being
            // if we go from the play pile back to the hand pile
            tween.Parallel().TweenProperty(cardNode, "scale", Vector2.One, duration)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }
    }

    /// <summary>
    /// Draw a card.
    /// </summary>
    /// <param name="choiceContext">The context with which to handle player choices.</param>
    /// <param name="player">Player who the hand and draw pile belongs to.</param>
    /// <returns>Card that was drawn, or null if no cards were drawn.</returns>
    public static async Task<CardModel?> Draw(PlayerChoiceContext choiceContext, Player player)
    {
        return (await Draw(choiceContext, 1, player)).FirstOrDefault();
    }

    /// <summary>
    /// Draw cards.
    /// </summary>
    /// <param name="choiceContext">The context with which to handle player choices.</param>
    /// <param name="count">Number of cards to draw.</param>
    /// <param name="player">Player who the hand and draw pile belongs to.</param>
    /// <param name="fromHandDraw">If this draw happened as part of the initial card draws at the start of your turn.</param>
    /// <returns>Cards that were drawn.</returns>
    public static async Task<IEnumerable<CardModel>> Draw(PlayerChoiceContext choiceContext, decimal count, Player player, bool fromHandDraw = false)
    {
        if (CombatManager.Instance.IsAboutToEnd) return [];
        if (!HookBus.Instance.ShouldDraw(player, fromHandDraw, out AbstractModel? modifier))
        {
            await HookBus.Instance.AfterPreventingDraw(modifier!);
            return [];
        }

        List<CardModel> result = [];
        CardPile hand = CardPileTarget.Hand.GetPile(player);
        CardPile drawPile = CardPileTarget.Draw.GetPile(player);
        CardPile discardPile = CardPileTarget.Discard.GetPile(player);

        for (int i = 0; i < count; i++)
        {
            if (drawPile.Cards.Count + discardPile.Cards.Count <= 0)
            {
                ThinkCmd.Play(new LocString("combat_messages", "NO_DRAW"), player.Creature, 2.0);
                continue;
            }

            if (hand.Cards.Count >= CardPile.maxCardsInHand)
            {
                ThinkCmd.Play(new LocString("combat_messages", "HAND_FULL"), player.Creature, 2.0);
                continue;
            }

            await ShuffleIfNecessary(choiceContext, player);

            CardModel? card = drawPile.Cards.FirstOrDefault();

            if (card != null)
            {
                result.Add(card);
                await Add(card, hand);
                CombatManager.Instance.History.CardDrawn(card);
                await HookBus.Instance.AfterCardDrawn(choiceContext, card, fromHandDraw);
                card.InvokeDrawn();
                NDebugAudioManager.Instance?.Play(TmpSfx.cardDeal, 0.25f, PitchVariance.Small);
            }
            else
            {
                // No cards left, end loop early.
                break;
            }
        }

        return result;
    }

    /// <summary>
    /// Shuffle the player's discard pile into their draw pile.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player whose piles we should shuffle.</param>
    public static async Task Shuffle(PlayerChoiceContext choiceContext, Player player)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        CardPile drawPile = CardPileTarget.Draw.GetPile(player);
        List<CardModel> cards = CardPileTarget.Discard.GetPile(player).Cards.ToList();

        // Shuffle is used:
        // - When you try to draw and your draw pile is empty
        // - When shuffling the draw pile while there are cards already in it (e.g. Orbit, Reboot)
        // For the second case, we need to remove & re-add all the cards in the draw pile, but only internally so that no
        // animation is shown. This should do nothing in the first case.
        HashSet<CardModel> drawPileCards = drawPile.Cards.ToHashSet();

        foreach (CardModel card in drawPileCards)
        {
            drawPile.RemoveInternal(card, true);
            cards.Add(card);
        }

        // Shuffle the discard + draw pile cards together
        cards.StableShuffle(player.ClimbState.Rng.Shuffle);
        HookBus.Instance.TryModifyDrawShuffleOrder(player, cards);

        // Put all the cards in the shuffled order in the draw pile
        foreach (CardModel card in cards)
        {
            // If this came from outside the draw pile, use the full add command
            if (!drawPileCards.Contains(card))
            {
                await Add(card, drawPile);
                await Cmd.Wait(Rng.Chaotic.NextFloat(0.02f, 0.07f));
            }
            // Otherwise, re-add it internally & silently
            else
            {
                drawPile.AddInternal(card, -1, true);
            }
        }

        await Cmd.CustomScaledWait(0.2f, 0.5f, 0.7f);
        await HookBus.Instance.AfterShuffle(choiceContext, player);
    }

    /// <summary>
    /// Play the top cards of the draw pile.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player whose draw pile we should play from.</param>
    /// <param name="count">Number of cards to play.</param>
    /// <param name="forceExhaust">Whether or not to force the played cards to be exhausted after.</param>
    public static async Task AutoPlayFromDrawPile(PlayerChoiceContext choiceContext, Player player, int count, bool forceExhaust)
    {
        if (CombatManager.Instance.IsAboutToEnd) return;

        List<CardModel> cards = new(count);
        CardPile drawPile = CardPileTarget.Draw.GetPile(player);

        // We don't want to play the same card multiple times if the deck is small enough, so collect the cards we are
        // going to play first, then play them after.
        for (int i = 0; i < count; i++)
        {
            await ShuffleIfNecessary(choiceContext, player);

            CardModel? card = drawPile.Cards.FirstOrDefault();

            if (card != null)
            {
                cards.Add(card);
                await Add(card, CardPileTarget.Play);
            }
            else
            {
                // No cards left, exit early.
                break;
            }
        }

        foreach (CardModel card in cards)
        {
            card.ExhaustOnNextPlay = forceExhaust;
            await CardCmd.AutoPlay(choiceContext, card, null);
        }
    }

    public static async Task ShuffleIfNecessary(PlayerChoiceContext choiceContext, Player player)
    {
        CardPile drawPile = CardPileTarget.Draw.GetPile(player);
        CardPile discardPile = CardPileTarget.Discard.GetPile(player);

        if (!drawPile.Cards.Any() && discardPile.Cards.Any())
        {
            await ShuffleFtueCheck();
            await Shuffle(choiceContext, player);
        }
    }

    /// <summary>
    /// The first time the discard pile is shuffled into the draw pile, the Shuffle FTUE shows up
    /// </summary>
    private static async Task ShuffleFtueCheck()
    {
        if (SaveManager.Instance.SeenFtue(NShuffleFtue.id)) return;
        if (NModalContainer.Instance == null) return;

        NShuffleFtue ftue = NShuffleFtue.Create()!;
        NModalContainer.Instance.Add(ftue);
        SaveManager.Instance.MarkFtueAsComplete(NShuffleFtue.id);

        await ftue.WaitForPlayerToConfirm();
    }

    public static async Task AddToCombatAndPreview<T>(IEnumerable<Creature> targets, CardPileTarget pileTarget, int count, bool addedByPlayer, CardPilePosition position = CardPilePosition.Bottom) where T : CardModel
    {
        foreach (Creature target in targets)
        {
            await AddToCombatAndPreview<T>(target, pileTarget, count, addedByPlayer, position);
        }
    }

    public static async Task AddToCombatAndPreview<T>(Creature target, CardPileTarget pileTarget, int count, bool addedByPlayer, CardPilePosition position = CardPilePosition.Bottom) where T : CardModel
    {
        Player player = target.Player ?? target.PetOwner!;
        if (player.Creature.IsDead) return;

        CardPileAddResult[] statusCards = new CardPileAddResult[count];

        for (int i = 0; i < count; i++)
        {
            CardModel card = target.CombatState!.CreateCard<T>(player);
            statusCards[i] = await AddGeneratedCardToCombat(card, pileTarget, addedByPlayer, position);
        }

        if (LocalContext.IsMe(player))
        {
            if (pileTarget == CardPileTarget.Hand)
            {
                // No need to preview adds to hand, they are in front of you already
                await Cmd.Wait(0.1f);
            }
            else
            {
                CardCmd.PreviewCardPileAdd(statusCards);
                await Cmd.Wait(1f);
            }
        }
    }

    public static async Task AddCurseToDeck<T>(Player owner) where T : CardModel
    {
        await AddCursesToDeck([ModelDb.Card<T>()], owner);
    }

    public static async Task AddCursesToDeck(IEnumerable<CardModel> curses, Player owner)
    {
        List<CardPileAddResult> results = [];

        foreach (CardModel curse in curses)
        {
            if (curse.Type != CardType.Curse) throw new ArgumentException($"{curse.Id.Entry} is not a curse");

            CardModel mutableCurse = owner.ClimbState.CreateCard(curse, owner);
            CardPileAddResult result = await Add(mutableCurse, CardPileTarget.Deck);
            results.Add(result);
        }

        CardCmd.PreviewCardPileAdd(results, 2f);
    }
}
