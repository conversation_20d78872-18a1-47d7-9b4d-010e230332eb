using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Commands;

/// <summary>
/// Creates a speech bubble. Used when a creature talks/screams/whispers to you. Generally during combat
/// </summary>
public static class TalkCmd
{
    private const double _defaultTimePerCharacter = 0.08;
    private const double _minTimeToDisplay = 1.5;

    public static void Play(LocString line, Creature speaker, double secondsToDisplay = -1, VfxColor vfxColor = VfxColor.White)
    {
        if (speaker.IsDead) return;

        string text = line.GetFormattedText();

        if (secondsToDisplay < 0)
        {
            secondsToDisplay = text.Length * _defaultTimePerCharacter;
        }

        if (secondsToDisplay < _minTimeToDisplay)
        {
            secondsToDisplay = _minTimeToDisplay;
        }

        NSpeechBubbleVfx? vfx = NSpeechBubbleVfx.Create(text, speaker, secondsToDisplay, vfxColor);

        if (vfx != null)
        {
            NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(vfx);
        }
    }
}