namespace MegaCrit.Sts2.Core.Events;

public enum EventLayoutType
{
    /// <summary>
    /// The standard event layout type.
    /// At the time I wrote this (10/1/2024), this layout aligns text and buttons to the right side of the screen.
    /// It shows full-screen static art in the background, with the focal point on the left side of the screen.
    /// </summary>
    Default = 0,

    /// <summary>
    /// The event layout type used for the Ancients at the beginning of acts.
    /// At the time I wrote this (10/1/2024), this layout aligns text and buttons to the bottom-middle of the screen.
    /// It shows a Spine animation in the background, with the focal point on the tip-middle of the screen.
    /// </summary>
    Ancient = 1,

    /// <summary>
    /// A totally custom scene.
    /// We use this when we want to show events that don't follow any standard layout.
    /// </summary>
    Custom = 2
}