using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NThinSliceVfx : Node2D
{
    private const string _scenePath = "res://scenes/vfx/thin_slice_vfx.tscn";

    private GpuParticles2D _slash = default!;
    private GpuParticles2D _sparkle = default!;
    private Vector2 _creatureCenter;
    private VfxColor _vfxColor;

    /// <summary>
    /// Thin Slice vfx will intersect the given position. Pass in the creature vfx center position!
    /// </summary>
    /// <param name="target"></param>
    /// <param name="vfxColor"></param>
    /// <returns></returns>
    public static NThinSliceVfx? Create(Creature? target, VfxColor vfxColor = VfxColor.Cyan)
    {
        if (TestMode.IsOn) return null;

        Vector2 position = NCombatRoom.Instance!.GetCreatureNode(target!)!.VfxSpawnPosition;

        NThinSliceVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NThinSliceVfx>();
        vfx._vfxColor = vfxColor;
        Vector2 offset = new(Rng.Chaotic.NextFloat(-50f, 50f), Rng.Chaotic.NextFloat(-50f, 50f));
        vfx._creatureCenter = position + offset;
        return vfx;
    }

    public override void _Ready()
    {
        _slash = GetNode<GpuParticles2D>("Slash");
        _slash.GlobalPosition = GenerateSpawnPosition();
        _slash.Rotation = GetAngle();
        _slash.Emitting = true;

        _sparkle = _slash.GetNode<GpuParticles2D>("Sparkle");
        _sparkle.GlobalPosition = _creatureCenter;
        _sparkle.Emitting = true;

        SetColor();

        TaskHelper.RunSafely(SelfDestruct());
    }

    private void SetColor()
    {
        ParticleProcessMaterial mat = (ParticleProcessMaterial)_slash.GetProcessMaterial();

        switch (_vfxColor)
        {
            case VfxColor.Red:
                mat.Color = new Color("FF9900");
                break;
            case VfxColor.Green:
                break;
            case VfxColor.Blue:
                break;
            case VfxColor.Purple:
                break;
            case VfxColor.Black:
                break;
            case VfxColor.White:
                mat.Color = Colors.White;
                break;
            case VfxColor.Cyan:
                mat.Color = new Color("C4FFE6");
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }
    }

    /// <summary>
    /// Spawns our slash particle in a random position around the given centerPoint
    /// </summary>
    private Vector2 GenerateSpawnPosition()
    {
        float angle = Rng.Chaotic.NextFloat(0f, Mathf.Pi * 2f);
        float radius = Rng.Chaotic.NextFloat(400f, 500f);
        return new Vector2(_creatureCenter.X + radius * Mathf.Cos(angle),
            _creatureCenter.Y + radius * Mathf.Sin(angle));
    }

    /// <summary>
    /// Returns the angle from one vector to another in radians.
    /// Written because Vector2.AngleTo() was confusing
    /// </summary>
    /// <returns></returns>
    private float GetAngle()
    {
        Vector2 direction = _creatureCenter - _slash.GlobalPosition;
        return Mathf.Atan2(direction.Y, direction.X);
    }

    private async Task SelfDestruct()
    {
        await Task.Delay(1000);
        this.QueueFreeSafely();
    }
}
