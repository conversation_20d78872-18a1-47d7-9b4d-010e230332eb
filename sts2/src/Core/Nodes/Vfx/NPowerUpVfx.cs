using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NPowerUpVfx : Node2D
{
    private float _timer;
    private const float _vfxDuration = 1f;

    private Control _creatureVisuals = default!;
    private Sprite2D _backVfx = default!;

    private static string NormalScenePath => SceneHelper.GetScenePath("/vfx/vfx_power_up/vfx_power_up");
    private static string GhostlyScenePath => SceneHelper.GetScenePath("/vfx/vfx_ghostly_power_up/vfx_ghostly_power_up");
    public static IEnumerable<string> AssetPaths => [NormalScenePath, GhostlyScenePath];

    public static NPowerUpVfx? CreateNormal(Creature target)
    {
        return CreatePowerUpVfx(target, NormalScenePath);
    }

    public static NPowerUpVfx? CreateGhostly(Creature target)
    {
        return CreatePowerUpVfx(target, GhostlyScenePath);
    }

    private static NPowerUpVfx? CreatePowerUpVfx(Creature target, string scenePath)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(target);
        if (creatureNode is not { IsInteractable: true }) return null;

        NPowerUpVfx node = PreloadManager.Cache.GetScene(scenePath).Instantiate<NPowerUpVfx>();
        node.GlobalPosition = creatureNode.VfxSpawnPosition;
        NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(node);

        return node;
    }

    public override void _Ready()
    {
        _timer = _vfxDuration;
        _backVfx = GetNode<Sprite2D>("%BackVfx");

        Vector2 position = _backVfx.GlobalPosition;
        _backVfx.Reparent(NCombatRoom.Instance!.BackCombatVfxContainer);
        _backVfx.GlobalPosition = position;
    }

    public override void _Process(double delta)
    {
        // Countdown to make this Vfx disappear
        _timer -= (float)delta;

        float alpha = 1f;
        if (Mathf.Abs(_timer / _vfxDuration - 0.5f) > 0.4f)
        {
            alpha = Mathf.Max(0f, 1f - (Mathf.Abs(_timer / _vfxDuration - 0.5f) - 0.4f) / 0.1f);
        }

        Modulate = new Color(1f, 1f, 1f, alpha);
        _backVfx.Modulate = new Color(1f, 1f, 1f, alpha);

        if (_timer < 0f)
        {
            this.QueueFreeSafely();
            _backVfx.QueueFreeSafely();
        }
    }
}
