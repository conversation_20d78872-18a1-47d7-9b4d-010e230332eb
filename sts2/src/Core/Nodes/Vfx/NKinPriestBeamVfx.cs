using Godot;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NKinPriestBeamVfx : Node2D
{

    // Use this to control how long it gets when it extends. It's not meant to be super precise,
    // as long as it generally crosses to the player character
    private const float _beamMaxLengthScale = 4f;

    // control the sweep motion
    private const float _startRotation = 1.0f;
    private const float _endRotation = -1.0f;

    private Sprite2D _beam = default!;
    private Node2D _beamHolder = default!;
    private GpuParticles2D _staticParticles = default!;
    private Vector2 _baseBeamScale;
    private Tween _lengthTween = null!;
    private Tween _rotationTween = null!;

    public override void _Ready()
    {

        _beam = GetNode<Sprite2D>("BeamHolder/Beam");
        _staticParticles = GetNode<GpuParticles2D>("BeamHolder/StaticParticles");
        _beamHolder = GetNode<Node2D>("BeamHolder");
        _baseBeamScale = _beam.Scale;
        
        _staticParticles.Emitting = false;
        _staticParticles.Visible = false;
        _beamHolder.Visible = false;
    }

    public override void _Process(double delta)
    {

        Vector2 scaleMod = new(Rng.Chaotic.NextFloat(-.05f, .05f), Rng.Chaotic.NextFloat(-.7f, .7f));
        _beam.Scale = _baseBeamScale + scaleMod;
        Color tempColor = Modulate;
        tempColor.A = Rng.Chaotic.NextFloat(0.8f, 1.0f);
        Modulate = tempColor;
    }

    public void Fire()
    {
        _staticParticles.Restart();
        _staticParticles.Visible = true;
        _beamHolder.Visible = true;

        _rotationTween = this.CreateTween();
        RotationDegrees = _startRotation;
        _rotationTween.TweenProperty(this, "rotation_degrees", _endRotation, 0.8f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Cubic);

        _beamHolder.Scale = Vector2.One;
        _lengthTween = this.CreateTween();
        _lengthTween.TweenProperty(_beamHolder, "scale:x", _beamMaxLengthScale, 0.38f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo);
        _lengthTween.Chain().TweenProperty(_beamHolder, "scale:x", 0.5, 0.6f).SetEase(Tween.EaseType.In).SetTrans(Tween.TransitionType.Expo);
        _lengthTween.TweenCallback(Callable.From(OnTweenComplete));
    }

    void OnTweenComplete()
    {
        _rotationTween.Kill();
        _lengthTween.Kill();

        _staticParticles.Emitting = false;
        _staticParticles.Visible = false;
        _beamHolder.Visible = false;
    }


}