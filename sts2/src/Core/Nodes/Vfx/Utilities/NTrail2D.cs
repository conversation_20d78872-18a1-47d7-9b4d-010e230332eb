using System.Collections.Generic;
using System.Linq;
using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;

public partial class NTrail2D : Line2D
{
    private int _maxSegments = 20;
    private Node2D _parent = default!;
    private readonly List<Vector2> _pointQueue = [];
    private bool _isActive;

    public override void _Ready()
    {
        _parent = GetParent<Node2D>();

        Connect(CanvasItem.SignalName.VisibilityChanged, Callable.From(OnToggleVisibility));
        OnToggleVisibility();
    }

    private void OnToggleVisibility()
    {
        _isActive = Visible;

        if (!Visible)
        {
            _pointQueue.Clear();
            Points = _pointQueue.ToArray();
        }
    }

    public override void _Process(double delta)
    {
        if (!_isActive) return;

        _pointQueue.Insert(0, _parent.GlobalPosition);

        if (_pointQueue.Count >= _maxSegments)
        {
            _pointQueue.RemoveAt(_pointQueue.Count - 1);
        }

        // TODO: If trail is too curvy, could try to smooth it out with some bezier curves
        Points = _pointQueue.Select(point => GetParent<Node2D>().ToLocal(point)).ToArray();
    }
}
