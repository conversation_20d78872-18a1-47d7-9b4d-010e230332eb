using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;

public partial class NSpriteAnimator : Sprite2D
{
    [ExportGroup("Animation Settings")]
    [Export]
    private Texture2D[] _frames = default!;

    [Export]
    private float _fps = 15f;

    [Export]
    private bool _loop;

    [ExportGroup("Rotation Settings")]
    [Export]
    private bool _randomizeRotation;

    [Export]
    private Vector2 _rotationRange;

    private readonly CancellationTokenSource _cancelToken = new();

    public override void _Ready()
    {
        if (_randomizeRotation)
        {
            RotationDegrees = new System.Random().Next((int)_rotationRange.X, (int)_rotationRange.Y);
        }

        TaskHelper.RunSafely(PlayAnimation());
    }

    public override void _ExitTree()
    {
        _cancelToken.Cancel();
    }

    private async Task PlayAnimation()
    {
        int i = 0;
        int interval = Mathf.RoundToInt(1000 / _fps);

        do
        {
            if (_cancelToken.IsCancellationRequested) break;

            Texture = _frames[i];

            i++;
            if (_loop)
            {
                i %= _frames.Length;
            }

            await Task.Delay(interval, _cancelToken.Token);
        } while (_frames.Length > i);

        if (!_cancelToken.IsCancellationRequested)
        {
            this.QueueFreeSafely();
        }
    }
}
