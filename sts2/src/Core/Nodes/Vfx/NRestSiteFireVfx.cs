using Godot;
using Godot.Collections;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NRestSiteFireVfx : Node2D
{
    [Export]
    private float _minFlickerScale = 0.85f;

    [Export]
    private float _maxFlickerScale = 1.05f;

    [Export]
    private float _minFlickerTime = 0.3f;

    [Export]
    private float _maxFlickerTime = 0.5f;

    [Export]
    private float _minSkew = -0.1f;

    [Export]
    private float _maxSkew = 0.1f;

    [Export]
    private float _minSkewTime = 0.8f;

    [Export]
    private float _maxSkewTime = 1.5f;

    [Export]
    private float _extinguishTime = 0.2f;

    [Export]
    private bool _enabled = true;

    [Export]
    private Array<CpuParticles2D> _cpuGlowParticles = [];

    [Export]
    private Array<GpuParticles2D> _gpuSparkParticles = [];

    private Vector2 _baseScale;
    private float _baseSkew;

    private Tween _scaleTweenRef = default!;
    //private Tween _swayTween = default!;

    public override void _Ready()
    {
        if (!_enabled) return;

        _baseScale = Scale;
        _baseSkew = Skew;

        Flicker();
        Sway();
    }

    private void Flicker()
    {
        Vector2 littleScale = new(_baseScale.X, Rng.Chaotic.NextFloat(_baseScale.Y * _minFlickerScale, _baseScale.Y));
        Vector2 bigScale = new(_baseScale.X, Rng.Chaotic.NextFloat(_baseScale.Y, _baseScale.Y * _maxFlickerScale));

        Tween scaleTween = CreateTween();
        scaleTween
            .TweenProperty(this, "scale", littleScale, Rng.Chaotic.NextFloat(_minFlickerTime, _maxFlickerTime))
            .SetTrans(Tween.TransitionType.Quad)
            .SetEase(Tween.EaseType.InOut);
        scaleTween
            .TweenProperty(this, "scale", bigScale, Rng.Chaotic.NextFloat(_minFlickerTime, _maxFlickerTime))
            .SetTrans(Tween.TransitionType.Quad)
            .SetEase(Tween.EaseType.InOut);
        scaleTween.TweenCallback(Callable.From(Flicker));
        _scaleTweenRef = scaleTween;
    }

    private void Sway()
    {
        float minS = Rng.Chaotic.NextFloat(_baseSkew + _minSkew, _baseSkew);
        float maxS = Rng.Chaotic.NextFloat(_baseSkew, _baseSkew + _maxSkew);

        Tween swayTween = CreateTween();
        swayTween
            .TweenProperty(this, "skew", minS, Rng.Chaotic.NextFloat(_minSkewTime, _maxSkewTime))
            .SetTrans(Tween.TransitionType.Sine)
            .SetEase(Tween.EaseType.InOut);
        swayTween
            .TweenProperty(this, "skew", maxS, Rng.Chaotic.NextFloat(_minSkewTime, _maxSkewTime))
            .SetTrans(Tween.TransitionType.Sine)
            .SetEase(Tween.EaseType.InOut);
        swayTween.TweenCallback(Callable.From(Sway));
    }

    /// <summary>
    /// Extinguish the fire.
    /// NOTE: This is public because it'll be used by front-end rest site classes in the future.
    /// Remove this note once we actually use it and Rider stops warning us.
    /// </summary>
    public void Extinguish()
    {
        Tween particlesTween = CreateTween().SetParallel();

        foreach (CpuParticles2D cp in _cpuGlowParticles)
        {
            cp.Emitting = false;
            particlesTween
                .TweenProperty(cp, "scale", Vector2.Zero, _extinguishTime)
                .SetTrans(Tween.TransitionType.Back)
                .SetEase(Tween.EaseType.In);
        }

        foreach (GpuParticles2D gp in _gpuSparkParticles)
        {
            // I used GPU Particles for sparks, so They should be given a chance to fly away peacefully
            gp.Emitting = false;
        }

        _scaleTweenRef.Kill();
        //_swayTween.Kill();
        Tween shrinkTween = CreateTween();
        shrinkTween
            .TweenProperty(this, "scale", Vector2.Zero, _extinguishTime)
            .SetTrans(Tween.TransitionType.Back)
            .SetEase(Tween.EaseType.In);
    }

    // Hotkey "G" for testing

    // public override void _Input(InputEvent inputEvent)
    // {
    //     if (inputEvent is InputEventKey keyEvent && keyEvent.Keycode == Key.G && keyEvent.IsReleased())
    //     {
    //         Extinguish();
    //
    //     }
    // }
}
