using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NPowerRemovedVfx : Node2D
{
    private const string _scenePath = "res://scenes/vfx/power_removed_vfx.tscn";

    private TextureRect _sprite = default!;
    private Label _powerField = default!;
    private Control _vfxContainer = default!;
    private PowerModel _power = default!;

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public override void _Ready()
    {
        _sprite = GetNode<TextureRect>("%TextureRect");
        _powerField = GetNode<Label>("%PowerField");
        _vfxContainer = GetNode<Control>("%Container");

        GlobalPosition = NCombatRoom.Instance!.GetCreatureNode(_power.Owner)!.GetTopOfHitbox();
        TaskHelper.RunSafely(StartVfx());
    }

    private async Task StartVfx()
    {
        _powerField.Text = _power.Title.GetFormattedText();
        _sprite.Texture = _power.BigIcon;
        _vfxContainer.Position -= _vfxContainer.Size * 0.5f;

        Tween textTween = CreateTween();
        textTween.TweenProperty(_vfxContainer, "modulate:a", 1f, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        textTween.TweenInterval(0.5f);
        textTween.TweenProperty(_vfxContainer, "modulate:a", 0f, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Linear);

        CreateTween().TweenProperty(_vfxContainer, "position:y", _powerField.Position.Y - 160f, 2.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Quart);

        await ToSignal(textTween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }

    public static NPowerRemovedVfx? Create(PowerModel power)
    {
        if (TestMode.IsOn) return null;
        if (NCombatUi.IsDebugHideTextVfx) return null;
        if (!power.ShouldPlayVfx) return null;

        NPowerRemovedVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NPowerRemovedVfx>();
        vfx._power = power;

        return vfx;
    }
}
