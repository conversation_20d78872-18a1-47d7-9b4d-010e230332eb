using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx.Events;

public partial class NSymbioteVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/symbiote_vfx");

    public static NSymbioteVfx? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NSymbioteVfx>();
    }

    public override void _Ready()
    {
        Position = new Vector2(268f, 49f);
    }
}
