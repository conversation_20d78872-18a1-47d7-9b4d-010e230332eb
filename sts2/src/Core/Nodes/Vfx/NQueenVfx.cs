using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NQueenVfx : Node2D
{
    [Export]
    private SpineSprite _spineNode = default!;

    [Export]
    private GpuParticles2D _sprayParticles = default!;

    private SpineAnimationState _animState = default!;

    public override void _Ready()
    {
        _animState = _spineNode.GetAnimationState();
        _sprayParticles.Emitting = false;
        
        _spineNode.Connect(SpineSprite.SignalName.AnimationStarted, Callable.From<GodotObject, GodotObject, GodotObject>(OnAnimationStart));
    }

    public override void _EnterTree()
    {
        base._EnterTree();
        _spineNode.AnimationEvent += OnSpineEvent;
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        _spineNode.AnimationEvent -= OnSpineEvent;
    }

    private void OnSpineEvent(GodotObject sprite, GodotObject animationState, GodotObject trackEntry, GodotObject eventObject)
    {
        string name = ((SpineEvent)eventObject).GetData().GetEventName();

        GD.Print(name);
        switch (name)
        {
            case "attack_start":
                StartAttack();
                break;
            case "attack_end":
                EndAttack();
                break;
        }
    }
    
    private void OnAnimationStart(GodotObject spineSprite, GodotObject animationState, GodotObject trackEntry)
    {
        if (((SpineAnimationState)animationState).GetCurrent(0).GetAnimation().GetName() != "attack")
        {
            EndAttack();
        }
    }

    private void StartAttack()
    {
        _sprayParticles.Restart();
    }

    private void EndAttack()
    {
        _sprayParticles.Emitting = false;
    }
}