using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NUiFlashVfx : Control
{
    private const string _scenePath = "res://scenes/vfx/ui_flash_vfx.tscn";

    private TextureRect _textureRect = default!;
    private Texture2D _texture = default!;
    private Color _modulate;

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public override void _Ready()
    {
        _textureRect = GetNode<TextureRect>("TextureRect");
        _textureRect.Texture = _texture;
    }

    public async Task StartVfx()
    {
        _textureRect.Modulate = _modulate with { A = 0f };
        _textureRect.PivotOffset = _textureRect.Size / 2f;

        Tween spriteTween = CreateTween();
        spriteTween.SetParallel();
        spriteTween.TweenProperty(_textureRect, "scale", Vector2.One * 1.3f, 0.5f);
        spriteTween.TweenProperty(_textureRect, "modulate:a", 1, 0.25f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Sine);
        spriteTween.SetParallel(false);
        spriteTween.TweenProperty(_textureRect, "modulate:a", 0, 0.25f).SetEase(Tween.EaseType.In).SetTrans(Tween.TransitionType.Sine);

        await ToSignal(spriteTween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }

    public static NUiFlashVfx? Create(Texture2D tex, Color modulate)
    {
        if (TestMode.IsOn) return null;

        NUiFlashVfx vfx = (NUiFlashVfx)PreloadManager.Cache.GetScene(_scenePath).Instantiate();
        vfx._texture = tex;
        vfx._modulate = modulate;

        return vfx;
    }
}
