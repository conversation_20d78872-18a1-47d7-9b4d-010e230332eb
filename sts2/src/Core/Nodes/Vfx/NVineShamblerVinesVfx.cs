using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NVineShamblerVinesVfx : Node2D
{
    private SpineSprite _frontVines = default!;
    private SpineSprite _backVines = default!;
    private GpuParticles2D _dirtBlast1 = default!;
    private GpuParticles2D _dirtBlast2 = default!;
    private GpuParticles2D _dirtBlast3 = default!;
    private GpuParticles2D _dirtBlast4 = default!;

    public override void _Ready()
    {
        _frontVines = GetNode<SpineSprite>("VinesFront");
        _backVines = GetNode<SpineSprite>("VinesBackScene/VinesBack");

        _dirtBlast1 = GetNode<GpuParticles2D>("DirtBlast1");
        _dirtBlast3 = GetNode<GpuParticles2D>("DirtBlast3");
        _dirtBlast2 = GetNode<GpuParticles2D>("VinesBackScene/DirtBlast2");
        _dirtBlast4 = GetNode<GpuParticles2D>("VinesBackScene/DirtBlast4");

        _dirtBlast1.Emitting = false;
        _dirtBlast1.OneShot = true;
        _dirtBlast2.Emitting = false;
        _dirtBlast2.OneShot = true;
        _dirtBlast3.Emitting = false;
        _dirtBlast3.OneShot = true;
        _dirtBlast4.Emitting = false;
        _dirtBlast4.OneShot = true;

        // we call deferred so that the vfx can be placed in the correct position before resetting the backVines position
        Vector2 backVineOffset = _backVines.GlobalPosition - _frontVines.GlobalPosition;
        _backVines.Reparent(NCombatRoom.Instance!.BackCombatVfxContainer);
        Callable.From(() => { _backVines.GlobalPosition = _frontVines.GlobalPosition + backVineOffset; }).CallDeferred();

        _frontVines.Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnFrontEvent));
        _frontVines.Connect(SpineSprite.SignalName.AnimationCompleted, Callable.From<GodotObject, GodotObject, GodotObject>(AnimationEnded));

        _frontVines.GetAnimationState().SetAnimation("animation");
        _backVines.GetAnimationState().SetAnimation("animation");
    }

    private void OnFrontEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "dirt_1":
                _dirtBlast1.Restart();
                break;
            case "dirt_2":
                _dirtBlast2.Restart();
                break;
            case "dirt_3":
                _dirtBlast3.Restart();
                break;
            case "dirt_4":
                _dirtBlast4.Restart();
                break;
        }
    }

    private void AnimationEnded(GodotObject _, GodotObject __, GodotObject ___)
    {
        this.QueueFreeSafely();
        _backVines.QueueFreeSafely();
    }
}