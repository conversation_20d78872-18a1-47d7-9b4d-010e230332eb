using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NMonsterDeathVfx : Node2D
{
    private const string _deathSfx = "event:/sfx/enemy/enemy_fade";
    private static string ScenePath => SceneHelper.GetScenePath("vfx/vfx_monster_death");

    public static IEnumerable<string> AssetPaths => [ScenePath];

    // If the length of the sub viewport is 0.15x of the size of the main viewport, it will take 2.5s
    private const float _refLength = 0.1f;
    private const float _refTweenDuration = 2.5f;
    private const float _minTweenDuration = 2.5f;
    private const float _tweenStartValue = 0f;
    private const string _shaderParamThreshold = "shader_parameter/threshold";

    private List<NCreature> _creatureNodes = default!;
    private List<Control> _hitboxes = default!;
    private CancellationToken _cancelToken;

    /// <summary>
    /// Creates an instance of NMonsterDeathVfx for the specified creature.
    /// </summary>
    /// <param name="creatureNode">The creature node.</param>
    /// <param name="cancelToken">The cancellation token.</param>
    /// <returns>The created NMonsterDeathVfx instance, or null if cancellation is requested or NCombatRoom.Instance is null.</returns>
    public static NMonsterDeathVfx? Create(NCreature creatureNode, CancellationToken cancelToken)
    {
        if (TestMode.IsOn) return null;
        if (cancelToken.IsCancellationRequested) return null;

        NMonsterDeathVfx node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NMonsterDeathVfx>();
        node._creatureNodes = [creatureNode];
        node._cancelToken = cancelToken;
        node._hitboxes = [creatureNode.Hitbox];
        return node;
    }

    /// <summary>
    /// Creates an instance of NMonsterDeathVfx for the specified creature.
    /// This should be used on creatures like the Decimillipede that are made of several different creatures that fade
    /// out together.
    /// </summary>
    /// <param name="creatureNodes">The creature nodes.</param>
    /// <returns>The created NMonsterDeathVfx instance, or null if cancellation is requested or NCombatRoom.Instance is null.</returns>
    public static NMonsterDeathVfx? Create(List<NCreature> creatureNodes)
    {
        if (TestMode.IsOn) return null;

        NMonsterDeathVfx node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NMonsterDeathVfx>();
        node._creatureNodes = creatureNodes;
        node._cancelToken = default;
        node._hitboxes = creatureNodes.Select(c => c.Hitbox).ToList();
        return node;
    }

    public async Task PlayVfx()
    {
        if (_cancelToken.IsCancellationRequested) return;

        SubViewport viewport = GetNode<SubViewport>("Viewport");

        Rect2? creatureBounds = null;

        // Calculate the bounds of the viewport necessary to contain all passed creatures.
        // The position of viewportBounds will be the global position of the minimum top-left of all creatures.
        // The size will be the extents needed to cover all creatures.
        for (int i = 0; i < _creatureNodes.Count; i++)
        {
            NCreature creatureNode = _creatureNodes[i];
            NCreatureVisuals visuals = creatureNode.Visuals;
            SpineSprite spine = visuals.SpineBody!;
            Vector2 padding = creatureNode.Entity.Monster?.ExtraDeathVfxPadding ?? MonsterModel.defaultDeathVfxPadding;

            if (visuals.HasSpineAnimation)
            {
                Vector2 combatRoomScale = NCombatRoom.Instance!.SceneContainer.Scale;
                Rect2 originalBounds = spine.GetSkeleton().GetBounds();
                Rect2 scaledBounds = new (originalBounds.Position * spine.Scale * combatRoomScale, originalBounds.Size * spine.Scale * combatRoomScale);

                Vector2 min = new(Math.Min(scaledBounds.Position.X, scaledBounds.End.X), Math.Min(scaledBounds.Position.Y, scaledBounds.End.Y));
                Vector2 max = new(Math.Max(scaledBounds.Position.X, scaledBounds.End.X), Math.Max(scaledBounds.Position.Y, scaledBounds.End.Y));

                Vector2 baseSize = max - min;
                Vector2 size = baseSize * padding;
                Vector2 offset = (size - baseSize) * 0.5f;
                Rect2 bounds = new(spine.GlobalPosition + min - offset, size);

                if (creatureBounds == null)
                {
                    creatureBounds = bounds;
                }
                else
                {
                    creatureBounds = creatureBounds.Value.Merge(bounds);
                }
            }
            else
            {
                Control hitbox = _hitboxes[i];
                Vector2 baseSize = hitbox.Size * hitbox.Scale;
                Vector2 size = baseSize * padding;
                Vector2 offset = (size - baseSize) * 0.5f;
                Rect2 bounds = new (hitbox.GlobalPosition - offset, size);
                if (creatureBounds == null)
                {
                    creatureBounds = bounds;
                }
                else
                {
                    creatureBounds = creatureBounds.Value.Merge(bounds);
                }
            }
        }

        // Set our position (we're centered on all the creatures)
        GlobalPosition = creatureBounds!.Value.Position + creatureBounds.Value.Size * 0.5f;
        Vector2 viewportSize = creatureBounds.Value.Size;

        // Make viewport square; is better for the shader
        int maxViewportExtent = Mathf.RoundToInt(Mathf.Max(viewportSize.X, viewportSize.Y));
        viewportSize = new Vector2(maxViewportExtent, maxViewportExtent);
        viewport.Size = new Vector2I(maxViewportExtent, maxViewportExtent);

        // Get the top-left of the display position of the viewport in canvas coords, relative to which we are positioning
        // our creatures
        Vector2 viewportTopLeft = GlobalPosition - viewportSize * 0.5f;

        // Reparent all creature visuals to the viewport in the correct position
        foreach (NCreature creature in _creatureNodes)
        {
            Vector2 originalGlobalPosition = creature.Visuals.Body.GlobalPosition;
            creature.Visuals.Body.Reparent(viewport);
            creature.Visuals.Body.Position = originalGlobalPosition - viewportTopLeft;
        }

        await PlayVfxInternal();
    }

    private async Task PlayVfxInternal()
    {
        SfxCmd.Play(_deathSfx);
        SubViewport viewport = GetNode<SubViewport>("Viewport");
        Sprite2D visual = GetNode<Sprite2D>("Visual");
        CpuParticles2D particles = GetNode<CpuParticles2D>("%Particles");

        particles.EmissionSphereRadius = viewport.Size.Y / 4f;
        particles.Emitting = true;

        float refLengthPx = _refLength * GetViewportRect().Size.X;
        float tweenDuration = Math.Min(viewport.Size.X / refLengthPx * _refTweenDuration, _minTweenDuration);

        using Tween tween = CreateTween();
        tween.TweenProperty(visual.Material, _shaderParamThreshold, _tweenStartValue, tweenDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);

        await ToSignal(tween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }
}
