using Godot;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NKnowledgeDemonVfx : SpineSprite
{
    private Node2D _fireNode1 = default!;
    private Node2D _fireNode2 = default!;
    private Node2D _fireNode3 = default!;
    private Node2D _fireNode4 = default!;
    private GpuParticles2D _explosionParticles = default!;
    private GpuParticles2D _damageParticles = default!;
    private GpuParticles2D _emberParticles = default!;
    private GpuParticles2D _thinEmberParticles = default!;

    public override void _Ready()
    {
        _fireNode1 = GetNode<Node2D>("FireSlot1/FireHolder1");
        _fireNode2 = GetNode<Node2D>("FireSlot2/FireHolder2");
        _fireNode3 = GetNode<Node2D>("FireSlot3/FireHolder3");
        _fireNode4 = GetNode<Node2D>("FireSlot4/FireHolder4");
        _explosionParticles = GetNode<GpuParticles2D>("ExplosionParticles");
        _damageParticles = GetNode<GpuParticles2D>("DamageParticles");
        _emberParticles = GetNode<GpuParticles2D>("EmberParticles");
        _thinEmberParticles = GetNode<GpuParticles2D>("ThinEmberParticles");

        _fireNode1.Visible = false;
        _fireNode2.Visible = false;
        _fireNode3.Visible = false;
        _fireNode4.Visible = false;

        _explosionParticles.Emitting = false;
        _explosionParticles.OneShot = true;

        _damageParticles.Emitting = false;
        _damageParticles.OneShot = true;

        _emberParticles.Emitting = false;
        _thinEmberParticles.Emitting = false;

        Connect(SpineSprite.SignalName.AnimationEvent, Callable.From<GodotObject, GodotObject, GodotObject, GodotObject>(OnAnimationEvent));

        OnBurningEnd();

        GetAnimationState().SetAnimation("idle_loop");
    }

    private void OnAnimationEvent(GodotObject _, GodotObject __, GodotObject ___, GodotObject spineEvent)
    {
        switch (((SpineEvent)spineEvent).GetData().GetEventName())
        {
            case "explode":
                OnExplode();
                break;

            case "take_damage":
                OnTakeDamage();
                break;

            case "burning_start":
                OnBurningStart();
                break;

            case "embers_start":
                OnEmbersStart();
                break;

            case "thin_embers_start":
                OnThinEmbersStart();
                break;

            case "burning_end":
                OnBurningEnd();
                break;

            case "embers_end":
                OnEmbersEnd();
                break;

            case "thin_embers_end":
                OnThinEmbersEnd();
                break;
        }
    }

    private void OnExplode()
    {
        _explosionParticles.Restart();
    }

    private void OnTakeDamage()
    {
        _damageParticles.Restart();
    }

    private void OnBurningStart()
    {
        _fireNode1.Visible = true;
        _fireNode2.Visible = true;
        _fireNode3.Visible = true;
        _fireNode4.Visible = true;
    }

    private void OnEmbersStart()
    {
        _emberParticles.Restart();
    }

    private void OnThinEmbersStart()
    {
        _thinEmberParticles.Restart();
    }

    private void OnBurningEnd()
    {
        _fireNode1.Visible = false;
        _fireNode2.Visible = false;
        _fireNode3.Visible = false;
        _fireNode4.Visible = false;
    }

    private void OnEmbersEnd()
    {
        _emberParticles.Emitting = false;
    }

    private void OnThinEmbersEnd()
    {
        _thinEmberParticles.Emitting = false;
    }
}
