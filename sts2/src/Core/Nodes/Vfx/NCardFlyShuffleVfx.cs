using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// Another card soul vfx when we have no card.
/// Used when the discard pile is shuffled into the draw pile.
/// </summary>
public partial class NCardFlyShuffleVfx : Control
{
    private NCardTrailVfx? _vfx;
    private CardPile _endPile = default!;

    private Tween? _fadeOutTween;
    private bool _vfxFading;

    private Vector2 _startPos;
    private Vector2 _endPos;
    private float _controlPointOffset;
    private float _duration;
    private float _speed;
    private float _accel;
    private float _arcDir;
    private string _trailPath = default!;

    private readonly CancellationTokenSource _cancelToken = new();
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/vfx_card_shuffle_fly");

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NCardFlyShuffleVfx? Create(CardPile start, CardPile end, string trailPath)
    {
        if (TestMode.IsOn) return null;

        NCardFlyShuffleVfx node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NCardFlyShuffleVfx>();
        node._startPos = start.Type.GetTargetPosition(null);
        node._endPos = end.Type.GetTargetPosition(null);
        node._endPile = end;
        node._trailPath = trailPath;

        return node;
    }

    public override void _Ready()
    {
        _controlPointOffset = Rng.Chaotic.NextFloat(-300f, 400f);
        _speed = Rng.Chaotic.NextFloat(1.1f, 1.25f);
        _accel = Rng.Chaotic.NextFloat(2f, 2.5f);
        _arcDir = _endPos.Y < 1080f * 0.5f ? -500f : (500f + _controlPointOffset);
        _duration = Rng.Chaotic.NextFloat(1f, 1.75f);

        _vfx = NCardTrailVfx.Create(this, _trailPath);
        if (_vfx != null)
        {
            NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(_vfx);
        }

        Node parent = GetParent();
        parent.MoveChild(this, parent.GetChildCount() - 1);

        TaskHelper.RunSafely(PlayAnim());
    }

    public override void _ExitTree()
    {
        _cancelToken.Cancel();
        _cancelToken.Dispose();
    }

    private async Task PlayAnim()
    {
        float time = 0f;

        while (time / _duration <= 1f)
        {
            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            if (_cancelToken.IsCancellationRequested) return;

            float delta = (float)GetProcessDeltaTime();
            time += _speed * delta;
            _speed += _accel * delta;

            Vector2 controlPoint = _startPos + (_endPos - _startPos) * 0.5f;

            controlPoint.Y -= _arcDir;

            GlobalPosition = MathHelper.BezierCurve(_startPos, _endPos, controlPoint, time / _duration);
            Vector2 futurePos = MathHelper.BezierCurve(_startPos, _endPos, controlPoint, (time + 0.05f) / _duration);
            Rotation = (futurePos - GlobalPosition).Angle() + Mathf.Pi * 0.5f;
        }

        _endPile.InvokeCardAddFinished();
        GlobalPosition = _endPos;

        time = 0f;

        while (time / _duration <= 1f)
        {
            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            if (_cancelToken.IsCancellationRequested) return;

            float delta = (float)GetProcessDeltaTime();
            time += _speed * delta;

            // After 25% of the way through this tween, stop emitting particles and fade out the NCardTrailVfx
            // associated with this node.
            if (time / _duration > 0.25f && !_vfxFading)
            {
                if (_vfx != null)
                {
                    _ = TaskHelper.RunSafely(_vfx.FadeOut());
                }

                _vfxFading = true;
            }

            Scale = Vector2.One * Mathf.Max(Mathf.Lerp(0.1f, -0.1f, time / _duration), 0f);
        }

        _fadeOutTween = CreateTween();
        _fadeOutTween.TweenProperty(this, "modulate:a", 0f, 0.8f);

        await Task.Delay(800);
        this.QueueFreeSafely();
    }
}
