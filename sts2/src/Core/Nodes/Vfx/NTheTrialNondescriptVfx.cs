using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NTheTrialNondescriptVfx : Node2D
{
	public static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/the_trial_nondescript_vfx");

	public static NTheTrialNondescriptVfx? Create()
	{
		if (TestMode.IsOn) return null;

		return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NTheTrialNondescriptVfx>();
	}

	public override void _Ready()
	{
		Position = new Vector2(292f, 68f);
	}
}
