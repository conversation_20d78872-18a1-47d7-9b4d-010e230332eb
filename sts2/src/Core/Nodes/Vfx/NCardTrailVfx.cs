using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// Holds both the Sprites (the card image + particle effects) and the Trails for the "card trail vfx".
/// We need these detached so that the Trail nodes' parents don't affect the trail's positions.
/// </summary>
public partial class NCardTrailVfx : Node2D
{
    private Control _nodeToFollow = default!;
    private Node2D _sprites = default!;
    private bool _updateSprites = true;

    public static NCardTrailVfx? Create(Control card, string characterTrailPath)
    {
        if (TestMode.IsOn) return null;

        NCardTrailVfx node = PreloadManager.Cache.GetScene(characterTrailPath).Instantiate<NCardTrailVfx>();
        node._nodeToFollow = card;
        return node;
    }

    public override void _Ready()
    {
        // Debug hides this vfx
        if (NCombatUi.IsDebugHidingPlayContainer)
        {
            Visible = false;
        }

        _sprites = GetNode<Node2D>("Sprites");
        _sprites.Modulate = StsColors.transparentWhite;

        // Scales down the card sprite so it doesn't peek out of the edges of the draw/discard pile UI assets
        Tween tween = CreateTween().SetParallel();
        tween.TweenProperty(_sprites, "scale", Vector2.One * 0.5f, 0.5f)
            .SetEase(Tween.EaseType.In)
            .SetTrans(Tween.TransitionType.Cubic)
            .SetDelay(0.25f);
        tween.TweenProperty(_nodeToFollow, "modulate:a", 0.75f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(_sprites, "modulate:a", 1f, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
    }

    public override void _Process(double delta)
    {
        if (!_updateSprites) return;

        GlobalPosition = _nodeToFollow.GlobalPosition;
        Rotation = _nodeToFollow.Rotation;
    }

    public async Task FadeOut()
    {
        _updateSprites = false;

        Tween tween = CreateTween().SetParallel();
        tween.TweenProperty(this, "modulate:a", 0f, 0.5f);
        StopParticles(tween);

        await ToSignal(tween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }

    /// <summary>
    /// Prevents the trail from emitting a ton of sparks upon reaching destination.
    /// </summary>
    private void StopParticles(Tween tween)
    {
        foreach (Node n in _sprites.GetChildren())
        {
            if (n is CpuParticles2D particles2D)
            {
                tween.TweenProperty(particles2D, "amount", 1, 0.5f);
            }
        }
    }
}
