using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NHitSparkVfx : Node2D
{
    private static string ScenePath => SceneHelper.GetScenePath("vfx/hit_spark_vfx");

    [Export]
    private GpuParticles2D _standardSparks = default!;

    [Export]
    private GpuParticles2D _smallSparks = default!;

    private NCreature _creatureNode = default!;

    public static IEnumerable<string> AssetPaths => [ScenePath];

    public static NHitSparkVfx? Create(Creature target)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(target);
        if (creatureNode is not { IsInteractable: true }) return null;

        NHitSparkVfx vfx = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NHitSparkVfx>();

        vfx._creatureNode = creatureNode;

        return vfx;
    }

    public override void _Ready()
    {
        GlobalPosition = _creatureNode.VfxSpawnPosition;
        _standardSparks.Emitting = true;
        _smallSparks.Emitting = true;
        TaskHelper.RunSafely(FlashAndFree());
    }

    private async Task FlashAndFree()
    {
        await ToSignal(_smallSparks, GpuParticles2D.SignalName.Finished);
        this.QueueFreeSafely();
    }
}
