using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// Renders 3 additive blended images of a relic above a creature
/// </summary>
public partial class NRelicFlashVfx : Control
{
    private const string _scenePath = "res://scenes/vfx/relic_flash_vfx.tscn";

    private TextureRect _sprite = default!;
    private TextureRect _sprite2 = default!;
    private TextureRect _sprite3 = default!;

    private static readonly Vector2 _targetScale = Vector2.One * 1.25f;
    private readonly CancellationTokenSource _cancelToken = new();
    private RelicModel? _relic;
    private Creature? _target;

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NRelicFlashVfx? Create(RelicModel relic)
    {
        if (TestMode.IsOn) return null;

        NRelicFlashVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NRelicFlashVfx>();
        vfx._relic = relic;

        return vfx;
    }

    public static NRelicFlashVfx? Create(RelicModel relic, Creature target)
    {
        NRelicFlashVfx? vfx = Create(relic);
        if (vfx == null) return null;

        vfx._target = target;

        return vfx;
    }

    public override void _Ready()
    {
        _sprite = GetNode<TextureRect>("Image1");
        _sprite2 = GetNode<TextureRect>("Image2");
        _sprite3 = GetNode<TextureRect>("Image3");

        if (_target != null)
        {
            GlobalPosition = NCombatRoom.Instance!.GetCreatureNode(_target)!.GetTopOfHitbox();
        }

        TaskHelper.RunSafely(StartVfx());
    }

    public override void _ExitTree()
    {
        _cancelToken.Cancel();
    }

    private async Task StartVfx()
    {
        if (_cancelToken.Token.IsCancellationRequested) return;

        _sprite.Texture = _relic!.Icon;
        _sprite2.Texture = _relic.Icon;
        _sprite3.Texture = _relic.Icon;

        Position += new Vector2(0f, 64f);
        Tween tween = CreateTween().SetParallel();
        tween.TweenProperty(this, "position:y", Position.Y - 64f, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        tween.TweenProperty(_sprite, "modulate:a", 1f, 0.01);
        tween.TweenProperty(_sprite, "scale", _targetScale, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        tween.TweenProperty(_sprite, "modulate:a", 0f, 1.5)
            .SetDelay(0.01);

        tween.TweenProperty(_sprite2, "modulate:a", 1f, 0.01)
            .SetDelay(0.2);
        tween.TweenProperty(_sprite2, "scale", _targetScale, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic)
            .SetDelay(0.2);
        tween.TweenProperty(_sprite2, "modulate:a", 0f, 1.5)
            .SetDelay(0.21);

        tween.TweenProperty(_sprite3, "modulate:a", 1f, 0.01)
            .SetDelay(0.4);
        tween.TweenProperty(_sprite3, "scale", _targetScale, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic)
            .SetDelay(0.4);
        tween.TweenProperty(_sprite3, "modulate:a", 0f, 1.5)
            .SetDelay(0.41);

        await ToSignal(tween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }
}
