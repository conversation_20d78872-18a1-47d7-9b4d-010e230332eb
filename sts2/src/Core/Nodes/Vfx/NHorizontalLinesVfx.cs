using Godot;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NHorizontalLinesVfx : GpuParticles2D
{
    public static IEnumerable<string> AssetPaths => [_scenePath];
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/whole_screen/horizontal_lines_vfx");
    private double _duration;
    private ParticleProcessMaterial _mat = default!;
    private bool _isMovingRight;

    public static NHorizontalLinesVfx? Create(Color color, double duration = 2.0, bool movingRightwards = true)
    {
        if (TestMode.IsOn) return null;

        // Due to the nature of additive blend it's hard to get good colors. Consider these pre-tested colors
        // Red: ed0023c0
        // Blue: ccf2ff80
        // Green: c8ffb360
        // White: ffffff80

        NHorizontalLinesVfx vfx = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NHorizontalLinesVfx>();
        vfx._duration = Mathf.Max(1.0, duration); // Minimum duration for this VFX is 1 second
        vfx._mat = (ParticleProcessMaterial)vfx.ProcessMaterial;
        vfx._mat.Color = color;
        vfx._isMovingRight = movingRightwards;

        return vfx;
    }

    public override void _Ready()
    {
        // Set the bounds of the particle generator box here
        Control parent = GetParent<Control>();
        _mat.EmissionShapeOffset = new Vector3(-500f, parent.Size.Y * 0.5f, 0f);
        _mat.EmissionShapeScale = new Vector3(200f, parent.Size.Y * 0.5f, 1f);

        // Flip the Vfx if we want it to go from left -> right
        if (!_isMovingRight)
        {
            RotationDegrees = 180f;
            Position = new Vector2(parent.Size.X, parent.Size.Y);
        }

        TaskHelper.RunSafely(PlayAnim());
    }

    private async Task PlayAnim()
    {
        Modulate = StsColors.transparentWhite;
        Tween tween = CreateTween().SetParallel();

        // Fade In
        tween.TweenProperty(this, "modulate:a", 1f, 0.45);

        // The length of this VFX can be extended (as is the case with Whirlwind)
        tween.Chain();
        tween.TweenInterval(_duration - 0.9);
        tween.Chain();

        // Fade Out
        tween.TweenProperty(this, "modulate:a", 0f, 0.45);

        await ToSignal(tween, Tween.SignalName.Finished);
    }
}
