using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NDecimillipedeRocksVfx : Node2D
{
    [Export] private SpineSprite[] _rocks = default!;
    private readonly CancellationTokenSource _cancelToken = new();

    public override void _Ready()
    {
        TaskHelper.RunSafely(Play(_cancelToken.Token));
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        _cancelToken.Cancel();
    }

    private async Task Play(CancellationToken cancellationToken)
    {
        foreach (SpineSprite rock in _rocks)
        {
            await Task.Delay(Rng.Chaotic.NextInt(100, 201), cancellationToken);
            rock.GetAnimationState().SetAnimation($"fall{Rng.Chaotic.NextInt(1, 5)}", false);
        }

        await Task.Delay(5000, cancellationToken);
        if (cancellationToken.IsCancellationRequested) return;
        this.QueueFreeSafely();
    }
}
