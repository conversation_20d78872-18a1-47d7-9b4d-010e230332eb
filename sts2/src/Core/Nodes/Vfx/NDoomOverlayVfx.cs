using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

/// <summary>
/// A simple vfx which renders a colorRect to tint the entire screen using an additive blend.
/// </summary>
public partial class NDoomOverlayVfx : BackBufferCopy
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("vfx/doom_overlay_vfx");

    private static NDoomOverlayVfx? _instance;

    private Tween? _tween;

    public static NDoomOverlayVfx? GetOrCreate()
    {
        if (TestMode.IsOn) return null;

        // If there is already a Doom overlay, reset the animation for that one instead.
        if (_instance != null)
        {
            _instance.PlayVfx();
        }
        else
        {
            _instance = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NDoomOverlayVfx>();
        }

        return _instance;
    }

    public override void _Ready()
    {
        Modulate = Colors.Transparent;

        // we have to set the size manually because BackBufferCopy is not a Control node
        GetNode<Control>("%Rect").Size = GetViewportRect().Size;
        PlayVfx();
    }

    private void PlayVfx()
    {
        // Since this triggers while a creature is dying, this lets us be resilient to timing issues and exit early if
        // this node has already been destroyed due to their death.
        if (!IsInstanceValid(this)) return;

        _tween?.Kill();

        _tween = CreateTween();
        _tween.TweenProperty(this, "modulate:a", 1f, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _tween.TweenInterval(1.2f);
        _tween.TweenProperty(this, "modulate:a", 0f, 0.5f);
        _tween.TweenCallback(Callable.From(() => { _instance = null; }));
        _tween.Finished += OnTweenFinished;
    }

    private void OnTweenFinished()
    {
        this.QueueFreeSafely();
    }
}
