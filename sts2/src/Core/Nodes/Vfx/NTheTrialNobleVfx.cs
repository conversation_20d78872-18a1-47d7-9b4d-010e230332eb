using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Vfx;

public partial class NTheTrialNobleVfx : Node2D
{
    public static string ScenePath => SceneHelper.GetScenePath("vfx/whole_screen/the_trial_noble_vfx");

    public static NTheTrialNobleVfx? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(ScenePath).Instantiate<NTheTrialNobleVfx>();
    }

    public override void _Ready()
    {
        Position = new Vector2(292f, 68f);
    }
}
