using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Relics;

/// <summary>
/// A basic relic display. In comparison with the top-bar relic holder, this cannot flash and never displays amounts.
/// It automatically supports hovering tweens and showing hovertips, but clicks must be handled externally.
/// Used in climb history, multiplayer expanded state, the relic collection, and the relic unlock screen.
/// </summary>
public partial class NRelicBasicHolder : NButton
{
    private static readonly string _scenePath = SceneHelper.GetScenePath("relics/relic_basic_holder");

    private NRelic _relic = default!;

    private Tween? _hoverTween;
    private RelicModel _model = default!;

    public NRelic Relic => _relic;

    public static NRelicBasicHolder? Create(RelicModel relic)
    {
        if (TestMode.IsOn) return null;

        NRelicBasicHolder node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NRelicBasicHolder>();
        node.Name = $"NRelicBasicHolder-{relic.Id}";
        node._model = relic;

        return node;
    }

    public override void _Ready()
    {
        ConnectSignals();

        _relic = GetNode<NRelic>("%Relic");
        _relic.Model = _model;
    }

    public override void _ExitTree()
    {
        _hoverTween?.Kill();
    }

    protected override void OnFocus()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween();
        _hoverTween.TweenProperty(_relic.Icon, "scale", Vector2.One * 1.25f, 0.05);

        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _relic.Model.HoverTips);
        tip.SetAlignmentForRelic(_relic);
    }

    protected override void OnUnfocus()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween();
        _hoverTween.TweenProperty(_relic.Icon, "scale", Vector2.One, 1.0)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        NHoverTipSet.Remove(this);
    }
}
