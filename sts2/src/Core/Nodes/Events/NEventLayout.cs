using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;
using MegaRichTextLabel = MegaCrit.Sts2.addons.mega_text.MegaRichTextLabel;

namespace MegaCrit.Sts2.Core.Nodes.Events;

public partial class NEventLayout : Control
{
    private const string _ancientScenePath = "res://scenes/events/ancient_event_layout.tscn";
    private const string _defaultScenePath = "res://scenes/events/default_event_layout.tscn";

    public static PackedScene CreateAncientLayoutScene() => PreloadManager.Cache.GetScene(_ancientScenePath);
    public static PackedScene CreateDefaultLayoutScene() => PreloadManager.Cache.GetScene(_defaultScenePath);
    public NEventOptionButton[] Options => _optionsContainer.GetChildren().OfType<NEventOptionButton>().ToArray();
    private VBoxContainer _optionsContainer = default!;
    public Control VfxContainer { get; private set; } = default!;
    private EventModel _event = default!;
    private TextureRect? _portrait;
    private NAncientBgContainer? _ancientBgContainer;
    private MegaLabel _title = default!;
    protected MegaRichTextLabel _description = default!;
    private MegaLabel _sharedEventLabel = default!;
    protected Tween? _descriptionTween;

    // Debug
    private static bool _isDebugUiVisible;

    public static IEnumerable<string> AssetPaths => [_defaultScenePath, _ancientScenePath];

    public override void _Ready()
    {
        _portrait = GetNodeOrNull<TextureRect>("%Portrait");
        _ancientBgContainer = GetNodeOrNull<NAncientBgContainer>("%AncientBgContainer");

        _title = GetNode<MegaLabel>("%Title");
        _description = GetNode<MegaRichTextLabel>("%Description");
        _description.Text = string.Empty;

        VfxContainer = GetNode<Control>("%VfxContainer");
        _sharedEventLabel = GetNode<MegaLabel>("%SharedEventLabel");
        _optionsContainer = GetNode<VBoxContainer>("%OptionsContainer");

        ApplyDebugUiVisibility();
    }

    public override void _EnterTree()
    {
        ClimbManager.Instance.EventSynchronizer.PlayerVoteChanged += OnPlayerVoteChanged;
    }

    public override void _ExitTree()
    {
        ClimbManager.Instance.EventSynchronizer.PlayerVoteChanged -= OnPlayerVoteChanged;
    }

    public void SetEvent(EventModel eventModel)
    {
        _event = eventModel;
        eventModel.OnRoomEnter();
    }

    public void SetAncientBackgroundScene(PackedScene scene)
    {
        if (_ancientBgContainer == null) throw new InvalidOperationException("Trying to set an ancient background scene in an event layout that doesn't have one.");

        foreach (Node node in _ancientBgContainer.GetChildren())
        {
            _ancientBgContainer.RemoveChildSafely(node);
        }

        _ancientBgContainer.AddChildSafely(scene.Instantiate<Control>());
    }

    public void SetPortrait(Texture2D portrait)
    {
        if (_portrait == null) throw new InvalidOperationException("Trying to set a portrait in an event layout that doesn't have one.");

        _portrait.Texture = portrait;
    }

    /// <summary>
    /// Adds a child node that's anchored to the portrait. Usually you'd want to add it to VfxContainer, but you can use
    /// this method instead if your VFX's position is dependent on the portrait (usually due to resolution stuff)
    /// </summary>
    /// <param name="vfx">VFX to add</param>
    public void AddVfxAnchoredToPortrait(Node? vfx)
    {
        _portrait!.AddChildSafely(vfx);
    }

    /// <summary>
    /// Removes ALL child nodes from the Portrait, vfx or not.
    /// Useful for swapping out vfx if an event has multiple portraits.
    /// </summary>
    public void RemoveNodesOnPortrait()
    {
        foreach (Node childNode in _portrait!.GetChildren())
        {
            _portrait!.RemoveChildSafely(childNode);
        }
    }

    public void SetTitle(string title)
    {
        _title.Text = title;
    }

    public void SetDescription(string description)
    {
        string text = $"[center]{description}[/center]";
        _description.SetTextAutoSize(text);
        AnimateIn();
    }

    protected virtual void AnimateIn()
    {
        _description.Modulate = StsColors.transparentWhite;
        _sharedEventLabel.Modulate = StsColors.transparentWhite;

        _descriptionTween?.Kill();
        _descriptionTween = CreateTween().SetParallel();

        if (SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast)
        {
            _descriptionTween.TweenInterval(0.2);
        }
        else
        {
            _descriptionTween.TweenInterval(0.5);
        }

        _descriptionTween.Chain();

        _descriptionTween.TweenProperty(_title, "modulate", Colors.White, 0.5);
        _descriptionTween.TweenProperty(_description, "modulate", Colors.White, 0.5)
            .SetDelay(0.25);
        _descriptionTween.TweenProperty(_sharedEventLabel, "modulate", Colors.White, 0.5);
    }

    public void ClearOptions()
    {
        foreach (Node option in _optionsContainer.GetChildren().ToList())
        {
            _optionsContainer.RemoveChildSafely(option);
            option.QueueFreeSafely();
        }
    }

    public void AddOptions(IEnumerable<EventOption> options)
    {
        _sharedEventLabel.Visible =
            _event is { IsShared: true, IsFinished: false } &&
            _event.Owner!.ClimbState.Players.Count > 1;

        foreach (EventOption option in options)
        {
            NEventOptionButton button = NEventOptionButton.Create(_event, option, _optionsContainer.GetChildCount());
            _optionsContainer.AddChildSafely(button);
            button.RefreshVotes();
        }
    }

    public async Task FlashOptionAfterChosen(EventOption option)
    {
        NEventOptionButton? chosenButton = null;

        // Disable all buttons and look for the button with the chosen option
        foreach (NEventOptionButton button in _optionsContainer.GetChildren().OfType<NEventOptionButton>())
        {
            button.Disable();

            if (button.Option == option)
            {
                chosenButton = button;
            }
            else
            {
                button.GrayOut();
            }
        }

        // Flash confirmation on the chosen one. Do NOT do this in the above loop, it will cause the disable on buttons
        // after the chosen one to happen after the await!
        if (chosenButton != null)
        {
            await chosenButton.FlashConfirmation();
        }
    }

    /// <summary>
    /// Called during a shared event when a player changes the option they voted on.
    /// </summary>
    private void OnPlayerVoteChanged(Player player)
    {
        foreach (NEventOptionButton button in _optionsContainer.GetChildren().OfType<NEventOptionButton>())
        {
            button.RefreshVotes();
        }
    }

    public override void _Input(InputEvent inputEvent)
    {
        // F1 key hides the rest site UI
        if (inputEvent.IsActionReleased(DebugHotkey.hideEventUi))
        {
            _isDebugUiVisible = !_isDebugUiVisible;
            ApplyDebugUiVisibility();
            NGame.Instance!.AddChildSafely(NFullscreenTextVfx.Create(_isDebugUiVisible ? "Hide Event UI" : "Show Event UI")!);
        }
    }

    private void ApplyDebugUiVisibility()
    {
        // Invisible...
        if (_isDebugUiVisible)
        {
            _optionsContainer.Visible = false;
            _title.Modulate = Colors.Transparent; // NOTE: Visible = false/true makes the Ancient rooms broken
            _description.Visible = false;
        }
        // Visible!
        else
        {
            _optionsContainer.Visible = true;
            _description.Visible = true;
        }
    }
}
