using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.GameOverScreen;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;

namespace MegaCrit.Sts2.Core.Nodes;

/// <summary>
/// The root Node which contains reference to all other game system Nodes in a given climb.
/// For example: Climb.Node.TopBar.Settings can get you access to the TopBarSettingsButton node.
/// </summary>
public partial class NClimb : Control
{
    private const string _scenePath = "res://scenes/climb.tscn";
    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NClimb? Instance => NGame.Instance?.CurrentClimbNode;

    private ClimbState _state = default!;
    private NSceneContainer _roomContainer = default!;
    private Button _testButton = default!;
    private Label _seedLabel = default!;

    public NVictoryRoom? VictoryRoom => _roomContainer.CurrentScene as NVictoryRoom;
    public NCombatRoom? CombatRoom => _roomContainer.CurrentScene as NCombatRoom;
    public NFinishedCombatRoom? FinishedCombatRoom => _roomContainer.CurrentScene as NFinishedCombatRoom;
    public NEventRoom? EventRoom => _roomContainer.CurrentScene as NEventRoom;
    public NRestSiteRoom? RestSiteRoom => _roomContainer.CurrentScene as NRestSiteRoom;
    public NMerchantRoom? MerchantRoom => _roomContainer.CurrentScene as NMerchantRoom;

    public NGlobalUi GlobalUi { get; private set; } = default!;

    public NClimbMusicController ClimbMusicController { get; private set; } = default!;
    public ScreenStateTracker ScreenStateTracker { get; private set; } = default!;

    public static NClimb Create(ClimbState state)
    {
        NClimb node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NClimb>();
        node._state = state;
        return node;
    }

    public override void _Ready()
    {
        _roomContainer = GetNode<NSceneContainer>("%RoomContainer");
        GlobalUi = GetNode<NGlobalUi>("%GlobalUi");
        GlobalUi.Initialize(_state);
        ScreenStateTracker = new ScreenStateTracker(GlobalUi.MapScreen, GlobalUi.CapstoneContainer, GlobalUi.Overlays);
        GlobalUi.MapScreen.SetMap(_state.Map, _state.Rng.Seed, true);

        ClimbMusicController = GetNode<NClimbMusicController>("%ClimbMusicController");
        ClimbMusicController.SetClimbState(_state);
        ClimbMusicController.UpdateMusic();

        _seedLabel = GetNode<Label>("%DebugSeed");
        _seedLabel.Text = _state.Rng.StringSeed;
    }

    public override void _Process(double delta)
    {
        ClimbManager.Instance.NetService.Update();
    }

    public override void _Notification(int what)
    {
        if (what == NotificationWMCloseRequest)
        {
            ClimbManager.Instance.CleanUp(false);
        }
    }

    public void SetCurrentRoom(Control? node)
    {
        if (node == null) return;
        _roomContainer.SetCurrentScene(node);
    }

    public void ShowGameOverScreen()
    {
        NCapstoneContainer.Instance!.Close();
        NMapScreen.Instance!.Close();
        NOverlayStack.Instance!.Push(NGameOverScreen.Create(_state)!);
    }
}
