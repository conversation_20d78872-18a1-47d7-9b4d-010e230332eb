using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Rooms;

/// <summary>
/// This room exists specifically for the case where a player finishes an encounter, saves and exits before
/// choosing their next room on the map, then loads the climb back up later on.
/// In this case, we want to "fast forward" the combat to the end (but before rewards were chosen) so they don't have
/// to play the combat again.
/// To keep this code separate from actual combat code, we move them to a separate "finished-combat" room where we
/// just display the current act's background and show the rewards screen.
/// </summary>
public partial class NVictoryRoom : Control
{
    public static NVictoryRoom? Instance => NClimb.Instance?.VictoryRoom;

    private IClimbState _climbState = default!;

    private readonly List<NCreature> _creatureNodes = [];
    private Control _creatureContainer = default!;

    private const string _scenePath = "res://scenes/rooms/victory_room.tscn";
    private Control _bgContainer = default!;
    private NProceedButton _proceedButton = default!;
    private Control _waitingForOtherPlayersOverlay = default!;
    public IEnumerable<NCreature> CreatureNodes => _creatureNodes;

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NVictoryRoom? Create(IClimbState climbState)
    {
        if (TestMode.IsOn) return null;

        NVictoryRoom victoryRoom = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NVictoryRoom>();
        victoryRoom._climbState = climbState;

        return victoryRoom;
    }

    public override void _Ready()
    {
        _creatureContainer = GetNode<Control>("%CreatureContainer");
        _proceedButton = GetNode<NProceedButton>("%ProceedButton");
        _waitingForOtherPlayersOverlay = GetNode<Control>("%WaitingForOtherPlayers");

        foreach (Player player in _climbState.Players)
        {
            NCreature node = NCreature.Create(player.Creature)!;
            _creatureNodes.Add(node);
            _creatureContainer.AddChildSafely(node);

            // No HP bar
            node.ToggleIsInteractable(false);
        }

        float playerPadding = Math.Min(250f, (Size.X - 200f) / (_creatureNodes.Count - 1));
        float xPos = (_creatureNodes.Count - 1) * -playerPadding * 0.5f;

        // Align players in a row, centered on the screen
        for (int i = 0; i < _creatureNodes.Count; i++)
        {
            _creatureNodes[i].Position = new Vector2(xPos, 200f);
            _creatureNodes[i].Visuals.SpineBody!.GetAnimationState().SetAnimation("relaxed_loop");
            xPos += playerPadding;
        }

        NMapScreen.Instance!.SetTravelEnabled(false);

        _bgContainer = GetNode<Control>("%BgContainer");

        // Setup the background
        _bgContainer.AddChildSafely(NCombatBackground.Create(_climbState.Act.GenerateBackgroundAssets()));

        _proceedButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnProceedButtonPressed));
        _proceedButton.UpdateText(NProceedButton.ProceedLoc);

        _proceedButton.Enable();
    }

    private void OnProceedButtonPressed(NButton _)
    {
        _proceedButton.Disable();

        if (ClimbManager.Instance.ActChangeSynchronizer.IsWaitingForOtherPlayers())
        {
            _waitingForOtherPlayersOverlay.Visible = true;
        }

        ClimbManager.Instance.ActChangeSynchronizer.SetLocalPlayerReady();
    }

    public void VictoryTriggered()
    {
        _waitingForOtherPlayersOverlay.Visible = false;
    }

    public NCreature GetCreatureNode(Creature creature)
    {
        return _creatureNodes.First(c => c.Entity == creature);
    }
}
