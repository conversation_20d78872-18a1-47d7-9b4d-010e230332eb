using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Events;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Rooms;

public partial class NEventRoom : Control, IFocusableScreen
{
    public static NEventRoom? Instance => NClimb.Instance?.EventRoom;

    private EventModel _event = default!;
    private NSceneContainer _eventContainer = default!;
    private const string _scenePath = "res://scenes/rooms/event_room.tscn";

    public NEventLayout? Layout => _eventContainer.CurrentScene as NEventLayout;
    public Control VfxContainer { get; private set; } = default!;

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NEventRoom? Create(EventModel eventModel)
    {
        if (TestMode.IsOn) return null;

        NEventRoom eventRoom = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NEventRoom>();
        eventRoom._event = eventModel;

        return eventRoom;
    }

    public override void _Ready()
    {
        _eventContainer = GetNode<NSceneContainer>("%EventContainer");
        NGame.Instance!.SetScreenShakeTarget(_eventContainer);
        PackedScene eventScene;

        if (_event.LayoutType == EventLayoutType.Ancient)
        {
            eventScene = NEventLayout.CreateAncientLayoutScene();
        }
        else
        {
            // TODO: Handle fully custom scenes
            eventScene = NEventLayout.CreateDefaultLayoutScene();
        }

        _eventContainer.SetCurrentScene(eventScene.Instantiate<Control>());

        if (_event is AncientEventModel ancientEventModel)
        {
            NClimb.Instance?.EventRoom?.AddChildSafely(NAncientNameBanner.Create(ancientEventModel));

            // The player is healed upon entering an ancient, but it happens too early for the player to see it.
            // Play the heal VFX after the room fully loads in and the fade in finishes.
            TaskHelper.RunSafely(PlayHealVfxAfterFadeIn(_event.Owner!, ancientEventModel.HealedAmount));
        }

        // TODO: Handle custom event scenes with no layout (like if we had a wheel spinning mini-game or something).
        VfxContainer = Layout!.VfxContainer;
        TaskHelper.RunSafely(Setup());
        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _ExitTree()
    {
        NGame.Instance!.ClearScreenShakeTarget();
        NControllerManager.Instance!.RemoveScreen(this);
        _event.StateChanged -= RefreshEventState;
    }

    private async Task Setup()
    {
        if (_event.Owner == null) throw new InvalidOperationException($"Event must be started before passed to NEventRoom!");
        Layout!.SetEvent(_event);

        switch (_event.LayoutType)
        {
            case EventLayoutType.Default:
                SetPortrait(_event.CreateInitialPortrait());
                break;
            case EventLayoutType.Ancient:
                SetBackgroundScene(_event.CreateBackgroundScene());
                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        SetTitle(_event.Title);

        _event.StateChanged += RefreshEventState;

        // Wait half a second for the fade out to finish before continuing
        await Cmd.Wait(0.2f);

        SetDescription(_event.Description!);
        SetOptions(_event);
    }

    private async Task PlayHealVfxAfterFadeIn(Player player, decimal healAmount)
    {
        await Cmd.Wait(0.2f);
        PlayerEventHealVfx.Play(player, healAmount);
    }

    private void SetBackgroundScene(PackedScene scene) => Layout!.SetAncientBackgroundScene(scene);
    public void SetPortrait(Texture2D portrait) => Layout!.SetPortrait(portrait);
    private void SetTitle(LocString title) => Layout!.SetTitle(title.GetFormattedText());

    private void SetDescription(LocString description)
    {
        CharacterModel character = _event.Owner!.Character;

        description.Add("pronounObject", character.PronounObject);
        description.Add("possessiveAdjective", character.PossessiveAdjective);
        description.Add("pronounPossessive", character.PronounPossessive);
        description.Add("pronounSubject", character.PronounSubject);

        _event.DynamicVars.AddTo(description);
        Layout!.SetDescription(description.GetFormattedText());
    }

    private void SetOptions(EventModel eventModel)
    {
        Layout!.ClearOptions();
        IReadOnlyList<EventOption> options = eventModel.CurrentOptions;

        if (eventModel.IsFinished)
        {
            options = [new EventOption(Proceed, "PROCEED", false, isProceed: true)];
        }

        foreach (EventOption option in options)
        {
            option.BeforeChosen += BeforeOptionChosen;
        }

        Layout.AddOptions(options);

        if (NControllerManager.Instance!.IsFocusedScreen(this))
        {
            OnFocusScreen();
        }
    }

    /// <summary>
    /// Occurs when the local player clicks an event option.
    /// </summary>
    public void OptionButtonClicked(EventOption option, int index)
    {
        if (option.IsLocked) return;

        // Proceed event is local only and doesn't need any more processing
        if (option.IsProceed)
        {
            TaskHelper.RunSafely(option.Chosen());
            return;
        }

        // Only clear options if we are in a non-shared event. In a shared event, the event synchronizer will tell us
        // when to advance.
        if (!_event.IsShared)
        {
            Layout!.ClearOptions();
        }

        // Sync event to remote players and choose option locally
        ClimbManager.Instance.EventSynchronizer.ChooseLocalOption(index);
    }

    /// <summary>
    /// Occurs after an option is executed. This may not occur at the same time as the local player clicking the button
    /// if we are in a shared multiplayer event.
    /// </summary>
    public async Task BeforeOptionChosen(EventOption option)
    {
        if (_event.Owner!.ClimbState.Players.Count > 1 &&
            ClimbManager.Instance.EventSynchronizer.IsShared &&
            !option.IsProceed)
        {
            // In a multiplayer shared event, the event can advance without the local player taking an action, so confirm
            // which option was chosen before advancing the event state.
            await Layout!.FlashOptionAfterChosen(option);
        }
    }

    /// <summary>
    /// Called whenever an event's state changes - i.e. when an option is chosen.
    /// Note that, in multiplayer, this does not necessarily occur at the same time the local player chooses an option.
    /// For shared events, this is called after all players vote on an option.
    /// </summary>
    private void RefreshEventState(EventModel eventModel)
    {
        SetDescription(_event.Description!);
        SetOptions(_event);
    }

    public static Task Proceed()
    {
        NMapScreen.Instance!.SetTravelEnabled(true);
        NMapScreen.Instance.Open();

        return Task.CompletedTask;
    }

    public void OnFocusScreen()
    {
        // Note: This will NPE once we have a non-default-layout-based event.
        Layout!.Options.FirstOrDefault()?.TryGrabFocus();
    }

    public void OnUnfocusScreen() { }
}
