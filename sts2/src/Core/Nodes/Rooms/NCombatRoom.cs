using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Rooms;

public partial class NCombatRoom : Control, IFocusableScreen
{
    // Amount of padding we'll attempt to keep at the center of the screen for the card play zone.
    private const float _centerSafeZone = 150f;
    private const float _defaultPadding = 50f;
    private const float _minimumAutoPadding = 5f;
    private const float _minAlternatingYPos = 40f;
    private const float _maxAlternatingYPos = 60f;
    private const float _alternateYPosBeginPadding = 30f;
    private const float _yPos = 200f;

    public static NCombatRoom? Instance => NClimb.Instance?.CombatRoom;
    private readonly List<NCreature> _creatureNodes = [];
    private readonly List<NCreature> _removingCreatureNodes = [];
    public NCombatUi Ui { get; private set; } = default!;
    private Control _allyContainer = default!;
    private Control _enemyContainer = default!;

    public IEnumerable<NCreature> CreatureNodes => _creatureNodes;
    public IEnumerable<NCreature> RemovingCreatureNodes => _removingCreatureNodes;

    private const string _scenePath = "res://scenes/rooms/combat_room.tscn";

    // NOTE: Pivot point is used for scaling
    public Control SceneContainer { get; private set; } = default!;
    private Control BgContainer { get; set; } = default!;

    private NRadialBlurVfx _radialBlur = default!;

    // Container for vfx that need to spawn behind creatures but in front of the background.
    public Node BackCombatVfxContainer { get; private set; } = default!;

    public Node CombatVfxContainer { get; private set; } = default!;

    private CombatRoom _room = default!;
    private Control? EncounterSlots { get; set; }

    public static IEnumerable<string> AssetPaths => [_scenePath];

    private struct PlayerAndPets
    {
        public NCreature player;
        public List<NCreature> pets;
    }

    private Window _window = default!;

    public static NCombatRoom? Create(CombatRoom room)
    {
        if (TestMode.IsOn) return null;

        NCombatRoom node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NCombatRoom>();
        node._room = room;

        return node;
    }

    public override void _Ready()
    {
        Ui = GetNode<NCombatUi>("%CombatUi");

        SceneContainer = GetNode<Control>("%CombatSceneContainer");
        _allyContainer = GetNode<Control>("%AllyContainer");
        _enemyContainer = GetNode<Control>("%EnemyContainer");

        BackCombatVfxContainer = GetNode<Node2D>("%BackCombatVfxContainer");
        CombatVfxContainer = GetNode("%CombatVfxContainer");
        _radialBlur = GetNode<NRadialBlurVfx>("RadialBlur");

        BgContainer = GetNode<Control>("%BgContainer");
        BgContainer.AddChildSafely(_room.Encounter.CreateBackground(_room.CombatState.ClimbState.Act));

        Log.Info($"Starting combat room with encounter={_room.Encounter.Id.Entry}.");

        if (_room.Encounter.HasScene)
        {
            EncounterSlots = _room.Encounter.CreateScene();
        }

        if (EncounterSlots != null)
        {
            _enemyContainer.AddChildSafely(EncounterSlots);

            // Container is centered on screen but encounter slots expect to be positioned at top-left. We assume that
            // all encounter scenes are sized 1920x1080 (the size changes after instantiation because they're full-rect
            // anchored)
            EncounterSlots.Position -= new Vector2(1920, 1080) * 0.5f;
        }

        CombatState combatState = _room.CombatState;
        List<Creature> allies = combatState.Allies.ToList();

        foreach (Creature ally in allies)
        {
            AddCreature(ally);
        }

        PositionPlayersAndPets(
            _creatureNodes.Where(c => allies.Contains(c.Entity)).ToList(),
            _room.Encounter.GetCameraScaling(combatState),
            _room.Encounter.FullyCenterPlayers
        );

        List<Creature> enemies = combatState.Enemies.ToList();

        foreach (Creature enemy in enemies)
        {
            AddCreature(enemy);
        }

        List<NCreature> enemyNodes = _creatureNodes.Where(c => enemies.Contains(c.Entity)).ToList();

        if (EncounterSlots != null)
        {
            PositionCreaturesWithSlots(enemyNodes);
        }
        else
        {
            PositionEnemies(enemyNodes, _room.Encounter.GetCameraScaling(combatState));
        }

        RandomizeEnemyScalesAndHues();

        SceneContainer.Scale = Vector2.One * _room.Encounter.GetCameraScaling(combatState);
        SceneContainer.Position += _room.Encounter.GetCameraOffset(combatState);

        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(AdjustCreatureScaleForAspectRatio));
        AdjustCreatureScaleForAspectRatio();

        // this needs to sit below the camera offset line or else the offset will be off
        NGame.Instance!.SetScreenShakeTarget(SceneContainer);
        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _EnterTree()
    {
        CombatManager.Instance.CombatEnded += RestrictControllerNavigation;
        CombatManager.Instance.CombatWon += RestrictControllerNavigation;
    }

    public override void _ExitTree()
    {
        NControllerManager.Instance!.RemoveScreen(this);
        NGame.Instance!.ClearScreenShakeTarget();

        CombatManager.Instance.CombatEnded -= RestrictControllerNavigation;
        CombatManager.Instance.CombatWon -= RestrictControllerNavigation;
    }

    private void AdjustCreatureScaleForAspectRatio()
    {
        int padding = 15;
        // reset size/positions so we can get accurate bounds
        _allyContainer.Scale = Vector2.One;
        _enemyContainer.Scale = Vector2.One;
        _enemyContainer.Position = SceneContainer.Size * 0.5f;

        float rightBound = 0;
        foreach (NCreature creatureNode in _creatureNodes)
        {
            rightBound = Math.Max(creatureNode.GlobalPosition.X + creatureNode.Hitbox.Size.X * 0.5f * SceneContainer.Scale.X, rightBound);
        }

        rightBound += padding;

        if (rightBound > Size.X)
        {
            float scaleAmount = Size.X / rightBound;
            _allyContainer.Scale = Vector2.One * scaleAmount;
            _enemyContainer.Scale = Vector2.One * scaleAmount;

            // move enemy container a bit to the left enough so that the farthest creature
            // is no longer off-screen
            _enemyContainer.Position += Vector2.Left * (rightBound - Size.X) * scaleAmount;
        }
    }

    private void PositionCreaturesWithSlots(List<NCreature> creatures)
    {
        foreach (NCreature node in creatures)
        {
            string slotName = node.Entity.SlotName!;
            node.GlobalPosition = EncounterSlots!.GetNode<Marker2D>(slotName).GlobalPosition;
        }
    }

    private void PositionEnemies(List<NCreature> creatures, float scaling)
    {
        float refWidth = 1920f * 0.5f / scaling;
        float padding = _defaultPadding;

        // Attempt to center entities on each side based on a 1080p screen
        float totalWidthWithoutPadding = creatures.Sum(n => n.Hitbox.Size.X);
        float totalWidth = totalWidthWithoutPadding + (creatures.Count - 1) * padding;
        float targetPos = (refWidth - totalWidth) * 0.5f;
        targetPos = Math.Max(targetPos, _centerSafeZone);

        float alternatingYPos = 0f;

        if (targetPos + totalWidth > refWidth)
        {
            // If the default padding is too large, then shrink it
            padding = Math.Max((refWidth - _centerSafeZone - totalWidthWithoutPadding) / (creatures.Count - 1), _minimumAutoPadding);
            totalWidth = totalWidthWithoutPadding + (creatures.Count - 1) * padding;
            targetPos = (refWidth - totalWidth) * 0.5f;

            // If the new padding is small, then shift enemies up and down on the Y axis to try to compensate
            if (padding < _alternateYPosBeginPadding)
            {
                alternatingYPos = float.Lerp(_maxAlternatingYPos, _minAlternatingYPos, (padding - _minimumAutoPadding) / (_alternateYPosBeginPadding - _minimumAutoPadding));
            }

            // If we can't fit even with minimum padding, then warn
            if (targetPos + totalWidth > refWidth)
            {
                Log.Warn($"Creatures for current encounter ({_room.Encounter.Title.GetFormattedText()}) are being displayed off-screen because they are too wide!");
            }
        }

        for (int i = 0; i < creatures.Count; i++)
        {
            NCreature node = creatures[i];
            node.Position = new Vector2(targetPos + node.Hitbox.Size.X * 0.5f, _yPos - (i % 2 != 0 ? alternatingYPos : 0));
            targetPos += node.Hitbox.Size.X + padding;
        }
    }

    /// <summary>
    /// Places player creatures and pets in the combat room.
    /// It places players in an offset grid. Row/column count is determined by the nearest square root of the player
    /// count.
    /// Grid rows above the first one are offset successively, and we attempt to fit all the players in the available
    /// space, leaving enough padding in the center for card plays (usually, see <see cref="fullyCenterPlayers"/>).
    /// Pets are placed around players' feet.
    /// Technically we only support four players, but I figured I'd make it easy for modders to expand the number of players!
    /// </summary>
    /// <param name="creatureNodes">All player and pet nodes.</param>
    /// <param name="scaling">Scaling added by the camera.</param>
    /// <param name="fullyCenterPlayers">
    /// Should the players be fully centered in the scene?
    /// Usually false (to leave enough padding in the center for card plays), but true in certain situations like
    /// "surrounded" combats.
    /// </param>
    public static void PositionPlayersAndPets(List<NCreature> creatureNodes, float scaling, bool fullyCenterPlayers)
    {
        List<PlayerAndPets> playersAndPets = [];

        foreach (NCreature creatureNode in creatureNodes)
        {
            if (!creatureNode.Entity.IsPlayer) continue;

            PlayerAndPets playerAndPets = new() { player = creatureNode, pets = [] };

            if (LocalContext.IsMe(creatureNode.Entity))
            {
                // The local player is always the one in front
                playersAndPets.Insert(0, playerAndPets);
            }
            else
            {
                playersAndPets.Add(playerAndPets);
            }
        }

        foreach (NCreature creature in creatureNodes)
        {
            if (creature.Entity.IsPlayer) continue;
            playersAndPets.First(p => p.player.Entity.Player == creature.Entity.PetOwner).pets.Add(creature);
        }

        float refWidth = 1920f * 0.5f / scaling;
        float padding = _defaultPadding;

        int playerGridSize = (int)Math.Ceiling(Math.Sqrt(playersAndPets.Count));
        int rowCount = (int)Math.Ceiling(playersAndPets.Count / (double)playerGridSize);

        float totalWidthWithoutPadding = creatureNodes.Take(playerGridSize).Sum(n => n.Hitbox.Size.X);
        float totalWidth = totalWidthWithoutPadding + (playerGridSize - 1) * padding;
        float additionalSpaceForBackPlayers = totalWidthWithoutPadding * 0.33f;
        float xPaddingPerRow = rowCount > 1 ? additionalSpaceForBackPlayers / (rowCount - 1) : 0;
        float yPaddingPerRow = rowCount > 1 ? 120f / (rowCount - 1) : 0;
        float originXPos;

        if (fullyCenterPlayers)
        {
            originXPos = creatureNodes.First(c => c.Entity.IsPlayer).Hitbox.Size.X * -0.5f;
        }
        else
        {
            // Attempt to center player row based on a 1080p screen
            originXPos = (refWidth - totalWidth) * 0.5f;
            originXPos = Math.Max(originXPos, _centerSafeZone);

            // If the second row is full, then add 50% of a character width to the total
            if (playersAndPets.Count >= playerGridSize * 2)
            {
                totalWidthWithoutPadding += additionalSpaceForBackPlayers;
            }

            if (originXPos + totalWidth > refWidth)
            {
                // If the default padding is too large, then shrink it
                padding = (refWidth - _centerSafeZone - totalWidthWithoutPadding) / (playerGridSize - 1);
                totalWidth = totalWidthWithoutPadding + (playerGridSize - 1) * padding;
                originXPos = (refWidth - totalWidth) * 0.5f;
            }
        }

        // Place the players in an offset grid.
        for (int y = 0; y < playerGridSize; y++)
        {
            float targetXPos = originXPos + xPaddingPerRow * y;

            for (int x = 0; x < playerGridSize; x++)
            {
                int index = y * playerGridSize + x;

                if (index >= playersAndPets.Count) break;

                PlayerAndPets playerAndPets = playersAndPets[index];
                NCreature playerNode = playerAndPets.player;
                List<NCreature> pets = playerAndPets.pets;

                playerNode.Position = new Vector2(-targetXPos - playerNode.Hitbox.Size.X * 0.5f, _yPos - yPaddingPerRow * y);

                // If this is the local player, and they have Osty, Osty needs to go in a special position
                if (LocalContext.IsMe(playerNode.Entity) && playerNode.Entity.Player!.Character is Necrobinder)
                {
                    NCreature? osty = null;

                    for (int petIndex = 0; petIndex < pets.Count; petIndex++)
                    {
                        NCreature pet = pets[petIndex];

                        if (pet.Entity.Monster is Osty)
                        {
                            osty = pet;
                            pets.RemoveAt(petIndex);
                            break;
                        }
                    }

                    PositionLocalPlayerOsty(ref targetXPos, playerNode.Position.Y, playerNode, osty);
                }

                float petPadding = pets.Count > 1 ? playerNode.Hitbox.Size.X / (pets.Count - 1) : 0;

                for (int petIndex = 0; petIndex < pets.Count; petIndex++)
                {
                    NCreature pet = pets[petIndex];

                    pet.Position = new Vector2(-targetXPos + 20f - petIndex * petPadding - pet.Hitbox.Size.X * 0.5f, playerNode.Position.Y + 10f);
                }

                // Darken players that are behind the first row
                if (y > 0)
                {
                    playerAndPets.player.Visuals.Modulate = new Color(0.5f, 0.5f, 0.5f);

                    foreach (NCreature pet in pets)
                    {
                        pet.Visuals.Modulate = new Color(0.5f, 0.5f, 0.5f);
                    }
                }

                targetXPos += playerAndPets.player.Hitbox.Size.X + padding;
            }
        }

        // Sort the nodes. Players in reverse order (front rows are indexed after back rows), and pets in front of their
        // respective players
        foreach (PlayerAndPets playerAndPets in playersAndPets)
        {
            // This just sets the node's own sibling index
            playerAndPets.player.GetParent().MoveChild(playerAndPets.player, 0);

            for (int i = 0; i < playerAndPets.pets.Count; i++)
            {
                NCreature pet = playerAndPets.pets[i];
                pet.GetParent().MoveChild(pet, i + 1);

                // Remote player pets are not hoverable
                if (!LocalContext.IsMe(playerAndPets.player.Entity))
                {
                    pet.Hitbox.Visible = false;
                }
            }
        }
    }

    private static void PositionLocalPlayerOsty(ref float targetXPos, float playerYPosition, NCreature player, NCreature? osty)
    {
        // Center player and Osty at their desired location
        player.Position = player.Position with { X = player.Position.X - 150 };

        if (osty != null)
        {
            osty.Position = new Vector2(-targetXPos + 150 - osty.Hitbox.Size.X * 0.5f, playerYPosition);
        }

        // Move all characters back a small amount
        targetXPos += 100f;
    }

    public NCreature? GetCreatureNode(Creature? creature)
    {
        return creature == null ? null : CreatureNodes.FirstOrDefault(c => c.Entity == creature);
    }

    public void RemoveCreatureNode(NCreature node)
    {
        _creatureNodes.Remove(node);
        _removingCreatureNodes.Add(node);
        UpdateCreatureNavigation();

        if (GetViewport().GuiGetFocusOwner() == node.Hitbox)
        {
            _creatureNodes[0].Hitbox.TryGrabFocus();
        }

        TaskHelper.RunSafely(RemoveCreatureWhenGone(node));
    }

    private async Task RemoveCreatureWhenGone(NCreature node)
    {
        if (node.DeathAnimationTask != null)
        {
            await node.DeathAnimationTask;
        }

        _removingCreatureNodes.Remove(node);
    }

    public void AddCreature(Creature creature)
    {
        NCreature node = NCreature.Create(creature)!;
        _creatureNodes.Add(node);

        if (creature.IsPlayer || creature.PetOwner != null)
        {
            _allyContainer.AddChildSafely(node);
        }
        else
        {
            _enemyContainer.AddChildSafely(node);
        }

        if (creature.SlotName != null)
        {
            if (EncounterSlots == null)
            {
                throw new InvalidOperationException($"Creature {creature} has slot name '{creature.SlotName}' but NCombatRoom.EncounterSlots is null.");
            }

            node.GlobalPosition = EncounterSlots.GetNode<Marker2D>(creature.SlotName).GlobalPosition;
        }

        UpdateCreatureNavigation();

        if (creature.PetOwner != null)
        {
            // Reposition all pets under the player
            NCreature ownerNode = GetCreatureNode(creature.PetOwner.Creature)!;
            Player player = ownerNode.Entity.Player!;
            List<NCreature> pets = _creatureNodes.Where(c =>
                c.Entity.PetOwner == player &&
                (c.Entity.Monster is not Osty || !LocalContext.IsMe(player))
            ).ToList();

            node.GetParent().MoveChild(node, ownerNode.GetIndex() + 1);

            // If we're the local Osty, then we get a special location
            if (creature.Monster is Osty && LocalContext.IsMe(player))
            {
                node.OstyScaleToSize(creature.MaxHp, 0);
                // we do this so that osty always is layered behind necrobinder
                // so that he never blocks her no matter how big he gets.
                node.GetParent().MoveChild(node, ownerNode.GetIndex());
            }
            else
            {
                float petPadding = pets.Count > 1 ? ownerNode.Hitbox.Size.X / (pets.Count - 1) : 0;

                for (int petIndex = 0; petIndex < pets.Count; petIndex++)
                {
                    NCreature pet = pets[petIndex];

                    pet.Position = new Vector2(ownerNode.Position.X - 20f + petIndex * petPadding + pet.Hitbox.Size.X * 0.5f, ownerNode.Position.Y + 10f);

                    // Remote player pets are not hoverable
                    pet.Hitbox.Visible = LocalContext.IsMe(player);
                }

                if (ownerNode.Position.Y < _yPos - 1)
                {
                    node.Visuals.Modulate = new Color(0.5f, 0.5f, 0.5f);
                }
            }
        }
    }

    private void UpdateCreatureNavigation()
    {
        List<NCreature> creaturesNodes = _creatureNodes.OrderBy(n => n.GlobalPosition.X).ToList();

        for (int i = 0; i < creaturesNodes.Count; i++)
        {
            creaturesNodes[i].Hitbox.FocusNeighborLeft = i > 0 ? creaturesNodes[i - 1].Hitbox.GetPath() : creaturesNodes[i].Hitbox.GetPath();
            creaturesNodes[i].Hitbox.FocusNeighborRight = i < creaturesNodes.Count - 1 ? creaturesNodes[i + 1].Hitbox.GetPath() : creaturesNodes[i].Hitbox.GetPath();
            creaturesNodes[i].Hitbox.FocusNeighborTop = creaturesNodes[i].Hitbox.GetPath();
            creaturesNodes[i].Hitbox.FocusNeighborBottom = Ui.Hand.CardHolderContainer.GetPath();
        }
    }

    public void OnFocusScreen()
    {
        EnableControllerNavigation();
        Ui.Hand.OnFocus();
    }

    public void OnUnfocusScreen()
    {
        RestrictControllerNavigation([]);
        Ui.Hand.CancelAllCardPlay();
    }

    private void RestrictControllerNavigation(CombatRoom _)
    {
        RestrictControllerNavigation([]);
    }

    /// <summary>
    /// Enables controller navigation for all control nodes on the whitelist (atm we assume these are creature hitboxes).
    /// Disables controller navigation for un-whitelisted creature hitboxes and cards in hand.
    /// Use case: restrict navigation to just monsters when we are targeting them with a card.
    /// </summary>
    /// <param name="whitelist">Controls that we still want to allow input navigation for</param>
    public void RestrictControllerNavigation(IEnumerable<Control> whitelist)
    {
        foreach (NCreature creature in _creatureNodes)
        {
            Control hitbox = creature.Hitbox;
            creature.Hitbox.FocusMode = whitelist.Contains(hitbox) ? FocusModeEnum.All : FocusModeEnum.None;
        }

        // we may need to revisit this structure if we need to whitelist specific cards
        Ui.Hand.DisableControllerNavigation();
    }

    /// <summary>
    /// Enables controller navigation for creatures and cards in hand.
    /// </summary>
    public void EnableControllerNavigation()
    {
        foreach (NCreature creature in _creatureNodes)
        {
            creature.Hitbox.FocusMode = FocusModeEnum.All;
        }

        Ui.Hand.EnableControllerNavigation();
    }

    public bool IsControllerHotkeysActive => NControllerManager.Instance!.IsFocusedScreen(this) && Ui.Hand.InCardPlay == false;

    private void RandomizeEnemyScalesAndHues()
    {
        const float minScaleSize = 250;
        const float maxScaleSize = 100;
        const float minScaleVariation = 0.1f;
        const float maxScaleVariation = 0.15f;
        const float maxHueVariation = 0.05f;

        Dictionary<Type, List<NCreature>> monstersByType = [];

        foreach (NCreature creature in _creatureNodes)
        {
            if (creature.Entity.Side == CombatSide.Player) continue;
            if (creature.Entity.Monster == null) continue;

            Type monsterType = creature.Entity.Monster.GetType();
            if (!monstersByType.TryGetValue(monsterType, out List<NCreature>? list))
            {
                list = [];
                monstersByType[monsterType] = list;
            }

            list.Add(creature);
        }

        foreach (KeyValuePair<Type, List<NCreature>> pair in monstersByType)
        {
            if (pair.Value.Count == 1) continue;

            foreach (NCreature creatureNode in pair.Value)
            {
                MonsterModel monster = creatureNode.Entity.Monster!;

                // Use unscaled monster HP because we're comparing the HP to the base HP of the monster
                int monsterHp = monster.Creature.MonsterMaxHpBeforeModification!.Value;

                // Get normalized HP based on the monster's min and max HP values. Range of [-1,1] based on the mean HP
                float scaleLerp;

                // if min == max, don't have any scale variation
                if (monster.MaxInitialHp == monster.MinInitialHp)
                {
                    scaleLerp = 0;
                }
                else
                {
                    scaleLerp = ((float)(monsterHp - monster.MinInitialHp) / (monster.MaxInitialHp - monster.MinInitialHp) - 0.5f) * 2f;
                }

                scaleLerp = Math.Clamp(scaleLerp, 0, 1);

                // Get the bigger dimension of width/height
                float maxDimension = Math.Max(creatureNode.Hitbox.Size.X, creatureNode.Hitbox.Size.Y);

                // Smaller monsters need to get scaled bigger. Get scale variation between min and max based on the monster size
                float normalizedScaleVariation = Math.Clamp(Mathf.InverseLerp(minScaleSize, maxScaleSize, maxDimension), 0, 1);
                float scaleVariation = float.Lerp(minScaleVariation, maxScaleVariation, normalizedScaleVariation);

                // Scale the monster based on its HP and its initial size
                creatureNode.SetScaleAndHue(1f + scaleLerp * scaleVariation, Rng.Chaotic.NextFloat(maxHueVariation));
            }
        }
    }

    public void RadialBlur(VfxPosition vfxPosition = VfxPosition.Center)
    {
        _radialBlur.Activate(vfxPosition);
    }
}
