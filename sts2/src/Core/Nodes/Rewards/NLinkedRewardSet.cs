using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Nodes.Rewards;

public partial class NLinkedRewardSet : Control
{
    private NRewardsScreen _rewardsScreen = default!;

    public LinkedRewardSet LinkedRewardSet { get; private set; } = default!;

    private Control _rewardContainer = default!;
    private Control _chainsContainer = default!;

    private static string ScenePath => SceneHelper.GetScenePath("/rewards/linked_reward_set");

    private static string ChainImagePath => ImageHelper.GetImagePath("/ui/reward_screen/reward_chain.png");

    public static IEnumerable<string> AssetPaths => new[] { ScenePath, ChainImagePath };

    public override void _Ready()
    {
        _rewardContainer = GetNode<Control>("%RewardContainer");
        _chainsContainer = GetNode<Control>("%ChainContainer");
        Reload();
    }

    public static NLinkedRewardSet Create(LinkedRewardSet linkedReward, NRewardsScreen screen)
    {
        NLinkedRewardSet node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NLinkedRewardSet>();
        node._rewardsScreen = screen;
        node.SetReward(linkedReward);
        return node;
    }

    private void SetReward(LinkedRewardSet linkedReward)
    {
        LinkedRewardSet = linkedReward;

        if (IsNodeReady())
        {
            Reload();
        }
    }

    private void Reload()
    {
        if (!IsNodeReady()) return;

        for (int i = 0; i < LinkedRewardSet.Rewards.Count; i++)
        {
            Reward reward = LinkedRewardSet.Rewards[i];
            NRewardButton button = NRewardButton.Create(reward, _rewardsScreen);
            button.CustomMinimumSize -= Vector2.Right * 20f;
            _rewardContainer.AddChildSafely(button);
            button.Connect(NRewardButton.SignalName.RewardClaimed, Callable.From(GetReward));

            if (i < LinkedRewardSet.Rewards.Count - 1)
            {
                TextureRect chainTex = new();
                chainTex.MouseFilter = MouseFilterEnum.Ignore;
                chainTex.Texture = PreloadManager.Cache.GetCompressedTexture2D(ChainImagePath);
                chainTex.Size = Vector2.One * 50f;
                _chainsContainer.AddChildSafely(chainTex);
                chainTex.GlobalPosition = _chainsContainer.GlobalPosition;
            }
        }
    }

    private void GetReward()
    {
        _rewardsScreen.RewardCollectedFrom(this);
        LinkedRewardSet.OnSkipped();
        this.QueueFreeSafely();
    }
}
