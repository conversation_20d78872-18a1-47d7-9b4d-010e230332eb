using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.sts2.Core.Nodes.TopBar;

public partial class NTopBarHp : Control
{
    private static readonly HoverTip _hoverTip = new(
        new LocString("static_hover_tips", "HIT_POINTS.title"),
        new LocString("static_hover_tips", "HIT_POINTS.description")
    );

    // Will be null until initialized.
    private Player? _player;

    private Label _hpLabel = default!;

    public override void _Ready()
    {
        _hpLabel = GetNode<Label>("%HpLabel");
        Connect(Control.SignalName.MouseEntered, Callable.From(OnMouseEntered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnMouseExited));
    }

    public override void _ExitTree()
    {
        if (_player != null)
        {
            _player.Creature.CurrentHpChanged -= UpdateHealth;
            _player.Creature.MaxHpChanged -= UpdateHealth;
        }
    }

    public void Initialize(Player player)
    {
        _player = player;
        _player.Creature.CurrentHpChanged += UpdateHealth;
        _player.Creature.MaxHpChanged += UpdateHealth;
        UpdateHealth(0, 0);
    }

    private void UpdateHealth(int _, int __)
    {
        if (_player == null) return;

        Creature player = _player.Creature;
        int currentHp = player.CurrentHp;
        int maxHp = player.MaxHp;
        _hpLabel.Text = $"{currentHp}/{maxHp}";
    }

    private void OnMouseEntered()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip);
        tip.GlobalPosition = GlobalPosition + new Vector2(0f, Size.Y + 20f);
    }

    private void OnMouseExited()
    {
        NHoverTipSet.Remove(this);
    }

    public async Task LerpAtNeow()
    {
        if (_player == null) return;

        // When starting a new game at neow, make it look like the player has 0 HP and is being revived
        _hpLabel.Text = $"0/{_player.Creature.MaxHp}";
        await Cmd.Wait(0.5f);
        Tween tween = CreateTween();
        tween.TweenMethod(Callable.From<float>(UpdateHpTween), 0f, 1f, 1f).SetEase(Tween.EaseType.InOut).SetTrans(Tween.TransitionType.Cubic);
    }

    private void UpdateHpTween(float tweenAmount)
    {
        if (_player == null) return;

        Creature player = _player.Creature;
        int tweenHp = (int)Math.Round(player.CurrentHp * tweenAmount);
        int maxHp = player.MaxHp;
        _hpLabel.Text = $"{tweenHp}/{maxHp}";
    }
}
