using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.TopBar;

public abstract partial class NTopBarButton : NButton
{
    protected bool IsScreenOpen { get; private set; }

    // Nodes and references to things in the scene
    protected Control _icon = default!;
    protected ShaderMaterial? _hsv;

    // Hover
    private const float _hoverAngle = -12f * Mathf.Pi / 180f; // Radians as we'll be using Math.LerpAngle()
    private const float _hoverShaderV = 1.1f;
    protected const float _hoverAnimDur = 0.5f;
    protected static readonly Vector2 _hoverScale = Vector2.One * 1.1f;
    private CancellationTokenSource? _hoverAnimCancelToken;

    // Unhover
    private const float _defaultV = 1f;
    protected const float _unhoverAnimDur = 1f;
    private CancellationTokenSource? _unhoverAnimCancelToken;

    // PressDown
    private const float _pressDownV = 0.4f;
    protected const float _pressDownDur = 0.25f;
    private CancellationTokenSource? _pressDownCancelToken;

    public override void _Ready()
    {
        ConnectSignals();
        _icon = GetNode<Control>("Control/Icon");
        _hsv = (ShaderMaterial)_icon.Material;
    }

    protected void InitTopBarButton()
    {
        ConnectSignals();
        _icon = GetNode<Control>("Control/Icon");
        _hsv = (ShaderMaterial)_icon.Material;
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        CancelAnimations();
    }

    protected override void OnRelease()
    {
        _pressDownCancelToken?.Cancel();
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();
        _hoverAnimCancelToken?.Cancel();
        _pressDownCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimPressDown(_pressDownCancelToken));
    }

    protected virtual async Task AnimPressDown(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;
        float targetAngle = startAngle + 24f * Mathf.Pi / 180f;

        while (timer < _pressDownDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, targetAngle, Ease.CubicOut(timer / _pressDownDur));
            _hsv?.SetShaderParameter("v", Mathf.Lerp(_hoverShaderV, _pressDownV, Ease.CubicOut(timer / _pressDownDur)));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _icon.Rotation = targetAngle;
        _hsv?.SetShaderParameter("v", _pressDownV);
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        if (IsScreenOpen)
        {
            _hsv?.SetShaderParameter("v", _hoverShaderV);
            _icon.Scale = _hoverScale;
        }
        else
        {
            _hsv?.SetShaderParameter("v", _hoverShaderV);
            _icon.Scale = _hoverScale;
            _unhoverAnimCancelToken?.Cancel();
            _hoverAnimCancelToken?.Cancel();
            _hoverAnimCancelToken = new CancellationTokenSource();
            TaskHelper.RunSafely(AnimHover(_hoverAnimCancelToken));
        }
    }

    protected override void OnEnable()
    {
        Modulate = Colors.White;
    }

    protected override void OnDisable()
    {
        Modulate = StsColors.disabledTopBarButton;
    }

    protected virtual async Task AnimHover(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;

        while (timer < _hoverAnimDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, _hoverAngle, Ease.BackOut(timer / _hoverAnimDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _icon.Rotation = _hoverAngle;
    }

    protected override void OnUnfocus()
    {
        if (IsScreenOpen)
        {
            _pressDownCancelToken?.Cancel();
            _hsv?.SetShaderParameter("v", _defaultV);
            _icon.Scale = Vector2.One;
        }
        else
        {
            _hoverAnimCancelToken?.Cancel();
            _pressDownCancelToken?.Cancel();
            _unhoverAnimCancelToken?.Cancel();
            _unhoverAnimCancelToken = new CancellationTokenSource();
            TaskHelper.RunSafely(AnimUnhover(_unhoverAnimCancelToken));
        }
    }

    protected virtual async Task AnimUnhover(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;

        while (timer < _unhoverAnimDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, 0f, Ease.ElasticOut(timer / _unhoverAnimDur));
            _hsv?.SetShaderParameter("v", Mathf.Lerp(_hoverShaderV, _defaultV, Ease.ExpoOut(timer / _unhoverAnimDur)));
            _icon.Scale = _hoverScale.Lerp(Vector2.One, Ease.ExpoOut(timer / _unhoverAnimDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _hsv?.SetShaderParameter("v", _defaultV);
        _icon.Rotation = 0f;
        _icon.Scale = Vector2.One;
    }

    protected void UpdateScreenOpen()
    {
        bool isScreenOpen = IsOpen();
        if (IsScreenOpen != isScreenOpen)
        {
            IsScreenOpen = isScreenOpen;

            if (!IsScreenOpen)
            {
                OnScreenClosed();
            }
        }
    }

    private void OnScreenClosed()
    {
        CancelAnimations();
        _unhoverAnimCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(AnimUnhover(_unhoverAnimCancelToken));
    }

    private void CancelAnimations()
    {
        _hoverAnimCancelToken?.Cancel();
        _pressDownCancelToken?.Cancel();
        _unhoverAnimCancelToken?.Cancel();
    }

    public abstract bool IsOpen();
}
