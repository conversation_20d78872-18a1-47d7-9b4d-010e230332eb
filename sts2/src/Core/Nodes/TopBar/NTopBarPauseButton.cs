using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.PauseMenu;

namespace MegaCrit.Sts2.Core.Nodes.TopBar;

public partial class NTopBarPauseButton : NTopBarButton
{
    protected override string Hotkey => MegaInput.settings;

    private static readonly HoverTip _hoverTip = new(
        new LocString("static_hover_tips", "SETTINGS.title"),
        new LocString("static_hover_tips", "SETTINGS.description")
    );

    // Hover, Unhover, Press
    private const float _hoverAngle = -180f * Mathf.Pi / 180f; // Radians as we'll be using Mathf.LerpAngle()
    private const float _hoverShaderV = 1.1f;
    private const float _defaultV = 0.9f;
    private const float _pressDownV = 0.4f;

    private IClimbState _climbState = default!;

    protected override void OnRelease()
    {
        base.OnRelease();

        if (IsOpen())
        {
            NCapstoneContainer.Instance!.Close();
        }
        else
        {
            NPauseMenu pauseMenu = (NPauseMenu)NClimb.Instance!.GlobalUi.SubmenuStack.ShowScreen(CapstoneSubmenuType.PauseMenu);
            pauseMenu.Initialize(_climbState);
        }

        UpdateScreenOpen();
        _hsv?.SetShaderParameter("v", _defaultV);
    }

    public override bool IsOpen()
    {
        return NCapstoneContainer.Instance!.CurrentCapstoneScreen is NCapstoneSubmenuStack { ScreenType: NetScreenType.PauseMenu };
    }

    public override void _Process(double delta)
    {
        if (IsScreenOpen)
        {
            _icon.Rotation += (float)delta;
        }
    }

    public void Initialize(IClimbState climbState)
    {
        _climbState = climbState;
    }

    protected override async Task AnimPressDown(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;
        float targetAngle = startAngle + 45f * Mathf.Pi / 180f;

        while (timer < _pressDownDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, targetAngle, Ease.CubicOut(timer / _pressDownDur));
            _hsv?.SetShaderParameter("v", Mathf.Lerp(_hoverShaderV, _pressDownV, Ease.CubicOut(timer / _pressDownDur)));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _icon.Rotation = targetAngle;
        _hsv?.SetShaderParameter("v", _pressDownV);
    }

    protected override async Task AnimHover(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;

        while (timer < _hoverAnimDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, _hoverAngle, Ease.BackOut(timer / _hoverAnimDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _icon.Rotation = _hoverAngle;
    }

    protected override async Task AnimUnhover(CancellationTokenSource cancelToken)
    {
        float timer = 0f;
        float startAngle = _icon.Rotation;

        while (timer < _unhoverAnimDur)
        {
            if (cancelToken.IsCancellationRequested) return;

            _icon.Rotation = Mathf.LerpAngle(startAngle, 0f, Ease.ElasticOut(timer / _unhoverAnimDur));
            _hsv?.SetShaderParameter("v", Mathf.Lerp(_hoverShaderV, _defaultV, Ease.ExpoOut(timer / _unhoverAnimDur)));
            _icon.Scale = _hoverScale.Lerp(Vector2.One, Ease.ExpoOut(timer / _unhoverAnimDur));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            timer += (float)GetProcessDeltaTime();
        }

        _hsv?.SetShaderParameter("v", _defaultV);
        _icon.Rotation = 0f;
        _icon.Scale = Vector2.One;
    }

    /// <summary>
    /// Toggles the anim state of this button.
    /// Utilized when an external UI (ie BackButton) closes this screen.
    /// </summary>
    public void ToggleAnimState()
    {
        UpdateScreenOpen();
    }

    protected override void OnFocus()
    {
        base.OnFocus();

        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip, HoverTipAlignment.None);
        tip.GlobalPosition = GlobalPosition + new Vector2(Size.X - tip.Size.X, Size.Y + 20f);
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        NHoverTipSet.Remove(this);
    }
}
