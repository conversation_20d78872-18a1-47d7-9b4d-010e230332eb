using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.sts2.Core.Nodes.TopBar;

public partial class NTopBarGold : Control
{
    private static readonly HoverTip _hoverTip = new(
        new LocString("static_hover_tips", "MONEY_POUCH.title"),
        new LocString("static_hover_tips", "MONEY_POUCH.description")
    );

    // Will be null until initialized.
    private Player? _player;

    private Label _goldLabel = default!;
    private Label _goldPopupLabel = default!;

    private int _currentGold;
    private int _additionalGold;

    private bool _alreadyRunning;

    public override void _Ready()
    {
        _goldLabel = GetNode<Label>("%GoldLabel");
        _goldPopupLabel = GetNode<Label>("%GoldPopup");
        _goldPopupLabel.Modulate = Colors.Transparent;

        Connect(Control.SignalName.MouseEntered, Callable.From(OnMouseEntered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnMouseExited));
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        if (_player != null)
        {
            _player.GoldChanged -= UpdateGold;
        }
    }

    public void Initialize(Player player)
    {
        _player = player;
        _currentGold = _player.Gold;
        _goldLabel.Text = $"{_currentGold}";

        _player.GoldChanged += UpdateGold;
    }

    private void UpdateGold()
    {
        TaskHelper.RunSafely(UpdateGoldAnim());
    }

    private async Task UpdateGoldAnim()
    {
        if (_player == null) return;

        int newGold = _player.Gold - _currentGold;
        _additionalGold = _currentGold = newGold;
        _currentGold = _player.Gold;
        _goldPopupLabel.Text = (_additionalGold > 0 ? "+" : "") + _additionalGold;

        if (_alreadyRunning) return;

        _alreadyRunning = true;
        Tween tweenIn = CreateTween().SetParallel();
        tweenIn.TweenProperty(_goldPopupLabel, "modulate:a", 1f, 0.15f);
        tweenIn.TweenProperty(_goldPopupLabel, "position:y", _goldPopupLabel.Position.Y + 30f, 0.25f);
        await ToSignal(tweenIn, Tween.SignalName.Finished);
        await Task.Delay(150);

        while (_additionalGold != 0)
        {
            int incrementAmount = 1;

            if ((Mathf.Abs(_additionalGold) > 100))
            {
                incrementAmount = 75;
            }
            else if ((Mathf.Abs(_additionalGold) > 50))
            {
                incrementAmount = 10;
            }

            _additionalGold = _additionalGold > 0 ? _additionalGold - incrementAmount : _additionalGold + incrementAmount;
            _goldPopupLabel.Text = (_additionalGold >= 0 ? "+" : "") + _additionalGold;
            _goldLabel.Text = $"{_player.Gold - _additionalGold}";
            await Task.Delay((int)Mathf.Lerp(10f, 20f, Mathf.Max(0, 10 - Mathf.Abs(_additionalGold))));
        }

        await Task.Delay(250);

        Tween tweenOut = CreateTween().SetParallel();
        tweenOut.TweenProperty(_goldPopupLabel, "modulate:a", 0f, 0.1f);
        tweenOut.TweenProperty(_goldPopupLabel, "position:y", _goldPopupLabel.Position.Y - 30f, 0.25f).FromCurrent();
        _goldLabel.Text = $"{_player.Gold}";
        _alreadyRunning = false;
    }

    private void OnMouseEntered()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip, HoverTipAlignment.None);
        tip.GlobalPosition = GlobalPosition + new Vector2(0f, Size.Y + 20f);
    }

    private void OnMouseExited()
    {
        NHoverTipSet.Remove(this);
    }
}
