using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NOpenFeedbackScreenButton : NButton
{
    private TextureRect _image = default!;
    private MegaLabel _label = default!;

    public override void _Ready()
    {
        ConnectSignals();
        _image = GetNode<TextureRect>("Image");
        _label = GetNode<MegaLabel>("Label");
        _label.Text = new LocString("settings_ui", "SEND_FEEDBACK_BUTTON_LABEL").GetFormattedText();
    }

    protected override void OnFocus()
    {
        base.OnFocus(); // Sfx

        _image.Scale = Vector2.One * 1.05f;
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _image.Scale = Vector2.One;
    }
}
