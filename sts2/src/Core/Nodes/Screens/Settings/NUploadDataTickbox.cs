using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Settings;

public partial class NUploadDataTickbox : NTickbox
{
    private NSettingsScreen _settingsScreen = default!;

    public override void _Ready()
    {
        ConnectSignals();

        _settingsScreen = this.GetAncestorOfType<NSettingsScreen>()!;
        IsTicked = SaveManager.Instance.SettingsSave.UploadData;
    }

    protected override void OnTick()
    {
        SaveManager.Instance.SettingsSave.UploadData = true;
    }

    protected override void OnUntick()
    {
        _settingsScreen.ShowToast(new LocString("settings_ui", "TOAST_NOT_IMPLEMENTED"));
        SaveManager.Instance.SettingsSave.UploadData = false;
    }
}
