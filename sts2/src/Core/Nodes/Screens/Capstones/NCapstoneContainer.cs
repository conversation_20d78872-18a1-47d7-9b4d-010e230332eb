using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Capstones;

/// <summary>
/// Node class that manages the current capstone screen.
/// If a new Capstone screen is opened, any previously open capstone screens are destroyed.
/// If a new Capstone screen is opened, any previously open overlay screens are hidden until there are no more capstone screens.
/// </summary>
public partial class NCapstoneContainer : Control
{
    [Signal]
    public delegate void ChangedEventHandler();

    [Signal]
    public delegate void CapstoneClosedEventHandler();

    // We keep track of overlays independently of node hierarchy so we have more flexibility in the timing of
    // adding/removing the child nodes.x
    public ICapstoneScreen? CurrentCapstoneScreen { get; private set; }
    public bool InUse => CurrentCapstoneScreen != null;
    public static NCapstoneContainer? Instance => NClimb.Instance?.GlobalUi.CapstoneContainer;

    private Control _backstop = default!;
    private Tween? _backstopFade;

    public override void _Ready()
    {
        _backstop = GetNode<Control>("CapstoneBackstop");
        _backstop.Modulate = Colors.Transparent;
    }

    public void Open(ICapstoneScreen screen)
    {
        NHoverTipSet.Clear();
        bool hadPreviousScreen = CurrentCapstoneScreen != null;

        if (hadPreviousScreen)
        {
            CloseInternal();
        }

        _backstopFade?.Kill();

        // Note: This has to come after the Close() call, since closing the capstone screen re-shows the overlay stack.
        NOverlayStack.Instance!.HideOverlays();

        if (!screen.UseSharedBackstop)
        {
            _backstop.Modulate = Colors.Transparent;
        }
        else if (hadPreviousScreen || NOverlayStack.Instance.ScreenCount > 0)
        {
            _backstop.Modulate = Colors.White;
        }
        else
        {
            // NOTE: Transparency is actually 0.85f as set by the backstop's Color field
            _backstopFade = CreateTween();
            _backstopFade.TweenProperty(_backstop, "modulate:a", 1f, 0.5)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }

        CurrentCapstoneScreen = screen;

        if (!GetChildren().Contains((Node)screen))
        {
            this.AddChildSafely((Node)screen);
        }

        ((Node)screen).ProcessMode = ProcessModeEnum.Inherit;

        screen.AfterCapstoneOpened();

        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Pause();
        }

        // TODO: this will be true for all overlay screens eventually
        if (screen is IFocusableScreen)
        {
            NControllerManager.Instance!.PushScreen((IFocusableScreen)screen);
        }

        EmitSignal(SignalName.Changed);
    }

    public void Close()
    {
        CloseInternal();
        EmitSignal(SignalName.CapstoneClosed);
        EmitSignal(SignalName.Changed);
    }

    private void CloseInternal()
    {
        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Unpause();
        }

        NOverlayStack.Instance!.ShowOverlays();

        if (NOverlayStack.Instance.ScreenCount > 0)
        {
            _backstop.Modulate = Colors.Transparent;
        }
        else
        {
            _backstopFade?.Kill();
            _backstopFade = CreateTween();
            _backstopFade.TweenProperty(_backstop, "modulate:a", 0f, 0.5)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Cubic);
        }

        ICapstoneScreen? closingScreen = CurrentCapstoneScreen;
        CurrentCapstoneScreen = null;

        // TODO: this will be true for all overlay screens eventually
        if (closingScreen is IFocusableScreen)
        {
            NControllerManager.Instance!.RemoveScreen((IFocusableScreen)closingScreen);
        }

        if (closingScreen is Node node)
        {
            node.ProcessMode = ProcessModeEnum.Disabled;
        }

        closingScreen?.AfterCapstoneClosed();
        NHoverTipSet.Clear();
    }

    // These functions should only be used in rare occasions!
    public void DisableBackstopInstantly()
    {
        _backstopFade?.Kill();
        _backstop.Modulate = Colors.Transparent;
    }

    public void EnableBackstopInstantly()
    {
        _backstopFade?.Kill();
        _backstop.Modulate = Colors.White;
    }

    /// <summary>
    /// Called just before the climb is disposed and Climb.Instance is unset.
    /// </summary>
    public void CleanUp()
    {
        // If we don't do this, if the player starts a multiplayer game, we will never unpause combat
        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Unpause();
        }
    }
}
