using System;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

/// <summary>
/// This is pretty close to NPaginator, but NPaginator only supports a static list of options whereas this needs to
/// - Set its text based on a DateTimeOffset that is (effectively) infinite
/// - Dynamically enable/disable arrows based on whether the query says there is a leaderboard
/// It uses the same scene as NPaginator but with the script replaced.
/// </summary>
public partial class NLeaderboardDayPaginator : Control
{
    protected MegaLabel _label = default!;

    // This is the label that animates away when we paginate. Fancy technology
    private MegaLabel _vfxLabel = default!;
    private NLeaderboardPageArrow _leftArrow = default!;
    private NLeaderboardPageArrow _rightArrow = default!;
    private Control _bg = default!;
    private Tween? _tween;

    private const double _animDuration = 0.25;
    private const float _animDistance = 90f;

    private DateTimeOffset _currentDay;
    private NDailyClimbLeaderboard? _leaderboard;

    public override void _Ready()
    {
        _label = GetNode<MegaLabel>("%Label");
        _vfxLabel = GetNode<MegaLabel>("%VfxLabel");
        _bg = GetNode<Control>("Bg");
        _leftArrow = GetNode<NLeaderboardPageArrow>("LeftArrow");
        _rightArrow = GetNode<NLeaderboardPageArrow>("RightArrow");

        _bg.Visible = HasFocus();
        Connect(Control.SignalName.FocusEntered, Callable.From(OnFocus));
        Connect(Control.SignalName.FocusExited, Callable.From(OnUnfocus));

        _leftArrow.Connect(PageLeft);
        _rightArrow.Connect(PageRight);
    }

    public void Initialize(NDailyClimbLeaderboard leaderboard, DateTimeOffset dateTime, bool showArrows)
    {
        _currentDay = dateTime;
        _leaderboard = leaderboard;
        OnDayChanged(false);

        _leftArrow.Visible = showArrows;
        _rightArrow.Visible = showArrows;
    }

    public override void _GuiInput(InputEvent input)
    {
        base._GuiInput(input);

        if (input.IsActionPressed(MegaInput.left))
        {
            PageLeft();
        }

        if (input.IsActionPressed(MegaInput.right))
        {
            PageRight();
        }
    }

    private void PageLeft()
    {
        _currentDay -= TimeSpan.FromDays(1);
        DayChangeHelper(true);
    }

    private void PageRight()
    {
        _currentDay += TimeSpan.FromDays(1);
        DayChangeHelper(false);
    }

    private void DayChangeHelper(bool pagedLeft)
    {
        _vfxLabel.Text = _label.Text;
        _vfxLabel.Modulate = _label.Modulate;

        OnDayChanged(true);

        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        _tween.TweenProperty(_label, "position:x", 0f, _animDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic)
            .From(pagedLeft ? -_animDistance : _animDistance);
        _tween.TweenProperty(_label, "modulate:a", 1f, _animDuration)
            .From(0.75f);

        _tween.TweenProperty(_vfxLabel, "position:x", pagedLeft ? _animDistance : -_animDistance, _animDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic)
            .From(0f);
        _tween.TweenProperty(_vfxLabel, "modulate", StsColors.transparentBlack, _animDuration);
    }

    private void OnDayChanged(bool changeLeaderboardDay)
    {
        _label.Text = _currentDay.ToString(NDailyClimbScreen.dateFormat);

        if (changeLeaderboardDay)
        {
            _leaderboard!.SetDay(_currentDay);
        }
    }

    public void Disable()
    {
        _leftArrow.Disable();
        _rightArrow.Disable();
    }

    public void Enable(bool leftArrowEnabled, bool rightArrowEnabled)
    {
        if (leftArrowEnabled)
        {
            _leftArrow.Enable();
        }

        if (rightArrowEnabled)
        {
            _rightArrow.Enable();
        }
    }

    private void OnFocus()
    {
        _bg.Visible = true;
    }

    private void OnUnfocus()
    {
        _bg.Visible = false;
    }
}
