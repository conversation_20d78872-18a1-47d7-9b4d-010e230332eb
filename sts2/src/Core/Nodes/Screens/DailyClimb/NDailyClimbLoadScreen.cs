using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

/// <summary>
/// Screen that displays when loading a multiplayer daily climb, allowing other players to join the game before it starts.
/// </summary>
public partial class NDailyClimbLoadScreen : NSubmenu, ILoadClimbLobbyListener
{
    private static readonly LocString _ascensionLoc = new("main_menu_ui", "DAILY_CLIMB_MENU.ASCENSION");
    public static readonly string dateFormat = LocManager.Instance.GetTable("main_menu_ui").GetRawText("DAILY_CLIMB_MENU.DATE_FORMAT");

    private MegaLabel _dateLabel = default!;
    private NConfirmButton _embarkButton = default!;
    private NDailyClimbCharacterContainer _characterContainer = default!;
    private NDailyClimbLeaderboard _leaderboard = default!;
    private Label _modifiersTitleLabel = default!;
    private Control _modifiersContainer = default!;
    private readonly List<NDailyClimbScreenModifier> _modifierContainers = [];
    private NRemoteLoadLobbyPlayerContainer _remotePlayerContainer = default!;
    private Control _readyAndWaitingContainer = default!;

    private LoadClimbLobby? _lobby;

    public override void _Ready()
    {
        ConnectSignals();

        _embarkButton = GetNode<NConfirmButton>("%ConfirmButton");
        _dateLabel = GetNode<MegaLabel>("%Date");
        _leaderboard = GetNode<NDailyClimbLeaderboard>("%Leaderboards");
        _modifiersTitleLabel = GetNode<Label>("%ModifiersLabel");
        _modifiersContainer = GetNode<Control>("%ModifiersContainer");
        _characterContainer = GetNode<NDailyClimbCharacterContainer>("%CharacterContainer");
        _remotePlayerContainer = GetNode<NRemoteLoadLobbyPlayerContainer>("%RemotePlayerLoadContainer");
        _readyAndWaitingContainer = GetNode<Control>("%ReadyAndWaitingPanel");

        foreach (NDailyClimbScreenModifier modifierContainer in _modifiersContainer.GetChildren().OfType<NDailyClimbScreenModifier>())
        {
            _modifierContainers.Add(modifierContainer);
        }

        _readyAndWaitingContainer.Visible = false;

        _embarkButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnEmbarkPressed));
        _leaderboard.Cleanup();

        ProcessMode = ProcessModeEnum.Disabled;
    }

    public void InitializeAsHost(INetGameService gameService, SerializableClimb climb)
    {
        if (gameService.Type != NetGameType.Host) throw new InvalidOperationException($"Initialized daily climb load screen with net service of type {gameService.Type} when hosting!");
        _lobby = new LoadClimbLobby(gameService, this, climb);

        try
        {
            _lobby.AddLocalHostPlayer();
            AfterMultiplayerStarted();
        }
        catch
        {
            // On any exception, stop hosting
            CleanUpLobby(true);
            throw;
        }
    }

    public void InitializeAsClient(INetGameService gameService, ClientLoadJoinResponseMessage message)
    {
        if (gameService.Type != NetGameType.Client) throw new InvalidOperationException($"Initialized daily climb load screen with net service of type {gameService.Type} when joining!");
        _lobby = new LoadClimbLobby(gameService, this, message);
        AfterMultiplayerStarted();
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();

        _leaderboard.Initialize(_lobby!.Climb.DailyTime!.Value, _lobby.Climb.Players.Select(p => p.NetId), true);
        _embarkButton.Enable();
        _remotePlayerContainer.Initialize(_lobby, false);
    }

    public override void OnSubmenuClosed()
    {
        _embarkButton.Disable();
        _remotePlayerContainer.Cleanup();
        _leaderboard.Cleanup();

        // Note that this method is not called when we transition to a climb, and is only called when this menu is
        // popped off the stack, e.g. by the back button, so we always pass true to disconnect session
        CleanUpLobby(true);
    }

    protected override void OnSubmenuShown()
    {
        ProcessMode = ProcessModeEnum.Inherit;
    }

    protected override void OnSubmenuHidden()
    {
        ProcessMode = ProcessModeEnum.Disabled;
    }

    private void InitializeDisplay()
    {
        if (_lobby == null) throw new InvalidOperationException("Tried to initialize daily climb display before lobby was initialized!");

        // Setup ascension loc
        _ascensionLoc.Add("ascension", _lobby.Climb.Ascension);

        DateTimeOffset runDailyTime = _lobby.Climb.DailyTime!.Value;

        // Set all the controls on the window
        SerializablePlayer localPlayer = _lobby.Climb.Players.First(p => p.NetId == _lobby.NetService.NetId);
        CharacterModel character = ModelDb.GetById<CharacterModel>(localPlayer.CharacterId!);
        _characterContainer.Fill(character, localPlayer.NetId, _lobby.Climb.Ascension, _lobby.NetService);

        _dateLabel.Text = runDailyTime.ToString(dateFormat);
        _embarkButton.Enable();

        for (int i = 0; i < _lobby.Climb.Modifiers.Count; i++)
        {
            ModifierModel modifier = ModifierModel.FromSerializable(_lobby.Climb.Modifiers[i]);
            _modifierContainers[i].Fill(modifier);
        }
    }

    private void OnEmbarkPressed(NButton _)
    {
        _embarkButton.Disable();
        _lobby!.SetReady(true);

        if (_lobby.NetService.Type != NetGameType.Singleplayer)
        {
            if (_lobby.Climb.Players.Any(p => !_lobby.IsPlayerReady(p.NetId)))
            {
                _readyAndWaitingContainer.Visible = true;
            }
        }
    }

    public override void _Process(double delta)
    {
        if (_lobby?.NetService.IsConnected ?? false)
        {
            _lobby.NetService.Update();
        }
    }

    private void CleanUpLobby(bool disconnectSession)
    {
        _lobby!.CleanUp(disconnectSession);
        _lobby = null!;

        if (disconnectSession)
        {
            NGame.Instance!.RemoteCursorContainer.Deinitialize();
            NGame.Instance.ReactionContainer.DeinitializeNetworking();
        }
    }

    public async Task<bool> ShouldAllowClimbToBegin()
    {
        // If not all players have joined the climb, then confirm that we want to proceed first.
        if (_lobby!.ConnectedPlayerIds.Count >= _lobby.Climb.Players.Count) return true;

        LocString body = new("gameplay_ui", "CONFIRM_LOAD_SAVE.body");
        body.Add("MissingCount", _lobby.Climb.Players.Count - _lobby.ConnectedPlayerIds.Count);

        NGenericMultiplayerConfirmationPopup confirmation = NGenericMultiplayerConfirmationPopup.Create()!;
        NModalContainer.Instance!.Add(confirmation);
        bool confirmed = await confirmation.WaitForConfirmation(
            body,
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.header"),
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.cancel"),
            new LocString("gameplay_ui", "CONFIRM_LOAD_SAVE.confirm")
        );

        return confirmed;
    }

    private async Task StartClimb()
    {
        Log.Info($"Loading a multiplayer climb. Players: {string.Join(",", _lobby!.ConnectedPlayerIds)}.");
        SerializablePlayer player = _lobby.Climb.Players.First(p => p.NetId == _lobby.NetService.NetId);
        await NGame.Instance!.Transition.FadeIn(0.8f, ModelDb.GetById<CharacterModel>(player.CharacterId!).CharacterSelectTransitionPath);

        ClimbState climbState = ClimbState.FromSerializable(_lobby.Climb);
        ClimbManager.Instance.SetUpSavedMultiPlayer(climbState, _lobby);

        await NGame.Instance.LoadClimb(climbState, _lobby.Climb.PreFinishedRoom);
        CleanUpLobby(false);
        await NGame.Instance.Transition.FadeOut();
    }

    public void PlayerConnected(ulong playerId)
    {
        Log.Info($"Player connected: {playerId}");
        _remotePlayerContainer.OnPlayerConnected(playerId);
    }

    public void PlayerReadyChanged(ulong playerId)
    {
        Log.Info($"Player ready changed: {playerId}");
        _remotePlayerContainer.OnPlayerChanged(playerId);

        // Re-enable confirm button if player has become unready
        if (playerId == _lobby!.NetService.NetId && !_lobby.IsPlayerReady(playerId))
        {
            _embarkButton.Enable();
        }
    }

    public void RemotePlayerDisconnected(ulong playerId)
    {
        Log.Info($"Player disconnected: {playerId}");
        _remotePlayerContainer.OnPlayerDisconnected(playerId);
    }

    public void BeginClimb()
    {
        NAudioManager.Instance?.StopMusic();
        TaskHelper.RunSafely(StartClimb());
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        // If we're the one that disconnected, then do nothing
        if (info.SelfInitiated && info.GetReason() == NetError.Quit) return;

        // Otherwise, close window and display error
        _stack.Pop();

        if (TestMode.IsOff)
        {
            NErrorPopup? popup = NErrorPopup.Create(info);

            if (popup != null)
            {
                NModalContainer.Instance!.AddChildSafely(popup);
            }
        }
    }

    private void AfterMultiplayerStarted()
    {
        NGame.Instance!.RemoteCursorContainer.Initialize(_lobby!.InputSynchronizer, _lobby.ConnectedPlayerIds);
        NGame.Instance.ReactionContainer.InitializeNetworking(_lobby.NetService);

        InitializeDisplay();

        // We might want to remove this at some point. It's here for debugging purposes.
        Logger.logLevelTypeMap[LogType.Network] = LogLevel.Debug;
        Logger.logLevelTypeMap[LogType.Actions] = LogLevel.VeryDebug;
        Logger.logLevelTypeMap[LogType.GameSync] = LogLevel.VeryDebug;
    }
}
