using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Nodes.Screens.DailyClimb;

public partial class NDailyClimbScreenModifier : Control
{
    private static readonly LocString _modifierLoc = new ("main_menu_ui", "DAILY_CLIMB_MENU.MODIFIER");

    private TextureRect _icon = default!;
    private MegaRichTextLabel _description = default!;

    public override void _Ready()
    {
        _icon = GetNode<TextureRect>("Icon");
        _description = GetNode<MegaRichTextLabel>("Description");
    }

    public void Fill(ModifierModel modifier)
    {
        _modifierLoc.Add("title", modifier.Title.GetFormattedText());
        _modifierLoc.Add("description", modifier.Description.GetFormattedText());
        _icon.Texture = modifier.Icon;
        _description.Text = _modifierLoc.GetFormattedText();
    }
}
