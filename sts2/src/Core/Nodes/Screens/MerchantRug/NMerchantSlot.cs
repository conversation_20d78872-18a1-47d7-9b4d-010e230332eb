using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MerchantRug;

/// <summary>
/// UI wrapper class for MerchantEntry.
/// </summary>
public abstract partial class NMerchantSlot : Control
{
    [Signal]
    public delegate void HoveredEventHandler(NMerchantSlot slot);

    [Signal]
    public delegate void UnhoveredEventHandler(NMerchantSlot slot);

    private bool _isHovered;

    // Default scale values used by every CardHolderContainer besides PlayerHand
    private static readonly Vector2 _hoverScale = Vector2.One * 0.8f;
    private static readonly Vector2 _smallScale = Vector2.One * 0.65f;

    protected NClickableControl _hitbox = default!;
    public NClickableControl Hitbox => _hitbox;

    protected Label _costLabel = default!;
    private Tween? _hoverTween;
    private Tween? _purchaseFailedTween;
    private NMerchantRug? _merchantRug;

    private bool _ignoreMouseRelease;
    private float? _originalVisualPosition;

    protected abstract MerchantEntry Entry { get; }
    protected abstract CanvasItem Visual { get; }

    protected Player? Player => _merchantRug?.Inventory.Player;

    public void Initialize(NMerchantRug rug)
    {
        _merchantRug = rug;

        Player!.GoldChanged += UpdateVisual;
        Connect(SignalName.Hovered, Callable.From<NMerchantSlot>(OnMerchantHandHovered));
        Connect(SignalName.Unhovered, Callable.From<NMerchantSlot>(OnMerchantHandUnhovered));
    }

    public override void _Ready()
    {
        if (GetType() != typeof(NMerchantSlot))
        {
            Log.Error($"{GetType()}");
            throw new InvalidOperationException("Don't call base._Ready()! Call ConnectSignals() instead.");
        }

        ConnectSignals();
    }

    protected virtual void ConnectSignals()
    {
        // We need to rely on an attached hitbox Node for mouse hover
        // detection as this Control's hitbox would be top-left aligned.
        _hitbox = GetNode<NClickableControl>("%Hitbox");
        Connect(Control.SignalName.FocusEntered, Callable.From(OnFocus));
        Connect(Control.SignalName.FocusExited, Callable.From(OnUnfocus));
        _hitbox.Connect(Control.SignalName.MouseEntered, Callable.From(OnFocus));
        _hitbox.Connect(Control.SignalName.MouseExited, Callable.From(OnUnfocus));

        _hitbox.Connect(NClickableControl.SignalName.MousePressed, Callable.From<InputEvent>(OnMousePressed));
        _hitbox.Connect(NClickableControl.SignalName.MouseReleased, Callable.From<InputEvent>(OnMouseReleased));

        _costLabel = GetNode<Label>("%CostLabel");
    }

    public override void _ExitTree()
    {
        _hoverTween?.Kill();

        if (Player != null)
        {
            Player.GoldChanged -= UpdateVisual;
        }
    }

    public override void _GuiInput(InputEvent inputEvent)
    {
        if (inputEvent.IsActionPressed(MegaInput.select))
        {
            TaskHelper.RunSafely(OnPressed());
        }
    }

    private void OnMousePressed(InputEvent inputEvent)
    {
        _ignoreMouseRelease = false;
    }

    private void OnMouseReleased(InputEvent inputEvent)
    {
        if (!_isHovered || _ignoreMouseRelease) return;

        if (inputEvent is InputEventMouseButton mouseAction)
        {
            if (mouseAction.ButtonIndex == MouseButton.Left)
            {
                TaskHelper.RunSafely(OnPressed());
            }
            else
            {
                OnPreview();
            }
        }
    }

    private void OnFocus()
    {
        _isHovered = true;
        _hoverTween?.Kill();
        Scale = _hoverScale;

        CreateHoverTip();

        EmitSignal(SignalName.Hovered, this);
    }

    private void OnUnfocus()
    {
        _isHovered = false;
        _hoverTween?.Kill();
        _hoverTween = CreateTween();
        _hoverTween.TweenProperty(this, "scale", _smallScale, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        ClearHoverTip();

        EmitSignal(SignalName.Unhovered, this);
    }

    private async Task OnPressed()
    {
        ClearHoverTip();
        await OnTryPurchase(_merchantRug?.Inventory);

        // mouse might no longer be hovering over when OnTryPurchase takes an extended
        // amount of time (i.e. Merchant Card Removal)
        if (Entry is { IsStocked: true } && _isHovered)
        {
            CreateHoverTip();
        }
    }

    protected abstract Task OnTryPurchase(MerchantInventory? inventory);

    protected virtual void OnPreview()
    {
        //no-op
    }

    protected abstract void CreateHoverTip();

    protected void ClearHoverTip()
    {
        NHoverTipSet.Remove(this);
    }

    private void OnMerchantHandHovered(NMerchantSlot _)
    {
        _merchantRug?.MerchantHand.PointAtTarget(GlobalPosition);
    }

    private void OnMerchantHandUnhovered(NMerchantSlot _)
    {
        _merchantRug?.MerchantHand.StopPointing(2f);
    }

    protected void OnPurchaseFailed(PurchaseStatus status)
    {
        if (status == PurchaseStatus.Success) return;

        if (_originalVisualPosition == null)
        {
            if (Visual is Node2D node2d)
            {
                _originalVisualPosition = node2d.Position.X;
            }
            else if (Visual is Control control)
            {
                _originalVisualPosition = control.Position.X;
            }
        }

        _purchaseFailedTween?.Kill();
        _purchaseFailedTween = CreateTween();

        // To 2f because two wiggles
        _purchaseFailedTween.TweenMethod(Callable.From<float>(WiggleAnimation), 0f, 2f, 0.4f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Quad);

        NDebugAudioManager.Instance?.Play(Rng.Chaotic.NextItem(TmpSfx.MerchantDispleasure)!, 1f, PitchVariance.Small);
    }

    protected virtual void UpdateVisual()
    {
        if (Entry.IsStocked)
        {
            _costLabel.Text = Entry.Cost.ToString();
        }
    }

    private void WiggleAnimation(float progress)
    {
        const float wiggleAmount = 10f;

        if (Visual is Node2D node2d)
        {
            node2d.Position = node2d.Position with { X = _originalVisualPosition!.Value + (float)Math.Sin(progress * Mathf.Pi * 2f) * wiggleAmount };
        }
        else if (Visual is Control control)
        {
            control.Position = control.Position with { X = _originalVisualPosition!.Value + (float)Math.Sin(progress * Mathf.Pi * 2f) * wiggleAmount };
        }
    }
}
