using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MerchantRug;

public partial class NMerchantCard : NMerchantSlot
{
    private Node2D _saleVisual = default!;
    private Control _cardHolder = default!;

    private NCard? _cardNode;

    private Tween? _hoverTween;

    private MerchantCardEntry _cardEntry = default!;

    public bool IsShowingUpgradedCard => _cardNode?.Model?.IsUpgraded ?? false;

    protected override MerchantEntry Entry => _cardEntry;
    protected override CanvasItem Visual => _cardHolder;

    public override void _Ready()
    {
        ConnectSignals();
        _cardHolder = GetNode<Control>("%CardHolder");
        _saleVisual = GetNode<Node2D>("%SaleVisual");
    }

    public void FillSlot(MerchantCardEntry cardEntry)
    {
        _cardEntry = cardEntry;
        cardEntry.EntryUpdated += UpdateVisual;
        cardEntry.PurchaseFailed += OnPurchaseFailed;
        UpdateVisual();
    }

    protected override void UpdateVisual()
    {
        base.UpdateVisual();

        if (_cardEntry.CreationResult == null)
        {
            Visible = false;
            MouseFilter = MouseFilterEnum.Ignore;
            ClearHoverTip();
        }
        else
        {
            if (_cardNode != null && _cardNode.Model != _cardEntry.CreationResult.Card)
            {
                _cardNode.QueueFreeSafely();
                _cardNode = null;
            }

            if (_cardNode == null)
            {
                _cardNode = NCard.Create(_cardEntry.CreationResult.Card);
                _cardHolder.AddChildSafely(_cardNode);
                _cardNode!.UpdateVisuals(CardPileTarget.None);
            }

            _costLabel.Text = _cardEntry.Cost.ToString();
            _saleVisual.Visible = _cardEntry.IsOnSale;

            if (!_cardEntry.EnoughGold)
            {
                _costLabel.Modulate = StsColors.red;
            }
            else
            {
                _costLabel.Modulate = _cardEntry.IsOnSale ? StsColors.green : StsColors.cream;
            }
        }
    }

    public void OnRugOpened()
    {
        if (_cardEntry.CreationResult?.HasBeenModified ?? false)
        {
            TaskHelper.RunSafely(DoRelicFlash());
        }
    }

    private async Task DoRelicFlash()
    {
        // Wait a little bit for the rug to be opened
        SceneTreeTimer timer = GetTree().CreateTimer(0.4);
        await timer.ToSignal(timer, SceneTreeTimer.SignalName.Timeout);

        foreach (RelicModel model in _cardEntry.CreationResult!.ModifyingRelics)
        {
            model.Flash();

            if (_cardNode == null) continue;
            NRelicFlashVfx flashVfx = NRelicFlashVfx.Create(model)!;
            _cardNode.AddChildSafely(flashVfx);
            flashVfx.Scale = Vector2.One * 2f;
            flashVfx.Position = _cardNode.Size * 0.5f;
        }
    }

    protected override async Task OnTryPurchase(MerchantInventory? inventory)
    {
        bool success = await _cardEntry.OnTryPurchaseWrapper(inventory);

        if (success)
        {
            // We reparent cards into the global UI so that they don't disappear with the screen
            NClimb.Instance?.GlobalUi.ReparentCard(_cardNode!);

            // This renders the card behind the "View Deck" button but above the TopBar asset when adding cards to deck.
            NClimb.Instance?.GlobalUi.TopBar.TrailContainer.AddChildSafely(
                NCardFlyVfx.Create(
                    _cardNode!,
                    CardPileTarget.Deck.GetTargetPosition(_cardNode),
                    true,
                    inventory!.Player.Character.TrailPath
                )
            );

            _cardNode = null;

            UpdateVisual();
        }
    }

    protected override void OnPreview()
    {
        ClearHoverTip();
        NGame.Instance!.InspectCardScreen.Open([_cardNode!.Model!], 0);
    }

    protected override void CreateHoverTip()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _cardEntry.CreationResult!.Card.HoverTips);
        // HACK: we want to align it to this, but we want the bounds to be the hitbox
        tip.SetAlignment(_hitbox, HoverTip.GetHoverTipAlignment(this));
    }
}
