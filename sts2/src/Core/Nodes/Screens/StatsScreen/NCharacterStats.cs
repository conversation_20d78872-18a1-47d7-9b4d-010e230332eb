using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.StatsScreen;

public partial class NCharacterStats : Node
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/stats_screen/character_stats");

    private CharacterStats _characterStats = default!;
    private Control _characterIcon = default!;
    private Node _statsContainer = default!;

    private Label _nameLabel = default!;
    private Label _unlocksLabel = default!;

    private NStatEntry _playtimeEntry = default!;
    private NStatEntry _winLossEntry = default!;
    private NStatEntry _streakEntry = default!;

    public static NCharacterStats Create(CharacterStats characterStats)
    {
        NCharacterStats node = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NCharacterStats>();
        node._characterStats = characterStats;
        return node;
    }

    public override void _Ready()
    {
        CharacterModel character = ModelDb.GetById<CharacterModel>(_characterStats.Id!);

        _characterIcon = GetNode<Control>("%CharacterIcon");
        _characterIcon.AddChildSafely(character.Icon);
        _statsContainer = GetNode<Node>("%StatsContainer");

        _playtimeEntry = CreateSection("uid://d0vw6xbikkm4m");
        _winLossEntry = CreateSection("uid://d3w3ub7j3xuxp");
        _streakEntry = CreateSection("uid://csyit3kkym0qx");

        // Hook up Label Nodes
        _nameLabel = GetNode<Label>("%NameLabel");
        _unlocksLabel = GetNode<Label>("%UnlocksLabel");

        // Set text for the Header section
        _nameLabel.Text = character.Title.GetRawText();
        _nameLabel.AddThemeColorOverride("font_color", character.NameColor);

        LoadStats();
    }

    private void LoadStats()
    {
        // Header!
        _unlocksLabel.Visible = false;
        // _unlocksLabel.Text = "N/A"; // TODO: Not yet implemented

        // Playtime & Fastest Win
        LocString loc = new("stats_screen", "ENTRY_CHAR_PLAYTIME.top");
        loc.Add("Playtime", TimeFormatting.Format(_characterStats.Playtime));
        _playtimeEntry.SetTopText(loc.GetFormattedText());
        if (_characterStats.FastestWinTime >= 0)
        {
            loc = new("stats_screen", "ENTRY_CHAR_PLAYTIME.bottom");
            loc.Add("FastestWin", TimeFormatting.Format(_characterStats.FastestWinTime));
            _playtimeEntry.SetBottomText(loc.GetFormattedText());
        }

        // Win & Loss + Total Ascensions
        loc = new LocString("stats_screen", "ENTRY_CHAR_WIN_LOSS.top");
        if (_characterStats.MaxAscension > 0)
        {
            loc.Add("Amount", _characterStats.MaxAscension - 1); // TODO: Need correct numbers
            _winLossEntry.SetTopText($"[red]{loc.GetFormattedText()}[/red]");
        }

        loc = new LocString("stats_screen", "ENTRY_CHAR_WIN_LOSS.bottom");
        loc.Add("Wins", _characterStats.TotalWins);
        loc.Add("Losses", _characterStats.TotalLosses);
        _winLossEntry.SetBottomText(loc.GetFormattedText());

        // Streak
        loc = new LocString("stats_screen", "ENTRY_CHAR_STREAK.top");
        loc.Add("Amount", _characterStats.CurrentWinStreak);
        _streakEntry.SetTopText(loc.GetFormattedText());
        loc = new LocString("stats_screen", "ENTRY_CHAR_STREAK.bottom");
        loc.Add("Amount", _characterStats.BestWinStreak);
        _streakEntry.SetBottomText(loc.GetFormattedText());
    }

    /// <summary>
    /// Helper function to create a Section in the StatsScreen.
    /// </summary>
    /// <param name="imgUrl"></param>
    private NStatEntry CreateSection(string imgUrl)
    {
        NStatEntry node = NStatEntry.Create(imgUrl);
        _statsContainer.AddChildSafely(node);
        return node;
    }
}
