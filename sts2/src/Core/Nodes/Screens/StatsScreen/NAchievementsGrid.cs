using System;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Platform;

namespace MegaCrit.Sts2.Core.Nodes.Screens.StatsScreen;

public partial class NAchievementsGrid : Control
{
    private const float _scrollAmount = 40f; // NOTE: How much we travel per scrollWheel tick. Probably better to get the expected scroll distance from the OS
    private const float _panScrollSpeed = 50f; // NOTE: How much we travel per touchpad gesture tick.
    private const float _dragLerpSpeed = 15f;
    private const float _snapThreshold = 0.5f;
    private const float _bounceBackStrength = 12f;

    private FlowContainer _flowContainer = default!;
    private NScrollbar _scrollbar = default!;

    private bool _scrollbarPressed;
    private Vector2 _startDragPos;
    private Vector2 _targetDragPos;
    private bool _isDragging;

    private float ScrollLimitBottom => -_flowContainer.Size.Y + Size.Y;

    public override void _Ready()
    {
        _flowContainer = GetNode<FlowContainer>("%FlowContainer");
        _scrollbar = GetNode<NScrollbar>("%Scrollbar");

        _scrollbar.Connect(NScrollbar.SignalName.MousePressed, Callable.From<InputEvent>(_ => { _scrollbarPressed = true; }));
        _scrollbar.Connect(NScrollbar.SignalName.MouseReleased, Callable.From<InputEvent>(_ => { _scrollbarPressed = false; }));

        foreach (Achievement achievement in Enum.GetValues<Achievement>())
        {
            NAchievementHolder holder = NAchievementHolder.Create(achievement)!;
            _flowContainer.AddChildSafely(holder);
        }
    }

    public override void _EnterTree()
    {
        AchievementsUtil.AchievementsChanged += OnAchievementsChanged;
    }

    public override void _ExitTree()
    {
        AchievementsUtil.AchievementsChanged -= OnAchievementsChanged;
    }

    public override void _GuiInput(InputEvent inputEvent)
    {
        if (!IsVisibleInTree()) return;

        ProcessMouseEvent(inputEvent);
        ProcessScrollEvent(inputEvent);
    }

    public override void _Process(double delta)
    {
        if (!IsVisibleInTree()) return;

        UpdateScrollPosition(delta);
    }

    private void SetScrollPosition(Vector2 position)
    {
        _targetDragPos = position;
    }

    /// <summary>
    /// Detects mouse click up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessMouseEvent(InputEvent inputEvent)
    {
        if (_isDragging && inputEvent is InputEventMouseMotion motion)
        {
            _targetDragPos += new Vector2(0f, motion.Relative.Y);
        }
        else if (inputEvent is InputEventMouseButton button)
        {
            if (button.ButtonIndex == MouseButton.Left)
            {
                if (button.Pressed)
                {
                    _isDragging = true;
                    _startDragPos = _flowContainer.Position;
                    _targetDragPos = _startDragPos;
                }
                else
                {
                    _isDragging = false;
                }
            }
            else if (!button.Pressed)
            {
                _isDragging = false;
            }
        }
    }

    /// <summary>
    /// Detects mouse wheel up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessScrollEvent(InputEvent inputEvent)
    {
        if (inputEvent is InputEventMouseButton mouseButton)
        {
            if (mouseButton.ButtonIndex == MouseButton.WheelUp)
            {
                _targetDragPos += new Vector2(0f, _scrollAmount);
            }
            else if (mouseButton.ButtonIndex == MouseButton.WheelDown)
            {
                _targetDragPos -= new Vector2(0f, _scrollAmount);
            }
        }
        // For OSX touchpad support (namely, macbooks)
        else if (inputEvent is InputEventPanGesture panGesture)
        {
            float panScrollAmount = panGesture.Delta.Y * _panScrollSpeed;
            _targetDragPos -= new Vector2(0f, panScrollAmount);
        }
    }

    private void ProcessGuiFocus(Control focusedControl)
    {
        if (!IsVisibleInTree()) return;
        if (!NControllerManager.Instance!.IsUsingController) return;

        if (focusedControl.GetParent() == _flowContainer)
        {
            float dragPos = -focusedControl.Position.Y + Size.Y * 0.5f;
            dragPos = Mathf.Clamp(dragPos, Mathf.Min(0, ScrollLimitBottom), 0);
            _targetDragPos = new Vector2(_targetDragPos.X, dragPos);
        }
    }

    private void UpdateScrollPosition(double delta)
    {
        if (!_flowContainer.Position.IsEqualApprox(_targetDragPos))
        {
            _flowContainer.Position = _flowContainer.Position.Lerp(_targetDragPos, Mathf.Clamp((float)delta * _dragLerpSpeed, 0, 1));

            // Snap into the target position
            if (_flowContainer.Position.DistanceTo(_targetDragPos) < _snapThreshold)
            {
                _flowContainer.Position = _targetDragPos;
            }

            if (!_scrollbarPressed)
            {
                _scrollbar.SetValueWithoutAnimation(Mathf.Clamp(_flowContainer.Position.Y / ScrollLimitBottom, 0, 1) * 100);
            }
        }

        if (_scrollbarPressed)
        {
            _targetDragPos = new Vector2(_targetDragPos.X, Mathf.Lerp(0, ScrollLimitBottom, (float)_scrollbar.Value / 100));
        }

        if (!_isDragging)
        {
            // We're too far up, scroll down!
            if (_targetDragPos.Y < Mathf.Min(ScrollLimitBottom, 0))
            {
                _targetDragPos = _targetDragPos.Lerp(new Vector2(0f, ScrollLimitBottom), (float)delta * _bounceBackStrength);
            }
            // We're too far down, scroll up!
            else if (_targetDragPos.Y > Mathf.Max(ScrollLimitBottom, 0))
            {
                _targetDragPos = _targetDragPos.Lerp(Vector2.Zero, (float)delta * _bounceBackStrength);
            }
        }
    }

    private void OnAchievementsChanged()
    {
        foreach (NAchievementHolder holder in _flowContainer.GetChildren().OfType<NAchievementHolder>())
        {
            holder.RefreshUnlocked();
        }
    }
}
