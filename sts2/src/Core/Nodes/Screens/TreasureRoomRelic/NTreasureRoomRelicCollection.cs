using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.TreasureRelicPicking;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Screens.TreasureRoomRelic;

/// <summary>
/// Selection screen for relics at the treasure room screen.
/// In singleplayer, this just shows one relic. In multiplayer, this shows multiple relics and handles animation that
/// occurs after all players select a relic.
/// </summary>
public partial class NTreasureRoomRelicCollection : Control, IFocusableScreen
{
    private static string ScenePath => SceneHelper.GetScenePath("screens/shared_relic_picking_screen");
    public static IEnumerable<string> AssetPaths => [ScenePath, ..NCardRewardAlternativeButton.AssetPaths];

    // Amount of milliseconds after the selection screen is opened before the player is allowed to select a relic.
    private const ulong _noSelectionTimeMsec = 200;

    private Control _fightBackstop = default!;
    private NHandImageCollection _hands = default!;

    private readonly List<NTreasureRoomRelicHolder> _multiplayerHolders = [];
    private NTreasureRoomRelicHolder _singleplayerHolder = default!;

    private List<NTreasureRoomRelicHolder> _holdersInUse = [];

    private TaskCompletionSource? _relicPickingTaskCompletionSource;
    private ulong _openedTicks;
    private IClimbState _climbState = default!;

    public override void _Ready()
    {
        _fightBackstop = GetNode<Control>("%FightBackstop");
        _hands = GetNode<NHandImageCollection>("%HandsContainer");

        Control container = GetNode<Control>("Container");
        _singleplayerHolder = container.GetNode<NTreasureRoomRelicHolder>("%SingleplayerRelicHolder");

        foreach (NTreasureRoomRelicHolder holder in container.GetChildren().OfType<NTreasureRoomRelicHolder>())
        {
            if (holder != _singleplayerHolder)
            {
                _multiplayerHolders.Add(holder);
            }
        }

        _fightBackstop.Modulate = _fightBackstop.Modulate with { A = 0f };
        _fightBackstop.Visible = false;

        ClimbManager.Instance.TreasureRoomRelicSynchronizer.VotesChanged += RefreshVotes;
        ClimbManager.Instance.TreasureRoomRelicSynchronizer.RelicsAwarded += OnRelicsAwarded;
    }

    public override void _ExitTree()
    {
        ClimbManager.Instance.TreasureRoomRelicSynchronizer.VotesChanged -= RefreshVotes;
        ClimbManager.Instance.TreasureRoomRelicSynchronizer.RelicsAwarded -= OnRelicsAwarded;
        NControllerManager.Instance!.RemoveScreen(this);
    }

    public void Initialize(IClimbState climbState)
    {
        _climbState = climbState;
        _hands.Initialize(climbState);
    }

    /// <summary>
    /// Initialize the relic display.
    /// This can't get called in _Ready because RelicPickingSynchronizer.BeginRelicPicking has not been called by then.
    /// </summary>
    public void InitializeRelics()
    {
        IReadOnlyList<RelicModel> relics = ClimbManager.Instance.TreasureRoomRelicSynchronizer.CurrentRelics!;

        // If singleplayer, then use the singleplayer-specific relic
        if (relics.Count == 1)
        {
            _singleplayerHolder.Initialize(relics[0], _climbState);
            _singleplayerHolder.Visible = true;
            _singleplayerHolder.Index = 0;
            _singleplayerHolder.Connect(NClickableControl.SignalName.Released, Callable.From<NTreasureRoomRelicHolder>(_ => PickRelic(_singleplayerHolder)));
            _holdersInUse = [_singleplayerHolder];

            foreach (NTreasureRoomRelicHolder multiplayerHolder in _multiplayerHolders)
            {
                multiplayerHolder.Visible = false;
            }
        }
        else
        {
            _singleplayerHolder.Visible = false;

            // Show only the amount of relics equal to the number of players
            for (int i = 0; i < _multiplayerHolders.Count; i++)
            {
                NTreasureRoomRelicHolder holder = _multiplayerHolders[i];
                if (i < relics.Count)
                {
                    holder.Visible = true;
                    holder.Relic.Model = relics[i];
                    holder.Initialize(relics[i], _climbState);
                }
                else
                {
                    holder.Visible = false;
                }

                holder.Index = i;
                holder.Connect(NClickableControl.SignalName.Released, Callable.From<NTreasureRoomRelicHolder>(_ => PickRelic(holder)));
                _holdersInUse.Add(holder);

                // Refresh votes in case a player has already picked
                holder.VoteContainer.RefreshPlayerVotes();
            }

            // In 2-player, the 2nd relic should be positioned at the 4th relic instead (bottom-right corner).
            if (relics.Count == 2)
            {
                _multiplayerHolders[1].Position = _multiplayerHolders[3].Position;
            }
        }

        NControllerManager.Instance!.PushScreen(this);
    }

    /// <summary>
    /// Await this to know when to hide the screen.
    /// </summary>
    public Task RelicPickingFinished()
    {
        _relicPickingTaskCompletionSource = new TaskCompletionSource();
        return _relicPickingTaskCompletionSource.Task;
    }

    /// <summary>
    /// Animates in the relic collection.
    /// </summary>
    /// <param name="chestVisual">Chest visual to fade out.</param>
    public void AnimIn(Node chestVisual)
    {
        Visible = true;
        Modulate = Colors.Transparent;
        Tween tween = CreateTween().SetParallel();
        tween.TweenProperty(this, "modulate", Colors.White, 0.4);
        tween.TweenProperty(chestVisual, "modulate", StsColors.halfTransparentWhite, 0.4);

        foreach (NTreasureRoomRelicHolder holder in _holdersInUse)
        {
            holder.MouseFilter = MouseFilterEnum.Ignore;
            float popDistance = _holdersInUse.Count == 1 ? 150f : 50f;
            float delay = 0.2f + 0.2f * Rng.Chaotic.NextFloat();

            holder.Modulate = Colors.Black;
            holder.Position = holder.Position with { Y = holder.Position.Y + popDistance };

            Tween holderTween = CreateTween().SetParallel();
            holderTween.TweenProperty(holder, "modulate", Colors.White, 0.2)
                .SetDelay(delay);
            holderTween.TweenProperty(holder, "position:y", holder.Position.Y - popDistance, 0.6)
                .SetDelay(delay)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back);
            holderTween.TweenCallback(Callable.From(() => holder.MouseFilter = MouseFilterEnum.Stop)).SetDelay(delay + 0.6f);
        }

        NClimb.Instance!.ScreenStateTracker.SetIsInSharedRelicPickingScreen(true);
    }

    /// <summary>
    /// Animates out the relic collection, after relic picking is done.
    /// </summary>
    /// <param name="chestVisual">Chest visual to fade back in.</param>
    public void AnimOut(Node chestVisual)
    {
        Modulate = Colors.White;
        Tween tween = CreateTween().Parallel();
        tween.TweenProperty(this, "modulate", StsColors.transparentWhite, 0.3);
        tween.TweenProperty(chestVisual, "modulate", Colors.White, 0.3);
        tween.TweenCallback(Callable.From(() => Visible = false));

        NClimb.Instance!.ScreenStateTracker.SetIsInSharedRelicPickingScreen(false);
    }

    private void PickRelic(NTreasureRoomRelicHolder holder)
    {
        if (Time.GetTicksMsec() - _openedTicks <= _noSelectionTimeMsec) return;

        ClimbManager.Instance.TreasureRoomRelicSynchronizer.PickRelicLocally(holder.Index);
    }

    private void OnRelicsAwarded(List<RelicPickingResult> results)
    {
        TaskHelper.RunSafely(AnimateRelicAwards(results));
    }

    private async Task AnimateRelicAwards(List<RelicPickingResult> results)
    {
        // Note that a bunch of hand stuff is called even in singleplayer, but nothing should happen and most waits
        // should be no-op'd
        _hands.BeforeRelicsAwarded();

        List<Task> tasksToWait = [];

        RelicPickingResultType? previousType = null;

        // Animate all the relics that were single-picks, then fights, then consolation prizes
        results.Sort((r1, r2) => r1.type.CompareTo(r2.type));
        foreach (RelicPickingResult result in results)
        {
            NTreasureRoomRelicHolder holder = _holdersInUse.First(h => h.Relic.Model == result.relic);
            holder.AnimateAwayVotes();

            if (previousType != null && result.type != previousType)
            {
                // Wait between grabbing types
                await Cmd.Wait(0.5f);
            }

            if (result.type == RelicPickingResultType.FoughtOver)
            {
                holder.ZIndex = 1;

                _fightBackstop.Visible = true;

                Tween tween = CreateTween();
                tween.TweenProperty(holder, "global_position", (_fightBackstop.Size - holder.Size) * 0.5f, 0.25)
                    .SetTrans(Tween.TransitionType.Back)
                    .SetEase(Tween.EaseType.In);
                tween.TweenProperty(_fightBackstop, "modulate:a", 1f, 0.25);

                _hands.BeforeFightStarted(result.fight!.playersInvolved);
                await ToSignal(tween, Tween.SignalName.Finished);
                await Cmd.Wait(1f);
                await _hands.DoFight(result, holder);

                tween = CreateTween();
                tween.TweenProperty(_fightBackstop, "modulate:a", 0f, 0.25);
                await ToSignal(tween, Tween.SignalName.Finished);

                _fightBackstop.Visible = false;
                holder.ZIndex = 0;
            }
            else
            {
                // Grab all single-picks simultaneously
                // Grab all consolation prizes simultaneously
                NHandImage? hand = _hands.GetHand(result.player.NetId);

                // No hands in singleplayer
                if (hand != null)
                {
                    tasksToWait.Add(TaskHelper.RunSafely(hand.GrabRelic(holder)));
                    await Cmd.Wait(0.25f);
                }
            }

            previousType = result.type;
        }

        await Task.WhenAll(tasksToWait);
        _hands.AnimateHandsAway();

        foreach (RelicPickingResult result in results)
        {
            NTreasureRoomRelicHolder holder = _holdersInUse.First(h => h.Relic.Model == result.relic);
            RelicModel mutableRelic = result.relic.ToMutable();

            // Do not block on the relic being obtained. For relics that require player choice (e.g. Kifuda), their
            // AfterObtained hook would block all input from all players until the choice is received.
            _ = TaskHelper.RunSafely(RelicCmd.Obtain(mutableRelic, result.player));

            if (LocalContext.IsMe(result.player))
            {
                NClimb.Instance!.GlobalUi.RelicInventory.AnimateRelic(mutableRelic, holder.GlobalPosition, holder.Scale);
            }

            // In singleplayer, the relic animates away kind of slowly, so remove it immediately
            if (_climbState.Players.Count == 1)
            {
                holder.Visible = false;
            }
        }

        _relicPickingTaskCompletionSource!.SetResult();
    }

    private void RefreshVotes()
    {
        foreach (NTreasureRoomRelicHolder holder in _holdersInUse)
        {
            holder.VoteContainer.RefreshPlayerVotes();
        }
    }

    public void OnFocusScreen()
    {
        if (_singleplayerHolder.Visible)
        {
            _singleplayerHolder.TryGrabFocus();
        }
        else
        {
            // TODO: Each character should default focus on a different holder
            _multiplayerHolders[0].TryGrabFocus();
        }
    }
    public void OnUnfocusScreen()
    {

    }
}
