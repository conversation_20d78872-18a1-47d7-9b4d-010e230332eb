using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Map;

public partial class NMapScreen : Control, IFocusableScreen, INetCursorPositionTranslator
{
    [Signal]
    public delegate void OpenedEventHandler();

    [Signal]
    public delegate void ClosedEventHandler();

    public static NMapScreen? Instance => NClimb.Instance?.GlobalUi.MapScreen;

    public bool IsTravelEnabled { get; private set; }
    public bool IsDebugTravelEnabled { get; private set; }

    private ActMap _map = NullActMap.Instance;

    private Control _mapContainer = default!;
    private Control _pathsContainer = default!;
    private Control _points = default!;

    // Can be null if no map has been set yet.
    private NBossMapPoint? _bossPointNode;

    // In most cases this is an Ancient node. Except in your first ever climb, where we make it a monster node
    private NMapPoint? _startingPointNode;

    private NMapBg _mapBgContainer = default!;
    private NMapDrawings _mapDrawings = default!;
    private NMapMarker _marker = default!;
    private NBackButton _backButton = default!;
    private NMapDrawingButton _mapDrawingButton = default!;
    private NMapErasingButton _mapErasingButton = default!;
    private NButton _clearMapDrawingButton = default!;
    private Control _mapLegend = default!;
    private Control _mapPrompt = default!;
    private RichTextLabel _mapPromptLabel = default!;
    private Control _backstop = default!;

    private Vector2 _startDragPos;
    private Vector2 _targetDragPos;
    private bool _isDragging;
    private bool _hasPlayedAnimation;

    // This bool is only true between the click and the screen fade out and is meant to fix race conditions.
    public bool IsTraveling { get; set; }

    // WARNING: This dictionary is used stupidly.
    // But this code isn't called often enough for it to matter.
    private readonly Dictionary<MapCoord, NMapPoint> _mapPointDictionary = new();
    private readonly Dictionary<(MapCoord, MapCoord), IReadOnlyList<TextureRect>> _paths = new();

    public event Action<MapPointType>? PointTypeHighlighted;

    // Map scroll/drag variables
    private float _mouseScrollAmount = 40f; // NOTE: How much we travel per scrollWheel tick. Probably better to get the expected scroll distance from the OS
    private float _panScrollSpeed = 50f; // NOTE: How much we travel per touchpad gesture tick.
    private float _controllerScrollAmount = 400f;
    private const float _dragLerpSpeed = 15f;
    private const float _snapThreshold = 0.5f;
    private const float _bounceBackStrength = 12f;
    private const float _scrollLimitTop = 1800f;
    private const float _scrollLimitBottom = -600f;

    // MapPoint visual variables
    private const float _totalHeight = 2325f;
    private const float _totalWidth = 1050f;
    private float _distX; // The X distance between each MapPoint (calculated based on the map and total width)
    private float _distY; // The Y distance between each MapPoint (calculated based on the map and total height)
    private const float _pointJitterX = 21f;
    private const float _pointJitterY = 25f;

    // Path tick visual variables
    private const float _tickDist = 22f;
    private const float _pathPosJitter = 3f;
    private const float _pathAngleJitter = 0.1f;
    private static readonly Vector2 _tickTraveledScale = Vector2.One * 1.2f;

    // Start of Act Animation variables
    private CancellationTokenSource? _startAnimCancelToken;
    private const int _topOfMapWaitDuration = 1000; // How long we're at the top before the anim starts (in ms)
    private float _mapScrollAnimTimer;
    private const string _mapTickScenePath = "res://scenes/ui/map_dot.tscn";
    private const float _defaultMapScrollAnimDuration = 3.25f;
    private ClimbState _climbState = default!;

    public Dictionary<Player, MapCoord?> PlayerVoteDictionary { get; private set; } = [];
    public NMapDrawings Drawings => _mapDrawings;
    public NetScreenType ScreenType => NetScreenType.Map;

    private float ScrollAnimDuration => _defaultMapScrollAnimDuration * (SaveManager.Instance.SettingsSave.FastMode == FastModeType.Fast ? 0.5f : 1f);

    public override void _Ready()
    {
        GetNode<MegaLabel>("MapLegend/Header").Text = new LocString("map", "LEGEND_HEADER").GetFormattedText();

        _mapContainer = GetNode<Control>("TheMap"); // NOTE: Named "TheMap" instead of Map because of data structure names, sorry.
        _mapBgContainer = GetNode<NMapBg>("%MapBg");
        _pathsContainer = GetNode<Control>("TheMap/Paths");
        _points = GetNode<Control>("TheMap/Points");
        _marker = GetNode<NMapMarker>("TheMap/MapMarker");
        _mapDrawings = GetNode<NMapDrawings>("TheMap/Drawings");
        _backButton = GetNode<NBackButton>("Back");
        _backButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnBackButtonPressed));
        _backButton.Disable();
        _mapLegend = GetNode<Control>("MapLegend");
        _mapPrompt = GetNode<Control>("%MapPrompt");
        _mapPromptLabel = GetNode<RichTextLabel>("%MapPromptLabel");
        _backstop = GetNode<Control>("%Backstop");

        _mapDrawingButton = GetNode<NMapDrawingButton>("%MapDrawingButton");
        _mapDrawingButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnMapDrawingButtonPressed));
        _mapErasingButton = GetNode<NMapErasingButton>("%MapErasingButton");
        _mapErasingButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnMapErasingButtonPressed));

        _clearMapDrawingButton = GetNode<NButton>("%ClearMapDrawingButton");
        _clearMapDrawingButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnClearMapDrawingButtonPressed));

        ClimbManager.Instance.MapSelectionSynchronizer.PlayerVoteChanged += OnPlayerVoteChanged;
        ClimbManager.Instance.MapSelectionSynchronizer.PlayerVoteCancelled += OnPlayerVoteCancelled;
        ProcessMode = Visible ? ProcessModeEnum.Inherit : ProcessModeEnum.Disabled;

        Connect(CanvasItem.SignalName.VisibilityChanged, Callable.From(OnVisibilityChanged));

        // GlobalUi is not ready yet, so defer this
        Callable.From(() =>
            NCapstoneContainer.Instance!.Connect(NCapstoneContainer.SignalName.Changed, Callable.From(OnCapstoneChanged))
        ).CallDeferred();
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        _startAnimCancelToken?.Cancel();

        ClimbManager.Instance.MapSelectionSynchronizer.PlayerVoteChanged -= OnPlayerVoteChanged;
        ClimbManager.Instance.MapSelectionSynchronizer.PlayerVoteCancelled -= OnPlayerVoteCancelled;

        NControllerManager.Instance!.RemoveScreen(this);
    }

    public void Initialize(ClimbState climbState)
    {
        _climbState = climbState;
        _mapDrawings.Initialize(ClimbManager.Instance.NetService, _climbState);
        _marker.Initialize(LocalContext.GetMe(_climbState)!);
        _mapBgContainer.Initialize(_climbState);
    }

    /// <summary>
    /// This is to be called by the backend so we can setup our map screen.
    /// </summary>
    /// <param name="map">The new map to set.</param>
    /// <param name="seed">The seed to use for the visual jitter on the map node positions.</param>
    /// <param name="clearDrawings">True if map drawings should be cleared.</param>
    public void SetMap(ActMap map, uint seed, bool clearDrawings)
    {
        _map = map;

        // Step 1: Clear old junk.
        _mapPointDictionary.Clear();
        _paths.Clear();
        RemoveAllMapPointsAndPaths();
        _marker.ResetMapPoint();

        if (clearDrawings)
        {
            _mapDrawings.ClearAllLines();
        }

        _hasPlayedAnimation = false;

        // Because each act map has a different number of rows, we need to calculate the row spacing dynamically
        int rowCount = map.GetRowCount();
        int colCount = map.GetColumnCount();
        _distY = _totalHeight / (rowCount - 1);
        _distX = _totalWidth / colCount;

        Rng jitterRng = new(seed, $"map_jitter_{_climbState.CurrentActIndex}");

        // Step 2: Initialize the MapPointNodes and apply rotation/position jitter + save this info in a HashMap _pointPositions
        Vector2 mapOffset = new(-500f, 740f);
        Vector2 mapPointSpacing = new(_distX, -_distY);
        foreach (MapPoint mapPoint in map.GetAllMapPoints())
        {
            // Instantiate Node + set its position. ~Beware~ magic numbers afoot
            NNormalMapPoint newPoint = NNormalMapPoint.Create(mapPoint, this, _climbState);

            newPoint.Position = new Vector2(mapPoint.coord.col, mapPoint.coord.row) * mapPointSpacing + mapOffset;

            // Apply jitter
            float xJitterOffset = jitterRng.NextFloat(-_pointJitterX, _pointJitterX);
            float yJitterOffset = jitterRng.NextFloat(-_pointJitterY, _pointJitterY);
            newPoint.Position += new Vector2(xJitterOffset, yJitterOffset);

#if DEBUG
            // Visualize the column and row of each MapPointNode
            // newPoint.GetChild<Label>(2).Text = $"[{mapPoint.coord.row},{mapPoint.coord.col}]";
            // newPoint.GetChild<Label>(2).Visible = true;
#endif

            // Place the Node into the scene and store this data into a Dictionary to help us draw paths later
            _mapPointDictionary.Add(mapPoint.coord, newPoint);
            _points.AddChildSafely(newPoint);

            // Apply jitter rotation (has to be after this.AddChildSafely())
            newPoint.SetAngle(Rng.Chaotic.NextGaussianFloat(stdDev: 8f));
        }

        // Step 3: Initialize the boss MapPoint
        _bossPointNode = NBossMapPoint.Create(map.BossMapPoint, this, _climbState);
        _bossPointNode.Position = new Vector2(-200f, -1980f); // NOTE: X position is Size/2f
        _points.AddChildSafely(_bossPointNode);
        _mapPointDictionary[map.BossMapPoint.coord] = _bossPointNode;

        // Step 4: Initialize the ancient MapPoint
        if (map.StartingMapPoint.PointType == MapPointType.Ancient)
        {
            _startingPointNode = NAncientMapPoint.Create(map.StartingMapPoint, this, _climbState);
            _startingPointNode.Position = new Vector2(-80f, map.StartingMapPoint.coord.row * -_distY + 720f); // TODO: cleanup
        }
        else
        {
            // If we're in the first run, the ancient point is actually a normal encounter
            _startingPointNode = NNormalMapPoint.Create(map.StartingMapPoint, this, _climbState);
            _startingPointNode.Position = new Vector2(-80f, map.StartingMapPoint.coord.row * -_distY + 800f); // TODO: cleanup
        }

        _points.AddChildSafely(_startingPointNode);
        _mapPointDictionary[map.StartingMapPoint.coord] = _startingPointNode;

        // Step 5: Draw the path lines from each MapPoint to its children
        foreach (MapPoint mapPoint in map.GetAllMapPoints())
        {
            DrawPaths(_mapPointDictionary[mapPoint.coord], mapPoint);
        }

        // Step 5: Draw the path lines from the Ancient Map point each of its children
        DrawPaths(_startingPointNode, map.StartingMapPoint);

        IReadOnlyList<MapCoord> visitedCoords = _climbState.VisitedMapCoords;

        for (int i = 0; i < visitedCoords.Count - 1; i++)
        {
            if (_paths.TryGetValue((visitedCoords[i], visitedCoords[i + 1]), out IReadOnlyList<TextureRect>? pathTicks))
            {
                foreach (TextureRect tick in pathTicks)
                {
                    tick.Modulate = _climbState.Act.MapTraveledColor;
                    tick.Scale = _tickTraveledScale;
                }
            }
        }

        // Check for vote icons that came in while we were loading
        InitMapVotes();

        foreach (NMapPoint point in _mapPointDictionary.Values)
        {
            point.RefreshPlayerVotes();
        }

        // Step 6: set up controller navigation between points
        for (int i = 0; i < map.GetRowCount(); i++)
        {
            IEnumerable<MapPoint> row = map.GetPointsInRow(i);
            List<NMapPoint> mapPoints = row.Select(p => _mapPointDictionary[p.coord]).ToList();
            for (int j = 0; j < mapPoints.Count; j++)
            {
                mapPoints[j].FocusNeighborLeft = j > 0 ? mapPoints[j - 1].GetPath() : mapPoints[j].GetPath();
                mapPoints[j].FocusNeighborRight = j < mapPoints.Count - 1 ? mapPoints[j + 1].GetPath() : mapPoints[j].GetPath();
                mapPoints[j].FocusNeighborTop = mapPoints[j].GetPath();
                mapPoints[j].FocusNeighborBottom = mapPoints[j].GetPath();
            }
        }

        _startingPointNode.FocusNeighborLeft = _startingPointNode.GetPath();
        _startingPointNode.FocusNeighborRight = _startingPointNode.GetPath();
        _startingPointNode.FocusNeighborTop = _startingPointNode.GetPath();
        _startingPointNode.FocusNeighborBottom = _startingPointNode.GetPath();

        _bossPointNode.FocusNeighborLeft = _bossPointNode.GetPath();
        _bossPointNode.FocusNeighborRight = _bossPointNode.GetPath();
        _bossPointNode.FocusNeighborTop = _bossPointNode.GetPath();
        _bossPointNode.FocusNeighborBottom = _bossPointNode.GetPath();

        // If the map is already shown and travel is already enabled, then we need to enable newly travleable points.
        // This can happen in multiplayer if one player has the map open while another player takes Golden Compass.
        if (IsVisible())
        {
            RecalculateTravelability();
            RefreshAllPointVisuals();
        }
    }

    private void DrawPaths(NMapPoint mapPointNode, MapPoint mapPoint)
    {
        foreach (MapPoint mapPointChild in mapPoint.Children)
        {
            if (!_mapPointDictionary.TryGetValue(mapPointChild.coord, out NMapPoint? mapPointChildNode))
            {
                throw new InvalidOperationException($"Map point child with coord {mapPointChild.coord} is not in the map point dictionary!");
            }

            Vector2 startPosition = GetLineEndpoint(mapPointNode);
            Vector2 endPosition = GetLineEndpoint(mapPointChildNode);

            IReadOnlyList<TextureRect> pathTickMarks = CreatePath(startPosition, endPosition);
            _paths.Add((mapPoint.coord, mapPointChild.coord), pathTickMarks);
        }
    }

    private Vector2 GetLineEndpoint(NMapPoint point)
    {
        // Normal map points are centered, other map points are top-left aligned
        if (point is NNormalMapPoint)
        {
            return point.Position;
        }

        return point.Position + point.Size * 0.5f;
    }

    private void RecalculateTravelability()
    {
        if (_climbState.VisitedMapCoords.Any())
        {
            // First, run through all the nodes and mark them untravelable.
            foreach (NMapPoint node in _mapPointDictionary.Values)
            {
                node.State = MapPointState.Untravelable;
            }

            // Next, run through all the already-visited nodes and mark them visited.
            foreach (MapCoord coord in _climbState.VisitedMapCoords)
            {
                _mapPointDictionary[coord].State = MapPointState.Traveled;
            }

            MapCoord lastVisitedCoord = _climbState.VisitedMapCoords[^1];

            if (lastVisitedCoord.row == _map.GetRowCount() - 1)
            {
                // Assume the next row should be the boss.
                _bossPointNode!.State = MapPointState.Travelable;
                return;
            }

            IEnumerable<MapPoint> travelablePoints;

            // Check if the Flight modifier is present.
            if (!_climbState.Modifiers.OfType<Flight>().Any())
            {
                // Default case - last visited node's children are travelable.
                travelablePoints = _mapPointDictionary[lastVisitedCoord].Point.Children;
            }
            else
            {
                // Flight modifier is enabled - all nodes in the next row are travelable.
                travelablePoints = _map.GetPointsInRow(lastVisitedCoord.row + 1);
            }

            // Finally, mark the nodes as travelable.
            foreach (MapPoint point in travelablePoints)
            {
                _mapPointDictionary[point.coord].State = MapPointState.Travelable;
            }
        }
        else
        {
            _startingPointNode!.State = MapPointState.Travelable;
        }
    }

    private void InitMapVotes()
    {
        foreach (Player player in _climbState.Players)
        {
            MapCoord? coord = ClimbManager.Instance.MapSelectionSynchronizer.GetVote(player)?.coord;

            if (coord != null)
            {
                OnPlayerVoteChangedInternal(player, null, coord.Value);
            }
        }
    }

    public void OnMapPointSelectedLocally(NMapPoint point)
    {
        Player localPlayer = LocalContext.GetMe(_climbState)!;

        if (!PlayerVoteDictionary.TryGetValue(localPlayer, out MapCoord? coord) || coord != point.Point.coord)
        {
            // Immediately show that we were voted on by ourselves.
            // We'll get this called twice - once now, once later when the action round-trips & reaches us - but this
            // makes it feel significantly more responsive.
            OnPlayerVoteChangedInternal(localPlayer, ClimbManager.Instance.MapSelectionSynchronizer.GetVote(localPlayer)?.coord, point.Point.coord);

            // Inform everyone else that we have voted for this map point.
            ClimbLocation currentLocation = new(_climbState.CurrentMapCoord, _climbState.CurrentActIndex);
            MapVote vote = new()
            {
                coord = point.Point.coord,
                mapGenerationCount = ClimbManager.Instance.MapSelectionSynchronizer.MapGenerationCount
            };
            VoteForMapCoordAction action = new(LocalContext.GetMe(_climbState)!, currentLocation, vote);
            ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(action);
        }
        else
        {
            // If the local player already voted for this coordinate, ping the map node instead.
            if (_climbState.Players.Count <= 1) return;

            ClimbManager.Instance.FlavorSynchronizer.SendMapPing(point.Point.coord);
        }
    }

    private void OnPlayerVoteChanged(Player player, MapVote? oldLocation, MapVote? newLocation)
    {
        Log.Info($"Player vote changed for {player.NetId}: {oldLocation}->{newLocation}");
        // If this is the local player, we have already updated our map vote based on our local inputs. If local inputs
        // are done very fast in sequence, this might be out-of-date.
        if (LocalContext.IsMe(player)) return;

        OnPlayerVoteChangedInternal(player, oldLocation?.coord, newLocation?.coord);
    }

    private void OnPlayerVoteCancelled(Player player)
    {
        Log.Info($"Player vote cancelled for {player.NetId}");
        OnPlayerVoteChangedInternal(player, PlayerVoteDictionary[player], null);
    }

    private void OnPlayerVoteChangedInternal(Player player, MapCoord? oldCoord, MapCoord? newCoord)
    {
        // If we are in singleplayer, TravelToMapNode is called right after this, and that will do an animation.
        if (_climbState.Players.Count <= 1) return;

        PlayerVoteDictionary[player] = newCoord;

        if (oldCoord != null)
        {
            NMapPoint oldPoint = _mapPointDictionary[oldCoord.Value];
            oldPoint.RefreshPlayerVotes();
        }
        else if (_climbState.CurrentLocation.coord != null)
        {
            // If there was no old coord, we render the vote at the current map point, so refresh that
            NMapPoint oldPoint = _mapPointDictionary[_climbState.CurrentLocation.coord.Value];
            oldPoint.RefreshPlayerVotes();
        }

        if (newCoord != null)
        {
            NMapPoint newPoint = _mapPointDictionary[newCoord.Value];
            newPoint.RefreshPlayerVotes();
        }
        else if (_climbState.CurrentLocation.coord != null)
        {
            // If there is no new coord, we render the vote at the current map point, so refresh that
            NMapPoint newPoint = _mapPointDictionary[_climbState.CurrentLocation.coord.Value];
            newPoint.RefreshPlayerVotes();
        }
    }

    /// <summary>
    /// Sets the marker to be at the specified coordinate.
    /// Since the player starts in the Neow room in a new run, TravelToMapCoord isn't called so we need to init the marker
    /// externally.
    /// </summary>
    /// <param name="coord"></param>
    public void InitMarker(MapCoord coord)
    {
        NMapPoint node = _mapPointDictionary[coord];
        _marker.SetMapPoint(node);
    }

    /// <summary>
    /// Animation and logic which occurs when a coord is finalized for travel.
    /// In multiplayer, this only occurs when all votes have been received and a map point has been selected.
    /// </summary>
    /// <param name="coord">The coordinate to travel to</param>
    public async Task TravelToMapCoord(MapCoord coord)
    {
        IsTraveling = true;
        RecalculateTravelability();

        // In singleplayer, this should do nothing because travel happens immediately when you click on a map node.
        // In multiplayer, you can be looking at your deck when travel happens, and we want to close that screen
        // specifically. Don't close other capstones (like the settings menu).
        if (NCapstoneContainer.Instance!.CurrentCapstoneScreen is NDeckViewScreen)
        {
            NCapstoneContainer.Instance.Close();
        }

        NMapPoint node = _mapPointDictionary[coord];
        node.OnSelected();

        float vfxScale = 1f;
        if (node is NAncientMapPoint)
        {
            vfxScale = 1.5f;
        }
        else if (node is NBossMapPoint)
        {
            vfxScale = 2f;
        }

        NMapNodeSelectVfx vfx = NMapNodeSelectVfx.Create(vfxScale)!;
        node.AddChildSafely(vfx);
        vfx.Position += node.PivotOffset;

        _marker.HideMapPoint();
        IReadOnlyList<MapCoord> visitedCoords = _climbState.VisitedMapCoords;
        IsTravelEnabled = false;

        if (visitedCoords.Any() && _paths.TryGetValue((visitedCoords[^1], node.Point.coord), out IReadOnlyList<TextureRect>? pathTicks))
        {
            float animLength = 0;
            switch (SaveManager.Instance.SettingsSave.FastMode)
            {
                case FastModeType.Instant:
                    break;
                case FastModeType.Fast:
                    animLength = 0.5f;
                    break;
                case FastModeType.Normal:
                    animLength = 0.8f;
                    break;
                case FastModeType.Slow:
                    animLength = 1.2f;
                    break;
                case FastModeType.None:
                // Fallthrough
                default:
                    throw new ArgumentOutOfRangeException();
            }

            float waitPerTick = animLength / pathTicks.Count;

            foreach (TextureRect tick in pathTicks)
            {
                // Tween punch scale
                await Cmd.Wait(waitPerTick);

                tick.Modulate = StsColors.pathDotTraveled;

                Tween tickPunch = CreateTween();
                tickPunch.TweenProperty(tick, "scale", _tickTraveledScale, 0.4)
                    .From(Vector2.One * 1.7f)
                    .SetEase(Tween.EaseType.Out)
                    .SetTrans(Tween.TransitionType.Cubic);
            }
        }

        _marker.SetMapPoint(node);
        NDebugAudioManager.Instance?.Play(TmpSfx.mapSelect);

        await Cmd.CustomScaledWait(0.2f, 0.6f, 1f);
        await ClimbManager.Instance.EnterMapCoord(coord);

        RefreshAllPointVisuals();

        // Clear old player vote icons
        PlayerVoteDictionary.Clear();

        foreach (NMapPoint point in _mapPointDictionary.Values)
        {
            point.RefreshPlayerVotes();
        }
    }

    /// <summary>
    /// Just a helper class to make Init() easier to parse, don't mind me.
    /// </summary>
    private void RemoveAllMapPointsAndPaths()
    {
        _points.FreeChildren();
        _pathsContainer.FreeChildren();

        // We might be removing all the points/paths before setting a map for the first time, so we check for null here.
        _bossPointNode?.QueueFreeSafely();
        _startingPointNode?.QueueFreeSafely();
    }

    /// <summary>
    /// Creates a bunch of dots to form a path from start to end
    /// </summary>
    /// <param name="start"></param>
    /// <param name="end"></param>
    [SuppressMessage("Performance", "CA1859:Use concrete types when possible for improved performance")]
    private IReadOnlyList<TextureRect> CreatePath(Vector2 start, Vector2 end)
    {
        List<TextureRect> ticks = [];

        // Calculate the distance and direction between the start and end points
        Vector2 direction = (end - start).Normalized();
        float angle = direction.Angle() + Mathf.Pi * 0.5f;
        float distance = start.DistanceTo(end);

        // Calculate the number of ticks based on the spacing
        int numberOfTicks = (int)(distance / _tickDist) + 1;

        // Create and place the ticks
        for (int i = 1; i < numberOfTicks; i++)
        {
            float spacing = i * _tickDist;

            TextureRect tick = PreloadManager.Cache.GetScene(_mapTickScenePath).Instantiate<TextureRect>();
            tick.Position = start + direction * spacing;
            tick.Position -= new Vector2(Size.X * 0.5f - 20f, Size.Y * 0.5f - 20f);
            tick.Position += new Vector2(Rng.Chaotic.NextFloat(-_pathPosJitter, _pathPosJitter), Rng.Chaotic.NextFloat(-_pathPosJitter, _pathPosJitter));
            tick.FlipH = Rng.Chaotic.NextBool();
            tick.Rotation = angle + Rng.Chaotic.NextGaussianFloat(stdDev: _pathAngleJitter);
            tick.Modulate = _climbState.Act.MapUntraveledColor;

            // TODO: Tick rotation and jitter
            // TODO2: Something fun like beziers and curves, check Unity proj
            _pathsContainer.AddChildSafely(tick);
            ticks.Add(tick);
        }

        return ticks;
    }

    public static IEnumerable<string> AssetPaths => NMapDrawings.AssetPaths.Append(_mapTickScenePath);

    // TODO2: Open(), we'll want to set the _targetPosition so that our "current node" sits around 1/3 or 1/4 bottom of the screen

    public override void _Process(double delta)
    {
        if (!IsVisibleInTree() || _startAnimCancelToken != null) return;

        UpdateScrollPosition(delta);
    }

    private void UpdateScrollPosition(double delta)
    {
        if (_mapContainer.Position != _targetDragPos)
        {
            _mapContainer.Position = _mapContainer.Position.Lerp(_targetDragPos, (float)delta * _dragLerpSpeed);

            // Snap into the target position
            if (_mapContainer.Position.DistanceTo(_targetDragPos) < _snapThreshold)
            {
                _mapContainer.Position = _targetDragPos;
            }
        }

        if (!_isDragging)
        {
            // We're too far down, scroll up!
            if (_targetDragPos.Y < _scrollLimitBottom)
            {
                _targetDragPos = _targetDragPos.Lerp(new Vector2(0f, _scrollLimitBottom), (float)delta * _bounceBackStrength);
            }
            // We're too far up, scroll down!
            else if (_targetDragPos.Y > _scrollLimitTop)
            {
                _targetDragPos = _targetDragPos.Lerp(new Vector2(0f, _scrollLimitTop), (float)delta * _bounceBackStrength);
            }
        }

        NGame.Instance!.RemoteCursorContainer.ForceUpdateAllCursors();
    }

    public override void _GuiInput(InputEvent inputEvent)
    {
        if (!IsVisibleInTree()) return;

        ProcessMouseEvent(inputEvent);
        ProcessScrollEvent(inputEvent);
    }

    /// <summary>
    /// Detects mouse click up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessMouseEvent(InputEvent inputEvent)
    {
        if (_mapDrawings.GetLocalDrawingMode() != DrawingMode.None)
        {
            if (inputEvent is InputEventMouseButton { ButtonIndex: MouseButton.Left } button)
            {
                if (button.Pressed)
                {
                    _mapDrawings.BeginLine(_mapDrawings.GetGlobalTransform().Inverse() * button.GlobalPosition, null);
                }
                else
                {
                    _mapDrawings.StopLine();
                }
            }
        }
        // No scrolling while player is drawing
        else
        {
            if (_isDragging && inputEvent is InputEventMouseMotion motion)
            {
                _targetDragPos += new Vector2(0f, motion.Relative.Y);
            }
            else if (inputEvent is InputEventMouseButton button)
            {
                if (button.ButtonIndex == MouseButton.Left)
                {
                    if (button.Pressed && CanScroll())
                    {
                        _isDragging = true;
                        _startDragPos = _mapContainer.Position;
                        _targetDragPos = _startDragPos;
                        TryCancelStartOfActAnim();
                    }
                    else
                    {
                        _isDragging = false;
                    }
                }
                else if (!button.Pressed)
                {
                    _isDragging = false;
                }
            }
        }

        // Can use right mouse to draw without being in drawing mode
        if (inputEvent is InputEventMouseButton buttonEvent)
        {
            if (buttonEvent.ButtonIndex == MouseButton.Right)
            {
                if (buttonEvent.Pressed && !_mapDrawings.IsLocalDrawing())
                {
                    _mapDrawings.BeginLine(_mapDrawings.GetGlobalTransform().Inverse() * buttonEvent.GlobalPosition, DrawingMode.Drawing);
                }
                else
                {
                    _mapDrawings.StopLine();
                }
            }
            else if (buttonEvent.ButtonIndex == MouseButton.Middle)
            {
                if (buttonEvent.Pressed && !_mapDrawings.IsLocalDrawing())
                {
                    _mapDrawings.BeginLine(_mapDrawings.GetGlobalTransform().Inverse() * buttonEvent.GlobalPosition, DrawingMode.Erasing);
                }
                else
                {
                    _mapDrawings.StopLine();
                }
            }
        }

        if (inputEvent is InputEventMouseMotion motionEvent && _mapDrawings.IsLocalDrawing())
        {
            _mapDrawings.UpdateCurrentLinePosition(_mapDrawings.GetGlobalTransform().Inverse() * motionEvent.GlobalPosition);
        }
    }

    /// <summary>
    /// Detects mouse wheel up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessScrollEvent(InputEvent inputEvent)
    {
        if (!CanScroll()) return;

        if (inputEvent is InputEventMouseButton mouseButton)
        {
            if (mouseButton.ButtonIndex == MouseButton.WheelUp)
            {
                _targetDragPos += new Vector2(0f, _mouseScrollAmount);
            }
            else if (mouseButton.ButtonIndex == MouseButton.WheelDown)
            {
                _targetDragPos += new Vector2(0f, -_mouseScrollAmount);
            }

            TryCancelStartOfActAnim();
        }
        // For OSX touchpad support (namely, macbooks)
        else if (inputEvent is InputEventPanGesture panGesture && panGesture.Delta.Y != 0f)
        {
            _targetDragPos += new Vector2(0f, panGesture.Delta.Y * _panScrollSpeed);
            TryCancelStartOfActAnim();
        }
    }

    private void ProcessControllerEvent(InputEvent inputEvent)
    {
        if (inputEvent.IsActionPressed(MegaInput.up) && CanScroll())
        {
            _targetDragPos += new Vector2(0f, _controllerScrollAmount);
            TryCancelStartOfActAnim();
        }
        else if (inputEvent.IsActionPressed(MegaInput.down) && CanScroll())
        {
            _targetDragPos += new Vector2(0f, -_controllerScrollAmount);
            TryCancelStartOfActAnim();
        }
        else if (inputEvent.IsActionPressed(MegaInput.right) || inputEvent.IsActionPressed(MegaInput.select))
        {
            if (_climbState.ActFloor == 0)
            {
                _targetDragPos = new Vector2(0f, _scrollLimitBottom);
            }
            else
            {
                int mapCoordY = _climbState.CurrentMapCoord?.row ?? 0;
                _targetDragPos = new Vector2(0f, _scrollLimitBottom + mapCoordY * _distY);
            }
        }
    }

    public void SetTravelEnabled(bool enabled)
    {
        IsTravelEnabled = enabled && HookBus.Instance.ShouldProceedToNextMapPoint();
        RefreshAllPointVisuals();
    }

    public void SetDebugTravelEnabled(bool enabled)
    {
        IsDebugTravelEnabled = enabled;
        RefreshAllPointVisuals();
    }

    private void RefreshAllPointVisuals()
    {
        foreach (NMapPoint point in _mapPointDictionary.Values)
        {
            point.RefreshVisualsInstantly();
        }

        _mapPointDictionary.Values.FirstOrDefault(n => n.IsEnabled)?.TryGrabFocus();
    }

    private void PlayStartOfActAnimation()
    {
        if (_hasPlayedAnimation)
        {
            Log.Warn($"Tried to play start of act animation twice! Ignoring second try");
            return;
        }

        _hasPlayedAnimation = true;

        NActBanner? bannerVfx = NActBanner.Create(_climbState.Act, _climbState.CurrentActIndex);
        NClimb.Instance?.GlobalUi.MapScreen.AddChildSafely(bannerVfx);

        _startAnimCancelToken = new CancellationTokenSource();
        TaskHelper.RunSafely(StartOfActAnim());
    }

    private async Task StartOfActAnim()
    {
        _mapScrollAnimTimer = 0f;

        Vector2 startPos = new(0f, _scrollLimitTop);
        Vector2 endPos = new(0f, _scrollLimitBottom);
        _mapContainer.Position = startPos;

        await Task.Delay(_topOfMapWaitDuration);

        while (_mapScrollAnimTimer < ScrollAnimDuration)
        {
            if (_startAnimCancelToken!.IsCancellationRequested)
            {
                _startAnimCancelToken = null;
                return;
            }

            _mapContainer.Position = startPos.Lerp(endPos, Ease.CubicInOut(_mapScrollAnimTimer / ScrollAnimDuration));

            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            _mapScrollAnimTimer += (float)GetProcessDeltaTime();
        }

        _startAnimCancelToken = null;
        _targetDragPos = endPos;
        _mapContainer.Position = endPos;

        _mapPromptLabel.Text = new LocString("map", "STARTING_ROOM_PROMPT").GetFormattedText();
        float t = 0f;

        while (t < 1f)
        {
            _mapPrompt.Modulate = Colors.Transparent.Lerp(Colors.White, t);
            t += (float)GetProcessDeltaTime();
            await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        }

        _mapPrompt.Modulate = Colors.White;
    }

    /// <summary>
    /// During the first half of the map scroll animation, moving the map is disabled.
    /// </summary>
    private bool CanScroll()
    {
        return _startAnimCancelToken == null || _mapScrollAnimTimer > ScrollAnimDuration * 0.5f;
    }

    /// <summary>
    /// Allows the player to abort (aka cancel) the start of act animation early.
    /// </summary>
    private void TryCancelStartOfActAnim()
    {
        // Player can scroll or drag after 50%% of the animation plays
        if (_startAnimCancelToken != null && _mapScrollAnimTimer > ScrollAnimDuration * 0.5f)
        {
            _startAnimCancelToken.Cancel();
            _targetDragPos = _mapContainer.Position;
        }
    }

    private void OnVisibilityChanged()
    {
        if (Visible)
        {
            ClimbManager.Instance.InputSynchronizer.StartOverridingCursorPositioning(this);
            NControllerManager.Instance!.PushScreen(this);
        }
        else
        {
            _isDragging = false;
            ClimbManager.Instance.InputSynchronizer.StopOverridingCursorPositioning();
            // We don't want this here because we don't want it to play when we transition to the next screen.
            // NAudioManager.Instance?.PlayOneShot(Sfx.closeMapScreen);
            _backButton.Disable();

            // If we somehow got closed while we were in the midst of drawing, cancel out of drawing
            _mapDrawings.StopLine();
            _mapDrawings.SetDrawingMode(DrawingMode.None);
            UpdateDrawingButtonStates();
            _mapPrompt.Modulate = Colors.Transparent;

            NControllerManager.Instance!.RemoveScreen(this);
        }
    }

    private void OnCapstoneChanged()
    {
        // Hide backstop if capstones are shown - pretend like we're using the shared backstop
        _backstop.Visible = !(NCapstoneContainer.Instance?.InUse ?? false);
    }

    public void Close()
    {
        if (!Visible) return;

        Visible = false;
        ProcessMode = ProcessModeEnum.Disabled;

        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Unpause();
        }

        // If the local player has voted for a map point, and we're not moving to a new map point, cancel the local
        // player's map vote
        Player localPlayer = LocalContext.GetMe(_climbState)!;
        if (PlayerVoteDictionary.ContainsKey(localPlayer) && !IsTraveling)
        {
            Log.Debug("Cancelling player map vote because they closed the map");

            // Since we want to be responsive, this screen relies on the local player updating their own votes
            OnPlayerVoteChangedInternal(localPlayer, ClimbManager.Instance.MapSelectionSynchronizer.GetVote(localPlayer)?.coord, null);

            ClimbLocation currentLocation = new(_climbState.CurrentMapCoord, _climbState.CurrentActIndex);
            VoteForMapCoordAction action = new(localPlayer, currentLocation, null);
            ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(action);
        }

        NClimb.Instance?.GlobalUi.TopBar.Map.ToggleAnimState();
        NDebugAudioManager.Instance?.Play(TmpSfx.closeMapScreen);
        EmitSignalClosed();
    }

    public NMapScreen Open()
    {
        if (Visible) return this;
        if (_climbState.IsGameOver) return this;

        Visible = true;
        ProcessMode = ProcessModeEnum.Inherit;

        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Pause();
        }

        // Checking just whether we have played the animation falls over if we save/load the game, so additionally check
        // if we have just started the act.
        // In act 1, we start at Neow. In acts 2 and 3, we start directly at the map.
        bool shouldPlayAnimation = _climbState.CurrentActIndex == 0 ? _climbState.ActFloor == 1 : _climbState.ActFloor == 0;

        if (shouldPlayAnimation && !_hasPlayedAnimation)
        {
            PlayStartOfActAnimation();
        }
        else // If we're not animating, we can enable the back button, map events, and scroll position right away
        {
            int mapCoordY = _climbState.CurrentMapCoord?.row ?? 0;
            _targetDragPos = new Vector2(0f, _scrollLimitBottom + mapCoordY * _distY);
            _mapContainer.Position = new Vector2(0f, _scrollLimitBottom + mapCoordY * _distY);
        }

        if (_climbState.ActFloor > 0)
        {
            _backButton.Enable();
        }

        RecalculateTravelability();

        // If we have visited coords, set the marker to the last visited coord
        if (_climbState.VisitedMapCoords.Count != 0)
        {
            MapCoord lastVisited = _climbState.VisitedMapCoords[^1];

            // If the last visited coord isn't the boss point or an ancient point
            // since these points take up a row all of their own, we just need to make sure they are the same row.
            // This is to account for if the map point unexpectedly changed.
            if (_bossPointNode!.Point.coord.row != lastVisited.row && _startingPointNode!.Point.coord.row != lastVisited.row)
            {
                NMapPoint lastVisitedMapPoint = _mapPointDictionary[lastVisited];
                _marker.SetMapPoint(lastVisitedMapPoint);
            }
        }

        NDebugAudioManager.Instance?.Play(TmpSfx.openMapScreen);
        EmitSignalOpened();
        return this;
    }

    private void OnBackButtonPressed(NButton _)
    {
        Close();
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent.IsActionReleased(DebugHotkey.unlockCharacters))
        {
            _mapLegend.Visible = !_mapLegend.Visible;
            NGame.Instance!.AddChildSafely(NFullscreenTextVfx.Create(_mapLegend.Visible ? "Show Legend" : "Hide Legend")!);
        }

        if (!IsVisibleInTree()) return;

        ProcessControllerEvent(inputEvent);
    }

    private void OnMapDrawingButtonPressed(NButton _)
    {
        _mapDrawings.SetDrawingMode(_mapDrawings.GetLocalDrawingMode() == DrawingMode.Drawing ? DrawingMode.None : DrawingMode.Drawing);
        UpdateDrawingButtonStates();
    }

    private void OnMapErasingButtonPressed(NButton _)
    {
        _mapDrawings.SetDrawingMode(_mapDrawings.GetLocalDrawingMode() == DrawingMode.Erasing ? DrawingMode.None : DrawingMode.Erasing);
        UpdateDrawingButtonStates();
    }

    private void UpdateDrawingButtonStates()
    {
        _mapDrawingButton.SetIsDrawing(_mapDrawings.GetLocalDrawingMode() == DrawingMode.Drawing);
        _mapErasingButton.SetIsErasing(_mapDrawings.GetLocalDrawingMode() == DrawingMode.Erasing);
    }

    private void OnClearMapDrawingButtonPressed(NButton _)
    {
        _mapDrawings.ClearDrawnLines();
    }

    public void HighlightPointType(MapPointType pointType)
    {
        PointTypeHighlighted?.Invoke(pointType);
    }

    public void OnFocusScreen()
    {
        // Defer the call to give the map screen a chance to refresh map point clickability
        Callable.From(() =>_mapPointDictionary.Values.FirstOrDefault(n => n.IsEnabled)?.TryGrabFocus()).CallDeferred();
    }

    public void PingMapCoord(MapCoord coord, Player player)
    {
        if (!_mapPointDictionary.TryGetValue(coord, out NMapPoint? mapPoint))
        {
            Log.Error($"Someone tried to ping map coord {coord} that doesn't exist!");
            return;
        }

        NMapPingVfx vfx = NMapPingVfx.Create()!;
        vfx.Modulate = player.Character.MapDrawingColor;

        mapPoint.AddChildSafely(vfx);
        mapPoint.MoveChild(vfx, 0);
        vfx.Position = Vector2.Zero;
        vfx.Size *= mapPoint.Size.X / 64f;
        vfx.PivotOffset = vfx.Size * 0.5f;

        NClimb.Instance!.GlobalUi.MultiplayerPlayerContainer.FlashPlayerReady(player);

        NDebugAudioManager.Instance!.Play(TmpSfx.mapPing, 1f, PitchVariance.Medium);
    }

    public void OnUnfocusScreen() { }

    public Vector2 GetNetPositionFromScreenPosition(Vector2 screenPosition)
    {
        Vector2 controlSpaceMousePos = _mapBgContainer.GetGlobalTransformWithCanvas().Inverse() * screenPosition;
        Vector2 halfExtents = _mapBgContainer.Size * 0.5f;
        Vector2 normalizedHalfExtents = new(960f, halfExtents.Y);
        Vector2 netPosition = (controlSpaceMousePos - halfExtents) / normalizedHalfExtents;
        return netPosition;
    }

    private Vector2 GetMapPositionFromNetPosition(Vector2 netPosition)
    {
        Vector2 halfExtents = _mapBgContainer.Size * 0.5f;
        Vector2 normalizedHalfExtents = new(960f, halfExtents.Y);
        Vector2 controlSpaceMousePos = netPosition * normalizedHalfExtents + halfExtents;
        return controlSpaceMousePos;
    }

    public Vector2 GetScreenPositionFromNetPosition(Vector2 netPosition)
    {
        Vector2 controlSpaceMousePos = GetMapPositionFromNetPosition(netPosition);
        Vector2 screenPosition = _mapBgContainer.GetGlobalTransformWithCanvas() * controlSpaceMousePos;
        return screenPosition;
    }

    public bool IsNodeOnScreen(NMapPoint mapPoint)
    {
        float yPos = mapPoint.GlobalPosition.Y;
        return yPos > 0f && yPos < Size.Y;
    }

    public void CleanUp()
    {
        // If we don't do this, if the player starts a multiplayer game, we will never unpause combat
        if (ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer)
        {
            CombatManager.Instance.Unpause();
        }
    }
}
