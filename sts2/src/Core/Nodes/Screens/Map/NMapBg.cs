using System;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Map;

/// <summary>
/// Exists solely to move the y position of the map asset when the aspect ratio becomes narrower than 16:9
/// </summary>
public partial class NMapBg : VBoxContainer
{
    private IClimbState _climbState = default!;

    private TextureRect _mapTop = default!;
    private TextureRect _mapMid = default!;
    private TextureRect _mapBot = default!;
    private NMapDrawings _drawings = default!;

    private Window _window = default!;
    private const float _sixteenByNine = 16f / 9f;
    private const float _fourByThree = 4f / 3f;
    private const float _defaultY = -1620f;
    private const float _adjustY = -1540f;
    private float _offsetX;

    public override void _Ready()
    {
        _mapTop = GetNode<TextureRect>("MapTop");
        _mapMid = GetNode<TextureRect>("MapMid");
        _mapBot = GetNode<TextureRect>("MapBot");
        _drawings = GetNode<NMapDrawings>("%Drawings");

        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));
        OnWindowChange();

        _offsetX = Position.X;
        Connect(CanvasItem.SignalName.VisibilityChanged, Callable.From(OnVisibilityChanged));
    }

    public void Initialize(IClimbState climbState)
    {
        _climbState = climbState;
    }

    private void OnVisibilityChanged()
    {
        ActModel act = _climbState.Act;
        _mapTop.Texture = act.MapTopBg;
        _mapMid.Texture = act.MapMidBg;
        _mapBot.Texture = act.MapBotBg;
    }

    private void OnWindowChange()
    {
        float ratio = Math.Max(_fourByThree, (float)_window.Size.X / _window.Size.Y);

        // Narrow case (window is narrower than 16:9)
        if (ratio < _sixteenByNine)
        {
            // Cubic remap function based on the ratio difference of the screen aspect ratio vs our default (16:9)
            float normalizedRatio = (ratio - _fourByThree) / (_sixteenByNine - _fourByThree);
            Position = new Vector2(_offsetX, Mathf.Remap(Ease.CubicOut(normalizedRatio), 0f, 1f, _adjustY, _defaultY));
        }
        // Default case (window is 16:9 or wider)
        else
        {
            Position = new Vector2(_offsetX, _defaultY);
        }

        _drawings.RepositionBasedOnBackground(this);
    }
}
