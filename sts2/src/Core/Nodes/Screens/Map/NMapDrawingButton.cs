using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Map;

public partial class NMapDrawingButton : NBaseMapDrawingButton
{
    private static readonly StringName _notDrawingImagePath = "res://images/packed/map/map_pencil_1.png";
    private static readonly StringName _whileDrawingImagePath = "res://images/packed/map/map_pencil_2.png";

    public static IEnumerable<string> AssetPaths => [_notDrawingImagePath, _whileDrawingImagePath];

    private TextureRect _image = default!;

    private HoverTip _hoverTip;
    private static readonly Vector2 _hoverTipOffset = new(180f, -150f);

    public override void _Ready()
    {
        ConnectSignals();
        _image = GetNode<TextureRect>("Image");

        _hoverTip = new HoverTip(new LocString("map", "DRAWING_BUTTON.title"),
            new LocString("map", "DRAWING_BUTTON.description"));
    }

    public void SetIsDrawing(bool isDrawing)
    {
        _image.Texture = PreloadManager.Cache.GetTexture2D(isDrawing ? _whileDrawingImagePath : _notDrawingImagePath);
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip);
        tip.GlobalPosition = GlobalPosition + Vector2.Up * (tip.Size.Y + 16f);
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();
        NHoverTipSet.Remove(this);
    }
}
