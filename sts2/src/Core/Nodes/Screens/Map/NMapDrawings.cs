using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Flavor;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Saves.MapDrawing;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Map;

/// <summary>
/// Takes care of drawing both local and remote players' map drawings.
///
/// Notes on the tech art side:
/// Lines are placed into a subviewport which renders to a half-resolution texture. Drawn lines are simple colored Line2D
/// nodes. Eraser lines are Line2D nodes with a shader with a subtractive blend mode. More details are in the shader itself.
/// The subviewport texture is drawn on top of the map. Importantly, it is drawn using the premultiplied-alpha blend mode.
/// Without this, black artifacts are seen on the edges of the lines because of the intermediate subviewport texture
/// blending.
/// </summary>
public partial class NMapDrawings : Control
{
    // The minimum millseconds between updates.
    private const int _minUpdateMsec = 50;

    private static readonly string _lineDrawScenePath = SceneHelper.GetScenePath("screens/map/map_line_draw");
    private static readonly string _lineEraseScenePath = SceneHelper.GetScenePath("screens/map/map_line_erase");
    private static readonly string _playerDrawingPath = SceneHelper.GetScenePath("screens/map/map_drawing");

    public const string drawingCursorPath = "res://images/packed/common_ui/cursor_pencil.png";
    public const string drawingCursorTiltedPath = "res://images/packed/common_ui/cursor_pencil_tilted.png";
    public static readonly Vector2 drawingCursorHotspot = Vector2.Zero;
    public const string erasingCursorPath = "res://images/packed/common_ui/cursor_eraser.png";
    public const string erasingCursorTiltedPath = "res://images/packed/common_ui/cursor_eraser_tilted.png";
    public static readonly Vector2 erasingCursorHotspot = Vector2.Down * 6f;

    private const float _minimumPointDistance = 2f;

    private static IEnumerable<string> SelfAssetPaths =>
    [
        _lineDrawScenePath, _lineEraseScenePath, drawingCursorPath, drawingCursorTiltedPath
    ];

    public static IEnumerable<string> AssetPaths => SelfAssetPaths.Concat(NMapDrawingButton.AssetPaths);

    private INetGameService _netService = default!;
    private IPlayerCollection _playerCollection = default!;
    private PackedScene _lineDrawScene = default!;
    private PackedScene _lineEraseScene = default!;
    private NCursorManager _cursorManager = default!;
    private Material _eraserMaterial = default!;

    private Vector2 _defaultSize;
    private readonly List<DrawingState> _drawingStates = [];

    private MapDrawingMessage? _queuedMessage;

    // Last time we sent a message to peers.
    private ulong _lastMessageMsec;

    // Task which will send _updateToSend to peers.
    private Task? _sendMessageTask;

    private class DrawingState
    {
        public bool IsDrawing => currentlyDrawingLine != null;
        public DrawingMode CurrentDrawingMode => overrideDrawingMode ?? drawingMode;

        public DrawingMode? overrideDrawingMode;
        public DrawingMode drawingMode;
        public ulong playerId;
        public Line2D? currentlyDrawingLine;
        public required SubViewport drawViewport;
    }

    public override void _Ready()
    {
        _lineDrawScene = PreloadManager.Cache.GetScene(_lineDrawScenePath);
        _lineEraseScene = PreloadManager.Cache.GetScene(_lineEraseScenePath);
        _cursorManager = NGame.Instance!.CursorManager;

        Line2D temp = _lineEraseScene.Instantiate<Line2D>();
        _eraserMaterial = temp.Material;
        temp.QueueFreeSafely();

        _defaultSize = Size;
    }

    public void Initialize(INetGameService netService, IPlayerCollection playerCollection)
    {
        _netService = netService;
        _playerCollection = playerCollection;

        _netService.RegisterMessageHandler<MapDrawingMessage>(HandleDrawingMessage);
        _netService.RegisterMessageHandler<ClearMapDrawingsMessage>(HandleClearMapDrawingsMessage);
        _netService.RegisterMessageHandler<MapDrawingModeChangedMessage>(HandleMapDrawingModeChangedMessage);
    }

    public override void _ExitTree()
    {
        _netService.UnregisterMessageHandler<MapDrawingMessage>(HandleDrawingMessage);
        _netService.UnregisterMessageHandler<ClearMapDrawingsMessage>(HandleClearMapDrawingsMessage);
        _netService.UnregisterMessageHandler<MapDrawingModeChangedMessage>(HandleMapDrawingModeChangedMessage);
    }

    /// <summary>
    /// Called when the local player begins drawing a line.
    /// </summary>
    /// <param name="position">The position at which the player started drawing.</param>
    /// <param name="overrideDrawingMode">The mode to override the current mode with. If null, the current mode in the
    /// set using SetDrawingMode is used. If it is null and the current mode is None, an exception is thrown.</param>
    public void BeginLine(Vector2 position, DrawingMode? overrideDrawingMode)
    {
        BeginLine(_netService.NetId, position, overrideDrawingMode);

        _queuedMessage ??= new MapDrawingMessage();
        _queuedMessage.events.Add(new NetMapDrawingEvent { type = MapDrawingEventType.BeginLine, position = ToNetPosition(position), overrideDrawingMode = overrideDrawingMode });
        TrySendSyncMessage();
        UpdateLocalCursor();
    }

    public void UpdateCurrentLinePosition(Vector2 position)
    {
        UpdateCurrentLinePosition(_netService.NetId, position);

        _queuedMessage ??= new MapDrawingMessage();
        _queuedMessage.events.Add(new NetMapDrawingEvent { type = MapDrawingEventType.ContinueLine, position = ToNetPosition(position) });
        TrySendSyncMessage();
    }

    public void StopLine()
    {
        StopDrawingLine(_netService.NetId);

        _queuedMessage ??= new MapDrawingMessage();
        _queuedMessage.events.Add(new NetMapDrawingEvent { type = MapDrawingEventType.EndLine });
        TrySendSyncMessage();
        UpdateLocalCursor();
    }

    public void SetDrawingMode(DrawingMode drawingMode)
    {
        SetDrawingMode(_netService.NetId, drawingMode);

        MapDrawingModeChangedMessage message = new() { drawingMode = drawingMode };
        _netService.SendMessage(message);
        UpdateLocalCursor();
    }

    public void ClearDrawnLines()
    {
        ClearAllLinesForPlayer(_netService.NetId);
        _netService.SendMessage(new ClearMapDrawingsMessage());
    }

    public bool IsDrawing(ulong playerId)
    {
        return GetDrawingStateForPlayer(playerId).IsDrawing;
    }

    public bool IsLocalDrawing()
    {
        return GetDrawingStateForPlayer(_netService.NetId).IsDrawing;
    }

    public DrawingMode GetDrawingMode(ulong playerId)
    {
        return GetDrawingStateForPlayer(playerId).CurrentDrawingMode;
    }

    public DrawingMode GetLocalDrawingMode()
    {
        return GetDrawingStateForPlayer(_netService.NetId).CurrentDrawingMode;
    }

    private Vector2 ToNetPosition(Vector2 pos)
    {
        // X should be relative to middle, Y relative to top
        pos.X -= Size.X / 2f;

        // Normalize position to handle different aspect ratios. X is static so that it doesn't stretch with screen size
        pos /= new Vector2(960f, Size.Y);

        return pos;
    }

    private Vector2 FromNetPosition(Vector2 pos)
    {
        pos *= new Vector2(960f, Size.Y);
        pos.X += Size.X / 2f;
        return pos;
    }

    private void HandleDrawingMessage(MapDrawingMessage message, ulong senderId)
    {
        foreach (NetMapDrawingEvent ev in message.events)
        {
            if (ev.type == MapDrawingEventType.BeginLine)
            {
                if (GetDrawingMode(senderId) != DrawingMode.None)
                {
                    // If we dropped an end line message, handle that now
                    StopDrawingLine(senderId);
                }

                BeginLine(senderId, FromNetPosition(ev.position), ev.overrideDrawingMode);
            }
            else if (ev.type == MapDrawingEventType.ContinueLine)
            {
                UpdateCurrentLinePosition(senderId, FromNetPosition(ev.position));
            }
            else
            {
                StopDrawingLine(senderId);
            }
        }
    }

    private void HandleClearMapDrawingsMessage(ClearMapDrawingsMessage message, ulong senderId)
    {
        ClearAllLinesForPlayer(senderId);
    }

    private void HandleMapDrawingModeChangedMessage(MapDrawingModeChangedMessage message, ulong senderId)
    {
        SetDrawingMode(senderId, message.drawingMode);
    }

    private void BeginLine(ulong playerId, Vector2 position, DrawingMode? overrideDrawingMode)
    {
        DrawingState state = GetDrawingStateForPlayer(playerId);
        Player player = _playerCollection.GetPlayer(playerId)!;

        DrawingMode drawingMode = overrideDrawingMode ?? state.drawingMode;

        if (drawingMode == DrawingMode.None) throw new InvalidOperationException($"Player {playerId} is not currently in a drawing mode and no override was passed!");

        state.overrideDrawingMode = overrideDrawingMode;
        state.currentlyDrawingLine = CreateLineForPlayer(player, drawingMode == DrawingMode.Erasing);
        state.currentlyDrawingLine.AddPoint(position * 0.5f);

        // This renders a dot when we start drawing
        state.currentlyDrawingLine.AddPoint(position * 0.5f + new Vector2(0f, 0.5f));

        state.drawViewport.AddChildSafely(state.currentlyDrawingLine);
        NGame.Instance!.RemoteCursorContainer.DrawingCursorStateChanged(playerId);
    }

    private Line2D CreateLineForPlayer(Player player, bool isErasing)
    {
        PackedScene scene = isErasing ? _lineEraseScene : _lineDrawScene;
        Line2D line = scene.Instantiate<Line2D>();
        line.DefaultColor = player.Character.MapDrawingColor;
        line.ClearPoints();
        line.Position = Vector2.Zero;

        return line;
    }

    private void StopDrawingLine(ulong playerId)
    {
        DrawingState state = GetDrawingStateForPlayer(playerId);
        state.overrideDrawingMode = null;
        state.currentlyDrawingLine = null;
        NGame.Instance!.RemoteCursorContainer.DrawingCursorStateChanged(playerId);
    }

    private void SetDrawingMode(ulong playerId, DrawingMode drawingMode)
    {
        DrawingState state = GetDrawingStateForPlayer(playerId);
        state.drawingMode = drawingMode;
        NGame.Instance!.RemoteCursorContainer.DrawingCursorStateChanged(playerId);
    }

    private void UpdateCurrentLinePosition(ulong playerId, Vector2 position)
    {
        DrawingState state = GetDrawingStateForPlayer(playerId);
        if (state.currentlyDrawingLine == null) return;

        Vector2 lastPosition = state.currentlyDrawingLine.Points[^1];

        if (lastPosition.DistanceSquaredTo(position) < _minimumPointDistance * _minimumPointDistance) return;
        state.currentlyDrawingLine.AddPoint(position * 0.5f);
    }

    private DrawingState GetDrawingStateForPlayer(ulong playerId)
    {
        DrawingState? state = _drawingStates.FirstOrDefault(s => s.playerId == playerId);

        if (state == null)
        {
            Control mapDrawingScene = PreloadManager.Cache.GetScene(_playerDrawingPath).Instantiate<Control>();
            this.AddChildSafely(mapDrawingScene);

            state = new DrawingState
            {
                playerId = playerId,
                drawViewport = mapDrawingScene.GetNode<SubViewport>("DrawViewport"),
            };

            // HACK: For some reason, for some frames the subviewport texture is invalid and displays some random video
            // memory, which results in a big flicker the first time you open the map. Make it invisible and get it to
            // clear itself first
            TaskHelper.RunSafely(SetVisibleLater(mapDrawingScene));

            _drawingStates.Add(state);
        }

        return state;
    }

    private async Task SetVisibleLater(Control mapDrawingScene)
    {
        TextureRect drawingTexture = mapDrawingScene.GetNode<TextureRect>("DrawViewportTextureRect");
        SubViewport drawViewport = mapDrawingScene.GetNode<SubViewport>("DrawViewport");
        drawViewport.RenderTargetUpdateMode = SubViewport.UpdateMode.Always;
        drawingTexture.Visible = false;

        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);

        drawingTexture.Visible = true;
        drawViewport.RenderTargetUpdateMode = SubViewport.UpdateMode.WhenVisible;
    }

    public void ClearAllLines()
    {
        foreach (DrawingState state in _drawingStates)
        {
            foreach (Line2D line in state.drawViewport.GetChildren().OfType<Line2D>())
            {
                line.QueueFreeSafely();
            }
        }
    }

    public SerializableMapDrawings GetSerializableMapDrawings()
    {
        SerializableMapDrawings drawings = new();

        foreach (DrawingState state in _drawingStates)
        {
            SerializablePlayerMapDrawings playerDrawings = new() { playerId = state.playerId };
            drawings.drawings.Add(playerDrawings);

            foreach (Line2D line in state.drawViewport.GetChildren().OfType<Line2D>())
            {
                SerializableMapDrawingLine serializableDrawingLine = new() { mapPoints = [] };
                serializableDrawingLine.isEraser = line.Material == _eraserMaterial;
                playerDrawings.lines.Add(serializableDrawingLine);

                foreach (Vector2 point in line.Points)
                {
                    serializableDrawingLine.mapPoints.Add(ToNetPosition(point));
                }
            }
        }

        return drawings;
    }

    public void LoadDrawings(SerializableMapDrawings drawings)
    {
        foreach (SerializablePlayerMapDrawings playerDrawings in drawings.drawings)
        {
            DrawingState state = GetDrawingStateForPlayer(playerDrawings.playerId);

            foreach (SerializableMapDrawingLine serializableLine in playerDrawings.lines)
            {
                Line2D line = CreateLineForPlayer(_playerCollection.GetPlayer(state.playerId)!, serializableLine.isEraser);
                state.drawViewport.AddChildSafely(line);

                foreach (Vector2 point in serializableLine.mapPoints)
                {
                    line.AddPoint(FromNetPosition(point));
                }
            }
        }
    }

    private void ClearAllLinesForPlayer(ulong playerId)
    {
        DrawingState state = GetDrawingStateForPlayer(playerId);

        foreach (Line2D line in state.drawViewport.GetChildren().OfType<Line2D>())
        {
            line.QueueFreeSafely();
        }
    }

    /// <summary>
    /// Sends a sync message if enough time has passed since the last one, or buffers it to be sent otherwise.
    /// </summary>
    private void TrySendSyncMessage()
    {
        // If we already have a message queued up, do nothing
        if (_sendMessageTask != null) return;

        // Net service may disconnect during the queue time
        if (!_netService.IsConnected) return;

        int delay = (int)(_lastMessageMsec + _minUpdateMsec - Time.GetTicksMsec());

        // If enough time has passed since the last message, send it immediately
        if (delay <= 0)
        {
            _sendMessageTask = TaskHelper.RunSafely(SendSyncMessageAfterSmallDelay());
        }
        else
        {
            // Otherwise, schedule the send for the future
            _sendMessageTask = TaskHelper.RunSafely(QueueSyncMessage(delay));
        }
    }

    private async Task QueueSyncMessage(int delayMsec)
    {
        await Task.Delay(delayMsec);
        SendSyncMessage();
    }

    private async Task SendSyncMessageAfterSmallDelay()
    {
        await Task.Yield();
        SendSyncMessage();
    }

    private void SendSyncMessage()
    {
        // Since this happens after a delay, we might be disconnected
        if (!_netService.IsConnected) return;

        _netService.SendMessage(_queuedMessage!);

        _lastMessageMsec = Time.GetTicksMsec();
        _queuedMessage = null;
        _sendMessageTask = null;
    }

    private void UpdateLocalCursor()
    {
        DrawingState state = GetDrawingStateForPlayer(_netService.NetId);

        if (state.CurrentDrawingMode == DrawingMode.Drawing)
        {
            Image drawingCursor = PreloadManager.Cache.GetAsset<Image>(drawingCursorPath);
            Image drawingCursorTilted = PreloadManager.Cache.GetAsset<Image>(drawingCursorTiltedPath);
            _cursorManager.OverrideCursor(drawingCursor, drawingCursorTilted, drawingCursorHotspot);
        }
        else if (state.CurrentDrawingMode == DrawingMode.Erasing)
        {
            Image erasingCursor = PreloadManager.Cache.GetAsset<Image>(erasingCursorPath);
            Image erasingCursorTilted = PreloadManager.Cache.GetAsset<Image>(erasingCursorTiltedPath);
            _cursorManager.OverrideCursor(erasingCursor, erasingCursorTilted, erasingCursorHotspot);
        }
        else
        {
            _cursorManager.StopOverridingCursor();
        }
    }

    public void RepositionBasedOnBackground(Control mapBg)
    {
        // Keep at top-center of the map background.
        Position = new Vector2(mapBg.Position.X + (mapBg.Size.X - Size.X) / 2f, mapBg.Position.Y);
    }
}
