using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Nodes.Screens.FeedbackScreen;

public partial class NSendFeedbackFlower : Control
{
    private const string _normalImage = "res://images/atlases/compressed.sprites/feedback/flower.tres";
    private const string _noddingImage = "res://images/atlases/compressed.sprites/feedback/flower_happy.tres";
    private const string _anticipationImage = "res://images/atlases/compressed.sprites/feedback/flower_anticipation.tres";

    private Tween? _tween;
    private Vector2 _originalPosition;

    public NSendFeedbackCartoon Cartoon { get; private set; } = default!;

    public enum State
    {
        None,
        Nodding,
        Anticipation,
        NoddingFast,
    }

    public State MyState { get; private set; }

    public override void _Ready()
    {
        _originalPosition = Position;
        Cartoon = GetNode<NSendFeedbackCartoon>("Flower");
    }

    public void SetState(State state)
    {
        if (state == State.Nodding)
        {
            _tween?.Kill();
            _tween = CreateTween();
            _tween.TweenProperty(this, "rotation", Mathf.DegToRad(8f), 0.5);
            _tween.TweenProperty(this, "rotation", Mathf.DegToRad(-8f), 0.5);
            _tween.SetLoops();

            Cartoon.Texture = PreloadManager.Cache.GetTexture2D(_noddingImage);
        }
        else if (state == State.NoddingFast)
        {
            _tween?.Kill();
            _tween = CreateTween();
            _tween.TweenProperty(this, "rotation", Mathf.DegToRad(8f), 0.2);
            _tween.TweenProperty(this, "rotation", Mathf.DegToRad(-8f), 0.2);
            _tween.SetLoops();

            Cartoon.Texture = PreloadManager.Cache.GetTexture2D(_noddingImage);
        }
        else if (state == State.Anticipation)
        {
            _tween?.Kill();
            _tween = null;

            Cartoon.Texture = PreloadManager.Cache.GetTexture2D(_anticipationImage);
            _tween = CreateTween();
            _tween.TweenInterval(0.05f);
            _tween.TweenCallback(Callable.From(SetRandomPosition));
            _tween.SetLoops();
        }
        else
        {
            Rotation = 0;
            _tween?.Kill();
            _tween = null;

            Cartoon.Texture = PreloadManager.Cache.GetTexture2D(_normalImage);
        }

        MyState = state;
    }

    private void SetRandomPosition()
    {
        Position = _originalPosition + new Vector2(Rng.Chaotic.NextFloat(-3f, 3f), Rng.Chaotic.NextFloat(-3f, 3f));
    }
}
