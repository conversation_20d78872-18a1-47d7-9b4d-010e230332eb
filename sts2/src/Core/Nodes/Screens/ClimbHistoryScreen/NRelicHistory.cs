using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Exceptions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Relics;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;

public partial class NRelicHistory : VBoxContainer
{
    private readonly LocString _relicHeader = new(NClimbHistory.locTable, "RELIC_HISTORY.header");
    private readonly LocString _relicCategories = new(NClimbHistory.locTable, "RELIC_HISTORY.categories");
    private MegaRichTextLabel _headerLabel = default!;
    private Control _relicsContainer = default!;

    [Signal]
    public delegate void HoveredEventHandler(NRelicBasicHolder relic);

    [Signal]
    public delegate void UnhoveredEventHandler(NRelicBasicHolder relic);

    public override void _Ready()
    {
        _headerLabel = GetNode<MegaRichTextLabel>("Header");
        _relicsContainer = GetNode<Control>("%RelicsContainer");
    }

    public void LoadRelics(IEnumerable<SerializableRelic> relics)
    {
        StringBuilder sb = new();

        foreach (Node child in _relicsContainer.GetChildren())
        {
            child.QueueFreeSafely();
        }

        Dictionary<RelicRarity, int> rarityCount = new();

        foreach (RelicRarity rarity in Enum.GetValues(typeof(RelicRarity)))
        {
            rarityCount.Add(rarity, 0);
        }

        List<SerializableRelic> relicSaves = relics.ToList();

        foreach (SerializableRelic relicSave in relicSaves)
        {
            RelicModel relic;
            try
            {
                relic = RelicModel.FromSerializable(relicSave);
            }
            catch (ModelNotFoundException)
            {
                relic = ModelDb.Relic<DeprecatedRelic>().ToMutable();
            }

            NRelicBasicHolder holder = NRelicBasicHolder.Create(relic)!;
            _relicsContainer.AddChildSafely(holder);

            holder.Connect(Control.SignalName.FocusEntered, Callable.From(() => { EmitSignal(SignalName.Hovered, holder); }));
            holder.Connect(Control.SignalName.FocusExited, Callable.From(() => { EmitSignal(SignalName.Unhovered, holder); }));
            holder.Connect(Control.SignalName.MouseEntered, Callable.From(() => { EmitSignal(SignalName.Hovered, holder); }));
            holder.Connect(Control.SignalName.MouseExited, Callable.From(() => { EmitSignal(SignalName.Unhovered, holder); }));
            holder.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(_ => OnRelicClicked(holder.Relic)));
            rarityCount[relic.Rarity]++;
        }

        _relicHeader.Add("totalRelics", relicSaves.Count);

        foreach (KeyValuePair<RelicRarity, int> kvp in rarityCount)
        {
            _relicCategories.Add(kvp.Key + "Relics", kvp.Value);
        }

        sb.Append($"[gold][b]{_relicHeader.GetFormattedText()}[/b][/gold]");
        sb.Append(_relicCategories.GetFormattedText());
        _headerLabel.Text = sb.ToString();
    }

    /// <summary>
    /// Opens the Inspect Relic screen and allows the player to paginate through the rest of the top bar relics.
    /// </summary>
    /// <param name="node"></param>
    private void OnRelicClicked(NRelic node)
    {
        List<RelicModel> relics = [];

        foreach (NRelicBasicHolder relic in _relicsContainer.GetChildren().OfType<NRelicBasicHolder>())
        {
            relics.Add(relic.Relic.Model);
        }

        NGame.Instance!.InspectRelicScreen.Open(relics, node.Model);
    }
}
