using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Models.Exceptions;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Potions;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves;
using FileAccess = Godot.FileAccess;

namespace MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;

/// <summary>
/// Rename to NClimbHistoryScreen someday.
/// </summary>
public partial class NClimbHistory : NSubmenu
{
    public const string locTable = "climb_history";

    private Control _screenContents = default!;

    // Top bar nodes
    private Control _playerIconContainer = default!;

    private Label _hpLabel = default!;
    private Label _goldLabel = default!;
    private Control _potionHolder = default!;
    private Label _floorLabel = default!;
    private Label _timeLabel = default!;

    // Top right corner Nodes
    private RichTextLabel _dateLabel = default!;
    private RichTextLabel _seedLabel = default!;
    private RichTextLabel _gameModeLabel = default!;
    private RichTextLabel _buildLabel = default!;

    private MegaRichTextLabel _deathQuoteLabel = default!;
    private NMapPointHistory _mapPointHistory = default!;
    private NRelicHistory _relicHistory = default!;
    private NDeckHistory _deckHistory = default!;
    private Control _outOfDateVisual = default!;

    private List<string>? _climbs;
    private int _index;
    private ClimbHistory _history = default!;

    // Buttons!
    private NButton _prevButton = default!;
    private NButton _nextButton = default!;
    private NClimbHistoryPlayerIcon? _selectedPlayerIcon;

    private Tween? _screenTween;

    public override void _Ready()
    {
        ConnectSignals();

        _screenContents = GetNode<Control>("ScreenContents");

        // Top bar
        _playerIconContainer = GetNode<Control>("%PlayerIconContainer");
        _hpLabel = GetNode<Label>("%HpLabel");
        _goldLabel = GetNode<Label>("%GoldLabel");
        _potionHolder = GetNode<Control>("%PotionHolders");
        _floorLabel = GetNode<Label>("%FloorNumLabel");
        _timeLabel = GetNode<Label>("%ClimbTimeLabel");

        // Top right corner
        _dateLabel = GetNode<RichTextLabel>("%DateLabel");
        _seedLabel = GetNode<RichTextLabel>("%SeedLabel");
        _gameModeLabel = GetNode<RichTextLabel>("%GameModeLabel");
        _buildLabel = GetNode<RichTextLabel>("%BuildLabel");

        _deathQuoteLabel = GetNode<MegaRichTextLabel>("%DeathQuoteLabel");
        _mapPointHistory = GetNode<NMapPointHistory>("%MapPointHistory");
        _relicHistory = GetNode<NRelicHistory>("%RelicHistory");
        _deckHistory = GetNode<NDeckHistory>("%DeckHistory");
        _outOfDateVisual = GetNode<Control>("%OutOfDateVisual");

        _prevButton = GetNode<NButton>("LeftArrow");
        _prevButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnLeftButtonButtonReleased));
        _nextButton = GetNode<NButton>("RightArrow");
        _nextButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnRightButtonButtonReleased));

        _mapPointHistory.SetDeckHistory(_deckHistory);
        _mapPointHistory.SetRelicHistory(_relicHistory);
    }

    private void OnLeftButtonButtonReleased(NButton _)
    {
        TaskHelper.RunSafely(LoadClimb(_index + 1));

        _screenTween?.Kill();
        _screenTween = CreateTween().SetParallel();
        _screenTween.TweenProperty(_screenContents, "position", Vector2.Zero, 0.5)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.Out)
            .From(Vector2.Zero + new Vector2(-1000f, 0f));
        _screenTween.TweenProperty(_screenContents, "modulate:a", 1f, 0.4)
            .SetTrans(Tween.TransitionType.Linear)
            .From(0f);
    }

    private void OnRightButtonButtonReleased(NButton _)
    {
        TaskHelper.RunSafely(LoadClimb(_index - 1));

        _screenTween?.Kill();
        _screenTween = CreateTween().SetParallel();
        _screenTween.TweenProperty(_screenContents, "position", Vector2.Zero, 0.5)
            .SetTrans(Tween.TransitionType.Expo)
            .SetEase(Tween.EaseType.Out)
            .From(Vector2.Zero + new Vector2(1000f, 0f));
        _screenTween.TweenProperty(_screenContents, "modulate:a", 1f, 0.4)
            .SetTrans(Tween.TransitionType.Linear)
            .From(0f);
    }

    public bool CanBeShown()
    {
        return SaveManager.Instance.GetClimbHistoryCount() > 0;
    }

    public override void OnSubmenuOpened()
    {
        TaskHelper.RunSafely(LoadClimb(0));
    }

    protected override void OnSubmenuShown()
    {
        if (!CanBeShown()) throw new InvalidOperationException("Tried to show climb history screen with no climbs!");

        _screenTween?.Kill();
        _screenTween = CreateTween();
        _screenTween.TweenProperty(_screenContents, "modulate:a", 1f, 0.4)
            .From(0f);
    }

    protected override void OnSubmenuHidden()
    {
        _screenTween?.Kill();
    }

    private async Task LoadClimb(int index)
    {
        // While loading climb, disable interaction
        _prevButton.Disable();
        _nextButton.Disable();
        _outOfDateVisual.Visible = false;

        // Have to reload it every time because we could abandon a climb from the menu
        _climbs = SaveManager.Instance.GetClimbHistories().ToList();
        _climbs.Reverse();

        try
        {
            FileAccessStream stream = new(_climbs[index], FileAccess.ModeFlags.Read);
            ClimbHistory? climb = await JsonSerializer.DeserializeAsync<ClimbHistory>(stream, ClimbHistory.defaultSerializerSettings);
            if (climb == null) throw new JsonException($"Loaded invalid climb history at index {index}");
            LoadClimb(climb);
        }
        catch (Exception e)
        {
            Log.Error($"Exception {e} while loading climb at path {_climbs[index]} (index {index})");
            // TODO: re-save as broken_19826387162.climb or something and put em somewhere else so we stop trying to load these outdated climbs :(
            _outOfDateVisual.Visible = true;
            throw;
        }
        finally
        {
            _index = index;

            if (index < _climbs.Count - 1)
            {
                _prevButton.Enable();
            }

            if (index > 0)
            {
                _nextButton.Enable();
            }

            _prevButton.Visible = index < _climbs.Count - 1;
            _nextButton.Visible = index > 0;
        }
    }

    private void LoadClimb(ClimbHistory history)
    {
        _selectedPlayerIcon?.Deselect();
        _selectedPlayerIcon = null;

        foreach (NClimbHistoryPlayerIcon icon in _playerIconContainer.GetChildren().OfType<NClimbHistoryPlayerIcon>())
        {
            icon.QueueFreeSafely();
        }

        _history = history;

        ulong playerId = PlatformUtil.GetLocalPlayerId(history.PlatformType);

        LoadPlayerFloor(history);
        LoadGameModeDetails(history);
        LoadTimeDetails(history);
        _mapPointHistory.LoadHistory(history);

        bool playerSelected = false;
        NClimbHistoryPlayerIcon? firstIcon = null;

        foreach (ClimbHistoryPlayer player in history.Players)
        {
            NClimbHistoryPlayerIcon playerIcon = PreloadManager.Cache.GetScene(NClimbHistoryPlayerIcon.scenePath).Instantiate<NClimbHistoryPlayerIcon>();
            firstIcon ??= playerIcon;
            _playerIconContainer.AddChildSafely(playerIcon);

            playerIcon.LoadClimb(player, history);
            playerIcon.Connect(NClickableControl.SignalName.MouseReleased, Callable.From<InputEvent>(_ => SelectPlayer(playerIcon)));

            if (player.Id == playerId)
            {
                playerSelected = true;
                SelectPlayer(playerIcon);
            }
        }

        if (!playerSelected)
        {
            if (history.Players.Count > 1)
            {
                Log.Warn($"Local player with ID {playerId} not found in multiplayer climb history file! Defaulting to first player");
            }

            // DO NOT use GetChildren().First() here! It will cause problems because of the use of QueueFree above
            SelectPlayer(firstIcon!);
        }
    }

    private void SelectPlayer(NClimbHistoryPlayerIcon playerIcon)
    {
        _selectedPlayerIcon?.Deselect();
        _selectedPlayerIcon = playerIcon;
        playerIcon.Select();

        if (_history.Players.Count == 1)
        {
            // If in singleplayer, display only the name of the character rather than the player name
            CharacterModel character = ModelDb.GetById<CharacterModel>(playerIcon.Player.Character);
            Color color = character.NameColor;
        }
        else
        {
            LocString playerNameStr = new(locTable, "PLAYER_NAME");
            playerNameStr.Add("PlayerName", PlatformUtil.GetPlayerName(_history.PlatformType, playerIcon.Player.Id));
        }

        LoadGoldHpAndPotionInfo(playerIcon);
        LoadDeathQuote(_history, playerIcon.Player.Character);
        _mapPointHistory.SetPlayer(playerIcon.Player);
        _relicHistory.LoadRelics(playerIcon.Player.Relics);
        _deckHistory.LoadDeck(playerIcon.Player.Deck);
    }

    private void LoadGoldHpAndPotionInfo(NClimbHistoryPlayerIcon icon)
    {
        MapPointHistoryEntry entry = _history.MapPointHistory.Last().Last();
        PlayerMapPointHistoryEntry stat = entry.PlayerStats.First(stat => stat.PlayerId == icon.Player.Id);

        _hpLabel.Text = $"{stat.CurrentHp}/{stat.MaxHp}";
        _goldLabel.Text = $"{stat.CurrentGold}";

        // Potion shenanigans start here
        _potionHolder.FreeChildren();
        List<ModelId> potions = [];

        // For every single Node
        foreach (MapPointHistoryEntry node in _history.MapPointHistory.SelectMany(list => list))
        {
            // Get the selected player
            PlayerMapPointHistoryEntry playerStat = node.PlayerStats.First(stat2 => stat2.PlayerId == icon.Player.Id);
            List<ModelChoiceHistoryEntry> picked = playerStat.PotionChoices.FindAll(choice => choice.wasPicked);

            foreach (ModelChoiceHistoryEntry choiceEntry in picked)
            {
                potions.Add(choiceEntry.choice);
            }

            potions.AddRange(stat.BoughtPotions);
            potions.RemoveAll(id => playerStat.PotionDiscarded.Contains(id));
            potions.RemoveAll(id => playerStat.PotionUsed.Contains(id));
        }

        List<NPotionHolder> slots = [];

        // This doesn't work if the potion slot count isn't 3.
        for (int i = 0; i < 3; i++)
        {
            NPotionHolder slot = NPotionHolder.Create(false);
            _potionHolder.AddChildSafely(slot);
            slots.Add(slot);
        }

        for (int i = 0; i < potions.Count && i < slots.Count; i++)
        {
            ModelId id = potions[i];
            NPotion potion = NPotion.Create(ModelDb.GetById<PotionModel>(id).ToMutable())!;
            slots[i].AddPotion(potion);
            potion.Position = Vector2.Zero;
        }
    }

    /// <summary>
    /// Counts the total number of MapPointHistoryEntry objects.
    /// This is how many floors the player has traversed I think.
    /// </summary>
    private void LoadPlayerFloor(ClimbHistory history)
    {
        // Subtracts one room per ANCIENT (so per act) as those rooms don't increment the floor count.
        int totalFloors = history.MapPointHistory.Sum(rooms => rooms.Count) - history.MapPointHistory.Count;

        _floorLabel.Text = $"{totalFloors}";
    }

    /// <summary>
    /// Sets the text in the top right that is like Singleplayer: Standard or Multiplayer: Custom, etc.
    /// </summary>
    /// <param name="history"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void LoadGameModeDetails(ClimbHistory history)
    {
        LocString gameModeString = new(locTable, "GAME_MODE.title");

        if (history.Players.Count > 1)
        {
            gameModeString.Add("PlayerCount", new LocString(locTable, "PLAYER_COUNT.multiplayer").GetFormattedText());
        }
        else
        {
            gameModeString.Add("PlayerCount", new LocString(locTable, "PLAYER_COUNT.singleplayer").GetFormattedText());
        }

        switch (history.GameMode)
        {
            case GameMode.Custom:
                gameModeString.Add("GameMode", new LocString(locTable, "GAME_MODE.custom").GetFormattedText());
                break;
            case GameMode.Daily:
                gameModeString.Add("GameMode", new LocString(locTable, "GAME_MODE.daily").GetFormattedText());
                break;
            case GameMode.Standard:
                gameModeString.Add("GameMode", new LocString(locTable, "GAME_MODE.standard").GetFormattedText());
                break;
            case GameMode.None:
            // Fallthrough
            default:
                // Handles deprecated content or a modder's game modes
                gameModeString.Add("GameMode", new LocString(locTable, "GAME_MODE.unknown").GetFormattedText());
                break;
        }

        _gameModeLabel.Text = $"[right]{gameModeString.GetFormattedText()}[/right]";
    }

    /// <summary>
    /// Given a ClimbHistory entry, returns how the climb ended (aka the GameOverType).
    /// </summary>
    public static GameOverType GetGameOverType(ClimbHistory history)
    {
        // TODO: Check for TrueVictory

        if (history.Win)
        {
            return GameOverType.FalseVictory;
        }

        if (history.KilledByEncounter != ModelId.none)
        {
            return GameOverType.CombatDeath;
        }

        // NOTE: The order matters. This check for AbandonedClimb must precede the KilledByEvent check below.
        if (history.WasAbandoned)
        {
            return GameOverType.AbandonedClimb;
        }

        if (history.KilledByEvent != ModelId.none)
        {
            return GameOverType.EventDeath;
        }

        Log.Error("How did the game end??");
        return GameOverType.None;
    }

    /// <summary>
    /// Helper method to generate a death quote given a climb history entry, characterId, and how they died (GameOverType).
    /// </summary>
    /// <param name="history"></param>
    /// <param name="characterId"></param>
    /// <param name="gameOverType"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public static string GetDeathQuote(ClimbHistory history, ModelId characterId, GameOverType gameOverType)
    {
        CharacterModel character = ModelDb.GetById<CharacterModel>(characterId);
        StringBuilder sb = new();

        sb.Append("[center]“");
        Rng rng = new((uint)StringHelper.GetDeterministicHashCode(history.Seed));

        switch (gameOverType)
        {
            case GameOverType.AbandonedClimb:
                LocString abandonMessage = new(
                    locTable,
                    LocString.GetRandomKey(locTable, "MAP_POINT_HISTORY.abandon.", rng)
                );
                abandonMessage.Add("character", character.Title.GetFormattedText());
                abandonMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
                sb.Append(abandonMessage.GetFormattedText());
                break;
            case GameOverType.EventDeath:
                EventModel eventModel;
                try
                {
                    eventModel = ModelDb.GetById<EventModel>(history.KilledByEvent);
                }
                catch (ModelNotFoundException)
                {
                    eventModel = ModelDb.Event<DeprecatedEvent>();
                }

                LocString eventDeathMessage = new("events", $"{eventModel.Id.Entry}.loss");
                eventDeathMessage.Add("character", character.Title.GetFormattedText());
                eventDeathMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
                eventDeathMessage.Add("pronounSubject", character.PronounSubject.GetFormattedText());
                eventDeathMessage.Add("event", eventModel.Title.GetFormattedText());

                sb.Append($"{eventDeathMessage.GetFormattedText()}");
                break;
            case GameOverType.CombatDeath:
                EncounterModel encounter;

                try
                {
                    encounter = ModelDb.GetById<EncounterModel>(history.KilledByEncounter);
                }
                catch (ModelNotFoundException)
                {
                    encounter = ModelDb.Encounter<DeprecatedEncounter>();
                }

                LocString deathMessage = encounter.LossMessage;
                deathMessage.Add("character", character.Title.GetFormattedText());
                deathMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
                deathMessage.Add("pronounSubject", character.PronounSubject.GetFormattedText());
                deathMessage.Add("encounter", encounter.Title.GetFormattedText());

                sb.Append($"{deathMessage.GetFormattedText()}");
                break;
            case GameOverType.FalseVictory:
                string key = LocString.GetRandomKey(locTable, "MAP_POINT_HISTORY.falseVictory.", rng);
                LocString victoryMessage = new(locTable, key);
                victoryMessage.Add("character", character.Title.GetFormattedText());
                victoryMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
                sb.Append($"{victoryMessage.GetFormattedText()}");
                break;
            case GameOverType.TrueVictory:
            case GameOverType.None:
            default:
                Log.Error($"Unimplemented GameOverType: {gameOverType.ToString()}");
                throw new ArgumentOutOfRangeException(nameof(gameOverType), gameOverType, null);
        }

        sb.Append("[/center]”");
        return sb.ToString();
    }

    private void LoadDeathQuote(ClimbHistory history, ModelId characterId)
    {
        CharacterModel character = ModelDb.GetById<CharacterModel>(characterId);
        StringBuilder sb = new();

        sb.Append("[center]“");

        Rng rng = new((uint)StringHelper.GetDeterministicHashCode(history.Seed));

        // False victory
        if (history.Win)
        {
            _deathQuoteLabel.AddThemeColorOverride("default_color", StsColors.green);
            string key = LocString.GetRandomKey(locTable, "MAP_POINT_HISTORY.falseVictory.", rng);
            LocString victoryMessage = new(locTable, key);
            victoryMessage.Add("character", character.Title.GetFormattedText());
            victoryMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
            sb.Append($"{victoryMessage.GetFormattedText()}");
        }
        // TODO: True victory (after Act 4 is implemented)
        // else if (climb.win + ??) {
        // Death by monster/encounter
        else if (history.KilledByEncounter != ModelId.none)
        {
            _deathQuoteLabel.AddThemeColorOverride("default_color", StsColors.red);
            EncounterModel encounter;

            try
            {
                encounter = ModelDb.GetById<EncounterModel>(history.KilledByEncounter);
            }
            catch (ModelNotFoundException)
            {
                encounter = ModelDb.Encounter<DeprecatedEncounter>();
            }

            LocString deathMessage = encounter.LossMessage;
            deathMessage.Add("character", character.Title.GetFormattedText());
            deathMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
            deathMessage.Add("pronounSubject", character.PronounSubject.GetFormattedText());
            deathMessage.Add("encounter", encounter.Title.GetFormattedText());

            sb.Append($"{deathMessage.GetFormattedText()}");
        }
        // It is important that we do this check before the event condition because we don't want to accidentally
        // attribute the loss to the event itself  (this can cause issues with events that have no way of killing the player).
        // However, we do this after the monster check because its safe (and imo more useful) to assume that they were going to
        // lose the combat anyways.
        else if (history.WasAbandoned)
        {
            _deathQuoteLabel.AddThemeColorOverride("default_color", StsColors.red);
            LocString abandonMessage = new(
                locTable,
                LocString.GetRandomKey(locTable, "MAP_POINT_HISTORY.abandon.", rng)
            );
            abandonMessage.Add("character", character.Title.GetFormattedText());
            abandonMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
            sb.Append(abandonMessage.GetFormattedText());
        }
        else if (history.KilledByEvent != ModelId.none)
        {
            _deathQuoteLabel.AddThemeColorOverride("default_color", StsColors.red);
            EventModel eventModel;
            try
            {
                eventModel = ModelDb.GetById<EventModel>(history.KilledByEvent);
            }
            catch (ModelNotFoundException)
            {
                eventModel = ModelDb.Event<DeprecatedEvent>();
            }

            LocString deathMessage = new("events", $"{eventModel.Id.Entry}.loss");
            deathMessage.Add("character", character.Title.GetFormattedText());
            deathMessage.Add("possessiveAdjective", character.PossessiveAdjective.GetFormattedText());
            deathMessage.Add("pronounSubject", character.PronounSubject.GetFormattedText());
            deathMessage.Add("event", eventModel.Title.GetFormattedText());

            sb.Append($"{deathMessage.GetFormattedText()}");
        }

        sb.Append("[/center]”");
        _deathQuoteLabel.Text = sb.ToString();
    }

    private void LoadTimeDetails(ClimbHistory history)
    {
        // TODO: Something is strange with the year format
        DateTimeFormatInfo dateInfo = LocManager.Instance.CultureInfo.DateTimeFormat;
        DateTimeOffset dateTimeOffset = DateTimeOffset.FromUnixTimeSeconds(history.StartTime);
        DateTime dateTime = TimeZoneInfo.ConvertTimeFromUtc(dateTimeOffset.UtcDateTime, TimeZoneInfo.Local);

        // Separate components for formatting
        // string dayOfWeek = dateTime.ToString("dddd", dateInfo); // Full day name (e.g., "Friday")
        string calendarDate = dateTime.ToString("MMMM d, yyyy", dateInfo); // "February 16, 2024"
        string time = dateTime.ToString("h:mm tt", dateInfo); // "9:21 PM"

        // OLD: _dateLabel.Text = $"[right][gold][b]{dayOfWeek}[/b][/gold], {calendarDate} [blue]{time}[/blue][/right]";
        _dateLabel.Text = $"[right][gold]{calendarDate}[/gold], [blue]{time}[/blue][/right]";
        _seedLabel.Text = $"[right][gold]Seed[/gold]: {history.Seed}[/right]";
        _buildLabel.Text = $"[right]{history.BuildId}[/right]";
        _timeLabel.Text = TimeFormatting.Format(history.ClimbTime);
    }
}
