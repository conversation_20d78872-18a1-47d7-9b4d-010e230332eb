using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

public partial class NCloseButton : NButton
{
    private Tween? _tween;

    public override void _Ready()
    {
        ConnectSignals();
    }

    protected override void OnFocus()
    {
        base.OnFocus();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "scale", Vector2.One * 1.05f, 0.05);
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "scale", Vector2.One, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "scale", Vector2.One * 0.95f, 0.25)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnRelease()
    {
        base.OnRelease();

        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "scale", Vector2.One, 0.05);
        MouseFilter = MouseFilterEnum.Ignore;

        _ = TaskHelper.RunSafely(GetParent<NEpochInspectScreen>().Close());
    }
}
