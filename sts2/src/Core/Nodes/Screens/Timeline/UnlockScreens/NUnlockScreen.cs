using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Timeline;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline.UnlockScreens;

/// <summary>
/// Abstract class of the Unlock Screens. Used for general animation and some logistics required for the Timeline Screen.
/// All Unlock Screens in the Timeline must use this!
/// </summary>
public abstract partial class NUnlockScreen : Control
{
    private NUnlockConfirmButton _unlockConfirmButton = default!;
    private Tween? _tween;

    public override void _Ready()
    {
        if (GetType() != typeof(NUnlockScreen))
        {
            Log.Error($"{GetType()}");
            throw new InvalidOperationException("Don't call base._Ready()! Call ConnectSignals() instead.");
        }

        ConnectSignals();
    }

    protected virtual void ConnectSignals()
    {
        _unlockConfirmButton = GetNode<NUnlockConfirmButton>("ConfirmButton");
    }

    /// <summary>
    /// Useful for UI stuff because this Node is in the Scene so the positions are accurate.
    /// See: NUnlockEpochScreen where we animate the Epochs on screen open!
    /// </summary>
    public virtual void Open()
    {
        NTimelineScreen.Instance!.DisableInput();
        _tween?.Kill();
        _tween = CreateTween();
        _tween.TweenProperty(this, "modulate:a", 1f, 0.5);
    }

    public virtual async Task Close()
    {
        Log.Info($"Closing: {Name}");
        _tween?.Kill();
        _tween = CreateTween().SetParallel();
        _tween.TweenProperty(this, "modulate", new Color(0f, 0f, 0f, 0f), 1.0);

        await ToSignal(_tween, Tween.SignalName.Finished);
        OnScreenClose();

        // If any other screens are queued up, open them!
        if (NTimelineScreen.Instance!.IsScreenQueued())
        {
            NTimelineScreen.Instance.OpenQueuedScreen();
        }
        else
        {
            bool expansionTwoTriggered = CheckForTimelineExpansionTwo();

            if (!expansionTwoTriggered)
            {
                await NTimelineScreen.Instance.HideBackstopAndShowUi();
            }
        }

        QueueFree();
    }

    /// <summary>
    /// Unlocks some Epochs and Queues up Timeline Expansion two (4 Slots -> 37 Slots) if the reqs are met.
    /// </summary>
    private bool CheckForTimelineExpansionTwo()
    {
        Log.Info("Checking for timeline expansion two...");

        // Specifically checks for TimelineExpansionTwo
        List<SerializableEpoch> stats = SaveManager.Instance.ProgressSave.Epochs;

        // If 4 Epochs are revealed... EXPAAAND.
        // NOTE: Suspicious code. If expansion two has occurred, we would have at least 20 Epochs.
        // This used to check for an exact Epoch count of 4 but because the player can obtain Epochs
        // without the slot available, this number can exceed this as of May 12, 2025.
        if (!NTimelineScreen.Instance!.IsScreenQueued() &&
            stats.Count < 20 &&
            stats.Count(a => a.State >= EpochState.Revealed) == 4)
        {
            Log.Info("TIMELINE EXPANSION TWO");
            EpochModel.QueueTimelineExpansionTwo();
            NTimelineScreen.Instance.OpenQueuedScreen();
            SaveManager.Instance.SaveProgressFile();

            return true;
        }

        return false;
    }

    protected virtual void OnScreenClose() { }
}
