using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

/// <summary>
/// The slots container intercepts inputs and transforms its child node "WhatsMoved" we do this because if this Node is moved too much we can't intercept GUI inputs and such.
/// We could probably bypass this by using _Input oops. Whatever.
/// </summary>
public partial class NSlotsContainer : Control
{
    private Control _whatsZoomed = default!;
    private Control _whatsMoved = default!;
    private Vector2 _dragStartPosition;
    private Vector2 _targetPosition;
    private bool _isDragging;

    // Scale
    private Tween? _scaleTween;
    private Vector2 _targetScale;
    private const float _zoomDuration = 0.3f; // How fast we lerp in/out when we scroll
    private const float _scrollSpeed = 0.05f; // How much each "tick" of the mouse wheel zooms in or out
    private const float _trackpadScrollSpeed = 0.1f; // Multiplier for trackpad zooming in/out

    public override void _Ready()
    {
        _whatsZoomed = GetNode<Control>("%WhatsZoomed");
        _whatsMoved = GetNode<Control>("%WhatsMoved");
        _targetPosition = _whatsMoved.Position;
        _targetScale = _whatsMoved.Scale;
        Connect(CanvasItem.SignalName.VisibilityChanged, Callable.From(OnToggleVisibility));
    }

    public override void _GuiInput(InputEvent inputEvent)
    {
        ProcessPanEvent(inputEvent);
        ProcessZoomEvent(inputEvent);
    }

    /// <summary>
    /// Processes clicking and dragging the screen horizontally.
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessPanEvent(InputEvent inputEvent)
    {
        if (inputEvent is InputEventMouseButton mouseButtonEvent && mouseButtonEvent.ButtonIndex == MouseButton.Left)
        {
            if (mouseButtonEvent.Pressed)
            {
                _isDragging = true;
                _dragStartPosition = mouseButtonEvent.Position;
            }
            else
            {
                _isDragging = false;
            }
        }
        else if (inputEvent is InputEventMouseMotion mouseMotionEvent && _isDragging)
        {
            Vector2 offset = mouseMotionEvent.Position - _dragStartPosition;
            _targetPosition += new Vector2(offset.X, 0f);
            _dragStartPosition = mouseMotionEvent.Position;
        }
    }

    /// <summary>
    /// Processes mouse wheel to zoom in and out of the Timeline screen.
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessZoomEvent(InputEvent inputEvent)
    {
        if (inputEvent is InputEventMouseButton mouseButton)
        {
            if (mouseButton.ButtonIndex == MouseButton.WheelUp)
            {
                _targetScale += Vector2.One * _scrollSpeed;
            }
            else if (mouseButton.ButtonIndex == MouseButton.WheelDown)
            {
                _targetScale -= Vector2.One * _scrollSpeed;
            }

            _targetScale = MathHelper.Clamp(_targetScale, 0.6f, 1.2f);

            _scaleTween?.Kill();
            _scaleTween = CreateTween();
            _scaleTween.TweenProperty(_whatsZoomed, "scale", _targetScale, _zoomDuration)
                .SetTrans(Tween.TransitionType.Cubic)
                .SetEase(Tween.EaseType.Out);
        }
        // For OSX touchpad support (namely, macbooks).
        // Untested, lol
        else if (inputEvent is InputEventPanGesture panGesture && panGesture.Delta.Y != 0f)
        {
            _targetScale += Vector2.One * panGesture.Delta.Y * _trackpadScrollSpeed;
            _targetScale = MathHelper.Clamp(_targetScale, 0.6f, 1.2f);

            _scaleTween?.Kill();
            _scaleTween = CreateTween();
            _scaleTween.TweenProperty(_whatsZoomed, "scale", _targetScale, _zoomDuration)
                .SetTrans(Tween.TransitionType.Cubic)
                .SetEase(Tween.EaseType.Out);
        }
    }

    public override void _Process(double delta)
    {
        _whatsMoved.Position = _whatsMoved.Position.Lerp(_targetPosition, (float)delta * 20f);
    }

    private void OnToggleVisibility()
    {
        if (Visible)
        {
            _targetPosition = _whatsMoved.Position;
            _dragStartPosition = Vector2.Zero;
        }
    }

    public void SetTargetScale(Vector2 setScale)
    {
        _targetScale = setScale;

        _scaleTween?.Kill();
        _scaleTween = CreateTween();
        _scaleTween.TweenProperty(_whatsZoomed, "scale", _targetScale, _zoomDuration)
            .SetTrans(Tween.TransitionType.Cubic)
            .SetEase(Tween.EaseType.Out);
    }

    public void Reset()
    {
        _whatsMoved.Position = new Vector2(-960f, _whatsMoved.Position.Y);
    }
}
