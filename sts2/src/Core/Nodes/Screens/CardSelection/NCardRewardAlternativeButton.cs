using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CardSelection;

/// <summary>
/// A simple button to skip choosing a card on some select screens (like the choose 1 of 3 card reward screen).
/// </summary>
public partial class NCardRewardAlternativeButton : NButton
{
    private static string ScenePath => SceneHelper.GetScenePath("/ui/card_reward_alternative_button");

    public static IEnumerable<string> AssetPaths => [ScenePath];

    private TextureRect _image = default!;
    private Label _label = default!;

    private string? _optionName;

    // For animating this button in
    private Tween? _animInTween;
    private Vector2 _showPosition;
    private static readonly Vector2 _animOffsetPosition = new(0f, -50f);

    private Tween? _currentTween;
    private ShaderMaterial _hsv = default!;
    private Variant _hsvDefault = 0.9;
    private Variant _hsvHover = 1.1;
    private Variant _hsvDown = 0.7;
    private static readonly Vector2 _defaultScale = Vector2.One;
    private static readonly Vector2 _hoverScale = Vector2.One * 1.05f;
    private static readonly Vector2 _downScale = Vector2.One * 0.95f;

    public override void _Ready()
    {
        ConnectSignals();
        _image = GetNode<TextureRect>("Image");
        _label = GetNode<Label>("Label");
        _hsv = (ShaderMaterial)_image.Material;
        _showPosition = Position;

        if (_optionName != null)
        {
            _label.Text = _optionName;
        }
    }

    public static NCardRewardAlternativeButton Create(string optionName)
    {
        NCardRewardAlternativeButton button = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NCardRewardAlternativeButton>();
        button._optionName = optionName;

        return button;
    }

    public void AnimateIn()
    {
        _animInTween?.Kill();
        _animInTween = CreateTween().SetParallel();
        _animInTween.TweenProperty(this, "modulate:a", 1f, 0.4)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(0f);
        _animInTween.TweenProperty(this, "position", _showPosition, 0.4)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back)
            .From(_showPosition + _animOffsetPosition);
    }

    protected override void OnPressDown()
    {
        _currentTween?.Kill();
        _currentTween = CreateTween().SetParallel();
        _currentTween.TweenMethod(Callable.From<float>(UpdateShaderParam), _hsvHover, _hsvDown, 0.2f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _currentTween.TweenProperty(this, "scale", _downScale, 0.2f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnFocus()
    {
        _currentTween?.Kill();
        Scale = _hoverScale;
        _hsv.SetShaderParameter("v", _hsvHover);
    }

    protected override void OnUnfocus()
    {
        _currentTween?.Kill();
        _currentTween = CreateTween().SetParallel();
        _currentTween.TweenMethod(Callable.From<float>(UpdateShaderParam), _hsv.GetShaderParameter("v"), _hsvDefault, 0.5f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo);
        _currentTween.TweenProperty(this, "scale", _defaultScale, 0.5f).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo);
    }

    private void UpdateShaderParam(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
