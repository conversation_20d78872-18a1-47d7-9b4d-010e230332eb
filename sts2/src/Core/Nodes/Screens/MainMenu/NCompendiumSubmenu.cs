using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.CardLibrary;
using MegaCrit.Sts2.Core.Nodes.Screens.PotionLab;
using MegaCrit.Sts2.Core.Nodes.Screens.RelicCollection;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

public partial class NCompendiumSubmenu : NSubmenu
{
    private NButton _confirmButton = default!;
    private NSubmenuButton _cardLibraryButton = default!;
    private NSubmenuButton _relicCollectionButton = default!;
    private NSubmenuButton _potionLabButton = default!;

    private NCardLibrary _cardLibrary = default!;
    private NRelicCollection _relicCollection = default!;
    private NPotionLab _potionLab = default!;

    public static IEnumerable<string> AssetPaths => new[]
    {
        NSubmenuButton.GetImagePath("COMPENDIUM_CARD_LIBRARY")
    }.Where(s => s != null).Select(s => s!); // Remove nulls

    public override void _Ready()
    {
        ConnectSignals();
        _cardLibraryButton = GetNode<NSubmenuButton>("CardLibraryButton");
        _cardLibraryButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenCardLibrary));
        _cardLibraryButton.SetIconAndLocalization("COMPENDIUM_CARD_LIBRARY");

        _relicCollectionButton = GetNode<NSubmenuButton>("RelicCollectionButton");
        _relicCollectionButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenRelicCollection));
        _relicCollectionButton.SetIconAndLocalization("COMPENDIUM_RELIC_COLLECTION");

        _potionLabButton = GetNode<NSubmenuButton>("PotionLabButton");
        _potionLabButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenPotionLab));
        _potionLabButton.SetIconAndLocalization("COMPENDIUM_POTION_LAB");

        _cardLibrary = GetNode<NCardLibrary>("%CardLibrary");
        _relicCollection = GetNode<NRelicCollection>("%RelicCollection");
        _potionLab = GetNode<NPotionLab>("%PotionLab");
    }

    public void Initialize(IClimbState climbState)
    {
        _cardLibrary.Initialize(climbState);
    }

    public override void OnFocusScreen()
    {
        _cardLibraryButton.TryGrabFocus();
    }

    private void OpenCardLibrary(NButton _)
    {
        _stack.Push(_cardLibrary);
    }

    private void OpenRelicCollection(NButton _)
    {
        _stack.Push(_relicCollection);
    }

    private void OpenPotionLab(NButton _)
    {
        _stack.Push(_potionLab);
    }
}
