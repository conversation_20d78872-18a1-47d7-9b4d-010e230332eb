using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;
using MegaCrit.Sts2.Core.Nodes.Screens.StatsScreen;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

public partial class NStatisticsSubmenu : NSubmenu
{
    private NButton _confirmButton = default!;

    private NSubmenuButton _characterStatsButton = default!;
    private NSubmenuButton _leaderboardsButton = default!;
    private NSubmenuButton _climbHistoryButton = default!;

    private NClimbHistory _climbHistoryScreen = default!;
    private NStatsScreen _statsScreen = default!;

    private const string _keyClimbHistory = "CLIMB_HISTORY";
    private const string _keyLeaderboards = "LEADERBOARDS";
    private const string _keyAchievements = "ACHIEVEMENTS";

    public static IEnumerable<string> AssetPaths => new[]
    {
        NSubmenuButton.GetImagePath(_keyClimbHistory),
        NSubmenuButton.GetImagePath(_keyLeaderboards),
        NSubmenuButton.GetImagePath(_keyAchievements),
    }.Where(s => s != null).Select(s => s!); // Remove nulls

    public override void _Ready()
    {
        ConnectSignals();
        _climbHistoryButton = GetNode<NSubmenuButton>("ClimbHistoryButton");
        _climbHistoryButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenClimbHistory));
        _climbHistoryButton.SetIconAndLocalization(_keyClimbHistory);

        _leaderboardsButton = GetNode<NSubmenuButton>("LeaderboardsButton");
        _leaderboardsButton.Disable();
        _leaderboardsButton.SetIconAndLocalization(_keyLeaderboards);

        _characterStatsButton = GetNode<NSubmenuButton>("CharacterStatsButton");
        _characterStatsButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OpenStatsScreen));
        _characterStatsButton.SetIconAndLocalization(_keyAchievements);

        _climbHistoryScreen = GetNode<NClimbHistory>("%ClimbHistory");
        _statsScreen = GetNode<NStatsScreen>("%StatsScreen");
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();
        _climbHistoryButton.Visible = _climbHistoryScreen.CanBeShown();
    }

    public override void OnFocusScreen()
    {
        _characterStatsButton.TryGrabFocus();
    }

    private void OpenClimbHistory(NButton _)
    {
        _stack.Push(_climbHistoryScreen);
    }

    private void OpenStatsScreen(NButton _)
    {
        _stack.Push(_statsScreen);
    }
}
