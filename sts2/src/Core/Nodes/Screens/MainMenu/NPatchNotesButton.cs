using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

public partial class NPatchNotesButton : NButton
{
    private ShaderMaterial _hsv = default!;
    private Control _icon = default!;
    private NPatchNotesScreen _patchNotesScreen = default!;

    public override void _Ready()
    {
        ConnectSignals();
        _icon = GetNode<TextureRect>("Icon");
        _hsv = (ShaderMaterial)_icon.Material;
        _patchNotesScreen = GetNode<NPatchNotesScreen>("%PatchNotesScreen");
    }

    protected override void OnRelease()
    {
        TaskHelper.RunSafely(PushButton());
    }

    private async Task PushButton()
    {
        if (!_patchNotesScreen.IsOpen)
        {
            _patchNotesScreen.Open();
        }
        else
        {
            Disable();
            await TaskHelper.RunSafely(_patchNotesScreen.Close());
            Enable();
        }
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        _hsv.SetShaderParameter("v", 1.2f);
        _icon.RotationDegrees = 5f;
    }

    protected override void OnUnfocus()
    {
        base.OnUnfocus();
        _hsv.SetShaderParameter("v", 1f);
        _icon.RotationDegrees = 0f;
    }
}
