using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Entities.UI;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Screens.CustomClimb;

namespace MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;

public partial class NCustomClimbModifiersList : Control
{
    [Signal]
    public delegate void ModifiersChangedEventHandler();

    private readonly List<NClimbModifierTickbox> _modifierTickboxes = [];

    private Control _container = default!;
    private MultiplayerUiMode _mode;

    public override void _Ready()
    {
        _container = GetNode<Control>("ScrollContainer/Mask/Content");

        foreach (Node child in _container.GetChildren())
        {
            child.QueueFreeSafely();
        }

        foreach (ModifierModel model in GetAllModifiers())
        {
            NClimbModifierTickbox tickbox = NClimbModifierTickbox.Create(model)!;
            _container.AddChildSafely(tickbox);
            _modifierTickboxes.Add(tickbox);

            tickbox.Connect(NTickbox.SignalName.Toggled, Callable.From<NClimbModifierTickbox>(AfterModifiersChanged));
        }
    }

    /// <summary>
    /// Call this once when the menu opens with the appropriate mode for the screen we're displayed on.
    /// </summary>
    /// <param name="mode">See the documentation for the enumeration for the correct value to pass.</param>
    public void Initialize(MultiplayerUiMode mode)
    {
        _mode = mode;

        if (mode is MultiplayerUiMode.Client or MultiplayerUiMode.Load)
        {
            foreach (NClimbModifierTickbox tickbox in _modifierTickboxes)
            {
                tickbox.Disable();
            }
        }
    }

    /// <summary>
    /// Call this when:
    /// - We are in client mode and the host changes the modifiers that are selected
    /// - Once when the game is loaded from a multiplayer save
    /// </summary>
    /// <param name="modifiers">The modifiers to display as checked.</param>
    public void SyncModifierList(IReadOnlyList<ModifierModel> modifiers)
    {
        if (_mode is MultiplayerUiMode.Singleplayer or MultiplayerUiMode.Host) throw new InvalidOperationException("This should only be called in client or load mode!");

        foreach (NClimbModifierTickbox tickbox in _modifierTickboxes)
        {
            tickbox.IsTicked = modifiers.FirstOrDefault(m => m.IsEquivalent(tickbox.Modifier!)) != null;
        }
    }

    private IEnumerable<ModifierModel> GetAllModifiers()
    {
        foreach (ModifierModel model in ModelDb.GoodModifiers.Concat(ModelDb.BadModifiers))
        {
            if (model is CharacterCards canonicalCharacterCardsModifier)
            {
                foreach (CharacterModel character in ModelDb.Characters)
                {
                    CharacterCards characterCardsModifier = (CharacterCards)canonicalCharacterCardsModifier.ToMutable();
                    characterCardsModifier.CharacterModel = character.Id;
                    yield return characterCardsModifier;
                }
            }
            else
            {
                yield return model.ToMutable();
            }
        }
    }

    private void UntickMutuallyExclusiveModifiersForTickbox(NClimbModifierTickbox tickbox)
    {
        // Nothing changes if the tickbox was just unticked
        if (!tickbox.IsTicked) return;

        // The modifier inside the tickbox is mutable, so find the first mutually exclusive set that contains a modifier
        // of the same _type_ as the modifier (not strict contains, because the tickbox has mutable modifiers)
        IReadOnlySet<ModifierModel>? mutuallyExclusiveModifiers =
            ModelDb.MutuallyExclusiveModifiers.FirstOrDefault(s => s.Any(m => m.GetType() == tickbox.Modifier!.GetType()));

        if (mutuallyExclusiveModifiers == null) return;

        foreach (NClimbModifierTickbox otherTickbox in _modifierTickboxes)
        {
            // Don't untick the one that the player just ticked or has the same type of modifier (Character Cards modifiers
            // are not mutually exclusive with one another)
            if (otherTickbox.Modifier!.GetType() == tickbox.Modifier!.GetType()) continue;

            // Untick the tickbox if the mutually exclusive modifiers contains a modifier of the same type
            if (mutuallyExclusiveModifiers.Any(m => m.GetType() == otherTickbox.Modifier!.GetType()))
            {
                otherTickbox.IsTicked = false;
            }
        }
    }

    private void AfterModifiersChanged(NClimbModifierTickbox tickbox)
    {
        UntickMutuallyExclusiveModifiersForTickbox(tickbox);
        EmitSignal(SignalName.ModifiersChanged);
    }

    /// <summary>
    /// Returns the list of modifiers that are ticked on.
    /// </summary>
    public List<ModifierModel> GetModifiersTickedOn()
    {
        return _modifierTickboxes.Where(t => t.IsTicked).Select(t => t.Modifier!).ToList();
    }
}
