using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Debug;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.UI;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Acts;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.Lobby;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Debug;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CharacterSelect;

public partial class NCharacterSelectScreen : NSubmenu, IStartClimbLobbyListener, ICharacterSelectButtonDelegate
{
    private MegaLabel _name = default!;
    private Control _infoPanel = default!;
    private MegaRichTextLabel _description = default!;
    private Label _hp = default!;
    private Label _gold = default!;
    private MegaRichTextLabel _relicTitle = default!;
    private MegaRichTextLabel _relicDescription = default!;
    private TextureRect _relicIcon = default!;
    private TextureRect _relicIconOutline = default!;
    private NCharacterSelectButton? _selectedButton;
    private Control _charButtonContainer = default!;
    private Control _bgContainer = default!;
    private Control _readyAndWaitingContainer = default!;
    private NConfirmButton _confirmButton = default!;
    private NAscensionPanel _ascensionPanel = default!;
    private NActDropdown _actDropdown = default!;
    private MegaRichTextLabel _actDropdownLabel = default!;
    private NRemoteLobbyPlayerContainer _remotePlayerContainer = default!;

    private Tween? _infoPanelTween;
    private Vector2 _infoPanelPosFinalVal;
    private const string _sceneCharSelectButtonPath = "res://scenes/screens/char_select/char_select_button.tscn";
    private IBootstrapSettings? _settings;
    private StartClimbLobby _lobby = default!;

    public StartClimbLobby Lobby => _lobby;
    public static IEnumerable<string> AssetPaths => [_sceneCharSelectButtonPath];

    public override void _Ready()
    {
        ConnectSignals();
        _infoPanel = GetNode<Control>("InfoPanel");
        _name = GetNode<MegaLabel>("InfoPanel/VBoxContainer/Name");
        _description = GetNode<MegaRichTextLabel>("InfoPanel/VBoxContainer/DescriptionLabel");
        _hp = GetNode<Label>("InfoPanel/VBoxContainer/HpGoldSpacer/HpGold/Hp/Label");
        _gold = GetNode<Label>("InfoPanel/VBoxContainer/HpGoldSpacer/HpGold/Gold/Label");
        _relicTitle = GetNode<MegaRichTextLabel>("InfoPanel/VBoxContainer/Relic/Name/RichTextLabel");
        _relicDescription = GetNode<MegaRichTextLabel>("InfoPanel/VBoxContainer/Relic/Description");
        _relicIcon = GetNode<TextureRect>("InfoPanel/VBoxContainer/Relic/Icon");
        _relicIconOutline = GetNode<TextureRect>("InfoPanel/VBoxContainer/Relic/Icon/Outline");
        _bgContainer = GetNode<Control>("AnimatedBg");
        _charButtonContainer = GetNode<Control>("CharSelectButtons/ButtonContainer");
        _ascensionPanel = GetNode<NAscensionPanel>("%AscensionPanel");
        _actDropdown = GetNode<NActDropdown>("%ActDropdown");
        _actDropdownLabel = GetNode<MegaRichTextLabel>("ActLabel");
        _remotePlayerContainer = GetNode<NRemoteLobbyPlayerContainer>("RemotePlayerContainer");
        _readyAndWaitingContainer = GetNode<Control>("ReadyAndWaitingPanel");

        _confirmButton = GetNode<NConfirmButton>("ConfirmButton");
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnEmbarkPressed));
        _ascensionPanel.Connect(NAscensionPanel.SignalName.AscensionLevelChanged, Callable.From(OnAscensionPanelLevelChanged));

        ProcessMode = ProcessModeEnum.Disabled;

        // Initialize all the character buttons and select the first one (Ironclad)
        InitCharacterButtons();

        Assembly assembly = Assembly.GetAssembly(typeof(NSceneBootstrapper))!;
        Type? settingsType = assembly.GetTypes().FirstOrDefault(t => t.GetInterfaces().Contains(typeof(IBootstrapSettings)));

        if (settingsType != null)
        {
            _settings = (IBootstrapSettings)Activator.CreateInstance(settingsType)!;
            PreloadManager.Enabled = _settings.DoPreloading;
        }
    }

    public void InitializeMultiplayerAsHost(INetGameService gameService, int maxPlayers)
    {
        if (gameService.Type != NetGameType.Host) throw new InvalidOperationException($"Initialized character select screen with GameService of type {gameService.Type} when hosting!");

        _lobby = new StartClimbLobby(GameMode.Standard, gameService, this, maxPlayers);
        _ascensionPanel.Initialize(MultiplayerUiMode.Host);
        _lobby.AddLocalHostPlayer(SaveManager.Instance.ProgressSave);

        AfterInitialized();
    }

    public void InitializeMultiplayerAsClient(INetGameService gameService, ClientLobbyJoinResponseMessage message)
    {
        if (gameService.Type != NetGameType.Client) throw new InvalidOperationException($"Initialized character select screen with GameService of type {gameService.Type} when joining!");

        _lobby = new StartClimbLobby(GameMode.Standard, gameService, this, -1);
        _ascensionPanel.Initialize(MultiplayerUiMode.Client);
        _lobby.InitializeFromMessage(message);

        AfterInitialized();
    }

    public void InitializeSingleplayer()
    {
        _lobby = new StartClimbLobby(GameMode.Standard, new NetSingleplayerGameService(), this, 1);

        _ascensionPanel.Initialize(MultiplayerUiMode.Singleplayer);
        _lobby.AddLocalHostPlayer(SaveManager.Instance.ProgressSave);

        AfterInitialized();
    }

    private void InitCharacterButtons()
    {
        foreach (CharacterModel character in ModelDb.Characters)
        {
            NCharacterSelectButton button = PreloadManager.Cache.GetScene(_sceneCharSelectButtonPath).Instantiate<NCharacterSelectButton>();
            button.Name = $"{character.Id.Entry}_button";
            _charButtonContainer.AddChildSafely(button);
            button.Init(character, this);
        }
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent.IsActionReleased(DebugHotkey.unlockCharacters))
        {
            DebugUnlockAllCharacters();
        }
    }

    private void DebugUnlockAllCharacters()
    {
        foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
        {
            button.DebugUnlock();
        }
    }

    public override void OnSubmenuOpened()
    {
        base.OnSubmenuOpened();

        // If we exited during a ready state, we need to re-enable all the buttons
        foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
        {
            if (!button.IsLocked)
            {
                button.Enable();

                // Reset the button to remove all old remote player icons and outlines
                button.Reset();
            }
            else
            {
                button.UnlockIfPossible();
            }
        }

        _confirmButton.Enable();
        _charButtonContainer.GetChild<NCharacterSelectButton>(0).Select();

        _remotePlayerContainer.Visible = _lobby.NetService.Type != NetGameType.Singleplayer;
        _remotePlayerContainer.Initialize(_lobby, false);

        // Called if it's a non-host multiplayer lobby
        if (_lobby.NetService.Type == NetGameType.Client)
        {
            _ascensionPanel.SetAscensionLevel(_lobby.Ascension);
        }

        // Non-host players in multiplayer can't see or select act 1.
        _actDropdown.Visible = ShouldShowActDropdown;
        _actDropdownLabel.Visible = _actDropdown.Visible;

        _readyAndWaitingContainer.Visible = false;

        // Select buttons for players already in the lobby.
        foreach (LobbyPlayer player in _lobby.Players)
        {
            RefreshButtonSelectionForPlayer(player);
        }

        ProcessMode = ProcessModeEnum.Inherit;
    }

    private bool ShouldShowActDropdown
    {
        get
        {
            // TODO: Comment this out and comment the line below in if we want to re-enable the act dropdown.
            return false;
            // return _lobby.NetService.Type is NetGameType.Singleplayer or NetGameType.Host;
        }
    }

    public override void OnSubmenuClosed()
    {
        base.OnSubmenuClosed();
        _confirmButton.Disable();
        _remotePlayerContainer.Cleanup();

        // Note that this method is not called when we transition to a climb, and is only called when this menu is
        // popped off the stack, e.g. by the back button, so we always pass true to disconnect session
        CleanUpLobby(true);
    }

    private void OnEmbarkPressed(NButton _)
    {
        _confirmButton.Disable();

        if (!SaveManager.Instance.SeenFtue(NAcceptTutorialsFtue.id))
        {
            // OnEmbarkPressed is called again after accepting/denying tutorials because the SeenFtue check will pass afterwards
            NModalContainer.Instance!.Add(NAcceptTutorialsFtue.Create(this, _lobby.LocalPlayer.character, () => OnEmbarkPressed(null!))!);
        }
        else
        {
            if (_lobby.NetService.Type is NetGameType.Host or NetGameType.Singleplayer)
            {
                _lobby.Act1 = _actDropdown.CurrentOption;
            }

            _lobby.SetReady(true);

            foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
            {
                button.Disable();
            }

            // In Multiplayer embarking sets us in a Ready state
            if (_lobby.NetService.Type != NetGameType.Singleplayer)
            {
                if (_lobby.Players.Any(p => !p.isReady))
                {
                    _readyAndWaitingContainer.Visible = true;
                }
            }
        }
    }

    public override void _Process(double delta)
    {
        if (_lobby.NetService.IsConnected)
        {
            _lobby.NetService.Update();
        }
    }

    public override void OnFocusScreen()
    {
        _charButtonContainer.GetChild<Control>(0).TryGrabFocus();
    }

    private void CleanUpLobby(bool disconnectSession)
    {
        _lobby.CleanUp(disconnectSession);
        _lobby = null!;

        if (disconnectSession)
        {
            NGame.Instance!.RemoteCursorContainer.Deinitialize();
            NGame.Instance.ReactionContainer.DeinitializeNetworking();
        }

        // Sometimes we get deallocated before this point
        if (IsInstanceValid(this))
        {
            ProcessMode = ProcessModeEnum.Disabled;
        }
    }

    private static ActModel? GetAct(string act1Key)
    {
        return act1Key switch
        {
            "overgrowth" => ModelDb.Act<Overgrowth>(),
            "underdocks" => ModelDb.Act<Underdocks>(),
            _ => null // If "random" is selected, leave it alone so it can be overridden.
        };
    }

    private async Task StartNewSingleplayerClimb(string seed, string act1)
    {
        Log.Info($"Embarking on a {_lobby.LocalPlayer.character.Id.Entry} climb with {_lobby.Players.Count} players. Ascension: {_lobby.Ascension}");
        await NGame.Instance!.Transition.FadeIn(0.8f, _lobby.LocalPlayer.character.CharacterSelectTransitionPath);

        List<ActModel> acts = ActModel.GetRandomList(seed).ToList();
        acts[0] = GetAct(act1) ?? acts[0];

        await NGame.Instance.StartNewSingleplayerClimb(_lobby.LocalPlayer.character, true, acts, [], seed, _lobby.Ascension);

        await SaveManager.Instance.SaveClimb(null);
        NControllerManager.Instance!.ClearScreens();

        CleanUpLobby(false);
    }

    private async Task StartNewMultiplayerClimb(string seed, string act1)
    {
        Log.Info($"Embarking on a multiplayer climb. Players: {string.Join(",", _lobby.Players)}. Ascension: {_lobby.Ascension}");
        await NGame.Instance!.Transition.FadeIn(0.8f, _lobby.LocalPlayer.character.CharacterSelectTransitionPath);

        List<ActModel> acts = ActModel.GetRandomList(seed).ToList();

        // Special handling for allowing the player to choose between Overgrowth and Underdocks during testing.
        // The UI for this is currently disabled because the Underdocks testing period is over.
        // If you're here trying to re-enable it for alt act 2/3, please update this comment.
        acts[0] = GetAct(act1) ?? acts[0];

        if (_settings is { BootstrapInMultiplayer: true })
        {
            acts[0] = _settings.Act;

            ClimbState climbState = ClimbState.CreateForNewClimb(
                _lobby.Players.Select(p => Player.CreateForNewClimb(p.character, p.id)).ToList(),
                acts.Select(a => a.ToMutable()).ToList(),
                _settings.Modifiers,
                _lobby.Ascension,
                seed
            );
            ClimbManager.Instance.SetUpNewMultiPlayer(climbState, _lobby, _settings.SaveClimbHistory);

            await PreloadManager.LoadClimbAssets(climbState.Players.Select(p => p.Character));
            await ClimbManager.Instance.FinalizeStartingRelics();

            ClimbManager.Instance.Launch();
            NGame.Instance.RootSceneContainer.SetCurrentScene(NClimb.Create(climbState));
            await ClimbManager.Instance.SetActDirectly(0);

            await SaveManager.Instance.SaveClimb(null);
            CleanUpLobby(false);

            // Undo setting players to 1 HP; often we don't go to Neow in bootstrap
            foreach (Player player in climbState.Players)
            {
                player.Creature.SetCurrentHpInternal(player.Creature.MaxHp);
            }

            climbState.AppendToMapPointHistory(_settings.MapPointType, _settings.RoomType);

            await _settings.Setup(LocalContext.GetMe(climbState)!);

            switch (_settings.RoomType)
            {
                case RoomType.RestSite:
                case RoomType.Treasure:
                case RoomType.Shop:
                    await ClimbManager.Instance.EnterRoomDebug(_settings.RoomType, showTransition: false);
                    ClimbManager.Instance.ActionExecutor.Unpause();
                    break;
                case RoomType.Event:
                    await ClimbManager.Instance.EnterRoomDebug(_settings.RoomType, model: _settings.Event, showTransition: false);
                    break;
                default:
                    await ClimbManager.Instance.EnterRoomDebug(
                        _settings.RoomType,
                        model: _settings.RoomType.IsCombatRoom() ? _settings.Encounter.ToMutable() : null,
                        showTransition: false
                    );
                    break;
            }
        }
        else
        {
            await NGame.Instance.StartNewMultiplayerClimb(_lobby, true, acts, [], seed, _lobby.Ascension);
            await SaveManager.Instance.SaveClimb(null);
            CleanUpLobby(false);
        }
    }

    public void Select(NCharacterSelectButton charSelectButton, CharacterModel characterModel)
    {
        _ascensionPanel.OnSelectedCharacterChanged(characterModel.Id);

        NDebugAudioManager.Instance?.Play(characterModel.CharacterSelectSfx);
        NGame.Instance?.ScreenShake(ShakeStrength.Weak, ShakeDuration.Short, 90f);

        if (_infoPanelTween != null)
        {
            _infoPanel.Position = _infoPanelPosFinalVal;
        }

        _infoPanelPosFinalVal = _infoPanel.Position;
        _infoPanelTween?.Kill();
        _infoPanelTween = CreateTween().SetParallel();
        _infoPanelTween.TweenProperty(_infoPanel, "position", _infoPanel.Position, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .From(_infoPanel.Position - new Vector2(300f, 0f));

        // Remove previous background
        foreach (Node? node in _bgContainer.GetChildren())
        {
            _bgContainer.RemoveChildSafely(node);
            node.QueueFreeSafely();
        }

        // Update button and background.
        _selectedButton = charSelectButton;
        Control bg = PreloadManager.Cache.GetScene(characterModel.CharacterSelectBg).Instantiate<Control>();
        bg.Name = $"{characterModel.Id.Entry}_bg";
        _bgContainer.AddChildSafely(bg);

        // Populate info panel with character model data
        string name = new LocString("characters", characterModel.CharacterSelectTitle).GetFormattedText();
        _name.SetTextAutoSize(name);
        _description.Text = new LocString("characters", characterModel.CharacterSelectDesc).GetFormattedText();
        _hp.Text = $"{characterModel.StartingHp}/{characterModel.StartingHp}";
        _gold.Text = $"{characterModel.StartingGold}";
        RelicModel startingRelic = characterModel.StartingRelics[0];
        _relicTitle.Text = startingRelic.Title.GetFormattedText();
        _relicDescription.Text = startingRelic.DynamicDescription.GetFormattedText();
        _relicIcon.Texture = startingRelic.Icon;
        _relicIconOutline.Texture = startingRelic.IconOutline;

        // Deselect all other buttons
        foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
        {
            if (button != _selectedButton)
            {
                button.Deselect();
            }
        }

        // Sync with multiplayer
        _lobby.SetLocalCharacter(characterModel);
    }

    /// <summary>
    /// Called when the ascension is changed from the ascension panel.
    /// When the host hits the left and right arrow, we want to send the ascension change to clients.
    /// On the clients, this is called when the ascension sync message is received, but we don't want to do anything.
    /// </summary>
    private void OnAscensionPanelLevelChanged()
    {
        if (_lobby.NetService.Type != NetGameType.Client &&
            _lobby.Ascension != _ascensionPanel.Ascension)
        {
            _lobby.SyncAscensionChange(_ascensionPanel.Ascension);
        }
    }

    /// <summary>
    /// Called when another player joins with a max ascension level lower than the current one.
    /// Also called when the lobby is first initialized.
    /// Called on both host and client.
    /// </summary>
    public void MaxMultiplayerAscensionChanged()
    {
        // In singleplayer, ascension is per-character.
        if (_lobby.NetService.Type != NetGameType.Singleplayer)
        {
            _ascensionPanel.SetMaxAscensionForMultiplayer(_lobby.MaxAscension);
        }
    }

    public void PlayerConnected(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerConnected(player);
        RefreshButtonSelectionForPlayer(player);
    }

    public void PlayerChanged(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerChanged(player);
        RefreshButtonSelectionForPlayer(player);
    }

    private void RefreshButtonSelectionForPlayer(LobbyPlayer player)
    {
        // This only handles remote players, local events handle the local player
        if (player.id == _lobby.LocalPlayer.id) return;

        foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
        {
            if (button.RemoteSelectedPlayers.Contains(player.id) && player.character != button.Character)
            {
                button.OnRemotePlayerDeselected(player.id);
            }
            else if (player.character == button.Character)
            {
                button.OnRemotePlayerSelected(player.id);
            }
        }
    }

    public void AscensionChanged()
    {
        // On client, if the ascension is set to zero, simply hide the panel
        if (_lobby.NetService.Type == NetGameType.Client)
        {
            _ascensionPanel.Visible = _lobby.Ascension > 0;
        }

        // On both host and client, if the ascension level changes, set the panel's level
        // This can change without input if a client with lower max ascension joins the climb
        _ascensionPanel.SetAscensionLevel(_lobby.Ascension);
    }

    public void SeedChanged()
    {
        throw new NotImplementedException("Seed should not be changed in standard mode!");
    }

    public void ModifiersChanged()
    {
        throw new NotImplementedException("Modifiers should not be changed in standard mode!");
    }

    public void RemotePlayerDisconnected(LobbyPlayer player)
    {
        _remotePlayerContainer.OnPlayerDisconnected(player);

        foreach (NCharacterSelectButton button in _charButtonContainer.GetChildren().OfType<NCharacterSelectButton>())
        {
            if (button.RemoteSelectedPlayers.Contains(player.id) && player.character == button.Character)
            {
                button.OnRemotePlayerDeselected(player.id);
            }
        }
    }

    public void BeginClimb(string seed, IReadOnlyList<ModifierModel> modifiers, string act1)
    {
        if (modifiers.Count > 0) Log.Error("Modifiers list is not empty while starting a standard climb, ignoring!");
        NAudioManager.Instance?.StopMusic();

        if (_lobby.NetService.Type == NetGameType.Singleplayer)
        {
            TaskHelper.RunSafely(StartNewSingleplayerClimb(seed, act1));
        }
        else
        {
            TaskHelper.RunSafely(StartNewMultiplayerClimb(seed, act1));
        }
    }

    public void LocalPlayerDisconnected(NetErrorInfo info)
    {
        // If we're the one that disconnected, then do nothing
        if (info.SelfInitiated && info.GetReason() == NetError.Quit) return;

        // Otherwise, close window and display error
        _stack.Pop();

        if (TestMode.IsOff)
        {
            NErrorPopup? popup = NErrorPopup.Create(info);

            if (popup != null)
            {
                NModalContainer.Instance!.AddChildSafely(popup);
            }
        }
    }

    private void AfterInitialized()
    {
        // Note that this happens for both multiplayer and singleplayer. Map drawings rely on the remote cursor
        // container's synchronizer for drawing state even in singleplayer.
        NGame.Instance!.RemoteCursorContainer.Initialize(_lobby.InputSynchronizer, _lobby.Players.Select(p => p.id));
        NGame.Instance.ReactionContainer.InitializeNetworking(_lobby.NetService);

        // We might want to remove this at some point. It's here for debugging purposes.
        Logger.logLevelTypeMap[LogType.Network] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.Debug;
        Logger.logLevelTypeMap[LogType.Actions] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;
        Logger.logLevelTypeMap[LogType.GameSync] = _lobby.NetService.Type == NetGameType.Singleplayer ? LogLevel.Info : LogLevel.VeryDebug;

        if (_lobby.NetService.Type != NetGameType.Singleplayer && (_settings?.BootstrapInMultiplayer ?? false))
        {
            NGame.Instance.DebugSeedOverride = _settings.Seed;
        }
        else
        {
            NGame.Instance.DebugSeedOverride = null;
        }
    }
}
