using System;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Entities.UI;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Screens.CharacterSelect;

public partial class NAscensionPanel : Control
{
    [Signal]
    public delegate void AscensionLevelChangedEventHandler();

    public int Ascension { get; private set; }
    private int _maxAscension;

    private NButton _leftArrow = default!;
    private NButton _rightArrow = default!;
    private MegaLabel _ascensionLevel = default!;
    private Label _infoHeader = default!;
    private MegaRichTextLabel _infoBody = default!;
    private bool _canChange = true;
    private MultiplayerUiMode _mode = MultiplayerUiMode.Singleplayer;

    public override void _Ready()
    {
        _leftArrow = GetNode<NButton>("HBoxContainer/LeftArrowContainer/LeftArrow");
        _rightArrow = GetNode<NButton>("HBoxContainer/RightArrowContainer/RightArrow");
        _ascensionLevel = GetNode<MegaLabel>("HBoxContainer/AscensionIcon/AscensionLevel");
        _infoHeader = GetNode<Label>("HBoxContainer/AscensionDescription/Header");
        _infoBody = GetNode<MegaRichTextLabel>("HBoxContainer/AscensionDescription/Description");

        _leftArrow.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(DecrementAscension));
        _rightArrow.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(IncrementAscension));
    }

    public void Initialize(MultiplayerUiMode mode)
    {
        _mode = mode;

        if (_mode == MultiplayerUiMode.Host)
        {
            // On multiplayer host, we start with our local preferred multiplayer ascension and max ascension.
            // These can change when a client joins (via OnMaxAscensionChanged).
            SetMaxAscensionForMultiplayer(SaveManager.Instance.ProgressSave.MaxMultiplayerAscension);
            SetAscensionLevel(SaveManager.Instance.ProgressSave.PreferredMultiplayerAscension);
        }
        else if (_mode is MultiplayerUiMode.Client or MultiplayerUiMode.Load)
        {
            // On multiplayer clients, we just listen to whatever the host says. Max is initialized to zero by default
            // because the lobby only updates max ascension if it is non-zero.
            SetMaxAscensionForMultiplayer(0);
            _canChange = false;
        }

        // In singleplayer, OnSelectedCharacterChanged handles ascension changes.
    }

    public void SetAscensionLevel(int ascension)
    {
        if (Ascension != ascension)
        {
            Ascension = ascension;
            EmitSignal(SignalName.AscensionLevelChanged);
        }

        RefreshAscensionText();
        RefreshArrowVisibility();
    }

    private void IncrementAscension(NButton obj)
    {
        SetAscensionLevel(Ascension + 1);
    }

    private void DecrementAscension(NButton obj)
    {
        SetAscensionLevel(Ascension - 1);
    }

    private void RefreshArrowVisibility()
    {
        _leftArrow.Visible = _canChange && Ascension != 0;
        _rightArrow.Visible = _canChange && Ascension != _maxAscension;
    }

    public void SetMaxAscensionForMultiplayer(int maxAscension)
    {
        Log.Info($"Max ascension changed to {maxAscension}");
        if (_mode == MultiplayerUiMode.Singleplayer) throw new InvalidOperationException("SetMaxAscensionForMultiplayer should only be called in host or client mode!");

        _maxAscension = maxAscension;

        if (Ascension >= _maxAscension)
        {
            SetAscensionLevel(_maxAscension);
        }

        Visible = _maxAscension > 0;
        RefreshArrowVisibility();
    }

    public void OnSelectedCharacterChanged(ModelId characterId)
    {
        // In singleplayer, the ascension depends on the character selected.
        // In multiplayer hosts, the ascension depends on the multiplayer ascension in progress save.
        // In multiplayer clients, the ascension depends on what the host has selected.
        if (_mode != MultiplayerUiMode.Singleplayer) return;

        bool isAscensionAvailable = false;
        int preferredAscension = 0;
        _maxAscension = 0;

        // Grab the player's preferred ascension level in the character select screen
        foreach (CharacterStats character in SaveManager.Instance.ProgressSave.CharStats)
        {
            if (character.Id == characterId)
            {
                // must make sure to set the Max ascension before call SetAscensionLevel
                // or else you wil improperly set the arrow buttons
                isAscensionAvailable = character.MaxAscension > 0;
                _maxAscension = character.MaxAscension;
                preferredAscension = Math.Min(character.PreferredAscension, character.MaxAscension);
                SetAscensionLevel(preferredAscension);
                break;
            }
        }

        if (!isAscensionAvailable)
        {
            Visible = false;
            _maxAscension = 0;
            SetAscensionLevel(0);
        }
        else
        {
            Log.Info($"Loading preferred Ascension for {characterId}: {preferredAscension}");
            Visible = true;
        }
    }

    private void RefreshAscensionText()
    {
        _ascensionLevel.Text = Ascension.ToString();
        _infoHeader.Text = AscensionHelper.GetTitle(Ascension).GetFormattedText();
        _infoBody.Text = AscensionHelper.GetDescription(Ascension).GetFormattedText();
    }
}
