using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.Screens.ClimbHistoryScreen;

namespace MegaCrit.Sts2.Core.Nodes.Screens.GameOverScreen;

/// <summary>
/// Animates the Climb Summary portion of the Death Screen.
/// </summary>
public partial class NClimbSummary : Control
{
    private NMapPointHistory _mapPointHistory = default!;
    private Control _discoveryContainer = default!;
    private Control _discoveryHeader = default!;

    private Control _discoveredCards = default!;
    private Control _discoveredRelics = default!;
    private Control _discoveredPotions = default!;
    private Control _discoveredEnemies = default!;
    private Control _discoveredEpochs = default!;

    private Label _cardCount = default!;
    private Label _relicCount = default!;
    private Label _potionCount = default!;
    private Label _enemyCount = default!;
    private Label _epochCount = default!;

    private Tween? _tween;
    private Tween? _waitTween;
    private bool _hurryUp;

    public override void _Ready()
    {
        _mapPointHistory = GetNode<NMapPointHistory>("%MapPointHistory");
        _discoveryContainer = GetNode<Control>("%DiscoveryContainer");
        _discoveryHeader = GetNode<Control>("%DiscoveryHeader");

        _cardCount = GetNode<Label>("%CardCount");
        _relicCount = GetNode<Label>("%RelicCount");
        _potionCount = GetNode<Label>("%PotionCount");
        _enemyCount = GetNode<Label>("%EnemyCount");
        _epochCount = GetNode<Label>("%EpochCount");

        // NOTE: GetParent() is faster than GetNode(). That's all
        _discoveredCards = _cardCount.GetParent<Control>();
        _discoveredRelics = _relicCount.GetParent<Control>();
        _discoveredPotions = _potionCount.GetParent<Control>();
        _discoveredEnemies = _enemyCount.GetParent<Control>();
        _discoveredEpochs = _epochCount.GetParent<Control>();

        _discoveredCards.Visible = false;
        _discoveredRelics.Visible = false;
        _discoveredPotions.Visible = false;
        _discoveredEnemies.Visible = false;
        _discoveredEpochs.Visible = false;
    }

    public void HurryUp()
    {
        _hurryUp = true;

        if (_waitTween != null && _waitTween.IsRunning())
        {
            _waitTween.Kill();
            _waitTween.EmitSignal(Tween.SignalName.Finished);
        }
    }

    private void SetDiscoveryContainerVisibility(Control container, uint amount)
    {
        if (amount > 0)
        {
            container.Visible = true;
            container.Modulate = StsColors.transparentBlack;
        }
    }

    public async Task AnimateInDiscoveries(ClimbState climbState)
    {
        Player player = LocalContext.GetMe(climbState)!;

        // No discoveries. Abort
        if (player.DiscoveredCards + player.DiscoveredRelics + player.DiscoveredPotions + player.DiscoveredEnemies + player.DiscoveredEpochs == 0)
        {
            Log.Info("No discoveries this time. Very sad");
            return;
        }

        _discoveryContainer.Visible = true;

        // Spawn the header
        Tween tween = CreateTween();
        tween.TweenProperty(_discoveryHeader, "modulate:a", 1f, 0.25);

        await Task.Delay(_hurryUp ? 100 : 500);

        // We set the visibility before we start the Tweens as creating them one by one will mess up the HBoxContainer
        SetDiscoveryContainerVisibility(_discoveredCards, player.DiscoveredCards);
        SetDiscoveryContainerVisibility(_discoveredRelics, player.DiscoveredRelics);
        SetDiscoveryContainerVisibility(_discoveredPotions, player.DiscoveredPotions);
        SetDiscoveryContainerVisibility(_discoveredEnemies, player.DiscoveredEnemies);
        SetDiscoveryContainerVisibility(_discoveredEpochs, player.DiscoveredEpochs);

        // Spawn each section
        if (_discoveredCards.Visible)
        {
            _cardCount.Text = $"{player.DiscoveredCards}";
            await TaskHelper.RunSafely(DiscoveryAnimHelper(_discoveredCards));
        }

        if (_discoveredRelics.Visible)
        {
            _relicCount.Text = $"{player.DiscoveredRelics}";
            await TaskHelper.RunSafely(DiscoveryAnimHelper(_discoveredRelics));
        }

        if (_discoveredPotions.Visible)
        {
            _potionCount.Text = $"{player.DiscoveredPotions}";
            await TaskHelper.RunSafely(DiscoveryAnimHelper(_discoveredPotions));
        }

        if (_discoveredEnemies.Visible)
        {
            _enemyCount.Text = $"{player.DiscoveredEnemies}";
            await TaskHelper.RunSafely(DiscoveryAnimHelper(_discoveredEnemies));
        }

        if (_discoveredEpochs.Visible)
        {
            _epochCount.Text = $"{player.DiscoveredEpochs}";
            await TaskHelper.RunSafely(DiscoveryAnimHelper(_discoveredEpochs));
        }
    }

    private async Task DiscoveryAnimHelper(Control node)
    {
        node.Modulate = StsColors.transparentBlack;
        _tween?.Kill();
        _tween = CreateTween().SetParallel();

        if (!_hurryUp)
        {
            _tween.TweenProperty(node, "modulate", Colors.White, 0.3);
            _tween.TweenProperty(node, "position:y", 0f, 0.3)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back)
                .From(100f);
        }
        else
        {
            _tween.TweenProperty(node, "modulate", Colors.White, 0.1);
            _tween.TweenProperty(node, "position:y", 0f, 0.1)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Back)
                .From(100f);
        }

        await ToSignal(_tween, Tween.SignalName.Finished);
    }
}
