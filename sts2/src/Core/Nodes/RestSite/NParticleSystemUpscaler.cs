using Godot;

namespace MegaCrit.Sts2.Core.Nodes.RestSite;

/// <summary>
/// A utility for particle systems whose textures can get downscaled
/// Scales up the particle system so that it matches the original texture's size.
/// Used mainly for the ground lightning in rest sites.
/// </summary>
public partial class NParticleSystemUpscaler : CpuParticles2D
{

    [Export]
    private Vector2 _originalResolution;
    
    public override void _Ready()
    {
        Texture2D particleTexture = Texture;
        Vector2 textureResolution = particleTexture.GetSize();

        float scaling = _originalResolution.X / textureResolution.X;
        ScaleAmountMin *= scaling;
        ScaleAmountMax = ScaleAmountMin;
    }
}