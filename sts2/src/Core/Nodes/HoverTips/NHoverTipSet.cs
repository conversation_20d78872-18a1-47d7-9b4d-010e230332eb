using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.Relics;
using MegaRichTextLabel = MegaCrit.Sts2.addons.mega_text.MegaRichTextLabel;

namespace MegaCrit.Sts2.Core.Nodes.HoverTips;

public partial class NHoverTipSet : Control
{
    private const float _hoverTipSpacing = 5;
    private const float _hoverTipWidth = 360;

    private const string _tipScenePath = "res://scenes/ui/hover_tip.tscn";
    private const string _tipSetScenePath = "res://scenes/ui/hover_tip_set.tscn";
    private const string _debuffMatPath = "res://materials/ui/hover_tip_debuff.tres";

    private static Node HoverTipsContainer => NGame.Instance!.HoverTipsContainer!;
    private static readonly Dictionary<Control, NHoverTipSet> _activeHoverTips = new();

    private VFlowContainer _textHoverTipContainer = default!;
    private NHoverTipCardContainer _cardHoverTipContainer = default!;

    private Vector2 TextHoverTipDimensions => _textHoverTipContainer.Size;
    private Vector2 CardHoverTipDimensions => _cardHoverTipContainer.Size;

    private Control _owner = default!;
    private bool _followOwner;
    private Vector2 _followOffset;

    public static IEnumerable<string> AssetPaths =>
    [
        _tipScenePath,
        _tipSetScenePath,
        _debuffMatPath
    ];

    public static NHoverTipSet CreateAndShow(Control owner, IHoverTip hoverTip, HoverTipAlignment alignment = HoverTipAlignment.None)
    {
        return CreateAndShow(owner, [hoverTip], alignment);
    }

    public static NHoverTipSet CreateAndShow(Control owner, IEnumerable<IHoverTip> hoverTips, HoverTipAlignment alignment = HoverTipAlignment.None)
    {
        NHoverTipSet tipSet = PreloadManager.Cache.GetScene(_tipSetScenePath).Instantiate<NHoverTipSet>();
        HoverTipsContainer.AddChildSafely(tipSet);
        _activeHoverTips.Add(owner, tipSet);
        tipSet.Init(owner, hoverTips);

        // DEBUG: Hide the HoverTip. For trailer purposes.
        if (NGame.IsDebugHidingHoverTips)
        {
            tipSet.Visible = false;
        }

        owner.Connect(Node.SignalName.TreeExiting, Callable.From(() => Remove(owner)));
        tipSet.SetAlignment(owner, alignment);
        return tipSet;
    }

    public static NHoverTipSet CreateAndShowMapPointHistory(Control owner, NMapPointHistoryHoverTip historyHoverTip)
    {
        NHoverTipSet tipSet = PreloadManager.Cache.GetScene(_tipSetScenePath).Instantiate<NHoverTipSet>();
        HoverTipsContainer.AddChildSafely(tipSet);
        _activeHoverTips.Add(owner, tipSet);
        tipSet._owner = owner;
        tipSet._textHoverTipContainer.AddChildSafely(historyHoverTip);

        // DEBUG: Hide the HoverTip. For trailer purposes.
        if (NGame.IsDebugHidingHoverTips)
        {
            tipSet.Visible = false;
        }

        owner.Connect(Node.SignalName.TreeExiting, Callable.From(() => Remove(owner)));
        return tipSet;
    }

    public override void _Ready()
    {
        _textHoverTipContainer = new VFlowContainer();
        _textHoverTipContainer.Name = "textHoverTipContainer";
        _textHoverTipContainer.MouseFilter = MouseFilterEnum.Ignore;
        this.AddChildSafely(_textHoverTipContainer);

        _cardHoverTipContainer = new NHoverTipCardContainer();
        _cardHoverTipContainer.Name = "cardHoverTipContainer";
        _cardHoverTipContainer.MouseFilter = MouseFilterEnum.Ignore;
        this.AddChildSafely(_cardHoverTipContainer);
    }

    public override void _Process(double delta)
    {
        if (!_followOwner) return;

        GlobalPosition = _owner.GlobalPosition - _followOffset;
    }

    private void Init(Control owner, IEnumerable<IHoverTip> hoverTips)
    {
        _owner = owner;

        foreach (IHoverTip hoverTip in IHoverTip.RemoveDupes(hoverTips))
        {
            if (hoverTip is HoverTip textTip)
            {
                Control tipNode = PreloadManager.Cache.GetScene(_tipScenePath).Instantiate<Control>();
                _textHoverTipContainer.AddChildSafely(tipNode);

                if (textTip.Title == null)
                {
                    tipNode.GetNode<Label>("%Title").Visible = false;
                }
                else
                {
                    tipNode.GetNode<Label>("%Title").Text = textTip.Title;
                }

                tipNode.GetNode<MegaRichTextLabel>("%Description").Text = textTip.Description;
                tipNode.GetNode<MegaRichTextLabel>("%Description").AutowrapMode = textTip.ShouldOverrideTextOverflow ? TextServer.AutowrapMode.Off : TextServer.AutowrapMode.WordSmart;
                tipNode.GetNode<TextureRect>("%Icon").Texture = textTip.Icon;

                if (textTip.IsDebuff)
                {
                    tipNode.GetNode<CanvasItem>("%Bg").Material = PreloadManager.Cache.GetMaterial(_debuffMatPath);
                }

                tipNode.ResetSize();
                if (_textHoverTipContainer.Size.Y + tipNode.Size.Y + _hoverTipSpacing < NGame.Instance!.GetViewportRect().Size.Y - 50f)
                {
                    _textHoverTipContainer.Size = new Vector2(_hoverTipWidth, _textHoverTipContainer.Size.Y + tipNode.Size.Y + _hoverTipSpacing);
                }
                else
                {
                    _textHoverTipContainer.Alignment = FlowContainer.AlignmentMode.Center;
                }
            }
            else
            {
                _cardHoverTipContainer.Add((CardHoverTip)hoverTip);
            }
        }
    }

    /// <summary>
    /// Sets the position of the hover tip relative to the given node based on the alignment set.
    /// </summary>
    /// <param name="node">The control node that we are aligning the hover tip to</param>.
    /// <param name="alignment">Dictates the position of the hover tip relative to the given node.
    /// ie: top left side of the node, Top right side of the node</param>
    public void SetAlignment(Control node, HoverTipAlignment alignment)
    {
        if (alignment != HoverTipAlignment.None)
        {
            _textHoverTipContainer.Position = Vector2.Zero;

            if (alignment == HoverTipAlignment.Left)
            {
                _textHoverTipContainer.GlobalPosition = node.GlobalPosition;
                _textHoverTipContainer.Position += Vector2.Left * _textHoverTipContainer.Size.X;
                _textHoverTipContainer.ReverseFill = true;
                _cardHoverTipContainer.LayoutResizeAndReposition(node.GlobalPosition + new Vector2(node.Size.X, 0) * node.Scale, HoverTipAlignment.Right);
            }
            else if (alignment == HoverTipAlignment.Right)
            {
                _cardHoverTipContainer.LayoutResizeAndReposition(node.GlobalPosition, HoverTipAlignment.Left);
                _textHoverTipContainer.GlobalPosition = node.GlobalPosition + new Vector2(node.Size.X, 0) * node.Scale;
            }
            else if (alignment == HoverTipAlignment.Center)
            {
                GlobalPosition = node.GlobalPosition + Vector2.Down * node.Size.Y * 1.5f;
                _cardHoverTipContainer.GlobalPosition += Vector2.Down * _textHoverTipContainer.Size.Y;
                _cardHoverTipContainer.LayoutResizeAndReposition(_cardHoverTipContainer.GlobalPosition, alignment);
            }
        }

        CorrectVerticalOverflow();
        CorrectHorizontalOverflow();
    }

    /// <summary>
    /// Align the hover teips to the given relic. We separate it because there are special positioning
    /// rules for relics.
    /// </summary>
    /// <param name="relic">the relic we are aligning too</param>
    public void SetAlignmentForRelic(NRelic relic)
    {
        HoverTipAlignment alignment = HoverTip.GetHoverTipAlignment(this);
        Vector2 relicSize = relic.Icon.Size * relic.GetGlobalTransform().Scale;
        _textHoverTipContainer.GlobalPosition = relic.GlobalPosition + Vector2.Down * (relicSize.Y + 10f);

        if (alignment == HoverTipAlignment.Left)
        {
            _textHoverTipContainer.Position += Vector2.Left * (_textHoverTipContainer.Size.X - relicSize.X);
        }

        _cardHoverTipContainer.LayoutResizeAndReposition(_textHoverTipContainer.GlobalPosition + Vector2.Down * _textHoverTipContainer.Size.Y, alignment);

        // We do this check so relics that are low enough on the screen (i.e. in the relic collection)
        // have their hover tips be above the relic.
        float windowYSize = NGame.Instance!.GetViewportRect().Size.Y;
        if (relic.GlobalPosition.Y > windowYSize * 0.75f)
        {
            _textHoverTipContainer.GlobalPosition = relic.GlobalPosition + Vector2.Up * _textHoverTipContainer.Size.Y;
        }

        CorrectVerticalOverflow();
        CorrectHorizontalOverflow();
    }

    /// <summary>
    /// Positions the hovertips given a Card.  We separate it because there are special positioning
    /// rules for cards.
    /// </summary>
    /// <param name="holder">the holder that we are aligning to</param>
    public void SetAlignmentForCardHolder(NCardHolder holder)
    {
        HoverTipAlignment alignment = HoverTip.GetHoverTipAlignment(holder);
        _textHoverTipContainer.Position = Vector2.Zero;
        Control holderHitbox = holder.Hitbox;

        if (alignment == HoverTipAlignment.Left)
        {
            _textHoverTipContainer.GlobalPosition = holderHitbox.GlobalPosition;
            _textHoverTipContainer.Position += Vector2.Left * _textHoverTipContainer.Size.X - new Vector2(10f, 0f);
            _textHoverTipContainer.ReverseFill = true;
            _cardHoverTipContainer.LayoutResizeAndReposition(holderHitbox.GlobalPosition + new Vector2(holderHitbox.Size.X, 0) * holderHitbox.Scale, HoverTipAlignment.Right);
        }
        else
        {
            _cardHoverTipContainer.LayoutResizeAndReposition(holderHitbox.GlobalPosition, HoverTipAlignment.Left);
            _textHoverTipContainer.GlobalPosition = holderHitbox.GlobalPosition + new Vector2(holderHitbox.Size.X + 10f, 0) * holderHitbox.Scale;
        }

        CorrectVerticalOverflow();
        CorrectHorizontalOverflow();

        _followOwner = true;
        _followOffset = holder.GlobalPosition - GlobalPosition;
    }

    private void CorrectVerticalOverflow()
    {
        float windowYSize = NGame.Instance!.GetViewportRect().Size.Y;

        if (_textHoverTipContainer.GlobalPosition.Y + _textHoverTipContainer.Size.Y > windowYSize)
        {
            _textHoverTipContainer.GlobalPosition = new Vector2(_textHoverTipContainer.GlobalPosition.X, windowYSize - _textHoverTipContainer.Size.Y);
        }

        if (_cardHoverTipContainer.GlobalPosition.Y + _cardHoverTipContainer.Size.Y > windowYSize)
        {
            _cardHoverTipContainer.GlobalPosition = new Vector2(_cardHoverTipContainer.GlobalPosition.X, windowYSize - _cardHoverTipContainer.Size.Y);
        }
    }

    private void CorrectHorizontalOverflow()
    {
        float windowXSize = NGame.Instance!.GetViewportRect().Size.X;

        Vector2 cardTipPos = _cardHoverTipContainer.GlobalPosition;
        float cardTipWidth = _cardHoverTipContainer.Size.X;
        Vector2 textTipPos = _textHoverTipContainer.GlobalPosition;
        float textTipWidth = _textHoverTipContainer.Size.X;

        if (cardTipPos.X + cardTipWidth > windowXSize || textTipPos.X + textTipWidth > windowXSize)
        {
            float cardHoverTipPosX = textTipPos.X + textTipWidth - cardTipWidth;
            _cardHoverTipContainer.GlobalPosition = new Vector2(cardHoverTipPosX, cardTipPos.Y);
            _textHoverTipContainer.GlobalPosition += Vector2.Left * cardTipWidth;
        }
        else if (cardTipPos.X < 0 || textTipPos.X < 0)
        {
            float cardHoverTipPosX = textTipPos.X;
            _cardHoverTipContainer.GlobalPosition = new Vector2(cardHoverTipPosX, cardTipPos.Y);
            _textHoverTipContainer.GlobalPosition += Vector2.Right * cardTipWidth;
        }
    }

    public static void Clear()
    {
        foreach (Control node in _activeHoverTips.Keys)
        {
            Remove(node);
        }
    }

    public static void Remove(Control owner)
    {
        if (_activeHoverTips.TryGetValue(owner, out NHoverTipSet? value))
        {
            value.QueueFreeSafely();
            _activeHoverTips.Remove(owner);
        }
    }
}
