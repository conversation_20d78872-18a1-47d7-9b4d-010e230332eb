using Godot;
using MegaCrit.Sts2.Core.Helpers;
using Range = Godot.Range;

namespace MegaCrit.Sts2.Core.Nodes.GodotExtensions;

[GlobalClass]
public partial class NScrollbar : Range
{
    [Signal]
    public delegate void MouseReleasedEventHandler(InputEvent inputEvent);

    [Signal]
    public delegate void MousePressedEventHandler(InputEvent inputEvent);

    private Control _handle = default!;

    private float _currentHandlePosition;
    private float _currentVelocity;
    private bool _isDragging;

    public override void _Ready()
    {
        _handle = GetNode<Control>("%Handle");
    }

    /// <summary>
    /// WARNING: If overriding, be sure to call the base function to retain
    /// OnPressDown and OnRelease functionality.
    /// </summary>
    /// <param name="inputEvent"></param>
    public override void _GuiInput(InputEvent inputEvent)
    {
        base._GuiInput(inputEvent);

        switch (inputEvent)
        {
            case InputEventMouseButton buttonEvent:
                if (buttonEvent.ButtonIndex is MouseButton.Left or MouseButton.Right)
                {
                    _isDragging = buttonEvent.IsPressed();
                    SetValueBasedOnMousePosition(buttonEvent.Position);
                    EmitSignal(buttonEvent.IsPressed() ? SignalName.MousePressed : SignalName.MouseReleased, inputEvent);
                }

                break;
            case InputEventMouseMotion motionEvent:
                if (_isDragging)
                {
                    SetValueBasedOnMousePosition(motionEvent.Position);
                }

                break;
        }
    }

    private void SetValueBasedOnMousePosition(Vector2 mousePosition)
    {
        Value = mousePosition.Y / Size.Y * MaxValue;
    }

    public void SetValueWithoutAnimation(double value)
    {
        _currentHandlePosition = (float)value;
        Value = value;
        UpdateHandlePosition();
    }

    public override void _Process(double delta)
    {
        _currentHandlePosition = (float)Value;
        UpdateHandlePosition();
    }

    private void UpdateHandlePosition()
    {
        float targetPosition = MathHelper.SmoothDamp(_handle.Position.Y, Size.Y * (float)(_currentHandlePosition / MaxValue) - _handle.Size.Y * 0.5f, ref _currentVelocity, 0.05f, (float)GetProcessDeltaTime());
        _handle.Position = new Vector2((Size.X - _handle.Size.X) * 0.5f, targetPosition);
    }
}
