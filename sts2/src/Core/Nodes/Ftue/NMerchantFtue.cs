using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Ftue;

/// <summary>
/// Ftue which appears when a player presses Proceed without interacting with the Merchant.
/// Auto-disables if the player interacts with the Merchant.
/// </summary>
public partial class NMerchantFtue : Control
{
    public const string id = "merchant_ftue";
    private static readonly string _scenePath = SceneHelper.GetScenePath("ftue/merchant_ftue");
    private NButton _confirmButton = default!;
    private Label _header = default!;
    private MegaRichTextLabel _description = default!;
    private Control _sneakyHitbox = default!;
    private NMerchantRoom _merchantRoom = default!;
    private int _defaultZIndex;

    public override void _Ready()
    {
        _header = GetNode<Label>("FtuePopup/Header");
        _header.Text = new LocString("ftues", "MERCHANT_FTUE_TITLE").GetFormattedText();
        _description = GetNode<MegaRichTextLabel>("FtuePopup/DescriptionContainer/Description");
        _description.Text = new LocString("ftues", "MERCHANT_FTUE_DESCRIPTION").GetFormattedText();

        _confirmButton = GetNode<NButton>("FtuePopup/FtueConfirmButton");
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseFtue));
        _sneakyHitbox = GetNode<Control>("SneakyHitbox");
        _sneakyHitbox.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseFtueAndOpenRug));
        // NOTE: Once aspect ratio work is done for ShopRoom bg, dynamically place this hitbox so it's more accurate
        // _sneakyHitbox.GlobalPosition = NCombatRoom.Instance.Ui.EndTurnButton.GlobalPosition;

        _defaultZIndex = _merchantRoom.MerchantButton.ZIndex;
        _merchantRoom.MerchantButton.ZIndex++;
    }

    public static NMerchantFtue? Create(NMerchantRoom merchantRoom)
    {
        if (TestMode.IsOn) return null;

        NMerchantFtue node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NMerchantFtue>();
        node._merchantRoom = merchantRoom;

        return node;
    }

    private void CloseFtueAndOpenRug(NButton _)
    {
        _merchantRoom.OpenRug();
        CloseFtue(_);
    }

    private void CloseFtue(NButton _)
    {
        _merchantRoom.MerchantButton.ZIndex = _defaultZIndex;
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }
}
