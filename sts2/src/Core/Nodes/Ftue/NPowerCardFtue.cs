using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Ftue;

/// <summary>
/// Ftue which appears when a player is presented with an option to receive a Power card in a Card Rewards screen,
/// Choose Card Selection screen, or a Shop screen. NOTE: I lied, these last two cases aren't implemented yet.
/// </summary>
public partial class NPowerCardFtue : Control
{
    public const string id = "power_card_ftue";
    private static readonly string _scenePath = SceneHelper.GetScenePath("ftue/power_card_ftue");
    private NButton _confirmButton = default!;
    private Label _header = default!;
    private MegaRichTextLabel _description = default!;
    private Control _card = default!;
    private Control _ftueHolder = default!;
    private int _defaultZIndex;

    public override void _Ready()
    {
        _header = GetNode<Label>("FtuePopup/Header");
        _header.Text = new LocString("ftues", "POWER_FTUE_TITLE").GetFormattedText();
        _description = GetNode<MegaRichTextLabel>("FtuePopup/DescriptionContainer/Description");
        _description.Text = new LocString("ftues", "POWER_FTUE_DESCRIPTION").GetFormattedText();

        _confirmButton = GetNode<NButton>("FtuePopup/FtueConfirmButton");
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseFtue));

        _ftueHolder = GetNode<Control>("FtuePopup");

        _defaultZIndex = _card.ZIndex;
        _card.ZIndex++;
    }

    public static NPowerCardFtue? Create(Control card)
    {
        if (TestMode.IsOn) return null;

        NPowerCardFtue ftue = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NPowerCardFtue>();
        ftue._card = card;

        return ftue;
    }

    private void CloseFtue(NButton _)
    {
        _card.ZIndex = _defaultZIndex;
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }
}
