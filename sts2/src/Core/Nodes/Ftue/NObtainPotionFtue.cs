using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Ftue;

public partial class NObtainPotionFtue : Control
{
    public const string id = "obtain_potion_ftue";

    private static readonly string _scenePath = SceneHelper.GetScenePath("ftue/obtain_potion_ftue");
    private NButton _confirmButton = default!;
    private Label _header = default!;
    private MegaRichTextLabel _description = default!;
    private int _defaultZIndex;

    public override void _Ready()
    {
        _header = GetNode<Label>("FtuePopup/Header");
        _header.Text = new LocString("ftues", "POTION_FTUE_TITLE").GetFormattedText();
        _description = GetNode<MegaRichTextLabel>("FtuePopup/DescriptionContainer/Description");
        _description.Text = new LocString("ftues", "POTION_FTUE_DESCRIPTION").GetFormattedText();
        _confirmButton = GetNode<NButton>("FtuePopup/FtueConfirmButton");
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseFtue));

        // NOTE: Can this be improved by having our arrow follow the potion in _Process?
        _defaultZIndex = NClimb.Instance!.GlobalUi.TopBar.PotionContainer.ZIndex;
        NClimb.Instance.GlobalUi.TopBar.PotionContainer.ZIndex++;
    }

    public static NObtainPotionFtue? Create()
    {
        if (TestMode.IsOn) return null;

        return PreloadManager.Cache.GetScene(_scenePath).Instantiate<NObtainPotionFtue>();
    }

    private void CloseFtue(NButton _)
    {
        NClimb.Instance!.GlobalUi.TopBar.PotionContainer.ZIndex = _defaultZIndex;
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }
}
