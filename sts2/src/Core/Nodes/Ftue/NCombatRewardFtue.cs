using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Ftue;

/// <summary>
/// Ftue which appears when a player presses Proceed with items remaining in the Combat Reward screen.
/// Auto-disables after you Proceed without forgetting.
/// </summary>
public partial class NCombatRewardFtue : Control
{
    public const string id = "combat_reward_ftue";
    private static readonly string _scenePath = SceneHelper.GetScenePath("ftue/combat_reward_ftue");
    private NButton _confirmButton = default!;
    private Label _header = default!;
    private MegaRichTextLabel _description = default!;
    private Control _rewardsContainer = default!;
    private int _defaultZIndex;

    public override void _Ready()
    {
        _header = GetNode<Label>("FtuePopup/Header");
        _header.Text = new LocString("ftues", "REWARDS_FTUE_TITLE").GetFormattedText();
        _description = GetNode<MegaRichTextLabel>("FtuePopup/DescriptionContainer/Description");
        _description.Text = new LocString("ftues", "REWARDS_FTUE_DESCRIPTION").GetFormattedText();

        _confirmButton = GetNode<NButton>("FtuePopup/FtueConfirmButton");
        _confirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(CloseFtue));
        _defaultZIndex = _rewardsContainer.ZIndex;
        _rewardsContainer.ZIndex++;
    }

    public static NCombatRewardFtue? Create(Control rewardsContainer)
    {
        if (TestMode.IsOn) return null;

        NCombatRewardFtue node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NCombatRewardFtue>();
        node._rewardsContainer = rewardsContainer;

        return node;
    }

    private void CloseFtue(NButton _)
    {
        _rewardsContainer.ZIndex = _defaultZIndex;
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }

    public async Task WaitForPlayerToConfirm()
    {
        await ToSignal(_confirmButton, NClickableControl.SignalName.Released);
    }
}
