using System.Collections.Generic;
using System.Data;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Screens.CardLibrary;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Cards.Holders;

public partial class NGridCardHolder : NCardHolder
{
    private static string ScenePath => SceneHelper.GetScenePath("cards/holders/grid_card_holder");
    public static IEnumerable<string> AssetPaths => [ScenePath];

    public NCardLibraryStats? CardLibraryStats { get; private set; }

    private CardModel _baseCard = default!;
    private CardModel? _upgradedCard;
    private bool _isPreviewingUpgrade;

    public override CardModel CardModel => _baseCard;

    public override bool IsShowingUpgradedCard => _isPreviewingUpgrade || base.IsShowingUpgradedCard;

    public static NGridCardHolder? Create(NCard cardNode)
    {
        if (TestMode.IsOn) return null;

        NGridCardHolder holder = PreloadManager.Cache.GetScene(ScenePath).Instantiate<NGridCardHolder>();
        holder.SetCard(cardNode);
        holder.UpdateCardModel();
        holder.UpdateName();
        holder.Scale = holder.SmallScale;

        return holder;
    }

    private void UpdateCardModel()
    {
        CardModel card = CardNode!.Model!;
        _baseCard = card;

        if (card.IsUpgradable)
        {
            // Normally we use ToMutable() (which only works on Canonical models) or ClonePreservingMutability(), but
            // this is a very general node that's used in both mutable (upgrade selection) and canonical (card library)
            // spots, and we want a mutable clone regardless of what we started with.
            _upgradedCard = (CardModel)card.MutableClone();
            _upgradedCard.UpgradeInternal();

            if (IsNodeReady())
            {
                bool shouldPreviewUpgrade = _isPreviewingUpgrade;
                _isPreviewingUpgrade = false;
                SetIsPreviewingUpgrade(shouldPreviewUpgrade);
            }
        }
    }

    public override void _Ready()
    {
        ConnectSignals();
        bool shouldPreviewUpgrade = _isPreviewingUpgrade;
        _isPreviewingUpgrade = false;
        SetIsPreviewingUpgrade(shouldPreviewUpgrade);
    }

    public void EnsureCardLibraryStatsExists()
    {
        if (CardLibraryStats != null) return;

        CardLibraryStats = NCardLibraryStats.Create();
        this.AddChildSafely(CardLibraryStats);
    }

    protected override void OnCardReassigned()
    {
        UpdateCardModel();
        UpdateName();
    }

    private void UpdateName()
    {
        Name = $"GridCardHolder-{CardNode!.Model!.Id}";
    }

    protected override void OnFocus()
    {
        base.OnFocus();
        MoveToFront();
    }

    public void SetIsPreviewingUpgrade(bool showUpgradePreview)
    {
        if (!Visible) return;
        if (!_baseCard.IsUpgradable && showUpgradePreview) throw new InvalidExpressionException($"{_baseCard.Id} is not upgradable.");

        if (_isPreviewingUpgrade != showUpgradePreview)
        {
            if (showUpgradePreview && _upgradedCard != null)
            {
                CardNode!.Model = _upgradedCard;
                CardNode.ShowUpgradePreview();
            }
            else
            {
                CardNode!.Model = _baseCard;
                CardNode.UpdateVisuals(CardNode.DisplayingCardPile);
            }

            _isPreviewingUpgrade = showUpgradePreview;
        }
    }
}
