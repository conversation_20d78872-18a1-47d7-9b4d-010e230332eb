using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.CardLibrary;

namespace MegaCrit.Sts2.Core.Nodes.Cards;

public partial class NCardGrid : Control
{
    protected Dictionary<SortingOrders, Func<CardModel, CardModel, int>> SortingAlgorithms => new()
    {
        { SortingOrders.RarityAscending, (a, b) => a.Rarity.CompareTo(b.Rarity) },
        { SortingOrders.CostAscending, (a, b) => a.CanonicalEnergyCost.CompareTo(b.CanonicalEnergyCost) },
        { SortingOrders.TypeAscending, (a, b) => a.Type.CompareTo(b.Type) },
        { SortingOrders.AlphabetAscending, (a, b) => a.TitleLocString.CompareTo(b.TitleLocString) },
        { SortingOrders.RarityDescending, (a, b) => -a.Rarity.CompareTo(b.Rarity) },
        { SortingOrders.CostDescending, (a, b) => -a.CanonicalEnergyCost.CompareTo(b.CanonicalEnergyCost) },
        { SortingOrders.TypeDescending, (a, b) => -a.Type.CompareTo(b.Type) },
        { SortingOrders.AlphabetDescending, (a, b) => -a.TitleLocString.CompareTo(b.TitleLocString) },
        { SortingOrders.Ascending, (a, b) => _cards.IndexOf(a).CompareTo(_cards.IndexOf(b)) },
        { SortingOrders.Descending, (a, b) => -_cards.IndexOf(a).CompareTo(_cards.IndexOf(b)) },
    };

    private const float _scrollAmount = 40f; // NOTE: How much we travel per scrollWheel tick. Probably better to get the expected scroll distance from the OS
    private const float _panScrollSpeed = 50f; // NOTE: How much we travel per touchpad gesture tick.
    private const float _dragLerpSpeed = 15f;
    private const float _snapThreshold = 0.5f;
    private const float _bounceBackStrength = 12f;

    [Signal]
    public delegate void HolderPressedEventHandler(NCardHolder card);

    // alt press would be something like right-clicking
    [Signal]
    public delegate void HolderAltPressedEventHandler(NCardHolder card);

    private float _startDrag;
    private float _targetDrag;
    private bool _isDragging;
    private bool _scrollingEnabled = true;
    private bool CanScroll => _scrollingEnabled && Visible;
    private int DisplayedRows { get; set; }

    private int Columns => (int)((_scrollContainer.Size.X + CardPadding) / (_cardSize.X + CardPadding));
    private float CardPadding => 40f;
    private float TopBottomMargin => 80f;
    protected virtual bool Animate => false;

    private Control _scrollContainer = default!;

    private float ScrollLimitBottom => Size.Y > _scrollContainer.Size.Y ? (Size.Y - _scrollContainer.Size.Y) / 2 : Size.Y -_scrollContainer.Size.Y;
    private float ScrollLimitTop => Size.Y > _scrollContainer.Size.Y && CenterGrid ? (Size.Y - _scrollContainer.Size.Y) / 2 : 0;

    private bool _scrollbarPressed;
    private NScrollbar _scrollbar = default!;

    private int _slidingWindowCardIndex;
    private CardPileTarget _pileType;
    protected Vector2 _cardSize;
    protected readonly List<CardModel> _cards = [];
    protected readonly List<List<NGridCardHolder>> _cardRows = [];

    private readonly List<CardModel> _highlightedCards = [];

    private bool _isAnimatingOut;
    private CancellationTokenSource? _animateInCancellation;

    private bool _isShowingUpgrades;

    private NCardHolder? _lastFocusedHolder;

    /// <summary>
    /// Returns the currently displayed cards.
    /// Since the grid only displays chunks of cards at a time, remember that this may not hold all the cards in the grid.
    /// </summary>
    public IEnumerable<NGridCardHolder> CurrentlyDisplayedCardHolders => _cardRows.SelectMany(r => r);

    public IEnumerable<CardModel> CurrentlyDisplayedCards => CurrentlyDisplayedCardHolders.Select(h => h.CardModel);
    public bool IsShowingUpgrades
    {
        get => _isShowingUpgrades;
        set
        {
            _isShowingUpgrades = value;

            foreach (List<NGridCardHolder> row in _cardRows)
            {
                foreach (NGridCardHolder holder in row)
                {
                    // check if the original instance can even be upgraded before trying to resetting it.
                    if (!_isShowingUpgrades && !holder.CardModel.CanonicalInstance.IsUpgradable) continue;

                    // check to see if the base card is already upgraded before trying to upgrade it again.
                    if (_isShowingUpgrades && !holder.CardModel.IsUpgradable) continue;

                    holder.SetIsPreviewingUpgrade(_isShowingUpgrades);
                }
            }
        }
    }

    // Used to make some room for the card sorting UI in the deck view screen
    public int YOffset { get; set; }

    // If the card grid is vertically smaller than the full screen and this is true, we center
    // the cards
    protected virtual bool CenterGrid => true;

    public override void _Ready()
    {
        if (GetType() != typeof(NCardGrid))
        {
            Log.Error($"{GetType()}");
            throw new InvalidOperationException("Don't call base._Ready()! Call ConnectSignals() instead.");
        }

        ConnectSignals();
    }

    protected virtual void ConnectSignals()
    {
        _scrollContainer = GetNode<Control>("%ScrollContainer");
        _scrollbar = GetNode<NScrollbar>("Scrollbar");


        NGridCardHolder holder = NGridCardHolder.Create(NCard.Create(ModelDb.Card<StrikeIronclad>())!)!;
        this.AddChildSafely(holder);
        _cardSize = holder.Hitbox.Size * holder.SmallScale;
        holder.QueueFreeSafely();

        _scrollContainer.Connect(CanvasItem.SignalName.ItemRectChanged, Callable.From(UpdateScrollLimitBottom));

        _scrollbar.Visible = false;
        _scrollbar.Connect(NScrollbar.SignalName.MousePressed, Callable.From<InputEvent>(_ => { _scrollbarPressed = true; }));
        _scrollbar.Connect(NScrollbar.SignalName.MouseReleased, Callable.From<InputEvent>(_ => { _scrollbarPressed = false; }));
    }

    public override void _EnterTree()
    {
        base._EnterTree();
        GetViewport().Connect(Viewport.SignalName.GuiFocusChanged, Callable.From<Control>(ProcessGuiFocus));
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        GetViewport().Disconnect(Viewport.SignalName.GuiFocusChanged, Callable.From<Control>(ProcessGuiFocus));
    }


    private void UpdateScrollLimitBottom()
    {
        float scrollBarThreshold = Size.Y;
        _scrollbar.Visible = _scrollContainer.Size.Y > scrollBarThreshold && CanScroll;
        _scrollbar.MouseFilter = _scrollContainer.Size.Y > scrollBarThreshold && CanScroll ? MouseFilterEnum.Stop : MouseFilterEnum.Ignore;
    }

    public override void _GuiInput(InputEvent inputEvent)
    {
        if (!IsVisibleInTree()) return;

        ProcessMouseEvent(inputEvent);
        ProcessScrollEvent(inputEvent);
    }

    public override void _Process(double delta)
    {
        if (!IsVisibleInTree()) return;
        if (!CanScroll) return;

        UpdateScrollPosition(delta);
    }

    public override void _Notification(int what)
    {
        base._Notification(what);

        if (what == NotificationResized)
        {
            // We defer this so that the positions/sizes can update
            // before we reset the columns
            Callable.From(ReflowColumns).CallDeferred();
        }
    }

    public void SetScrollPosition(float scrollY)
    {
        _targetDrag = scrollY;
        _scrollContainer.Position = new Vector2(_scrollContainer.Position.X, scrollY);
    }

    public void SetCanScroll(bool canScroll)
    {
        _scrollingEnabled = canScroll;

        if (!CanScroll)
        {
            _isDragging = false;
        }
    }

    // Call this if the grid is displayed during gameplay, to avoid cards clipping into the top bar.
    // Not needed if the anchors are already set inside the scene.
    public void InsetForTopBar()
    {
        SetAnchorAndOffset(Side.Top, 0f, 80f);
    }

    /// <summary>
    /// Detects mouse click up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessMouseEvent(InputEvent inputEvent)
    {
        if (_isDragging && inputEvent is InputEventMouseMotion motion)
        {
            _targetDrag += motion.Relative.Y;
        }
        else if (inputEvent is InputEventMouseButton button)
        {
            if (button.ButtonIndex == MouseButton.Left)
            {
                if (button.Pressed)
                {
                    _isDragging = true;
                    _startDrag = _scrollContainer.Position.Y;
                    _targetDrag = _startDrag;
                }
                else
                {
                    _isDragging = false;
                }
            }
            else if (!button.Pressed)
            {
                _isDragging = false;
            }
        }
    }

    /// <summary>
    /// Detects mouse wheel up/down and updates our scroll target accordingly
    /// </summary>
    /// <param name="inputEvent"></param>
    private void ProcessScrollEvent(InputEvent inputEvent)
    {
        if (inputEvent is InputEventMouseButton mouseButton)
        {
            if (mouseButton.ButtonIndex == MouseButton.WheelUp)
            {
                _targetDrag += _scrollAmount;
            }
            else if (mouseButton.ButtonIndex == MouseButton.WheelDown)
            {
                _targetDrag -= _scrollAmount;
            }
        }
        // For OSX touchpad support (namely, macbooks)
        else if (inputEvent is InputEventPanGesture panGesture)
        {
            float panScrollAmount = panGesture.Delta.Y * _panScrollSpeed;
            _targetDrag -= panScrollAmount;
        }
    }

    private void ProcessGuiFocus(Control focusedControl)
    {
        if (!IsVisibleInTree()) return;
        if (!CanScroll) return;
        if (!NControllerManager.Instance!.IsUsingController) return;

        if (focusedControl.GetParent() == _scrollContainer)
        {
            float dragPos = -focusedControl.Position.Y + Size.Y * 0.5f;
            dragPos = Mathf.Clamp(dragPos, Mathf.Min(ScrollLimitTop, ScrollLimitBottom), 0);
            _targetDrag = dragPos;
        }
    }

    private void UpdateScrollPosition(double delta)
    {
        float yPos = _scrollContainer.Position.Y;

        if (Math.Abs(yPos - _targetDrag) > 0.1f)
        {
            yPos = Mathf.Lerp(yPos, _targetDrag, Mathf.Clamp((float)delta * _dragLerpSpeed, 0, 1));

            // Snap into the target position
            if (Mathf.Abs(yPos - _targetDrag) < _snapThreshold)
            {
                yPos = _targetDrag;
            }

            AllocateCardHolders();

            if (!_scrollbarPressed && CanScroll)
            {
                _scrollbar.SetValueWithoutAnimation(Mathf.Clamp(_scrollContainer.Position.Y / ScrollLimitBottom, 0, 1) * 100);
            }
        }

        if (_scrollbarPressed)
        {
            _targetDrag = Mathf.Lerp(0, ScrollLimitBottom, (float)_scrollbar.Value / 100);
            AllocateCardHolders();
        }

        if (!_isDragging)
        {
            // We're too far up, scroll down!
            if (_targetDrag < Mathf.Min(ScrollLimitBottom, ScrollLimitTop))
            {
                _targetDrag = Mathf.Lerp(_targetDrag, Mathf.Min(ScrollLimitBottom, ScrollLimitTop), (float)delta * _bounceBackStrength);
            }
            // We're too far down, scroll up!
            else if (_targetDrag > Mathf.Max(ScrollLimitTop, ScrollLimitBottom))
            {
                _targetDrag = Mathf.Lerp(_targetDrag, Mathf.Max(ScrollLimitTop, ScrollLimitBottom), (float)delta * _bounceBackStrength);
            }
        }

        _scrollContainer.Position = new Vector2(_scrollContainer.Position.X, yPos);
    }

    public void SetCards(IReadOnlyList<CardModel> cards, CardPileTarget pileType, List<SortingOrders> sortingPriority)
    {
        _cards.Clear();
        _cards.AddRange(cards);

        // Cards passed in are assumed to already be in ascending order.
        if (sortingPriority[0] == SortingOrders.Descending)
        {
            _cards.Reverse();
        }
        else if (sortingPriority[0] != SortingOrders.Ascending)
        {
            _cards.Sort((x, y) =>
            {
                int result = ModelDb.CardPools.ToList().IndexOf(x.Pool).CompareTo(ModelDb.CardPools.ToList().IndexOf(y.Pool));
                if (result != 0)
                {
                    return result;
                }

                foreach (SortingOrders sort in sortingPriority)
                {
                    result = SortingAlgorithms[sort].Invoke(x, y);
                    if (result != 0)
                    {
                        return result;
                    }
                }

                return x.Id.CompareTo(y.Id);
            });
        }

        _pileType = pileType;

        if (_isAnimatingOut)
        {
            // If we're animating out, then we don't need to do anything - once animated out, we'll go to the new cards
            return;
        }

        if (Animate)
        {
            TaskHelper.RunSafely(InitGridAnimated());
        }
        else
        {
            InitGrid();
        }
    }

    private async Task InitGridAnimated()
    {
        // If we're animating in, kill the tween before animating out again
        if (_animateInCancellation != null)
        {
            await _animateInCancellation.CancelAsync();
        }

        Tween tween;

        List<NGridCardHolder> oldHolders = _cardRows.SelectMany(c => c).ToList();
        if (oldHolders.Count > 0)
        {
            tween = CreateTween().SetParallel();

            foreach (NGridCardHolder holder in oldHolders)
            {
                tween.TweenProperty(holder, "position:y", holder.Position.Y + 40, 0.2).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo);
                tween.TweenProperty(holder, "modulate:a", 0, 0.2);
            }

            _isAnimatingOut = true;
            await ToSignal(tween, Tween.SignalName.Finished);
            _isAnimatingOut = false;
        }

        InitGrid();

        tween = CreateTween().SetParallel();

        List<NGridCardHolder> newHolders = _cardRows.SelectMany(c => c).ToList();
        if (newHolders.Count > 0)
        {
            const float totalDelay = 0.2f;

            for (int i = 0; i < newHolders.Count; i++)
            {
                NGridCardHolder holder = newHolders[i];
                float delay = i / (float)newHolders.Count * totalDelay;
                float targetY = holder.Position.Y;
                holder.Position = holder.Position with { Y = holder.Position.Y + 40 };
                holder.Modulate = holder.Modulate with { A = 0f };
                tween.TweenProperty(holder, "position:y", targetY, 0.4).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Back).SetDelay(delay);
                tween.TweenProperty(holder, "modulate:a", 1, 0.4).SetEase(Tween.EaseType.Out).SetTrans(Tween.TransitionType.Expo).SetDelay(delay);
            }

            _animateInCancellation = new CancellationTokenSource();

            while (tween.IsRunning())
            {
                if (_animateInCancellation.IsCancellationRequested)
                {
                    tween.Kill();
                }

                await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame);
            }
        }
    }

    private int CalculateRowsNeeded()
    {
        // This adds 2 rows for padding on either side. The viewport can show exactly ceil(size/spacing) cards,
        // but we need to add a couple of rows so that when we despawn/spawn in new rows, they're hidden
        int desiredRows = Mathf.CeilToInt((Size.Y + CardPadding) / (_cardSize.Y + CardPadding)) + 2;
        return Mathf.Min(desiredRows, GetTotalRowCount());
    }

    protected virtual void InitGrid()
    {
        _scrollContainer.Position = new Vector2(_scrollContainer.Position.X, ScrollLimitTop);
        _slidingWindowCardIndex = 0; // reset the sliding window index
        _scrollbar.Value = 0;
        DisplayedRows = CalculateRowsNeeded();
        List<NGridCardHolder>? holderPool = null;

        if (_cardRows.Count > 0)
        {
            holderPool = _cardRows.SelectMany(c => c).ToList();
        }

        _cardRows.Clear();

        if (_cards.Count != 0)
        {
            int index = 0;
            for (int r = 0; r < DisplayedRows; r++)
            {
                List<NGridCardHolder> row = [];

                for (int col = 0; col < Columns; col++)
                {
                    if (index >= _cards.Count) break; // We are outta cards
                    CardModel card = _cards[index];
                    NGridCardHolder holder;

                    if (holderPool?.Count > 0)
                    {
                        holder = holderPool[^1];
                        holderPool.RemoveAt(holderPool.Count - 1);
                        holder.ReassignToCard(card, _pileType, null, false);
                        holder.Visible = true;
                        row.Add(holder);
                    }
                    else
                    {
                        NCard cardNode = CreateCardNode(card);
                        holder = NGridCardHolder.Create(cardNode)!;
                        row.Add(holder);

                        holder.Connect(NCardHolder.SignalName.Pressed, Callable.From<NCardHolder>(OnHolderPressed));
                        holder.Connect(NCardHolder.SignalName.AltPressed, Callable.From<NCardHolder>(OnHolderAltPressed));

                        holder.Visible = true;
                        holder.MouseFilter = MouseFilterEnum.Pass;
                        holder.Scale = holder.SmallScale;

                        _scrollContainer.AddChildSafely(holder);
                        cardNode.UpdateVisuals(_pileType);
                    }

                    if (holder.CardModel.CanonicalInstance.IsUpgradable)
                    {
                        holder.SetIsPreviewingUpgrade(IsShowingUpgrades);
                    }

                    index++;
                }

                _cardRows.Add(row);
            }
        }

        if (holderPool != null)
        {
            foreach (NGridCardHolder holder in holderPool)
            {
                holder.QueueFreeSafely();
            }
        }

        _scrollContainer.Size = new Vector2(_scrollContainer.Size.X, GetContainedCardsSize().Y + TopBottomMargin * 2f + YOffset);
        UpdateGridPositions(0);
        UpdateGridNavigation();
    }

    protected virtual NCard CreateCardNode(CardModel card) => NCard.Create(card)!;

    private Vector2 GetContainedCardsSize()
    {
        int totalRows = GetTotalRowCount();
        return new Vector2(Columns, totalRows) * _cardSize + new Vector2(Columns - 1, totalRows - 1) * CardPadding;
    }

    private void ReflowColumns()
    {
        if (_cards.Count == 0) return;
        InitGrid();
    }

    private void UpdateGridPositions(int index)
    {
        Vector2 offset = new Vector2((_scrollContainer.Size.X - GetContainedCardsSize().X) / 2f, YOffset + TopBottomMargin) + _cardSize / 2f;
        foreach (List<NGridCardHolder> row in _cardRows)
        {
            foreach (NGridCardHolder holder in row)
            {
                int rowIndex = index / Columns;
                int columnIndex = index % Columns;
                holder.Position = offset + new Vector2(columnIndex * (_cardSize.X + CardPadding), rowIndex * (_cardSize.Y + CardPadding));
                index++;
            }
        }
    }

    public NGridCardHolder? GetCardHolder(CardModel model)
    {
        NGridCardHolder? holder = _cardRows.SelectMany(row => row).FirstOrDefault(h => h.CardModel == model);
        return holder;
    }

    public NCard? GetCardNode(CardModel model)
    {
        return GetCardHolder(model)?.CardNode;
    }

    private void OnHolderPressed(NCardHolder holder)
    {
        _lastFocusedHolder = holder;
        EmitSignal(SignalName.HolderPressed, holder);
    }

    private void OnHolderAltPressed(NCardHolder holder)
    {
        _lastFocusedHolder = holder;
        EmitSignal(SignalName.HolderAltPressed, holder);
    }

    private int GetTotalRowCount() => Mathf.CeilToInt((float)_cards.Count / Columns);

    // Method to allocate cardholders based on the scroll position
    private void AllocateCardHolders()
    {
        if (_cardRows.Count == 0) return; // if the grid is uninitialized, don't do anything
        Rect2 viewportRect = GetViewportRect();
        float viewportHeight = viewportRect.Size.Y;

        const float topThreshold = 0f;
        float bottomThreshold = viewportHeight;

        List<NGridCardHolder> topRow = _cardRows[0];
        float topCardPosition = topRow[0].GlobalPosition.Y;

        List<NGridCardHolder> bottomRow = _cardRows[^1]; // ^1 is the last element
        float bottomCardPosition = bottomRow[0].GlobalPosition.Y;

        // if the current set of cards is way off from the screen, completely refresh the
        // card grid to the new position
        if (Mathf.Abs(topCardPosition - topThreshold) > Size.Y * 2)
        {
            ReallocateAll();
        }
        // The approximate center of the current top row has passed the top of the viewport
        else if (topCardPosition > topThreshold)
        {
            ReallocateAbove(_cardRows[^1]);
        }
        // The approximate center of the bottom row has passed the bottom of the viewport
        else if (bottomCardPosition < bottomThreshold)
        {
            ReallocateBelow(_cardRows[0]);
        }
    }

    private void ReallocateAll()
    {
        // calculate how many rows I am at based on distance from current rows
        List<NGridCardHolder> topRow = _cardRows[0];
        float topRowPosition = topRow[0].GlobalPosition.Y;
        const float topThreshold = 0;

        float distance = topRowPosition - topThreshold;
        int rowDiff = Mathf.RoundToInt(distance / (_cardSize.Y + CardPadding));
        int newCardIndex = Mathf.Max(0, _slidingWindowCardIndex - Columns * rowDiff);
        _slidingWindowCardIndex = newCardIndex;

        int rowCount = _cardRows.Count;
        for (int i = 0; i < rowCount; i++)
        {
            AssignCardsToRow(_cardRows[i], _slidingWindowCardIndex + i * Columns);
        }

        UpdateGridPositions(_slidingWindowCardIndex);
        UpdateGridNavigation();
    }

    private void ReallocateAbove(List<NGridCardHolder> row)
    {
        int newCardIndex = _slidingWindowCardIndex - Columns;
        if (newCardIndex < 0) return; // if we're at the top of the card list, don't reallocate

        // Update the sliding window index since the top row is removed
        _slidingWindowCardIndex = newCardIndex;

        // Remove the bottom row (the last)
        _cardRows.RemoveAt(_cardRows.Count - 1);

        // assign new cards
        AssignCardsToRow(row, _slidingWindowCardIndex);

        // Add the row to the top (the first)
        _cardRows.Insert(0, row);

        List<NGridCardHolder> topSecondRow = _cardRows[1];
        float topSecondRowPosition = topSecondRow[0].Position.Y;

        // Update the Y position of the new row
        foreach (NGridCardHolder holder in row)
        {
            holder.Position = holder.Position with { Y = topSecondRowPosition - _cardSize.Y - CardPadding};
        }

        UpdateGridNavigation();
    }

    private void ReallocateBelow(List<NGridCardHolder> row)
    {
        // since we're reallocating below, we need to get the index for the cards in the bottom row
        int gridSize = Columns * DisplayedRows;
        int bottomRowCardIndex = _slidingWindowCardIndex + gridSize;
        if (bottomRowCardIndex >= _cards.Count) return; // If we're at the bottom of the card list, don't reallocate
        // Update the sliding window index since the top row is removed
        _slidingWindowCardIndex += Columns;

        // Remove the top row (the first)
        _cardRows.RemoveAt(0);

        // assign new cards to the row
        AssignCardsToRow(row, bottomRowCardIndex);

        // Add the row to the bottom (the last)
        _cardRows.Add(row);

        List<NGridCardHolder> bottomSecondRow = _cardRows[^2];
        float bottomSecondRowPosition = bottomSecondRow[0].Position.Y;

        // Update the Y position of the new row
        foreach (NGridCardHolder holder in row)
        {
            holder.Position = holder.Position with { Y = bottomSecondRowPosition + _cardSize.Y + CardPadding};
        }

        UpdateGridNavigation();
    }

    public void HighlightCard(CardModel card)
    {
        _highlightedCards.Add(card);

        // Node may be offscreen
        NCard? node = GetCardNode(card);
        node?.CardHighlight.AnimShow();
    }

    public void UnhighlightCard(CardModel card)
    {
        _highlightedCards.Remove(card);

        // Node may be offscreen
        NCard? node = GetCardNode(card);
        node?.CardHighlight.AnimHide();
    }

    protected virtual void AssignCardsToRow(List<NGridCardHolder> row, int startIndex)
    {
        // assign new cards to the row

        for (int i = 0; i < row.Count; i++)
        {
            NGridCardHolder holder = row[i];
            // We don't have enough cards to populate the grid, so hide the remaining
            if (startIndex + i >= _cards.Count)
            {
                holder.Visible = false;
                continue;
            }

            CardModel card = _cards[startIndex + i];

            holder.ReassignToCard(card, CardPileTarget.None, null, false);
            holder.Visible = true;

            if (_highlightedCards.Contains(card))
            {
                holder.CardNode!.CardHighlight.AnimShow();
            }
            else
            {
                holder.CardNode!.CardHighlight.AnimHide();
            }

            if (_isShowingUpgrades)
            {
                holder.CardNode!.ShowUpgradePreview();
            }
        }
    }

    public void OnFocus()
    {
        if (_lastFocusedHolder != null)
        {
            _lastFocusedHolder.TryGrabFocus();
        }
        else
        {
            _cardRows[0][0].TryGrabFocus();
            //_cardRows[_cardRows.Count/2][_cardRows[_cardRows.Count/2].Count/2].TryGrabFocus();
        }

        _lastFocusedHolder = null;
    }

    private void UpdateGridNavigation()
    {
        for (int i = 0; i < _cardRows.Count; i++)
        {
            for (int j = 0; j < _cardRows[i].Count; j++)
            {
                NCardHolder holder = _cardRows[i][j];
                holder.FocusNeighborLeft = j > 0 ? _cardRows[i][j - 1].GetPath() : _cardRows[i][_cardRows[i].Count - 1].GetPath();
                holder.FocusNeighborRight = j < _cardRows[i].Count - 1 ? _cardRows[i][j + 1].GetPath() : _cardRows[i][0].GetPath();
                holder.FocusNeighborTop = i > 0 ? _cardRows[i - 1][j].GetPath() : _cardRows[i][j].GetPath();
                holder.FocusNeighborBottom = i < _cardRows.Count - 1 && j < _cardRows[i + 1].Count ? _cardRows[i + 1][j].GetPath() : _cardRows[i][j].GetPath();
            }
        }
    }
}
