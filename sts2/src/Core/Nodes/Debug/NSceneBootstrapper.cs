using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Debug;

/// <summary>
/// Helper class to quickly load screens of the game without having to go through the main menu.
/// Can specify character, room type, and room details in the parameters you want to load into.
/// And then start the scene scene_bootstrapper.tscn
/// </summary>
public partial class NSceneBootstrapper : Node
{
    private bool _openConsole;
    private NGame _game = default!;

    public override void _Ready()
    {
        if (GetParent() is NGame parentGame)
        {
            _game = parentGame;
        }
        else
        {
            _game = SceneHelper.Instantiate<NGame>("game");
            _game.StartOnMainMenu = false;
            this.AddChildSafely(_game);
        }

        TaskHelper.RunSafely(StartNewClimb());
    }

    private async Task StartNewClimb()
    {
        Assembly assembly = Assembly.GetAssembly(typeof(NSceneBootstrapper))!;
        Type? settingsType = assembly.GetTypes().FirstOrDefault(t => t.GetInterfaces().Contains(typeof(IBootstrapSettings)));

        if (settingsType == null)
        {
            Log.Error($"No type implementing IBootstrapSettings found in the project! To use the bootstrap scene, you should " +
                $"copy src/Core/Nodes/Debug/BootstrapSettings.cs.template and rename it to BootstrapSettings.cs");

            return;
        }

        IBootstrapSettings settings = (IBootstrapSettings)Activator.CreateInstance(settingsType)!;
        PreloadManager.Enabled = settings.DoPreloading;

        string seed = settings.Seed ?? SeedHelper.GetRandomSeed();

        List<ActModel> acts = ActModel.GetDefaultList().ToList();
        acts[0] = settings.Act;

        ClimbState climbState = ClimbState.CreateForNewClimb(
            [Player.CreateForNewClimb(settings.Character, NetSingleplayerGameService.defaultNetId)],
            acts.Select(a => a.ToMutable()).ToList(),
            settings.Modifiers,
            settings.Ascension,
            seed
        );
        ClimbManager.Instance.SetUpNewSinglePlayer(climbState, settings.SaveClimbHistory);

        await PreloadManager.LoadClimbAssets([settings.Character]);

        ClimbManager.Instance.Launch();
        _game.RootSceneContainer.SetCurrentScene(NClimb.Create(climbState));
        await ClimbManager.Instance.SetActDirectly(0);

        ClimbManager.Instance.ClimbLocationTargetedBuffer.OnClimbLocationChanged(climbState.CurrentLocation);
        ClimbManager.Instance.MapSelectionSynchronizer.OnClimbLocationChanged(climbState.CurrentLocation);

        if (settings.RoomType != RoomType.Unassigned)
        {
            climbState.AppendToMapPointHistory(settings.MapPointType, settings.RoomType);
        }

        await settings.Setup(climbState.Players[0]);

        switch (settings.RoomType)
        {
            case RoomType.Unassigned:
                await ClimbManager.Instance.EnterAct(0);
                break;
            case RoomType.RestSite:
            case RoomType.Treasure:
            case RoomType.Shop:
                await ClimbManager.Instance.EnterRoomDebug(settings.RoomType, showTransition: false);
                ClimbManager.Instance.ActionExecutor.Unpause();
                break;
            case RoomType.Event:
                await ClimbManager.Instance.EnterRoomDebug(settings.RoomType, model: settings.Event, showTransition: false);
                break;
            case RoomType.Victory:
                await ClimbManager.Instance.EnterRoomDebug(settings.RoomType);
                break;
            default:
                await ClimbManager.Instance.EnterRoomDebug(
                    settings.RoomType,
                    model: settings.RoomType.IsCombatRoom() ? settings.Encounter.ToMutable() : null,
                    showTransition: false
                );

                break;
        }

        if (_openConsole) // for faster console debugging
        {
            NDevConsole devConsoleNode = GetNode<NDevConsole>("/root/DevConsole/ConsoleScreen");
            devConsoleNode.ShowConsole();
            devConsoleNode.MakeFullScreen();
            devConsoleNode.SetBackgroundColor(Colors.White);
        }
    }
}

public interface IBootstrapSettings
{
    public CharacterModel Character { get; }
    public RoomType RoomType { get; }
    public EncounterModel Encounter { get; }
    public EventModel Event { get; }

    // ActIndex was changed to this on 2/25/2025. Set it to the ActModel you want to bootstrap into.
    // If you continue your "climb" after the bootstrapped room, this act will be act 1 of your climb.
    public ActModel Act { get; }

    // This was added 10/15/2024. It should be 0 by default in BootstrapSettings.cs
    public int Ascension { get; }

    // This was added 6/12/2024. It should be false by default in BootstrapSettings.cs
    public bool SaveClimbHistory { get; }

    // This was added 6/20/2024. It should be null by default in BootstrapSettings.cs
    // It was changed from uint to string on 5/29/2025, so if you get an error, change your BootstrapSettings.Seed from
    // uint to string.
    public string? Seed { get; }

    // This was added 10/14/2024. Set to false to disable the preloader when using the bootstrapper
    public bool DoPreloading { get; }

    // This was added 10/14/2024. Set to true to use bootstrap settings when in multiplayer
    public bool BootstrapInMultiplayer { get; }

    // This was added 5/15/2025. It should be an empty list by default
    public List<ModifierModel> Modifiers { get; }

    // This was added 6/18/2024. It should be empty by default in BootstrapSettings.cs
    // The localPlayer param was added 6/4/2025.
    Task Setup(Player localPlayer);

    public MapPointType MapPointType
    {
        get
        {
            return RoomType switch
            {
                RoomType.Monster => MapPointType.Monster,
                RoomType.Elite => MapPointType.Elite,
                RoomType.Boss => MapPointType.Boss,
                RoomType.Treasure => MapPointType.Treasure,
                RoomType.Shop => MapPointType.Shop,
                RoomType.Event => MapPointType.Unknown,
                RoomType.RestSite => MapPointType.RestSite,
                RoomType.Map => MapPointType.Unknown,
                RoomType.Victory => MapPointType.Boss,
                RoomType.Unassigned => throw new ArgumentOutOfRangeException()
            };
        }
    }
}
