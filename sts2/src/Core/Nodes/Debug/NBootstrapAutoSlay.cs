using Godot;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.Debug;

public partial class NBootstrapAutoSlay : Node
{
    public override void _Ready()
    {
        // Launch the game
        NGame game = SceneHelper.Instantiate<NGame>("game");
        SceneTree tree = GetTree();
        tree.Root.CallDeferred("add_child", game);

        // Parse the seed from the command line
        string seed = ParseSeedFromCommandLine();

        // Start the autoslay
        StartAutoSlay(seed);
    }

    private static string ParseSeedFromCommandLine()
    {
        string[] args = OS.GetCmdlineArgs();
        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] == "--seed" && i < args.Length - 1)
            {
                return args[i + 1];
            }
            if (args[i].StartsWith("--seed=") && i < args.Length)
            {
                return args[i].Split('=')[1];
            }
        }

        return "";
    }

    private void StartAutoSlay(string seed)
    {
        NAutoSlayer autoSlayer = NAutoSlayer.Create();
        this.AddChildSafely(autoSlayer);
        // Defer it so it runs in the main thread
        Callable.From(() => autoSlayer.Start(seed, isBootstrapped: true)).CallDeferred();
    }
}
