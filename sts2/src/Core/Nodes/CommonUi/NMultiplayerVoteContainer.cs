using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

public partial class NMultiplayerVoteContainer : Control
{
    public delegate bool PlayerVotedDelegate(Player player);

    private const string _voteIconPath = "ui/multiplayer_vote_icon";
    public static IEnumerable<string> AssetPaths => [SceneHelper.GetScenePath(_voteIconPath)];

    private readonly List<EndTurnIcon> _votes = [];
    private PlayerVotedDelegate _playerVotedDelegate = default!;
    private readonly List<Player> _players = [];

    private struct EndTurnIcon
    {
        public Player player;
        public TextureRect node;
    }

    public void Initialize(PlayerVotedDelegate del, IReadOnlyList<Player> players)
    {
        _playerVotedDelegate = del;
        _players.AddRange(players);
    }

    public void RefreshPlayerVotes()
    {
        // In singleplayer, display and do absolutely nothing
        if (_players.Count == 1) return;

        // Check if votes are still present
        for (int i = 0; i < _votes.Count; i++)
        {
            EndTurnIcon vote = _votes[i];

            if (_playerVotedDelegate(vote.player)) continue;

            // Remove the vote indicator
            vote.node.QueueFreeSafely();
            _votes.RemoveAt(i);
            i--;
        }

        // Add votes that are not present
        foreach (Player player in _players)
        {
            if (!_playerVotedDelegate(player)) continue;

            int index = _votes.FindIndex(p => p.player == player);

            // Ignore votes that are already accounted for
            if (index >= 0) continue;

            // Make new indicator for votes that are not accounted for
            EndTurnIcon vote = new()
            {
                player = player,
                node = SceneHelper.Instantiate<TextureRect>(_voteIconPath)
            };

            vote.node.Texture = player.Character.IconTexture;
            _votes.Add(vote);
            this.AddChildSafely(vote.node);
        }
    }
}
