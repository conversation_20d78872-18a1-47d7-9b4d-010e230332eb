using Godot;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

/// <summary>
/// Arrow buttons are used for toggling left and right on several screens. The Man. The Myth. The Legend.
/// </summary>
public partial class NGoldArrowButton : NButton
{
    private Control _icon = default!;
    private ShaderMaterial _hsv = default!;
    private Tween? _animTween;

    // Shader stuff
    private float _valueDefault = 0.9f;
    private float _valueHovered = 1.2f;

    private Vector2 _hoverScale = Vector2.One * 1.1f;

    public override void _Ready()
    {
        ConnectSignals();
        _icon = GetNode<Control>("TextureRect");
        _hsv = (ShaderMaterial)_icon.Material;
        _hsv.SetShaderParameter("v", _valueDefault);

        Connect(Control.SignalName.MouseEntered, Callable.From(OnHovered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnUnhovered));
    }

    private void OnHovered()
    {
        _animTween?.Kill();
        _hsv.SetShaderParameter("v", _valueHovered);
        _icon.Scale = _hoverScale;
    }

    private void OnUnhovered()
    {
        _animTween?.Kill();
        _animTween = CreateTween().SetParallel();
        _animTween.TweenMethod(Callable.From<float>(UpdateShaderParam), _valueHovered, _valueDefault, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _animTween.TweenProperty(_icon, "scale", Vector2.One, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        base.OnPressDown();
        _animTween?.Kill();
        _animTween = CreateTween();
        _hsv.SetShaderParameter("v", 0.7f);
        _animTween.TweenProperty(_icon, "scale", Vector2.One, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnRelease()
    {
        OnHovered();
    }

    private void UpdateShaderParam(float newV)
    {
        _hsv.SetShaderParameter("v", newV);
    }
}
