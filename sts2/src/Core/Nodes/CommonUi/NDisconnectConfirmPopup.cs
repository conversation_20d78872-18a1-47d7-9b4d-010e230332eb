using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Screens.MainMenu;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

/// <summary>
/// The disconnection confirmation popup to prevent the player from accidentally leaving a climb in progress.
/// Renders above the capstone screens (above top bar).
/// </summary>
public partial class NDisconnectConfirmPopup : Control, IFocusableScreen
{
    private MegaLabel _header = default!;
    private MegaRichTextLabel _description = default!;
    private NButton _noButton = default!;
    private NButton _yesButton = default!;
    private NMainMenu? _mainMenuNode;

    private static readonly string _scenePath = SceneHelper.GetScenePath("ui/disconnect_confirm_popup");

    public static IEnumerable<string> AssetPaths => [_scenePath];

    private NVerticalPopup _verticalPopup = default!;

    public override void _Ready()
    {
        _verticalPopup = GetNode<NVerticalPopup>("VerticalPopup");
        _verticalPopup.SetText(new LocString("settings_ui", "DISCONNECT_CONFIRMATION.header"), new LocString("settings_ui", "DISCONNECT_CONFIRMATION.body"));
        _verticalPopup.InitNoButton(new LocString("main_menu_ui", "GENERIC_POPUP.cancel"), OnNoButtonPressed);
        _verticalPopup.InitYesButton(new LocString("main_menu_ui", "GENERIC_POPUP.confirm"), OnYesButtonPressed);
    }

    public override void _EnterTree()
    {
        base._EnterTree();
        NControllerManager.Instance!.PushScreen(this);
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        NControllerManager.Instance!.RemoveScreen(this);
    }

    public static NDisconnectConfirmPopup? Create()
    {
        if (TestMode.IsOn) return null;

        NDisconnectConfirmPopup node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NDisconnectConfirmPopup>();

        return node;
    }

    private void OnYesButtonPressed(NButton _)
    {
        ClimbManager.Instance.NetService.Disconnect(NetError.Quit);
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }

    private void OnNoButtonPressed(NButton _)
    {
        // Player has changed their mind on disconnecting
        TaskHelper.RunSafely(NModalContainer.Instance!.HideBackstop());
        this.QueueFreeSafely();
    }

    public void OnFocusScreen()
    {
        // no-op
    }

    public void OnUnfocusScreen()
    {
        // no-op
    }
}
