using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Relics;
using MegaCrit.Sts2.Core.Nodes.Screens;
using MegaCrit.Sts2.Core.Nodes.Screens.Capstones;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.CommonUi;

/// <summary>
/// Container for climb-level global UI elements.
/// Also adjusts the Window size to meet aspect ratio requirements
/// </summary>
public partial class NGlobalUi : Control
{
    // Min/max aspect ratios allowed for this game
    private const float _maxNarrowRatio = 1024f / 768f;
    private const float _maxWideRatio = 3440f / 1440f;

    private Window _window = default!;

    public NTopBar TopBar { get; private set; } = default!;
    public NOverlayStack Overlays { get; private set; } = default!;
    public NCapstoneContainer CapstoneContainer { get; private set; } = default!;
    public NRelicInventory RelicInventory { get; private set; } = default!;
    public Control CardPreviewContainer { get; private set; } = default!;
    public NMessyCardPreviewContainer MessyCardPreviewContainer { get; private set; } = default!;
    public NMapScreen MapScreen { get; private set; } = default!;
    public NMultiplayerPlayerStateContainer MultiplayerPlayerContainer { get; private set; } = default!;
    public NCapstoneSubmenuStack SubmenuStack { get; private set; } = default!;
    public NTargetManager TargetManager { get; private set; } = default!;

    public override void _Ready()
    {
        _window = GetTree().Root;
        _window.Connect(Viewport.SignalName.SizeChanged, Callable.From(OnWindowChange));

        CardPreviewContainer = GetNode<Control>("%CardPreviewContainer");
        MessyCardPreviewContainer = GetNode<NMessyCardPreviewContainer>("%MessyCardPreviewContainer");

        TopBar = GetNode<NTopBar>("%TopBar");
        Overlays = GetNode<NOverlayStack>("%OverlayScreensContainer");
        CapstoneContainer = GetNode<NCapstoneContainer>("%CapstoneScreenContainer");
        MapScreen = GetNode<NMapScreen>("%MapScreen");
        SubmenuStack = GetNode<NCapstoneSubmenuStack>("%CapstoneSubmenuStack");
        RelicInventory = GetNode<NRelicInventory>("%RelicInventory");
        MultiplayerPlayerContainer = GetNode<NMultiplayerPlayerStateContainer>("%MultiplayerPlayerContainer");
        TargetManager = GetNode<NTargetManager>("TargetManager");
    }

    private void OnWindowChange()
    {
        if (SaveManager.Instance.SettingsSave.AspectRatioSetting != AspectRatioSetting.Auto) return;

        float ratio = (float)_window.Size.X / _window.Size.Y;

        // Too wide
        if (ratio > _maxWideRatio)
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.KeepWidth;
            _window.ContentScaleSize = new Vector2I(2580, 1080);
        }
        // Too narrow
        else if (ratio < _maxNarrowRatio)
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.KeepHeight;
            _window.ContentScaleSize = new Vector2I(1680, 1260);
        }
        // Default case (window is between 4:3 and 21:9 aspect ratios)
        else
        {
            _window.ContentScaleAspect = Window.ContentScaleAspectEnum.Expand;
            _window.ContentScaleSize = new Vector2I(1680, 1080);
        }
    }

    // Reparent cards into the globalUI
    // We do this often so that they don't disappear if the screen is removed
    public void ReparentCard(NCard card)
    {
        Vector2 pos = card.GlobalPosition;
        card.GetParent()?.RemoveChildSafely(card);
        TopBar.TrailContainer.AddChildSafely(card);
        card.GlobalPosition = pos;
    }

    public void Initialize(ClimbState climbState)
    {
        TopBar.Initialize(climbState);
        MultiplayerPlayerContainer.Initialize(climbState);
        RelicInventory.Initialize(climbState);
        MapScreen.Initialize(climbState);
    }
}
