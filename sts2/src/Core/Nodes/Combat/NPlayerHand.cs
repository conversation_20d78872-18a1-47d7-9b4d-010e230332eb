using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.addons.mega_text;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.Overlays;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NPlayerHand : Control
{
    [Signal]
    public delegate void ModeChangedEventHandler();

    public enum Mode
    {
        None = 0,
        Play = 1,
        SimpleSelect = 2,
        UpgradeSelect = 3
    }

    public static NPlayerHand? Instance => NCombatRoom.Instance?.Ui.Hand;

    public Control CardHolderContainer { get; private set; } = default!;


    private Control _selectModeBackstop = default!;
    private readonly HashSet<CardModel> _selectedCards = [];
    private CardSelectorPrefs _prefs;
    private TaskCompletionSource<IEnumerable<CardModel>>? _selectionCompletionSource;
    private Control _upgradePreviewContainer = default!;
    private NSelectedHandCardContainer _selectedHandCardContainer = default!;
    private NUpgradePreview _upgradePreview = default!;
    private NConfirmButton _selectModeConfirmButton = default!;
    private MegaRichTextLabel _selectionHeader = default!;
    private NPeekButton _peekButton = default!;

    private NCardPlay? _currentCardPlay;
    public bool InCardPlay => _currentCardPlay != null && IsInstanceValid(_currentCardPlay);

    public bool InCardSelection => CurrentMode is Mode.SimpleSelect or Mode.UpgradeSelect;

    private Mode _currentMode = Mode.Play;

    public Mode CurrentMode
    {
        get => _currentMode;
        private set
        {
            _currentMode = value;
            EmitSignal(SignalName.ModeChanged);
        }
    }

    private Func<CardModel, bool>? _currentSelectionFilter;

    /// <summary>
    /// If you're dragging a card, it won't be a child of the card holder container for the duration of the drag,
    /// so we need to leave an empty space for it in your hand. This index represents where that empty space should be.
    ///
    /// For example, let's say you start dragging the card in your hand at index 2.
    /// Once it's removed from your hand for the drag, the card at index 3 moves to index 2, but visually, we
    /// still want to position it at index 3 so we can "leave a space" at index 2.
    /// However, the position/angle of the cards at indexes 0 and 1 should be unchanged.
    /// </summary>
    private int _draggedHolderIndex = -1;

    private bool HasDraggedHolder => _draggedHolderIndex >= 0;

    /// <summary>
    /// There are two types of cards that are considered "awaiting play":
    /// 1. The card you're currently dragging and preparing to play.
    /// 2. If you've queued up a bunch of cards for play quickly, all the cards that haven't started playing yet.
    ///    For example, if I play a Defend, then quickly play 2 Strikes while the Defend's animations are still running,
    ///    the 2 Strikes are considered "awaiting play" for that time.
    ///
    /// Cards that are awaiting play exist in a sort of "purgatory" state. From a back-end perspective, they're still in
    /// your hand (they count towards hand size, etc.), but on the front-end, they should be floating around somewhere
    /// separate from the other cards in your hand.
    ///
    /// The value contained in this dictionary is the old index of the card, for putting things back in your hand when
    /// cancelling.
    /// </summary>
    private readonly Dictionary<NHandCardHolder, int> _holdersAwaitingPlay = [];

    // Anim disable/enable variables
    private Tween? _animEnableTween;
    private bool _isDisabled;
    private const float _enableDisableDuration = 0.2f;
    private static readonly Vector2 _disablePosition = new(0f, 150f);
    private static readonly Color _disableModulate = StsColors.gray;

    // Anim in/out variables
    private Tween? _animInTween;
    private Tween? _animOutTween;
    private Tween? _selectedCardScaleTween;
    private const float _showHideAnimDuration = 0.8f;
    private static readonly Vector2 _showPosition = Vector2.Zero;
    private static readonly Vector2 _hidePosition = new(0f, 500f);

    public Func<CardModel, bool>? SelectModeGoldGlowOverride => _prefs.ShouldGlowGold;

    public NHandCardHolder? FocusedHolder { get; private set; }
    public IReadOnlyList<NHandCardHolder> ActiveHolders => Holders.Where(child => child.Visible).ToList();
    private IReadOnlyList<NHandCardHolder> Holders => CardHolderContainer.GetChildren().OfType<NHandCardHolder>().ToList();

    public override void _Ready()
    {
        _selectModeBackstop = GetNode<Control>("%SelectModeBackstop");
        CardHolderContainer = GetNode<Control>("%CardHolderContainer");
        _upgradePreviewContainer = GetNode<Control>("%UpgradePreviewContainer");
        _selectModeConfirmButton = GetNode<NConfirmButton>("%SelectModeConfirmButton");
        _upgradePreview = GetNode<NUpgradePreview>("%UpgradePreview");

        _selectionHeader = GetNode<MegaRichTextLabel>("%SelectionHeader");
        _selectionHeader.Visible = false;

        _selectedHandCardContainer = GetNode<NSelectedHandCardContainer>("%SelectedHandCardContainer");
        _selectedHandCardContainer.Hand = this;

        _selectModeConfirmButton.Connect(NClickableControl.SignalName.Released, Callable.From<NButton>(OnSelectModeConfirmButtonPressed));
        _selectModeConfirmButton.Disable();

        _selectedHandCardContainer.Connect(Node.SignalName.ChildExitingTree, Callable.From<Node>(OnCardDeselected));
        _selectedHandCardContainer.Connect(Node.SignalName.ChildEnteredTree, Callable.From<Node>(OnCardSelected));

        _peekButton = GetNode<NPeekButton>("%PeekButton");
        _peekButton.Visible = false;
        _peekButton.AddTargets(
            _selectModeBackstop,
            _upgradePreviewContainer,
            _selectModeConfirmButton,
            _selectionHeader,
            _selectedHandCardContainer
        );
        _peekButton.Connect(NPeekButton.SignalName.Toggled, Callable.From<NPeekButton>(OnPeekButtonToggled));

        CardHolderContainer.Connect(Control.SignalName.FocusEntered, Callable.From(() =>
        {
            if (ActiveHolders.Count > 0)
            {
                ActiveHolders[ActiveHolders.Count / 2].TryGrabFocus();
            }

        }));
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        CombatManager.Instance.PlayerActionsDisabledChanged += OnPlayerActionsDisabledChanged;
        CombatManager.Instance.PlayerUnendedTurn += OnPlayerUnendedTurn;
        CombatManager.Instance.StateTracker.CombatStateChanged += OnCombatStateChanged;
        CombatManager.Instance.CombatEnded += OnCombatEnded;
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        // We have to do a null check here (unlike other uses of completion source) because the hand persists for much
        // longer, and it is null until we need to do a hand selection screen.
        if (_selectionCompletionSource is { Task.IsCompleted: false })
        {
            _selectionCompletionSource.SetResult([]);
        }

        CombatManager.Instance.PlayerActionsDisabledChanged -= OnPlayerActionsDisabledChanged;
        CombatManager.Instance.PlayerUnendedTurn -= OnPlayerUnendedTurn;
        CombatManager.Instance.StateTracker.CombatStateChanged -= OnCombatStateChanged;
        CombatManager.Instance.CombatEnded -= OnCombatEnded;
    }

    public NCard? GetCard(CardModel card) => GetCardHolder(card)?.CardNode;

    public bool IsAwaitingPlay(NHandCardHolder? holder) => holder != null && _holdersAwaitingPlay.ContainsKey(holder);

    public NCardHolder? GetCardHolder(CardModel card)
    {
        return Holders
            .Concat<NCardHolder>(_selectedHandCardContainer.Holders)
            .Concat(_holdersAwaitingPlay.Keys)
            .FirstOrDefault(h => h.CardNode != null && h.CardNode.Model == card);
    }

    /// <summary>
    /// Called whenever we add a card into the hand.
    /// Prunes any empty CardHolders and creates a CardHolder for the newly added card.
    /// </summary>
    /// <param name="card"></param>
    /// <returns></returns>
    public NHandCardHolder Add(NCard card)
    {
        Vector2 pos = card.GlobalPosition;
        NHandCardHolder holder = NHandCardHolder.Create(card, this);
        AddCardHolder(holder);
        holder.GlobalPosition = pos;

        // this is so the holder lerps from the cards original position
        RefreshLayout();
        return holder;
    }

    /// <summary>
    /// Remove an NCard and its holder from the hand.
    /// </summary>
    /// <param name="card">CardModel whose node we want to remove</param>
    public void Remove(CardModel card)
    {
        NCardHolder? holder = GetCardHolder(card);
        if (holder == null) throw new InvalidOperationException($"No holder for card {card.Id}");

        // Prevents softlock if player is dragging a card while it's getting exhausted.
        if (InCardPlay && card == _currentCardPlay!.Holder.CardModel)
        {
            // This will happen if you play a card while no other actions are on the queue, because in this case,
            // the card will be instantly moved from the hand to the play pile (instead of sitting on the queue and
            // waiting some frames).
            _currentCardPlay!.CancelPlayCard();
        }

        RemoveCardHolder(holder);
    }

    private void AddCardHolder(NHandCardHolder holder)
    {
        // Note: This cannot be a one-shot connection, because the player may click the card while in peek mode.
        // This will prevent the click from doing anything, but it'll still incorrectly eat the one-shot connection.
        holder.Connect(NCardHolder.SignalName.Pressed, Callable.From<NCardHolder>(OnHolderPressed));
        holder.Connect(NHandCardHolder.SignalName.HolderMouseClicked, Callable.From<NCardHolder>(OnHolderPressed));

        holder.Connect(NHandCardHolder.SignalName.HolderFocused, Callable.From<NHandCardHolder>(_ =>
        {
            FocusedHolder = holder;
            ClimbManager.Instance.HoveredModelTracker.OnLocalCardHovered(FocusedHolder.CardModel!);
            RefreshLayout();
        }));

        holder.Connect(NHandCardHolder.SignalName.HolderUnfocused, Callable.From<NHandCardHolder>(_ =>
        {
            FocusedHolder = null;
            ClimbManager.Instance.HoveredModelTracker.OnLocalCardUnhovered();
            RefreshLayout();
        }));

        CardHolderContainer.AddChildSafely(holder);

        // this is so the holder lerps from the cards original position
        RefreshLayout();

        if (CardHolderContainer.HasFocus())
        {
            holder.TryGrabFocus();
        }
    }

    private void RemoveCardHolder(NCardHolder holder)
    {
        if (holder is NHandCardHolder handHolder &&
            _holdersAwaitingPlay.ContainsKey(handHolder))
        {
            _holdersAwaitingPlay.Remove(handHolder);
        }

        if (InCardPlay && _currentCardPlay!.Holder == holder)
        {
            // This will happen if you play a card while no other actions are on the queue, because in this case,
            // the card will be instantly moved from the hand to the play pile (instead of sitting on the queue and
            // waiting some frames).
            _currentCardPlay.CancelPlayCard();
        }

        // We have to store this, because the holder will lose focus when it is removed from its parent.
        bool hadFocus = holder.HasFocus();

        holder.Clear();
        holder.GetParent().RemoveChildSafely(holder);

        holder.QueueFreeSafely();
        RefreshLayout();


        if (hadFocus)
        {
            OnFocus();
        }
    }

    /// <summary>
    /// If the specific model is being played, then cancel it.
    /// </summary>
    /// <param name="card">Card we are trying to stop from being played</param>
    public void TryCancelCardPlay(CardModel card)
    {
        NCardHolder? cardHolder = GetCardHolder(card);
        if (cardHolder is NHandCardHolder handCardHolder && IsAwaitingPlay(handCardHolder))
        {
            ReturnHolderToHand(handCardHolder);

            // When they were in the play zone, the cards don't get updated, so update it as it comes back into the hand
            handCardHolder.UpdateCard();

            if (InCardPlay && _currentCardPlay!.Holder == handCardHolder)
            {
                _currentCardPlay.CancelPlayCard();
            }
            else
            {
                RefreshLayout();
            }
        }
    }

    public void CancelAllCardPlay()
    {
        if (InCardPlay)
        {
            _currentCardPlay!.CancelPlayCard();
        }
    }

    private void ReturnHolderToHand(NHandCardHolder holder)
    {
        if (IsAwaitingPlay(holder))
        {
            int holderIndex = _holdersAwaitingPlay[holder];
            _holdersAwaitingPlay.Remove(holder);

            // Reset the card holder's transform properties to normal.
            holder.Reparent(CardHolderContainer);

            if (holderIndex >= 0)
            {
                CardHolderContainer.MoveChild(holder, holderIndex);
            }

            holder.SetDefaultTargets();
        }
    }

    /// <summary>
    /// Be sure you know what your doing when you call this function. Should only be used
    /// by the NHandCardCountTickbox to toggle on/off the player hand count labels right away.
    /// </summary>
    public void ForceRefreshCardIndices()
    {
        RefreshLayout();
    }

    /// <summary>
    /// Call this whenever we gain/lose cards so that we fan out the cards for our players.
    /// </summary>
    private void RefreshLayout()
    {
        int count = ActiveHolders.Count;
        if (count <= 0) return; // Do nothing if empty
        int visualCount = count;

        Vector2 cardScale = HandPosHelper.GetScale(count);

        int focusedIndex = -1;
        if (FocusedHolder != null)
        {
            focusedIndex = ActiveHolders.IndexOf(FocusedHolder);
        }

        for (int i = 0; i < count; i++)
        {
            int visualIndex = i;

            Vector2 targetPos = HandPosHelper.GetPosition(visualCount, visualIndex);
            if (focusedIndex > -1)
            {
                float magnitude = Mathf.Lerp(100, 0, Mathf.Min(1, Mathf.Abs(focusedIndex - i) / 4.0f));
                targetPos += Vector2.Left * Mathf.Sign(focusedIndex - i) * magnitude;
            }

            NHandCardHolder holder = ActiveHolders[i];

            if (focusedIndex == i)
            {
                holder.SetAngleInstantly(0f);
                holder.SetScaleInstantly(Vector2.One);

                // add 2 pixels so that we are just flush with the bottom of the screen
                targetPos.Y = -holder.Hitbox.Size.Y * 0.5f + 2f;
                holder.Position = new Vector2(holder.Position.X, targetPos.Y);
                holder.SetTargetPosition(targetPos);
            }
            else
            {
                holder.SetTargetPosition(targetPos);
                holder.SetTargetScale(cardScale);
                holder.SetTargetAngle(HandPosHelper.GetAngle(visualCount, visualIndex));
            }

            holder.SetIndexLabel(i + 1);
            holder.Hitbox.MouseFilter = HasDraggedHolder ? MouseFilterEnum.Ignore : MouseFilterEnum.Stop;
            holder.FocusNeighborLeft = i > 0 ? ActiveHolders[i - 1].GetPath() : ActiveHolders[^1].GetPath();
            holder.FocusNeighborRight = i < ActiveHolders.Count - 1 ? ActiveHolders[i + 1].GetPath() : ActiveHolders[0].GetPath();
            holder.FocusNeighborBottom = holder.GetPath();
        }
    }

    private void OnPlayerUnendedTurn(Player player)
    {
        // If the local player ends their turn at approximately the same time someone else un-ends their turn:
        // - We'll stop the hand from disabling because of the condition in OnPlayerActionsDisabledChanged
        // - Then we'll notice that someone has unreadied, and that we're ready, and then we'll do the hand disable anim
        OnPlayerActionsDisabledChanged(player.Creature.CombatState!);
    }

    private void OnPlayerActionsDisabledChanged(CombatState state)
    {
        if (CombatManager.Instance.PlayerActionsDisabled)
        {
            Player localPlayer = LocalContext.GetMe(state)!;

            // If everyone else is ready to end turn (or this is SP), then don't disable - the hand is about to animate out anyway
            if (!state.Players.Except([localPlayer]).All(CombatManager.Instance.IsPlayerReadyToEndTurn))
            {
                AnimDisable();
            }
        }
        else
        {
            AnimEnable();
        }
    }

    private void OnCombatStateChanged(CombatState _)
    {
        foreach (NHandCardHolder holder in Holders)
        {
            holder.UpdateCard();
        }

        foreach (NHandCardHolder holder in _holdersAwaitingPlay.Keys)
        {
            holder.UpdateCard();
        }

        foreach (NSelectedHandCardHolder holder in _selectedHandCardContainer.Holders)
        {
            holder.CardNode?.UpdateVisuals(CardPileTarget.Hand);
        }
    }

    private void OnCombatEnded(CombatRoom _)
    {
        CancelAllCardPlay();
    }

    #region Select Mode

    private void OnPeekButtonToggled(NPeekButton button)
    {
        UpdateSelectModeCardVisibility();
    }

    public async Task<IEnumerable<CardModel>> SelectCards(CardSelectorPrefs prefs, Func<CardModel, bool>? filter, AbstractModel? source, Mode mode = Mode.SimpleSelect)
    {
        _selectModeBackstop.Visible = true;
        _selectModeBackstop.MouseFilter = MouseFilterEnum.Stop;
        _selectModeBackstop.SelfModulate = _selectModeBackstop.SelfModulate with { A = 0f };
        Tween tween = CreateTween();
        tween.TweenProperty(_selectModeBackstop, "self_modulate:a", 1f, 0.2f);

        bool wasDisabled = _isDisabled;
        if (_isDisabled)
        {
            AnimEnable();
        }

        CurrentMode = mode;
        _currentSelectionFilter = filter;

        NCombatUi combatUi = NCombatRoom.Instance!.Ui;
        combatUi.OnHandSelectModeEntered();
        NCombatRoom.Instance.RestrictControllerNavigation([]);
        EnableControllerNavigation();

        OnFocus();

        _prefs = prefs;
        _selectionCompletionSource = new TaskCompletionSource<IEnumerable<CardModel>>();
        _selectionHeader.Visible = true;
        _selectionHeader.Text = $"[center]{prefs.Prompt.GetFormattedText()}[/center]";

        _peekButton.Visible = true;

        UpdateSelectModeCardVisibility();

        OnFocus();
        RefreshSelectModeConfirmButton();

        IEnumerable<CardModel> result = await _selectionCompletionSource.Task;

        tween.Kill();
        AfterCardsSelected(source);

        if (wasDisabled)
        {
            AnimDisable();
        }

        return result;
    }

    private void UpdateSelectModeCardVisibility()
    {
        if (CurrentMode != Mode.SimpleSelect && CurrentMode != Mode.UpgradeSelect)
        {
            throw new InvalidOperationException("Can only be used when we are selecting a card");
        }

        foreach (NHandCardHolder holder in Holders)
        {
            if (holder.CardNode == null) continue;
            if (_peekButton.IsPeeking)
            {
                holder.Visible = true;
                holder.CardNode.SetPretendCardCanBePlayed(false);
                holder.CardNode.SetForceUnpoweredPreview(false);
            }
            else
            {
                holder.Visible = _currentSelectionFilter?.Invoke(holder.CardNode.Model!) ?? true;
                holder.CardNode.SetPretendCardCanBePlayed(_prefs.PretendCardsCanBePlayed);
                holder.CardNode.SetForceUnpoweredPreview(_prefs.UnpoweredPreviews);
            }

            holder.UpdateCard();
        }

        RefreshLayout();
    }

    private void AfterCardsSelected(AbstractModel? source)
    {
        _selectedCards.Clear();

        foreach (NHandCardHolder holder in Holders)
        {
            // TODO: Turn off filter UI
            // holder.CanBeSelected = true;
            holder.InSelectMode = false;
            holder.Visible = true;
            holder.CardNode?.SetPretendCardCanBePlayed(false);
            holder.CardNode?.SetForceUnpoweredPreview(false);
            holder.UpdateCard();
        }

        RefreshLayout();

        _selectModeBackstop.Visible = false;
        _selectModeBackstop.MouseFilter = MouseFilterEnum.Ignore;
        Tween tween = CreateTween();
        tween.TweenProperty(_selectModeBackstop, "self_modulate:a", 0f, 0.2f);

        _selectModeConfirmButton.Disable();
        _upgradePreviewContainer.Visible = false;
        _selectionHeader.Visible = false;
        _peekButton.Visible = false;
        _prefs = default;
        CurrentMode = Mode.Play;
        _currentSelectionFilter = null;

        NCombatRoom.Instance!.Ui.OnHandSelectModeExited();

        if (source != null)
        {
            source.ExecutionFinished += OnSelectModeSourceFinished;
        }
        else
        {
            OnSelectModeSourceFinished(null);
        }
    }

    private void CancelHandSelectionIfNecessary()
    {
        if (InCardSelection && _selectionCompletionSource != null)
        {
            _selectionCompletionSource.SetCanceled();
            AfterCardsSelected(null);
        }
    }

    /// <summary>
    /// Called when a card is clicked in the hand.
    /// </summary>
    /// <param name="holder">CardHolder that was clicked.</param>
    private void OnHolderPressed(NCardHolder holder)
    {
        // Don't allow card selection in peek mode
        if (_peekButton.IsPeeking) return;

        NHandCardHolder handCardHolder = (NHandCardHolder)holder;
        if (handCardHolder.CardNode == null) return;
        if (!CombatManager.Instance.IsInProgress) return;

        // This probably means we are click on the card via peeking from
        // a screen selection, so don't select the card
        if (NOverlayStack.Instance!.ScreenCount > 0) return;

        switch (CurrentMode)
        {
            case Mode.None:
                break;
            case Mode.Play:
                if (CanPlayCards())
                {
                    StartCardPlay(handCardHolder);
                }

                break;
            case Mode.SimpleSelect:
                SelectCardInSimpleMode(handCardHolder);
                break;
            case Mode.UpgradeSelect:
                SelectCardInUpgradeMode(handCardHolder);
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(CurrentMode));
        }
    }

    private bool CanPlayCards()
    {
        return !InCardPlay &&
            !CombatManager.Instance.PlayerActionsDisabled &&
            !_peekButton.IsPeeking;
    }

    private void StartCardPlay(NHandCardHolder holder)
    {
        // set up card so that it can be dragged
        _draggedHolderIndex = holder.GetIndex();
        _holdersAwaitingPlay.Add(holder, _draggedHolderIndex);
        holder.Reparent(this);
        holder.SetIndexLabel(-1);
        holder.BeginDrag();

        _currentCardPlay = NControllerManager.Instance!.IsUsingController ? NControllerCardPlay.Create(holder) : NMouseCardPlay.Create(holder);
        this.AddChildSafely(_currentCardPlay);

        _currentCardPlay.Connect(NCardPlay.SignalName.Finished, Callable.From<bool>(success =>
        {
            ClimbManager.Instance.HoveredModelTracker.OnLocalCardDeselected();

            if (!success)
            {
                ReturnHolderToHand(holder);
            }

            _draggedHolderIndex = -1;
            RefreshLayout();
        }));

        ClimbManager.Instance.HoveredModelTracker.OnLocalCardSelected(holder.CardNode!.Model!);
        _currentCardPlay.Start();
        RefreshLayout();
    }

    private void SelectCardInSimpleMode(NHandCardHolder holder)
    {
        if (_selectedCards.Count >= _prefs.MaxSelect)
        {
            _selectedHandCardContainer.DeselectCard(_selectedCards.Last());
        }

        _selectedCards.Add(holder.CardNode!.Model!);
        _selectedHandCardContainer.Add(holder);
        RemoveCardHolder(holder);
        RefreshSelectModeConfirmButton();
        OnFocus();
    }

    private void SelectCardInUpgradeMode(NHandCardHolder holder)
    {
        CardModel card = holder.CardNode!.Model!;

        if (_selectedCards.Any())
        {
            NCard node = NCard.Create(_selectedCards.Last())!;
            node.GlobalPosition = _upgradePreview.SelectedCardPosition;
            DeselectCard(node);
        }

        _selectedCards.Add(card);

        _upgradePreviewContainer.Visible = true;
        _upgradePreview.Card = card;

        RemoveCardHolder(holder);
        RefreshSelectModeConfirmButton();
        OnFocus();
    }

    /// <summary>
    /// Deselect a card when in a Select Mode.
    /// </summary>
    /// <param name="card"></param>
    public void DeselectCard(NCard card)
    {
        if (!InCardSelection) throw new InvalidOperationException("Only valid when in Select Mode.");

        NHandCardHolder handHolder = Add(card);
        handHolder.InSelectMode = true;
        handHolder.Visible = true; // We can assume this since it was already selected.

        _selectedCards.Remove(card.Model!);
        RefreshSelectModeConfirmButton();

        handHolder.TryGrabFocus();
    }

    private void OnSelectModeConfirmButtonPressed(NButton _)
    {
        _selectionCompletionSource!.SetResult(_selectedCards.ToList());
    }

    private void CheckIfSelectionComplete()
    {
        // TODO: Handle multi-select with confirm
        if (_selectedCards.Count >= _prefs.MaxSelect)
        {
            _selectionCompletionSource!.SetResult(_selectedCards.ToList());
        }
    }

    private void RefreshSelectModeConfirmButton()
    {
        int count = _selectedCards.Count;

        if (count >= _prefs.MinSelect && count <= _prefs.MaxSelect)
        {
            _selectModeConfirmButton.Enable();
        }
        else
        {
            _selectModeConfirmButton.Disable();
        }
    }

    private void OnSelectModeSourceFinished(AbstractModel? source)
    {
        // If any selected cards are still in the player's hand (meaning they didn't get moved to a different pile),
        // visually move them back to the hand area.
        foreach (NSelectedHandCardHolder holder in _selectedHandCardContainer.Holders.ToList())
        {
            NCard node = holder.CardNode!;
            holder.QueueFreeSafely();
            Add(node);
        }

        if (_upgradePreview.Card != null)
        {
            Add(NCard.Create(_upgradePreview.Card)!);
            _upgradePreview.Card = null;
        }

        if (source != null)
        {
            source.ExecutionFinished -= OnSelectModeSourceFinished;
        }
    }

    #endregion

    /// <summary>
    /// Animation for entering combat
    /// </summary>
    public void AnimIn()
    {
        _animOutTween?.Kill();
        _animEnableTween?.Kill();

        _animInTween = CreateTween();
        _animInTween.TweenProperty(this, "position", _showPosition, _showHideAnimDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    /// <summary>
    /// Animation when winning combat
    /// </summary>
    public void AnimOut()
    {
        CancelHandSelectionIfNecessary();
        _animInTween?.Kill();
        _animEnableTween?.Kill();

        _animOutTween = CreateTween();
        _animOutTween.TweenProperty(this, "position", _hidePosition, _showHideAnimDuration)
            .SetEase(Tween.EaseType.In)
            .SetTrans(Tween.TransitionType.Back);
    }

    /// <summary>
    /// Animation when player actions are disabled during player turn
    /// </summary>
    private void AnimDisable()
    {
        if (_isDisabled) return;

        _animEnableTween = CreateTween().Parallel();
        _animEnableTween.TweenProperty(this, "position", _disablePosition, _enableDisableDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _animEnableTween.TweenProperty(this, "modulate", _disableModulate, _enableDisableDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);

        _isDisabled = true;
    }

    /// <summary>
    /// Animation when player actions are re-enabled during player turn
    /// </summary>
    private void AnimEnable()
    {
        if (!_isDisabled) return;

        _animEnableTween = CreateTween().Parallel();
        _animEnableTween.TweenProperty(this, "position", _showPosition, _enableDisableDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _animEnableTween.TweenProperty(this, "modulate", Colors.White, _enableDisableDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);

        _isDisabled = false;
    }

    public void FlashPlayableHolders()
    {
        foreach (NHandCardHolder holder in Holders)
        {
            if (holder.CardNode != null && holder.CardNode.Model!.CanPlay())
            {
                holder.Flash();
            }
        }
    }

    private void OnCardSelected(Node _) => UpdateSelectedCardContainer(_selectedHandCardContainer.GetChildCount());
    private void OnCardDeselected(Node _) => UpdateSelectedCardContainer(_selectedHandCardContainer.GetChildCount() - 1);

    private void UpdateSelectedCardContainer(int count)
    {
        float scale = 1f;
        float yPos = Size.Y * 0.5f;
        if (count > 6)
        {
            scale = 0.55f;
            yPos -= 150f;
        }
        else if (count > 3)
        {
            scale = 0.8f;
            yPos -= 75f;
        }

        _selectedCardScaleTween?.Kill();
        _selectedCardScaleTween = CreateTween().SetParallel();
        _selectedCardScaleTween.TweenProperty(_selectedHandCardContainer, "position:y", yPos, 0.5f).SetTrans(Tween.TransitionType.Quad).SetEase(Tween.EaseType.InOut);
        _selectedCardScaleTween.TweenProperty(_selectedHandCardContainer, "scale", Vector2.One * scale, 0.5f).SetTrans(Tween.TransitionType.Quad).SetEase(Tween.EaseType.InOut);
    }

    public void OnFocus()
    {
        if (ActiveHolders.Count > 0)
        {
            ActiveHolders[ActiveHolders.Count / 2].TryGrabFocus();
        }
        else
        {
            // setup navigation to the first creature (typically the player creature)
            // this is for the case the hand is empty.
            CardHolderContainer.TryGrabFocus();
            CardHolderContainer.FocusNeighborTop = NCombatRoom.Instance!.CreatureNodes.FirstOrDefault()?.Hitbox.GetPath();
            CardHolderContainer.FocusNeighborBottom = CardHolderContainer.GetPath();
            CardHolderContainer.FocusNeighborLeft = CardHolderContainer.GetPath();
            CardHolderContainer.FocusNeighborRight = CardHolderContainer.GetPath();
        }
    }

    public void EnableControllerNavigation()
    {
        foreach (NHandCardHolder holder in Holders)
        {
            holder.FocusMode = FocusModeEnum.All;
        }

        if (InCardPlay)
        {
            _currentCardPlay!.Holder.FocusMode = FocusModeEnum.All;
        }
    }

    public void DisableControllerNavigation()
    {
        foreach (NHandCardHolder holder in Holders)
        {
            holder.FocusMode = FocusModeEnum.None;
        }

        if (InCardPlay)
        {
            _currentCardPlay!.Holder.FocusMode = FocusModeEnum.None;
        }
    }
}
