using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NIntent : Control
{
    private Control _intentHolder = default!;
    private AnimatedSprite2D _intentSprite = default!;
    private RichTextLabel _valueLabel = default!;
    private CpuParticles2D _intentParticle = default!;

    private Creature _owner = default!;
    private IEnumerable<Creature> _targets = default!;
    private AbstractIntent _intent = default!;
    private float _timeOffset;
    private bool _isFrozen;
    private const string _scenePath = "res://scenes/combat/intent.tscn";

    // Bob up and down vfx vars
    private const float _bobSpeed = Mathf.Pi;
    private const float _bobDistance = 10f;
    private const float _bobOffset = 8f;

    public override void _Ready()
    {
        _intentHolder = GetNode<Control>("%IntentHolder");
        _intentSprite = GetNode<AnimatedSprite2D>("%Intent");
        _valueLabel = GetNode<RichTextLabel>("%Value");
        _intentParticle = GetNode<CpuParticles2D>("%IntentParticle");

        Connect(Control.SignalName.MouseEntered, Callable.From(OnHovered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnUnhovered));

        // Debug enables/disables intent visibility
        _intentHolder.Modulate = NCombatUi.IsDebugHidingIntent ? Colors.Transparent : Colors.White;
    }

    public override void _EnterTree()
    {
        CombatManager.Instance.StateTracker.CombatStateChanged += OnCombatStateChanged;
        NCombatRoom.Instance!.Ui.DebugToggleIntent += DebugToggleVisibility;
    }

    private void DebugToggleVisibility()
    {
        _intentHolder.Modulate = NCombatUi.IsDebugHidingIntent ? Colors.Transparent : Colors.White;
    }

    public override void _ExitTree()
    {
        CombatManager.Instance.StateTracker.CombatStateChanged -= OnCombatStateChanged;
        NCombatRoom.Instance!.Ui.DebugToggleIntent -= DebugToggleVisibility;
    }

    public void UpdateIntent(AbstractIntent intent, IEnumerable<Creature> targets, Creature owner)
    {
        _owner = owner;
        _targets = targets;
        _intent = intent;
        UpdateVisuals();
    }

    private void OnCombatStateChanged(CombatState _)
    {
        if (!_isFrozen)
        {
            UpdateVisuals();
        }
    }

    private void UpdateVisuals()
    {
        if (_intentSprite.Animation != _intent.GetAnimation(_targets, _owner))
        {
            // only update the sprite if the animation changed
            _intentSprite.Play(_intent.GetAnimation(_targets, _owner));
        }

        _intentParticle.Texture = _intent.GetTexture(_targets, _owner);

        _valueLabel.Text = _intent switch
        {
            AttackIntent attackIntent => $"{attackIntent.GetIntentLabel(_targets, _owner).GetFormattedText()}",
            StatusIntent => $"{_intent.GetIntentLabel(_targets, _owner).GetFormattedText()}",
            _ => string.Empty
        };
    }

    public override void _Process(double delta)
    {
        _intentHolder.Position = Vector2.Up * (Mathf.Sin(Time.GetTicksMsec() * 0.001f * _bobSpeed + _timeOffset) * _bobDistance + _bobOffset);
    }

    public static NIntent Create(float startTime)
    {
        NIntent intent = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NIntent>();
        intent._timeOffset = startTime;
        return intent;
    }

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public void PlayPerform()
    {
        _intentParticle.Emitting = true;
    }

    public void SetFrozen(bool isFrozen)
    {
        _isFrozen = isFrozen;
    }

    private void OnHovered()
    {
        if (!_intent.HasIntentTip) return;

        NCombatRoom.Instance?.GetCreatureNode(_owner)?.ShowHoverTips([_intent.GetHoverTip(_targets, _owner)]);
    }

    private void OnUnhovered()
    {
        NCombatRoom.Instance?.GetCreatureNode(_owner)?.HideHoverTips();
    }
}
