using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NPowerContainer : Control
{
    private Creature? _creature;

    private Vector2 _originalPosition;
    private readonly List<NPower> _powerNodes = [];

    public override void _EnterTree()
    {
        base._EnterTree();

        // This subscription already exists in SetCreature, but we duplicate it here in case the node gets reparented.
        if (_creature != null)
        {
            _creature.PowerApplied += OnPowerApplied;
            _creature.PowerRemoved += OnPowerRemoved;
        }
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        if (_creature != null)
        {
            _creature.PowerApplied -= OnPowerApplied;
            _creature.PowerRemoved -= OnPowerRemoved;
        }
    }

    public void SetCreatureBounds(Control bounds)
    {
        GlobalPosition = new Vector2(bounds.GlobalPosition.X, GlobalPosition.Y);
        Size = new Vector2(bounds.Size.X * bounds.Scale.X + 25, Size.Y);
        _originalPosition = Position;
    }

    private void Add(PowerModel power)
    {
        if (!power.IsVisible) return;

        NPower node = NPower.Create(power);
        node.Container = this;
        _powerNodes.Add(node);
        this.AddChildSafely(node);

        UpdatePositions();
    }

    private void Remove(PowerModel power)
    {
        // if combat has ended, we want the power visuals to remain as a way for players to see
        // the final state of the game as they wrap up rewards and such
        if (!CombatManager.Instance.IsInProgress) return;
        NPower? node = _powerNodes.FirstOrDefault(n => n.Model == power);

        // An invisible power has no node.
        if (node == null) return;

        _powerNodes.Remove(node);
        UpdatePositions();
        node.QueueFreeSafely();
    }

    private void UpdatePositions()
    {
        if (_powerNodes.Count == 0) return;

        float powerSize = _powerNodes[0].Size.X;
        float columnCount = Mathf.CeilToInt(Size.X / powerSize);

        // make sure we only have a max of 2 rows
        columnCount = Mathf.Max(Mathf.CeilToInt(_powerNodes.Count / 2.0f), columnCount);

        for (int i = 0; i < _powerNodes.Count; i++)
        {
            _powerNodes[i].Position = new Vector2(powerSize * (i % columnCount), Mathf.Floor(i / columnCount) * powerSize);
        }

        // if the rowWidth exceeds the power containers original width,
        // we recenter the container
        float rowWidth = powerSize * Mathf.Min(columnCount, _powerNodes.Count);
        Position = _originalPosition + Vector2.Left * Mathf.Max(0, rowWidth - Size.X) / 2;

    }

    public void SetCreature(Creature creature)
    {
        if (_creature != null) throw new InvalidOperationException("Creature was already set.");

        _creature = creature;
        _creature.PowerApplied += OnPowerApplied;
        _creature.PowerRemoved += OnPowerRemoved;

        // Show any powers that the creature already has.
        foreach (PowerModel power in _creature.Powers)
        {
            Add(power);
        }
    }

    private void OnPowerApplied(PowerModel power) => Add(power);
    private void OnPowerRemoved(PowerModel power) => Remove(power);
}
