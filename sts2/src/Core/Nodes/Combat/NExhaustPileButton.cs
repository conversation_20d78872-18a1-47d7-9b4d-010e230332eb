using Godot;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Cards;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Node script for the Exhaust Pile.
/// </summary>
public partial class NExhaustPileButton : NCombatCardPile
{
    protected override string Hotkey => MegaInput.viewExhaustPile;

    private Viewport _viewport = default!;
    private Vector2 _posOffset;
    private static readonly Vector2 _hideOffset = new(150f, 0f);

    protected override CardPileTarget PileType => CardPileTarget.Exhaust;

    public override void _Ready()
    {
        ConnectSignals();
        Visible = false;
        _viewport = GetViewport();
        _posOffset = new Vector2(OffsetRight + 100f, -OffsetBottom + 90f);
        GetTree().Root.Connect(Viewport.SignalName.SizeChanged, Callable.From(SetAnimInOutPositions));
        SetAnimInOutPositions();
    }

    /// <summary>
    /// The text "bump" animation for pile UI.
    /// </summary>
    protected override void AddCard()
    {
        base.AddCard();

        if (!Visible)
        {
            AnimIn();
        }
    }

    protected override void SetAnimInOutPositions()
    {
        _showPosition = NGame.Instance!.Size - _posOffset;
        _hidePosition = _showPosition + _hideOffset;
    }

    public override void AnimIn()
    {
        base.AnimIn();
        Visible = true;
    }
}
