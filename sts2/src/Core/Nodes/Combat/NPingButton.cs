using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Ping button. Flies in from off-screen after a short delay when the local player is ready but remote players are not.
/// </summary>
public partial class NPingButton : NButton
{
    private const float _flyInOutDuration = 0.5f;

    private State _state = State.Hidden;

    private Control _visuals = default!;
    private TextureRect _image = default!;
    private Label _label = default!;
    private Viewport _viewport = default!;
    private ShaderMaterial _hsv = default!;

    private CancellationTokenSource? _showCancelTokenSource;

    private static readonly Vector2 _showPosRatio = new Vector2(1536f, 932f) / NGame.devResolution;
    private static readonly Vector2 _hidePosRatio = _showPosRatio + new Vector2(0f, 250f) / NGame.devResolution;

    private Vector2 ShowPos => _showPosRatio * _viewport.GetVisibleRect().Size;
    private Vector2 HidePos => _hidePosRatio * _viewport.GetVisibleRect().Size;

    private Tween? _positionTween;
    private Tween? _hoverTween;

    protected override string Hotkey => MegaInput.select;
    protected override bool IsControllerHotkeyActive => NCombatRoom.Instance!.IsControllerHotkeysActive;

    private enum State
    {
        // Button is shown and enabled
        Enabled,

        // Button is shown but cannot be clicked
        Disabled,

        // Button has been animated away and cannot be clicked
        Hidden,
    }

    public override void _Ready()
    {
        ConnectSignals();

        _visuals = GetNode<Control>("Visuals");
        _image = GetNode<TextureRect>("Visuals/Image");
        _label = GetNode<Label>("Visuals/Label");
        _viewport = GetViewport();
        _hsv = (ShaderMaterial)_image.Material;

        LocString loc = new("gameplay_ui", "PING_BUTTON");
        _label.Text = loc.GetFormattedText();

        Position = HidePos;
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        CombatManager.Instance.AboutToSwitchToEnemyTurn += OnAboutToSwitchToEnemyTurn;
        CombatManager.Instance.PlayerEndedTurn += AfterPlayerEndedTurn;
        CombatManager.Instance.PlayerUnendedTurn += AfterPlayerUnendedTurn;
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        CombatManager.Instance.AboutToSwitchToEnemyTurn -= OnAboutToSwitchToEnemyTurn;
        CombatManager.Instance.PlayerEndedTurn -= AfterPlayerEndedTurn;
        CombatManager.Instance.PlayerUnendedTurn -= AfterPlayerUnendedTurn;
    }

    private void AfterPlayerEndedTurn(Player player, bool _)
    {
        if (CombatManager.Instance.AllPlayersReadyToEndTurn())
        {
            Disable();
        }
        else if (LocalContext.IsMe(player))
        {
            _showCancelTokenSource = new CancellationTokenSource();
            TaskHelper.RunSafely(AnimInAfterDelay());
        }
    }

    private void AfterPlayerUnendedTurn(Player player)
    {
        if (!LocalContext.IsMe(player)) return;

        _showCancelTokenSource?.Cancel();
        AnimOut();
    }

    private async Task AnimInAfterDelay()
    {
        await Task.Delay(500, _showCancelTokenSource!.Token);

        if (!_showCancelTokenSource!.IsCancellationRequested)
        {
            Enable();
        }
    }

    private void OnAboutToSwitchToEnemyTurn(CombatState _)
    {
        _showCancelTokenSource?.Cancel();
        AnimOut();
    }

    protected override void OnRelease()
    {
        ClimbManager.Instance.FlavorSynchronizer.SendEndTurnPing();

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), IsFocused ? 1.5f : 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", Vector2.Zero, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_label, "modulate", IsEnabled ? StsColors.cream : StsColors.gray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnEnable()
    {
        _image.Modulate = Colors.White;
        _label.Modulate = StsColors.cream;

        if (_state == State.Hidden)
        {
            AnimIn();
        }

        _state = State.Enabled;
    }

    /// <summary>
    /// Called when the player presses End Turn or combat ends.
    /// </summary>
    protected override void OnDisable()
    {
        NHoverTipSet.Remove(this);

        // If we're in multiplayer and the end turn action takes a while to get to the host and back, we should show
        // that the end turn button is not clickable for that duration
        _image.Modulate = StsColors.gray;
        _label.Modulate = StsColors.gray;

        if (_state == State.Enabled)
        {
            _state = State.Disabled;
        }
    }

    /// <summary>
    /// Animates the button out off the screen and disables the button if it is currently enabled.
    /// </summary>
    public void AnimOut()
    {
        _hoverTween?.Kill();
        _positionTween?.Kill();
        _positionTween = CreateTween();
        _positionTween.TweenProperty(this, "position", HidePos, _flyInOutDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        if (_state == State.Enabled)
        {
            Disable();
        }

        _state = State.Hidden;
    }

    /// <summary>
    /// Animates the button in from off the screen.
    /// </summary>
    private void AnimIn()
    {
        _positionTween?.Kill();
        _positionTween = CreateTween();
        _positionTween.TweenProperty(this, "position", ShowPos, _flyInOutDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnFocus()
    {
        base.OnFocus();

        _hoverTween?.Kill();
        _hsv.SetShaderParameter("v", 1.5);
        _visuals.Position = new Vector2(0f, -2f);
    }

    protected override void OnUnfocus()
    {
        NHoverTipSet.Remove(this);

        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", Vector2.Zero, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_label, "modulate", IsEnabled ? StsColors.cream : StsColors.gray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    protected override void OnPressDown()
    {
        _hoverTween?.Kill();
        _hoverTween = CreateTween().SetParallel();
        _hoverTween.TweenMethod(Callable.From<float>(UpdateShaderV), _hsv.GetShaderParameter("v"), 1f, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        _hoverTween.TweenProperty(_visuals, "position", new Vector2(0f, 4f), 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);
        _hoverTween.TweenProperty(_label, "modulate", Colors.DarkGray, 0.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    private void UpdateShaderV(float value)
    {
        _hsv.SetShaderParameter("v", value);
    }
}
