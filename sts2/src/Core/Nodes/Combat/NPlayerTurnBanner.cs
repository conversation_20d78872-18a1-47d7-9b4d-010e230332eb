using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NPlayerTurnBanner : Control
{
    private Label _label = default!;
    private Label _turnLabel = default!;
    private int _roundNumber;

    private static readonly string _scenePath = SceneHelper.GetScenePath("combat/player_turn_banner");
    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NPlayerTurnBanner? Create(int roundNumber)
    {
        if (TestMode.IsOn) return null;
        if (NCombatUi.IsDebugHideTextVfx) return null;

        NPlayerTurnBanner node = PreloadManager.Cache.GetScene(_scenePath).Instantiate<NPlayerTurnBanner>();
        node._roundNumber = roundNumber;

        return node;
    }

    public override void _Ready()
    {
        _label = GetNode<Label>("Label");
        _label.Text = new LocString("gameplay_ui", "PLAYER_TURN").GetFormattedText();

        _turnLabel = GetNode<Label>("TurnNumber");
        LocString turnNumString = new("gameplay_ui", "TURN_COUNT");
        turnNumString.Add("turnNumber", _roundNumber);
        _turnLabel.Text = turnNumString.GetFormattedText();

        Modulate = Colors.Transparent;

        TaskHelper.RunSafely(Display());
    }

    private async Task Display()
    {
        NDebugAudioManager.Instance?.Play(TmpSfx.startPlayerTurn);

        Tween tween = CreateTween();
        tween.SetParallel();
        tween.TweenProperty(this, "modulate:a", 1f, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        // Moves our text in an exciting way
        tween.TweenProperty(_label, "position", _label.Position + new Vector2(0f, -50f), 1.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        tween.TweenProperty(_turnLabel, "position", _turnLabel.Position + new Vector2(0f, 50f), 1.5f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);

        await ToSignal(tween, Tween.SignalName.Finished);

        // Show our effect for 0.4 seconds, then fade it out
        tween = CreateTween();
        tween.TweenInterval(0.4);
        tween.TweenProperty(this, "modulate:a", 0f, 0.3f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);

        await ToSignal(tween, Tween.SignalName.Finished);

        this.QueueFreeSafely();
    }
}
