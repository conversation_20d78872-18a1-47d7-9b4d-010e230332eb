using System;
using Godot;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Settings;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Controls visual logic for the healthbar! This includes the red part, the text, block icon, and handles Poison/Doom.
/// This does NOT control the behavior/layout of Powers.
/// </summary>
public partial class NHealthBar : Control
{
    // Nodes
    private Control _hpForegroundContainer = default!;
    private Control _hpForeground = default!;
    private Control _poisonForeground = default!; // Note that Poison is... red?? When poisoned, the normal hpForeground is Green and is rendered above the PoisonForeground.
    private Control _doomForeground = default!;
    private Control _hpMiddleground = default!;
    private Label _hpLabel = default!;
    private Control _blockContainer = default!;
    private Label _blockLabel = default!;
    private Control _blockOutline = default!;

    private Creature _creature = default!;
    private Creature? _blockTrackingCreature; // Allows Osty to track Necrobinder's block
    private Vector2 _creatureSize;

    private readonly LocString _healthBarDead = new("gameplay_ui", "HEALTH_BAR.DEAD");

    // Used when a creature has Infinite HP (eg Illusions)
    private TextureRect _infinityTex = default!;

    private Tween? _blockTween;
    private Tween? _hpLabelFadeTween;
    private Tween? _middlegroundTween;

    private Vector2 _originalBlockPosition;
    private Vector2 _originalPosition;

    private int _currentHpOnLastRefresh = -1;
    private int _maxHpOnLastRefresh = -1;

    private const float _minSize = 12f; // Below this size, our sliced sprite gets wonky.
    private static readonly Vector2 _blockAnimOffset = new(0f, 20f);

    // Text colors
    private static readonly Color _defaultFontColor = StsColors.cream;
    private static readonly Color _defaultFontOutlineColor = new("900000"); // Red
    private static readonly Color _blockOutlineColor = new("1B3045");

    // Bar colors
    private static readonly Color _redForegroundColor = new("F1373E");
    private static readonly Color _blockHpForegroundColor = new("3B6FA3");
    private static readonly Color _invincibleForegroundColor = new("C5BBED");

    private float MaxFgWidth => _hpForegroundContainer.Size.X;

    public Control HpBarContainer { get; private set; } = default!;

    public void SetCreature(Creature creature)
    {
        if (_creature != null) throw new InvalidOperationException("Creature was already set.");

        _creature = creature;

        // Set the positions at the start of combat so we don't animate it on load
        _hpForeground.OffsetRight = GetFgWidth(_creature.CurrentHp) - MaxFgWidth;
        _hpMiddleground.OffsetRight = _hpForeground.OffsetRight - 2f;
    }

    public override void _Ready()
    {
        HpBarContainer = GetNode<Control>("%HpBarContainer");
        _hpForegroundContainer = GetNode<Control>("%HpForegroundContainer");

        _hpMiddleground = GetNode<Control>("%HpMiddleground");
        _hpForeground = GetNode<Control>("%HpForeground");
        _poisonForeground = GetNode<Control>("%PoisonForeground");
        _doomForeground = GetNode<Control>("%DoomForeground");

        _hpLabel = GetNode<Label>("%HpLabel");
        _blockContainer = GetNode<Control>("%BlockContainer");
        _blockLabel = GetNode<Label>("%BlockLabel");
        _blockOutline = GetNode<Control>("%BlockOutline");

        _infinityTex = GetNode<TextureRect>("%InfinityTex");

        _originalPosition = Position;
        _originalBlockPosition = _blockContainer.Position;
    }

    private void DebugToggleVisibility()
    {
        Visible = !NCombatUi.IsDebugHidingHpBar;
    }

    public void UpdateLayoutForCreatureBounds(Control bounds)
    {
        HpBarContainer.GlobalPosition = new Vector2(bounds.GlobalPosition.X, HpBarContainer.GlobalPosition.Y);
        SetHpBarContainerSize(new Vector2(bounds.Size.X * bounds.GetGlobalTransform().Scale.X, HpBarContainer.Size.Y));

        float blockIconWidthOffset = _blockContainer.Size.X * 0.5f;
        _blockContainer.GlobalPosition = new Vector2(bounds.GlobalPosition.X - blockIconWidthOffset, _blockContainer.GlobalPosition.Y);
        _originalBlockPosition = _blockContainer.Position;
    }

    /// <summary>
    /// Linearly interpolates the width of the HP bar based on the reference values.
    /// </summary>
    /// <param name="refMaxHp">A reference max HP.</param>
    /// <param name="refWidth">The width of the HP bar when the max HP is equal to refMaxHp.</param>
    public void UpdateWidthRelativeToReferenceValue(float refMaxHp, float refWidth)
    {
        SetHpBarContainerSize(HpBarContainer.Size with { X = _creature.MaxHp / refMaxHp * refWidth });
        RefreshValues();
    }

    private void SetHpBarContainerSize(Vector2 size)
    {
        HpBarContainer.Size = size;

        // Update offsets, otherwise the bars will look wrong or otherwise not match
        _hpForeground.OffsetRight = GetFgWidth(_creature.CurrentHp) - MaxFgWidth;
        _hpMiddleground.OffsetRight = _hpForeground.OffsetRight - 2f;
    }

    /// <summary>
    /// Forces the Healthbar to look right and start/stop animations.
    /// Called often, even if the healthbar isn't affected.
    /// </summary>
    public void RefreshValues()
    {
        RefreshBlockUi(); // The "Block" shield UI and text on the shield
        RefreshForeground(); // Evil priority-based logic for the red/green/purple bar
        RefreshMiddleground(); // The orange bar
        RefreshText(); // The text that displays HP. e.g. 72/72
    }

    private void RefreshMiddleground()
    {
        // No HP = No bars. Exit early
        if (_creature.CurrentHp <= 0)
        {
            _hpMiddleground.Visible = false;
            return;
        }

        _hpMiddleground.Visible = true;

        // Casey: I have no idea why this shifts at the start of combat but this fixes it.
        _hpMiddleground.Position = new Vector2(1f, 0f);

        int currentHp = _creature.CurrentHp;
        int maxHp = _creature.MaxHp;

        // If we refreshed values without current/max HP changing, skip the middleground tween.
        // Without this, the middleground tween can temporarily get stuck halfway through, because we refresh during
        // most game events (card draws, turn changes, etc.), and most of these won't actually change the creature's HP
        // values.
        if (currentHp != _currentHpOnLastRefresh || maxHp != _maxHpOnLastRefresh)
        {
            _currentHpOnLastRefresh = currentHp;
            _maxHpOnLastRefresh = maxHp;

            float targetFgOffset = _creature.HasPower<Poison>() ? _poisonForeground.OffsetRight : _hpForeground.OffsetRight;

            // We play this middleground tween immediately if we're tweening to a larger right offset (meaning we're healing
            // instead of taking damage, in which case we don't want the middleground floating around).
            //
            // Otherwise, we give a 1-second delay. This way, if a creature takes a bunch of small hits in quick succession,
            // the middleground stays put for a sec so we can see how much damage they took.
            bool immediate = targetFgOffset >= _hpMiddleground.OffsetRight;

            _hpMiddleground.OffsetRight += 1f;
            _middlegroundTween?.Kill();
            _middlegroundTween = CreateTween();
            _middlegroundTween.TweenProperty(_hpMiddleground, "offset_right", targetFgOffset - 2f, 1.0)
                .SetDelay(immediate ? 0.0 : 1.0)
                .SetEase(Tween.EaseType.Out)
                .SetTrans(Tween.TransitionType.Expo);
        }
    }

    private void RefreshForeground()
    {
        if (_creature.CurrentHp <= 0)
        {
            _poisonForeground.Visible = false;
            _doomForeground.Visible = false;
            _hpForeground.Visible = false;
            return;
        }

        _hpForeground.Visible = true;

        float hpFgOffset = GetFgWidth(_creature.CurrentHp) - MaxFgWidth;
        _hpForeground.OffsetRight = hpFgOffset;

        // Invincible
        if (_creature.ShowsInfiniteHp)
        {
            _hpForeground.SelfModulate = _invincibleForegroundColor;
            return;
        }

        int doomDamage = _creature.GetPowerAmount<Doom>();
        int poisonDamage = _creature.GetPower<Poison>()?.CalculateTotalDamageNextTurn() ?? 0;

        // Poison
        if (_creature.HasPower<Poison>())
        {
            _poisonForeground.Visible = true;

            if (poisonDamage > 0)
            {
                _poisonForeground.OffsetRight = _hpForeground.OffsetRight;

                // Non-lethal. Make the foreground green
                if (IsPoisonLethal(poisonDamage))
                {
                    // Lethal. Don't bother rendering the red bar.
                    _hpForeground.Visible = false;
                }
                else
                {
                    // How much space the HP portion of the health bar should take up?
                    float poisonFgOffset = GetFgWidth(_creature.CurrentHp - poisonDamage) - MaxFgWidth;
                    _hpForeground.OffsetRight = poisonFgOffset;
                    _hpForeground.Visible = true;
                }
            }
        }
        else
        {
            _poisonForeground.Visible = false;
        }

        // Doom
        if (_creature.HasPower<Doom>())
        {
            _doomForeground.Visible = true;

            if (doomDamage > 0)
            {
                _doomForeground.Visible = true;
                float doomFgOffset = GetFgWidth(doomDamage) - MaxFgWidth;

                if (IsDoomLethal(doomDamage, poisonDamage))
                {
                    // Doom is lethal but poison is not
                    if (!IsPoisonLethal(poisonDamage))
                    {
                        _doomForeground.OffsetRight = _hpForeground.OffsetRight;
                        _hpForeground.Visible = false;
                    }
                    else
                    {
                        // Both Poison AND Doom are lethal. Don't render the red OR purple bar.
                        _hpForeground.Visible = false;
                        _doomForeground.Visible = false;
                    }
                }
                else
                {
                    _doomForeground.OffsetRight = doomFgOffset;
                    _hpForeground.Visible = true;
                }
            }
            else
            {
                _doomForeground.Visible = false;
            }
        }
        else
        {
            _doomForeground.Visible = false;
        }
    }

    /// <summary>
    /// Determines whether the Block icon should be visible + the color of the healthbar if you have Block.
    /// Note that the text on the healthbar is handled elsewhere -> RefreshText();
    /// </summary>
    private void RefreshBlockUi()
    {
        if (_creature.Block > 0 || _blockTrackingCreature is { Block: > 0 })
        {
            _blockOutline.Visible = true;
            _hpForeground.SelfModulate = _blockHpForegroundColor;

            // Only show block amount if THIS health bar's creature has block, not the block-tracking creature.
            if (_creature.Block > 0)
            {
                _blockContainer.Visible = true;
                _blockLabel.Text = _creature.Block.ToString();
            }
        }
        else
        {
            if (_blockContainer.Visible)
            {
                NBlockBrokenVfx? vfx = NBlockBrokenVfx.Create();

                if (vfx != null)
                {
                    this.AddChildSafely(vfx);
                    vfx.GlobalPosition = _blockContainer.GlobalPosition + _blockContainer.Size * 0.5f;
                }
            }

            _blockContainer.Visible = false;
            _blockOutline.Visible = false;
            _hpForeground.SelfModulate = _redForegroundColor;
        }
    }

    /// <summary>
    /// The text that displays HP. e.g. 72/72
    /// This logic controls this text's color and its outline as it's
    /// affected by having Block, being in lethal due to Poison/Doom, and Invincibility.
    /// </summary>
    private void RefreshText()
    {
        if (_creature.CurrentHp <= 0)
        {
            _hpLabel.AddThemeColorOverride("font_color", _defaultFontColor);
            _hpLabel.AddThemeColorOverride("font_outline_color", _defaultFontOutlineColor);
            _hpLabel.Text = _healthBarDead.GetRawText();
            return;
        }

        // If the creature is Invincible, render the infinity symbol (it's a texture).
        if (_creature.ShowsInfiniteHp)
        {
            _infinityTex.Visible = _creature.IsAlive;
            _hpLabel.Visible = !_infinityTex.Visible;
            return;
        }

        _hpLabel.Visible = true;

        Color fontColor;
        Color outlineColor;

        int poisonDamage = _creature.GetPower<Poison>()?.CalculateTotalDamageNextTurn() ?? -1;
        int doomDamage = _creature.HasPower<Doom>() ? _creature.GetPowerAmount<Doom>() : -1;

        // Priority is Poison lethality > Doom lethality > Block > Default
        if (IsPoisonLethal(poisonDamage))
        {
            fontColor = new("76FF40");
            outlineColor = new("074700");
        }
        else if (IsDoomLethal(doomDamage, poisonDamage))
        {
            fontColor = new("FB8DFF");
            outlineColor = new("2D1263");
        }
        else if (_creature.Block > 0 || _blockTrackingCreature is { Block: > 0 })
        {
            fontColor = _defaultFontColor;
            outlineColor = _blockOutlineColor;
        }
        else
        {
            fontColor = _defaultFontColor;
            outlineColor = _defaultFontOutlineColor;
        }

        _hpLabel.AddThemeColorOverride("font_color", fontColor);
        _hpLabel.AddThemeColorOverride("font_outline_color", outlineColor);
        _hpLabel.Text = $"{_creature.CurrentHp}/{_creature.MaxHp}";
    }

    private bool IsPoisonLethal(int poisonDamage)
    {
        if (poisonDamage == 0 || !_creature.HasPower<Poison>()) return false;

        return poisonDamage >= _creature.CurrentHp;
    }

    /// <summary>
    /// Checks if Doom is lethal when it's to trigger next. Poison damage is also incorporated as the
    /// creature's HP may drop at the start of their turn and their remaining HP may enter Doom range.
    /// </summary>
    /// <param name="doomDamage"></param>
    /// <param name="poisonDamage"></param>
    /// <returns></returns>
    private bool IsDoomLethal(int doomDamage, int poisonDamage)
    {
        if (doomDamage == 0 || !_creature.HasPower<Doom>()) return false;

        return doomDamage >= _creature.CurrentHp - poisonDamage;
    }

    /// <summary>
    /// Calculates the width of the foreground given an "amount" which is a ratio of this and the MaxHP.
    /// Example: If amount is currentHp. Then if you have 12 currentHP and 50 maxHp, this would return
    /// 0.24 (12/50) multiplied by the width of the foreground bar's total length. If the bar is 240px,
    /// then we would return 240 x 0.24 as a float.
    /// </summary>
    private float GetFgWidth(int amount)
    {
        if (_creature.MaxHp <= 0) return 0f;

        float fg = amount / (float)_creature.MaxHp * MaxFgWidth;
        return Math.Max(fg, _creature.CurrentHp > 0 ? _minSize : 0f);
    }

    public void FadeOutHpLabel(float duration, float finalAlpha)
    {
        _hpLabelFadeTween?.Kill();
        _hpLabelFadeTween = CreateTween();
        _hpLabelFadeTween.TweenProperty(_hpLabel, "modulate:a", finalAlpha, duration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    public void FadeInHpLabel(float duration)
    {
        _hpLabelFadeTween?.Kill();
        _hpLabelFadeTween = CreateTween();
        _hpLabelFadeTween.TweenProperty(_hpLabel, "modulate:a", 1f, duration);
    }

    public void AnimateInBlock(int oldBlock, int blockGain)
    {
        if (oldBlock != 0 || blockGain == 0) return;

        _blockContainer.Visible = true;

        if (SaveManager.Instance.SettingsSave.FastMode == FastModeType.Instant) return;

        _blockContainer.Modulate = StsColors.transparentWhite;
        _blockContainer.Position = _originalBlockPosition - _blockAnimOffset;

        _blockTween?.Kill();
        _blockTween = CreateTween().SetParallel();
        _blockTween.TweenProperty(_blockContainer, "modulate:a", 1f, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Sine);
        _blockTween.TweenProperty(_blockContainer, "position", _originalBlockPosition, 0.5)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Back);
    }

    /// <summary>
    /// See <see cref="NCreature.TrackBlockStatus"/> for details.
    /// </summary>
    public void TrackBlockStatus(Creature creature)
    {
        _blockTrackingCreature = creature;
    }
}
