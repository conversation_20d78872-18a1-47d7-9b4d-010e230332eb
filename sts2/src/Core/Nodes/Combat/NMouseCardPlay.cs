using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NMouseCardPlay : NCardPlay
{
    // The amount of pixels below the play space the player must drag a card before card play is canceled.
    private const float _allowExitingPlaySpaceBuffer = 25f;

    // If a player begins dragging the card from very low, we make the play zone lower so that they don't have to drag
    // the card as high. This "fake" play zone begins 100px above where they dragged the card.

    private const float _fakeLowerEnterPlayZoneDistance = 100f;

    // If the player begins to drag the card from high, set the "fake" play zone 100px above where they dragged the card.
    // that way they aren't immediately in the play zone.
    private const float _fakeUpperEnterPlayZoneDistance = 50f;

    // Percentage from the top of the screen that constitutes the play zone.
    private const float _playZoneScreenProportion = 0.75f;

    // Y coordinate below which we consider the card to be in the play zone.
    private float PlayZoneThreshold
    {
        get
        {
            float playZoneThreshold = _viewport.GetVisibleRect().Size.Y * _playZoneScreenProportion;
            if (_dragStartYPosition > playZoneThreshold)
            {
                return Mathf.Max(playZoneThreshold, _dragStartYPosition - _fakeLowerEnterPlayZoneDistance);
            }
            else
            {
                return Mathf.Min(playZoneThreshold, _dragStartYPosition - _fakeUpperEnterPlayZoneDistance);
            }
        }
    }

    // Y coordinate at which we started the last drag.
    private float _dragStartYPosition;
    private Creature? _target;
    private bool _isLeftMouseDown = true; // Assume that we hit left mouse to start targeting
    private CancellationTokenSource _cancellationTokenSource = default!;

    public static NMouseCardPlay Create(NHandCardHolder holder)
    {
        NMouseCardPlay ret = new();
        ret.Holder = holder;
        ret.Player = holder.CardModel!.Owner;
        return ret;
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent is not InputEventMouseButton buttonEvent) return;

        switch (buttonEvent.ButtonIndex)
        {
            case MouseButton.Left:
                if (buttonEvent.IsPressed())
                {
                    _isLeftMouseDown = true;
                }
                else if (buttonEvent.IsReleased())
                {
                    _isLeftMouseDown = false;
                }

                break;
            case MouseButton.Right:
                if (buttonEvent.IsPressed())
                {
                    CancelPlayCard();
                }

                break;
        }
    }

    public override void Start()
    {
        Holder.Hitbox.MouseFilter = Control.MouseFilterEnum.Ignore;
        _cancellationTokenSource = new CancellationTokenSource();
        TaskHelper.RunSafely(StartAsync());
    }

    private async Task StartAsync()
    {
        // 1. When you first start dragging the card, but before it's traveled high enough to enter the Play Space.
        //    This is the stage after you first start dragging the card, but before the targeting arrow appears for
        //    targeted cards (Strike) or the creature selection reticle appears for un-targeted cards (Defend).
        await StartCardDrag();

        // Make sure the card is playable.
        // It could be unplayable for a number of reasons, for example:
        // * You don't have enough energy.
        // * Some effect is blocking card play (like the Normality curse).
        if (!Card.CanPlay(out UnplayableReason reason, out AbstractModel? preventer))
        {
            CannotPlayThisCardFtueCheck(Card);
            CancelPlayCard();

            LocString? locString = reason.GetPlayerDialogueLine(preventer);

            if (locString != null)
            {
                NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(
                    NThoughtBubbleVfx.Create(locString.GetFormattedText(), Card.Owner.Creature, 1f)
                );
            }

            return;
        }

        // 2. When the card has traveled high enough to enter the Play Space.
        //    This is the stage _after_ the targeting arrow and/or creature reticle appears.
        CardNode.CardHighlight.AnimFlash();
        TargetMode targetMode = _isLeftMouseDown ? TargetMode.ReleaseMouseToTarget : TargetMode.ClickMouseToTarget;
        await TargetSelection(targetMode);

        // if card play has been cancelled, don't try to play the card
        if (!_cancellationTokenSource.IsCancellationRequested)
        {
            // once target selection has been finished, try to play the card
            TryPlayCard(_target);
        }
    }

    private async Task StartCardDrag()
    {
        NDebugAudioManager.Instance?.Play(TmpSfx.cardSelect, 0.5f);
        NHoverTipSet.Remove(Holder);
        _dragStartYPosition = _viewport.GetMousePosition().Y;

        do
        {
            await LerpToMouse(Holder);
        } while (!HasCardEnteredPlaySpace() && !_cancellationTokenSource.IsCancellationRequested);
    }

    private async Task TargetSelection(TargetMode targetMode)
    {
        TryShowEvokingOrbs();
        CardNode.CardHighlight.AnimFlash();

        if (Card.TargetEnemy == UiTargetEnemy.Any)
        {
            await SingleCreatureTargeting(targetMode, ActionTarget.AnyEnemy);
        }
        else if (Card.TargetPlayer == UiTargetPlayer.Ally)
        {
            await SingleCreatureTargeting(targetMode, ActionTarget.AnyAlly);
        }
        else
        {
            await MultiCreatureTargeting(targetMode);
        }
    }

    // For cards that target a single enemy, drag the mouse to the creature they want to target.
    private async Task SingleCreatureTargeting(TargetMode targetMode, ActionTarget targetType)
    {
        if (_cancellationTokenSource.IsCancellationRequested) return;
        CenterCard();

        // Set up the targeting manager. This will control the drawing of the targeting arrow.
        NTargetManager targetManager = NTargetManager.Instance;

        targetManager.Connect(NTargetManager.SignalName.CreatureHovered, Callable.From<NCreature>(OnCreatureHover));
        targetManager.Connect(NTargetManager.SignalName.CreatureUnhovered, Callable.From<NCreature>(OnCreatureUnhover));

        // This hands off control to the targeting manager. Control will return when the targeting manager
        // determines you've chosen a target OR you've canceled card play.
        // We set the starting mode to ReleaseToTarget, but if the target manager sees an immediate release, it'll
        // update it to ClickToTarget.
        targetManager.StartTargeting(targetType, CardNode, targetMode, HasCardExitedPlaySpace, null);
        Node? target = await targetManager.SelectionFinished();

        if (target != null)
        {
            _target = target switch
            {
                NCreature creatureNode => creatureNode.Entity,
                NMultiplayerPlayerState playerState => playerState.Player.Creature,
                _ => throw new ArgumentOutOfRangeException(nameof(target), target, null),
            };
        }

        targetManager.Disconnect(NTargetManager.SignalName.CreatureHovered, Callable.From<NCreature>(OnCreatureHover));
        targetManager.Disconnect(NTargetManager.SignalName.CreatureUnhovered, Callable.From<NCreature>(OnCreatureUnhover));
    }

    // For cards that target all/random enemies, show a selection reticle on all enemy creatures.
    private async Task MultiCreatureTargeting(TargetMode targetMode)
    {
        if (Card.TargetEnemy is UiTargetEnemy.All or UiTargetEnemy.Random)
        {
            IReadOnlyList<Creature> targets = Card.CombatState!.HittableEnemies;

            // If there's only one enemy, pretend they're the target.
            if (targets.Count == 1)
            {
                CardNode.SetPreviewTarget(targets[0]);
            }

            CardNode.UpdateVisuals(CardNode.Model?.Pile?.Type ?? CardPileTarget.None);

            foreach (Creature enemy in targets)
            {
                // HACKY: We need to set the ? here because there can be a gap between when an enemy is
                // first spawned in and when its node is set up. For example: AxeBots will apply Stock
                // to themselves before they spawn, leaving a split second where the creature is up but the
                // node is not.
                // This also means creatures spawned after the reticle is set don't have the reticle over them
                // which is incorrect, but for now its such a wierd edge case that its probably ok not to worry
                // about it for now.
                NCombatRoom.Instance!.GetCreatureNode(enemy)?.SelectionReticle.OnSelect();
            }
        }

        switch (Card.TargetPlayer)
        {
            case UiTargetPlayer.AllAllies:
            {
                IEnumerable<Creature> targets = Card.CombatState!.PlayerCreatures.Where(c => c.IsAlive);
                foreach (Creature target in targets)
                {
                    NCombatRoom.Instance!.GetCreatureNode(target)?.SelectionReticle.OnSelect();
                }
                break;
            }
            case UiTargetPlayer.Self:
                NCombatRoom.Instance!.GetCreatureNode(Card.Owner.Creature)?.SelectionReticle.OnSelect();
                break;
            case UiTargetPlayer.Osty when Card.Owner.Osty != null:
                NCombatRoom.Instance!.GetCreatureNode(Card.Owner.Osty)?.SelectionReticle.OnSelect();
                break;
        }

        Func<bool> shouldFinishTargeting = targetMode == TargetMode.ReleaseMouseToTarget ? () => !_isLeftMouseDown : () => _isLeftMouseDown;

        do
        {
            await LerpToMouse(Holder);
        } while (!shouldFinishTargeting.Invoke() && !_cancellationTokenSource.IsCancellationRequested && !HasCardExitedPlaySpace());

        if (HasCardExitedPlaySpace())
        {
            CancelPlayCard();
        }
    }

    protected override void OnCancelPlayCard()
    {
        Holder.Hitbox.MouseFilter = Control.MouseFilterEnum.Stop;
        _cancellationTokenSource.Cancel();
    }

    private async Task LerpToMouse(NHandCardHolder cardHolder)
    {
        cardHolder.SetTargetPosition(_viewport.GetMousePosition());
        await ToSignal(GetTree(), SceneTree.SignalName.ProcessFrame); // Let 1 frame go by.
    }

    /// <summary>
    /// Has the card moved up into the Play Space?
    /// This happens when the card was near the bottom of the screen (meaning you just started dragging it) and then you
    /// drag it up higher to start targeting it.
    /// </summary>
    private bool HasCardEnteredPlaySpace() => _viewport.GetMousePosition().Y < PlayZoneThreshold;

    /// <summary>
    /// Has the card moved down out of the Play Space?
    /// This happens when the card was in the Play Space (meaning you were targeting it) and then you drag it back down
    /// to cancel the play.
    ///
    /// Note: We use a greater value for the "exit" boundary than the "enter" boundary because we want you to have to
    /// move the card down a meaningful amount before we stop targeting.
    /// </summary>
    private bool HasCardExitedPlaySpace()
    {
        return _viewport.GetMousePosition().Y > PlayZoneThreshold + _allowExitingPlaySpaceBuffer;
    }
}
