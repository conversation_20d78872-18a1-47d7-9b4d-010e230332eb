using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NEnergyCounter : Control
{
    private const string _darkenedMatPath = "res://materials/ui/energy_orb_dark.tres";

    private Player _player = default!;

    private Label _label = default!;
    private Control _layers = default!;
    private Control _rotationLayers = default!;
    private CpuParticles2D _backParticles = default!;
    private CpuParticles2D _frontParticles = default!;

    private HoverTip _hoverTip;

    // Anim in/out variables
    private Tween? _animInTween;
    private Tween? _animOutTween;
    private const float _animDuration = 0.6f;
    private static readonly Vector2 _showPosition = Vector2.Zero;
    private static readonly Vector2 _hidePosition = new(-480f, 128f);

    private int _previousEnergy;

    public static IEnumerable<string> AssetPaths => [_darkenedMatPath];

    public static NEnergyCounter? Create(Player player)
    {
        if (TestMode.IsOn) return null;

        NEnergyCounter node = PreloadManager.Cache.GetScene(player.Character.EnergyCounterPath).Instantiate<NEnergyCounter>();
        node._player = player;
        return node;
    }

    public override void _Ready()
    {
        _label = GetNode<Label>("Label");
        _layers = GetNode<Control>("%Layers");
        _rotationLayers = GetNode<Control>("%RotationLayers");
        _backParticles = GetNode<CpuParticles2D>("%BurstBack")!;
        _frontParticles = GetNode<CpuParticles2D>("%BurstFront")!;

        LocString description = new("static_hover_tips", "ENERGY_COUNT.description");
        description.Add("energyPrefix", EnergyHelper.GetIconPrefix(_player.Character.CardPool));

        _hoverTip = new HoverTip(
            new LocString("static_hover_tips", "ENERGY_COUNT.title"),
            description
        );

        Connect(Control.SignalName.MouseEntered, Callable.From(OnHovered));
        Connect(Control.SignalName.MouseExited, Callable.From(OnUnhovered));

        RefreshLabel();
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        _player.PlayerCombatState!.EnergyChanged += OnEnergyChanged;
    }

    public override void _ExitTree()
    {
        base._ExitTree();

        _player.PlayerCombatState!.EnergyChanged -= OnEnergyChanged;
    }

    private void OnHovered()
    {
        NHoverTipSet tip = NHoverTipSet.CreateAndShow(this, _hoverTip);
        tip.GlobalPosition = GlobalPosition + new Vector2(-70f, -200f);
    }

    private void OnUnhovered()
    {
        NHoverTipSet.Remove(this);
    }

    private void OnEnergyChanged(int _, int __)
    {
        RefreshLabel();
    }

    private void RefreshLabel()
    {
        PlayerCombatState playerCombatState = _player.PlayerCombatState!;
        _label.Text = $"{playerCombatState.Energy}/{playerCombatState.MaxEnergy}";
        _label.AddThemeColorOverride("font_color", playerCombatState.Energy == 0 ? StsColors.red : StsColors.cream);
        _label.AddThemeColorOverride("font_outline_color", playerCombatState.Energy == 0 ? StsColors.unplayableEnergyCostOutline : OutlineColor);

        Material? mat = playerCombatState.Energy == 0 ? PreloadManager.Cache.GetMaterial(_darkenedMatPath) : null;

        foreach (Control child in _layers.GetChildren().OfType<Control>())
        {
            child.Material = mat;
        }

        foreach (Control child in _rotationLayers.GetChildren().OfType<Control>())
        {
            child.Material = mat;
        }

        _layers.Modulate = playerCombatState.Energy == 0 ? Colors.DarkGray : Colors.White;

        if (_previousEnergy < playerCombatState.Energy)
        {
            _frontParticles.Emitting = true;
            _backParticles.Emitting = true;
        }

        _previousEnergy = playerCombatState.Energy;
    }

    private Color OutlineColor => _player.Character.EnergyLabelOutlineColor;

    public override void _Process(double delta)
    {
        float rotSpeed = _player.PlayerCombatState!.Energy == 0 ? 5f : 30f;

        for (int i = 0; i < _rotationLayers.GetChildCount(); i++)
        {
            _rotationLayers.GetChild<Control>(i).RotationDegrees += (float)delta * rotSpeed * (i + 1);
        }
    }

    public void AnimIn()
    {
        _animOutTween?.Kill();
        _animInTween = CreateTween();
        Position = _hidePosition;
        _animInTween.TweenProperty(this, "position", _showPosition, _animDuration)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
    }

    public void AnimOut()
    {
        _animInTween?.Kill();
        _animOutTween = CreateTween();
        Position = _showPosition;
        _animOutTween.TweenProperty(this, "position", _hidePosition, _animDuration)
            .SetEase(Tween.EaseType.In)
            .SetTrans(Tween.TransitionType.Back);
    }
}
