using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// An abstract base class in charge of managing the logic for playing a card from your hand.
/// Inherited classes manage card play behavior for different types of inputs.
/// Is created and destroyed on demand.
/// </summary>
public abstract partial class NCardPlay : Node

{
    private static int _totalCardsPlayedForFtue;
    private const int _numCardPlayedUntilDisableFtue = 8;

    [Signal]
    public delegate void FinishedEventHandler(bool success);

    public NHandCardHolder Holder { get; protected set; } = default!;

    protected NCard CardNode => Holder.CardNode!;

    protected CardModel Card => CardNode.Model!;

    protected NCreature CardOwnerNode => NCombatRoom.Instance!.GetCreatureNode(Card.Owner.Creature)!;

    public Player Player { get; protected set; } = default!;

    protected Viewport _viewport = default!;

    private bool _isTryingToPlayCard;

    public override void _Ready()
    {
        _viewport = GetViewport();
    }

    public abstract void Start();

    protected void TryPlayCard(Creature? target)
    {
        if (Card.TargetEnemy == UiTargetEnemy.Any && target == null)
        {
            CancelPlayCard();
            return;
        }

        if (Card.TargetPlayer == UiTargetPlayer.Ally && target == null)
        {
            CancelPlayCard();
            return;
        }

        if (!Holder.CardModel!.CanPlayTargeting(target))
        {
            CannotPlayThisCardFtueCheck(Holder.CardModel);
            CancelPlayCard();
            return;
        }

        CardModel card = Card;
        bool success;
        _isTryingToPlayCard = true;
        if (card.TargetEnemy == UiTargetEnemy.Any || card.TargetPlayer == UiTargetPlayer.Ally)
        {
            // If it's a targeted card (like Strike), try to play it with the specified target.
            success = card.TryManualPlay(target);
        }
        else
        {
            // If it's an un-targeted card (like Defend), try to play it with no target.
            success = card.TryManualPlay(null);
        }

        _isTryingToPlayCard = false;

        if (success)
        {
            // If the card play succeeded, clean up this card play.

            AutoDisableCannotPlayCardFtueCheck();

            // If the card is targeted and queued, then it is currently obscuring the cards in the player's hand. Move
            // the card up out of the way to let the player queue more cards. Note that this is overridden by
            // TryManualPlay if the card is being played.
            if (card.TargetEnemy == UiTargetEnemy.Any && Holder.IsInsideTree())
            {
                Vector2 windowSize = GetViewport().GetVisibleRect().Size;
                Holder.SetTargetPosition(new Vector2(
                    windowSize.X / 2f,
                    windowSize.Y - Holder.Size.Y)
                );
            }

            Cleanup();
            EmitSignal(SignalName.Finished, true);
            NCombatRoom.Instance?.Ui.Hand.TryGrabFocus();
        }
        else
        {
            // If the card play failed (like if you didn't have enough energy), cancel this card play.
            CancelPlayCard();
        }
    }

    public void CancelPlayCard()
    {
        // if we are in the middle of trying to play the card, don't cancel the card play just yet.
        if (_isTryingToPlayCard) return;

        Cleanup();
        EmitSignal(SignalName.Finished, false);
        OnCancelPlayCard();
    }

    protected virtual void OnCancelPlayCard()
    {
        //no-op
    }

    protected virtual void Cleanup()
    {
        // Hide any creature selection reticles we showed during targeting.
        foreach (NCreature creatureNode in NCombatRoom.Instance!.CreatureNodes)
        {
            creatureNode.SelectionReticle.OnDeselect();
        }

        // Update the card text in case the card was canceled while targeting an enemy.
        // Can't use CardNode because if the card was already played, holder's card node becomes null
        Holder.CardNode?.SetPreviewTarget(null);

        HideEvokingOrbs();
        QueueFree();
    }

    protected void OnCreatureHover(NCreature creature)
    {
        Card.RecalculateValues(creature.Entity);
        CardNode.SetPreviewTarget(creature.Entity);
    }

    protected void OnCreatureUnhover(NCreature _)
    {
        Card.RecalculateValues();
        CardNode.SetPreviewTarget(null);
    }

    // Centers the cards at the bottom of the screen so players can focus on selecting a creature to target.
    protected void CenterCard()
    {
        const float cardTargetScale = 0.75f;

        // Position the card near the bottom-middle of the screen so we can start drawing a targeting arrow from it.
        Vector2 windowSize = _viewport.GetVisibleRect().Size;
        Holder.SetTargetPosition(new Vector2(
            windowSize.X / 2f,
            windowSize.Y - Holder.Hitbox.Size.Y * cardTargetScale / 2f)
        );
        Holder.SetTargetScale(Vector2.One * cardTargetScale);
    }

    protected void CannotPlayThisCardFtueCheck(CardModel card)
    {
        if (!SaveManager.Instance.SeenFtue(NCannotPlayCardFtue.id) &&
            // Only display this FTUE if we couldn't play the card specifically because we didn't have enough energy
            !card.CanPlay(out UnplayableReason reason, out _) &&
            reason == UnplayableReason.EnergyCostTooHigh)
        {
            // Player attempted to play a card when they can't. We must help them!
            NModalContainer.Instance!.Add(NCannotPlayCardFtue.Create()!);
            SaveManager.Instance.MarkFtueAsComplete(NCannotPlayCardFtue.id);
        }
    }

    private void AutoDisableCannotPlayCardFtueCheck()
    {
        _totalCardsPlayedForFtue++;
        if (_totalCardsPlayedForFtue == _numCardPlayedUntilDisableFtue && !SaveManager.Instance.SeenFtue(NCannotPlayCardFtue.id))
        {
            Log.Info("Cannot play cards FTUE was disabled, the player never saw it!!");
            SaveManager.Instance.MarkFtueAsComplete(NCannotPlayCardFtue.id);
        }
    }

    protected void TryShowEvokingOrbs()
    {
        // Check to see if we need to update the orb UI.
        if (Card.EvokesOrbs)
        {
            NCreature cardOwnerNode = NCombatRoom.Instance!.GetCreatureNode(Player.Creature)!;
            cardOwnerNode.OrbManager!.UpdateVisuals(true);
        }
    }

    private void HideEvokingOrbs()
    {
        NCreature cardOwnerNode = NCombatRoom.Instance!.GetCreatureNode(Player.Creature)!;
        cardOwnerNode.OrbManager!.UpdateVisuals(false);
    }
}
