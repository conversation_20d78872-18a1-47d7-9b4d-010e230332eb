using System;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Multiplayer;
using MegaCrit.Sts2.Core.Nodes.RestSite;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NTargetManager : Node2D
{
    [Signal]
    public delegate void CreatureHoveredEventHandler(NCreature creature);

    [Signal]
    public delegate void CreatureUnhoveredEventHandler(NCreature creature);

    [Signal]
    public delegate void NodeHoveredEventHandler(Node node);

    [Signal]
    public delegate void NodeUnhoveredEventHandler(Node node);

    [Signal]
    public delegate void TargetingBeganEventHandler();

    [Signal]
    public delegate void TargetingEndedEventHandler();

    public static NTargetManager Instance => NClimb.Instance!.GlobalUi.TargetManager;

    private NTargetingArrow _targetingArrow = default!;

    private TaskCompletionSource<Node?>? _completionSource;
    public bool IsInSelection => _targetMode != TargetMode.None;

    private Func<bool>? _exitEarlyCondition;
    private Func<Node, bool>? _nodeFilter;
    private TargetMode _targetMode = TargetMode.None;

    private ActionTarget _validTargets;

    private Node? HoveredNode { get; set; }

    /// <summary>
    /// This is the frame on which the last targeting was cancelled or finished as reported by SceneTree.GetFrame.
    /// </summary>
    public long LastTargetingFinishedFrame { get; set; }

    public override void _Ready()
    {
        _targetingArrow = GetNode<NTargetingArrow>("TargetingArrow");
    }

    public override void _EnterTree()
    {
        base._EnterTree();

        CombatManager.Instance.CombatEnded += OnCombatEnd;
    }

    public override void _ExitTree()
    {
        base._ExitTree();
        CombatManager.Instance.CombatEnded -= OnCombatEnd;
    }

    public override void _Input(InputEvent inputEvent)
    {
        if (!IsInSelection) return;

        bool selectionComplete = false;
        bool cancel = false;

        // mouse input
        if (inputEvent is InputEventMouseButton buttonEvent)
        {
            switch (buttonEvent.ButtonIndex)
            {
                case MouseButton.Left:
                    if (buttonEvent.IsReleased())
                    {
                        switch (_targetMode)
                        {
                            case TargetMode.ReleaseMouseToTarget:
                                if (HoveredNode != null)
                                {
                                    selectionComplete = true;
                                }
                                else
                                {
                                    // All card drags start as TargetMode.ReleaseToTarget.
                                    // If we drag-released on nothing, we assume it was a release from clicking on a
                                    // card, so we switch to ClickToTarget.
                                    _targetMode = TargetMode.ClickMouseToTarget;
                                }

                                break;
                            case TargetMode.ClickMouseToTarget:
                                selectionComplete = true;
                                break;
                        }
                    }

                    break;
                case MouseButton.Right:
                    selectionComplete = buttonEvent.IsPressed();
                    cancel = true;
                    break;
            }
        }

        // controller input
        if (inputEvent is InputEventAction actionEvent)
        {
            if (actionEvent.IsActionPressed(MegaInput.select) && HoveredNode is NCreature)
            {
                selectionComplete = true;
                cancel = false;
            }
            else if (actionEvent.IsActionPressed(MegaInput.cancel))
            {
                selectionComplete = true;
                cancel = true;
            }
        }

        if (_exitEarlyCondition != null && _exitEarlyCondition.Invoke())
        {
            selectionComplete = true;
            cancel = true;
        }

        if (selectionComplete)
        {
            FinishTargeting(cancel);
        }
    }

    public override void _Process(double delta)
    {
        if (_exitEarlyCondition != null && _exitEarlyCondition.Invoke())
        {
            FinishTargeting(true);
        }
    }

    private void OnCombatEnd(CombatRoom _)
    {
        if (_exitEarlyCondition != null)
        {
            FinishTargeting(true);
        }
    }

    private void FinishTargeting(bool cancel)
    {
        _exitEarlyCondition = null;
        _completionSource!.SetResult(cancel ? null : HoveredNode);
        LastTargetingFinishedFrame = GetTree().GetFrame();
        EmitSignal(SignalName.TargetingEnded);

        _targetMode = TargetMode.None;
        _targetingArrow.StopDrawing();

        if (HoveredNode is NCreature creature)
        {
            creature.SelectionReticle.OnDeselect();
        }
        else if (HoveredNode is NRestSiteCharacter restSiteCharacter)
        {
            restSiteCharacter.Deselect();
        }

        HoveredNode = null;

        ClimbManager.Instance.InputSynchronizer.SyncLocalIsTargeting(false);
    }

    public async Task<Node?> SelectionFinished()
    {
        return await _completionSource!.Task;
    }

    public void StartTargeting(ActionTarget validTargets, Vector2 startPosition, TargetMode startingMode, Func<bool>? exitEarlyCondition, Func<Node, bool>? nodeFilter)
    {
        if (!validTargets.IsSingleTarget()) throw new InvalidOperationException($"Tried to begin targeting with invalid ActionTarget {validTargets}!");
        _validTargets = validTargets;
        _targetingArrow.StartDrawingFrom(startPosition, startingMode == TargetMode.Controller);
        _completionSource = new TaskCompletionSource<Node?>();
        _exitEarlyCondition = exitEarlyCondition;
        _nodeFilter = nodeFilter;
        _targetMode = startingMode;

        EmitSignal(SignalName.TargetingBegan);
        ClimbManager.Instance.InputSynchronizer.SyncLocalIsTargeting(true);

        // If a creature is already hovered when targeting begins, we want to hover the creature.
        foreach (NCreature creature in NCombatRoom.Instance?.CreatureNodes ?? [])
        {
            creature.OnTargetingStarted();
        }
    }

    public void StartTargeting(ActionTarget validTargets, Control control, TargetMode startingMode, Func<bool>? exitEarlyCondition, Func<Node, bool>? nodeFilter)
    {
        if (!validTargets.IsSingleTarget()) throw new InvalidOperationException($"Tried to begin targeting with invalid ActionTarget {validTargets}!");
        _validTargets = validTargets;
        _targetingArrow.StartDrawingFrom(control, startingMode == TargetMode.Controller);
        _completionSource = new TaskCompletionSource<Node?>();
        _exitEarlyCondition = exitEarlyCondition;
        _nodeFilter = nodeFilter;
        _targetMode = startingMode;

        EmitSignal(SignalName.TargetingBegan);
        ClimbManager.Instance.InputSynchronizer.SyncLocalIsTargeting(true);

        // If a creature is already hovered when targeting begins, we want to hover the creature.
        foreach (NCreature creature in NCombatRoom.Instance?.CreatureNodes ?? [])
        {
            creature.OnTargetingStarted();
        }
    }

    public bool AllowedToTargetNode(Node node)
    {
        if (_nodeFilter != null && !_nodeFilter.Invoke(node)) return false;

        if (node is NCreature creature)
        {
            return AllowedToTargetCreature(creature.Entity);
        }
        else if (node is NMultiplayerPlayerState playerState)
        {
            return AllowedToTargetCreature(playerState.Player.Creature);
        }

        return true;
    }

    private bool AllowedToTargetCreature(Creature creature)
    {
        switch (_validTargets)
        {
            case ActionTarget.AnyEnemy:
                if (creature.Side != CombatSide.Enemy) return false;
                break;
            case ActionTarget.AnyPlayer:
                if (!creature.IsPlayer || creature.IsDead) return false;
                break;
            case ActionTarget.AnyAlly:
                if (!creature.IsPlayer || creature.IsDead) return false;
                if(LocalContext.IsMe(creature.Player)) return false;
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(_validTargets), _validTargets, null);
        }

        return true;
    }

    public void OnNodeHovered(Node node)
    {
        if (!IsInSelection) return;
        if (!AllowedToTargetNode(node)) return;

        switch (node)
        {
            case NCreature creature:
                OnCreatureHovered(creature);
                break;
            default:
                HoveredNode = node;
                _targetingArrow.SetHighlightingOn(false);
                EmitSignal(SignalName.NodeHovered, node);
                break;
        }
    }

    public void OnNodeUnhovered(Node node)
    {
        if (!IsInSelection) return;
        if (!AllowedToTargetNode(node)) return;

        switch (node)
        {
            case NCreature creature:
                OnCreatureUnhovered(creature);
                break;
            default:
                HoveredNode = null;
                _targetingArrow.SetHighlightingOff();
                EmitSignal(SignalName.NodeUnhovered, node);
                break;
        }
    }

    private void OnCreatureHovered(NCreature creature)
    {
        if (HookBus.Instance.ShouldAllowTargeting(creature.Entity, out AbstractModel? preventer))
        {
            HoveredNode = creature;

            _targetingArrow.SetHighlightingOn(creature.Entity.IsEnemy);
            creature.SelectionReticle.OnSelect();
            EmitSignal(SignalName.CreatureHovered, creature);

            if (_targetMode == TargetMode.Controller)
            {
                _targetingArrow.UpdateDrawingTo(creature.VfxSpawnPosition);
            }
        }
        else
        {
            TaskHelper.RunSafely(preventer!.AfterTargetingBlockedVfx(creature.Entity));
        }
    }

    private void OnCreatureUnhovered(NCreature creature)
    {
        EmitSignal(SignalName.CreatureUnhovered, creature);

        if (HoveredNode == creature)
        {
            HoveredNode = null;
        }

        _targetingArrow.SetHighlightingOff();

        if (_targetMode != TargetMode.None)
        {
            creature.SelectionReticle.OnDeselect();
        }
    }
}
