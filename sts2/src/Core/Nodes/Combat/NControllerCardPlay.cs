using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Cards.Holders;
using MegaCrit.Sts2.Core.Nodes.GodotExtensions;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NControllerCardPlay : NCardPlay
{
    [Signal]
    public delegate void ConfirmedEventHandler();

    [Signal]
    public delegate void CanceledEventHandler();

    public override void _Input(InputEvent inputEvent)
    {
        if (inputEvent is not InputEventAction actionEvent) return;

        if (actionEvent.IsActionPressed(MegaInput.select))
        {
            EmitSignal(SignalName.Confirmed);
        }

        if (actionEvent.IsActionPressed(MegaInput.cancel))
        {
            EmitSignal(SignalName.Canceled);
        }
    }

    public static NControllerCardPlay Create(NHandCardHolder holder)
    {
        NControllerCardPlay ret = new();
        ret.Holder = holder;
        ret.Player = holder.CardModel!.Owner;
        return ret;
    }

    public override void Start()
    {
        NAudioManager.Instance?.PlayOneShot(TmpSfx.cardSelect);
        NHoverTipSet.Remove(Holder);

        if (!Card.CanPlay(out UnplayableReason reason, out AbstractModel? preventer))
        {
            CannotPlayThisCardFtueCheck(Card);
            CancelPlayCard();

            LocString? locString = reason.GetPlayerDialogueLine(preventer);

            if (locString != null)
            {
                NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(
                    NThoughtBubbleVfx.Create(locString.GetFormattedText(), Card.Owner.Creature, 1.0)
                );
            }
        }
        else
        {
            TryShowEvokingOrbs();
            CardNode.CardHighlight.AnimFlash();
            CenterCard();

            if (Card.TargetEnemy == UiTargetEnemy.Any)
            {
                TaskHelper.RunSafely(SingleCreatureTargeting(ActionTarget.AnyEnemy));
            }
            else if (Card.TargetPlayer == UiTargetPlayer.Ally)
            {
                TaskHelper.RunSafely(SingleCreatureTargeting(ActionTarget.AnyAlly));
            }
            else
            {
                MultiCreatureTargeting();
            }
        }
    }

    private async Task SingleCreatureTargeting(ActionTarget targetType)
    {
        // Set up the targeting manager. This will control the drawing of the targeting arrow.
        NTargetManager targetManager = NTargetManager.Instance;

        targetManager.Connect(NTargetManager.SignalName.CreatureHovered, Callable.From<NCreature>(OnCreatureHover));
        targetManager.Connect(NTargetManager.SignalName.CreatureUnhovered, Callable.From<NCreature>(OnCreatureUnhover));

        // This hands off control to the targeting manager. Control will return when the targeting manager
        // determines you've chosen a target OR you've canceled card play.
        // We set the starting mode to ReleaseToTarget, but if the target manager sees an immediate release, it'll
        // update it to ClickToTarget.
        targetManager.StartTargeting(targetType, CardNode, TargetMode.Controller, () => !IsInstanceValid(this), null);

        Creature owner = Card.Owner.Creature;
        List<Creature> validCreatures = owner.CombatState!.GetOpponentsOf(owner).Where(c => c.IsAlive).ToList();
        NCombatRoom.Instance!.RestrictControllerNavigation(validCreatures.Select(c => NCombatRoom.Instance.GetCreatureNode(c)!.Hitbox));
        NCombatRoom.Instance.GetCreatureNode(validCreatures.First())!.Hitbox.TryGrabFocus();

        NCreature? target = (NCreature?)(await targetManager.SelectionFinished());

        // if we were interrupted (i.e. hot-keyed into a different screen) and the instance was ended early
        // TODO: revisit this, see if we can solve this from the the start targeting side...
        if (IsInstanceValid(this))
        {
            targetManager.Disconnect(NTargetManager.SignalName.CreatureHovered, Callable.From<NCreature>(OnCreatureHover));
            targetManager.Disconnect(NTargetManager.SignalName.CreatureUnhovered, Callable.From<NCreature>(OnCreatureUnhover));

            if (target != null)
            {
                TryPlayCard(target.Entity);
            }
            else
            {
                CancelPlayCard();
            }
        }
    }

    private void MultiCreatureTargeting()
    {
        NCombatRoom.Instance!.RestrictControllerNavigation([]);

        // For cards that target all/random enemies, show a selection reticle on all enemy creatures.
        if (Card.TargetEnemy is UiTargetEnemy.All or UiTargetEnemy.Random)
        {
            IReadOnlyList<Creature> targets = Card.CombatState!.HittableEnemies;

            if (targets.Count == 1)
            {
                CardNode.SetPreviewTarget(targets[0]);
            }

            CardNode.UpdateVisuals(Card.Pile?.Type ?? CardPileTarget.None);

            foreach (Creature enemy in targets)
            {
                // HACKY: We need to set the ? here because there can be a gap between when an enemy is
                // first spawned in and when its node is set up. For example: AxeBots will apply Stock
                // to themselves before they spawn, leaving a split second where the creature is up but the
                // node is not.
                // This also means creatures spawned after the reticle is set don't have the reticle over them
                // which is incorrect, but for now its such a wierd edge case that its probably ok not to worry
                // about it for now.
                NCombatRoom.Instance.GetCreatureNode(enemy)?.SelectionReticle.OnSelect();
            }
        }

        switch (Card.TargetPlayer)
        {
            case UiTargetPlayer.AllAllies:
            {
                IEnumerable<Creature> targets = Card.CombatState!.PlayerCreatures.Where(c => c.IsAlive);
                foreach (Creature target in targets)
                {
                    NCombatRoom.Instance.GetCreatureNode(target)?.SelectionReticle.OnSelect();
                }
                break;
            }
            case UiTargetPlayer.Self:
                NCombatRoom.Instance.GetCreatureNode(Card.Owner.Creature)?.SelectionReticle.OnSelect();
                break;
            case UiTargetPlayer.Osty when Card.Owner.Osty != null:
                NCombatRoom.Instance.GetCreatureNode(Card.Owner.Osty)?.SelectionReticle.OnSelect();
                break;
        }

        Connect(SignalName.Confirmed, Callable.From(() => { TryPlayCard(null); }));
        Connect(SignalName.Canceled, Callable.From(CancelPlayCard));
    }

    protected override void OnCancelPlayCard()
    {
        Holder.TryGrabFocus();
    }

    protected override void Cleanup()
    {
        base.Cleanup();
        NCombatRoom.Instance!.EnableControllerNavigation();
        NCombatRoom.Instance.Ui.Hand.OnFocus();
    }
}
