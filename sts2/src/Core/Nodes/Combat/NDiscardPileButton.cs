using Godot;
using MegaCrit.Sts2.Core.ControllerInput;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Localization;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

/// <summary>
/// Node script for the Discard Pile.
/// </summary>
public partial class NDiscardPileButton : NCombatCardPile
{
    protected override string Hotkey => MegaInput.viewDiscardPile;

    protected override CardPileTarget PileType => CardPileTarget.Discard;

    public override void _Ready()
    {
        ConnectSignals();
        _emptyPileMessage = new LocString("combat_messages", "OPEN_EMPTY_DISCARD");
    }

    protected override void SetAnimInOutPositions()
    {
        _showPosition = Position;
        _hidePosition = Position + new Vector2(150f, 100f);
    }
}
