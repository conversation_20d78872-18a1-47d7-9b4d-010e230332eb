using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Nodes.Combat;

public partial class NEnemyTurnBanner : Control
{
    private Label _label = default!;
    private static readonly string _scenePath = SceneHelper.GetScenePath("combat/enemy_turn_banner");

    public static IEnumerable<string> AssetPaths => [_scenePath];

    public static NEnemyTurnBanner? Create()
    {
        if (TestMode.IsOn) return null;
        if (NCombatUi.IsDebugHideTextVfx) return null;

        return PreloadManager.Cache.GetScene(_scenePath).Instantiate<NEnemyTurnBanner>();
    }

    public override void _Ready()
    {
        _label = GetNode<Label>("Label");
        _label.Text = new LocString("gameplay_ui", "ENEMY_TURN").GetFormattedText();
        Modulate = Colors.Transparent;

        TaskHelper.RunSafely(Display());
    }

    private async Task Display()
    {
        NDebugAudioManager.Instance?.Play(TmpSfx.startEnemyTurn);
        _label.Scale = Vector2.One * 2f;

        Tween tween = CreateTween();
        tween.SetParallel();

        // Fade in
        tween.TweenProperty(_label, "scale", Vector2.One, 0.75f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        tween.TweenProperty(this, "modulate:a", 1f, 1.3f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo);
        tween.Chain();

        // Fade out
        tween.TweenProperty(_label, "modulate", Colors.Red, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Expo)
            .FromCurrent();
        tween.TweenProperty(this, "modulate:a", 0f, 1f)
            .SetEase(Tween.EaseType.Out)
            .SetTrans(Tween.TransitionType.Cubic);

        await ToSignal(tween, Tween.SignalName.Finished);
        this.QueueFreeSafely();
    }
}