
using System;
using System.Net.Http;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.Daily;

/// <summary>
/// Static class for interfacing with MegaCrit's time server.
/// </summary>
public static class TimeServer
{
    private const string _timeServerUrl = "https://time.megacrit.com";
    private const int _maxRetry = 2;
    private const int _waitTimeCap = 4;

    private static readonly Logger _logger = new(nameof(TimeServer), LogType.Network);
    public static Task<TimeServerResult?>? RequestTimeTask { get; private set; }

    private static async Task<TimeServerResult?> RequestTime(string url)
    {
        using HttpClient client = new();
        Exception? exception = null;
        int retries = 0;

        while (retries < _maxRetry)
        {
            long? serverTime;

            try
            {
                serverTime = await RequestTimeInternal(client, url);
            }
            catch (Exception e)
            {
                // Don't log the full exception here since it gets rethrown at the bottom of the method if the entire thing fails
                Log.Warn($"Caught exception while requesting server time of type {e.GetType()}");
                exception = e;
                serverTime = null;
            }

            if (serverTime != null)
            {
                TimeServerResult result = new()
                {
                    serverTime = DateTimeOffset.FromUnixTimeSeconds(serverTime.Value),
                    localReceivedTime = DateTimeOffset.UtcNow
                };

                return result;
            }
            else
            {
                retries++;
                Log.Info($"Retries: {retries}/{_maxRetry}");

                if (retries < _maxRetry)
                {
                    long waitTime = Math.Min((long)Math.Pow(2, retries), _waitTimeCap);
                    await Task.Delay(TimeSpan.FromSeconds(waitTime));
                }
            }
        }

        _logger.Warn("Gave up trying to retrieve server time. Will use local time instead");

        if (exception != null)
        {
            throw exception;
        }

        return null;
    }

    private static async Task<long?> RequestTimeInternal(HttpClient client, string url)
    {
        HttpResponseMessage response = await client.GetAsync(url);
        string responseString = await response.Content.ReadAsStringAsync();

        if (response.IsSuccessStatusCode)
        {
            _logger.Info($"Successfully queried time server. Response: {responseString}");
            if (long.TryParse(responseString, out long parsedLong))
            {
                return parsedLong;
            }
        }
        else
        {
            _logger.Info($"Failed to retrieve server time. Status: {response.StatusCode} Response: {responseString}");
        }

        return null;
    }

    /// <summary>
    /// Begins fetching the time from the time server. After this, RequestTimeTask is non-null.
    /// </summary>
    public static Task<TimeServerResult?> FetchDailyTime()
    {
        RequestTimeTask = RequestTime(_timeServerUrl);
        return RequestTimeTask;
    }
}
