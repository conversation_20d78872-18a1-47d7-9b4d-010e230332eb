using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Leaderboard;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Daily;

/// <summary>
/// A little helper for uploading the score at the end of a daily run.
/// </summary>
public static class DailyClimbUtility
{
    /// <summary>
    /// Uploads a score to the daily leaderboard for the given time.
    /// </summary>
    public static async Task UploadScore(DateTimeOffset time, int score, List<SerializablePlayer> players)
    {
        List<ulong> playerIdsInClimb = players.Select(p => p.NetId).ToList();

        // If this is a singleplayer climb, then there will be a single net ID of 1 in the list. Substitute it here for
        // the true net ID of the player, because that's what we'd expect from a platform API
        if (playerIdsInClimb.Count == 1 && playerIdsInClimb[0] == 1)
        {
            playerIdsInClimb[0] = PlatformUtil.GetLocalPlayerId(LeaderboardManager.CurrentPlatform);
        }

        // First, check if the player has already uploaded a score. If they have, then don't upload another one.
        string leaderboardName = GetLeaderboardName(time, players.Count);
        ILeaderboardHandle? handle = await LeaderboardManager.GetLeaderboard(leaderboardName);
        bool shouldUploadScore = await ShouldUploadScore(handle, playerIdsInClimb);

        if (!shouldUploadScore)
        {
            // One of the players in the climb already has a score for the day. No upload.
            Log.Info($"Player already uploaded score for daily {time}, ignoring new score");
            return;
        }

        // Call this again to create the leaderboard if it doesn't exist
        handle = await LeaderboardManager.GetOrCreateLeaderboard(leaderboardName);

        // Upload the score
        await LeaderboardManager.UploadLocalScore(handle, score, playerIdsInClimb);
        Log.Info($"Uploaded score of {score} for daily {time} to leaderboard {leaderboardName}");
    }

    /// <summary>
    /// Figures out whether or not we should upload a score to the passed leaderboard.
    /// If any player in the climb has already submitted a score, then this returns false.
    /// </summary>
    public static async Task<bool> ShouldUploadScore(ILeaderboardHandle? handle, IReadOnlyList<ulong> playerIdsInClimb)
    {
        if (handle == null) return true;

        // A note on why this is the way it is:
        // If you have completed a multiplayer daily with anyone, even if it's not the group of people you're currently
        // with, the entire _group_ cannot submit a new daily score. When in a multiplayer climb, only the host submits
        // the real score, which gets associated with the player IDs of everyone in the lobby. Clients _also_ upload a
        // score, but it's an invalid negative score. So, to see if the current group of people can submit a new daily
        // score, check if anyone in the group has submitted a score.
        // This unfortunately falls over when the host saves & quits, then abandons the score. In that case, clients do
        // not upload their score, because they don't know that the host has abandoned the climb. For that, we will need
        // this task: https://linear.app/megacrit/issue/PRG-2608/investigate-creating-our-own-leaderboard-service
        List<LeaderboardEntry> entries = await LeaderboardManager.QueryLeaderboardForUsers(handle, playerIdsInClimb);

        // If no player in the climb has submitted a leaderboard score, then allow submitting a score
        return entries.Count <= 0;
    }

    /// <param name="dateTime">Date to retrieve the leaderboard for. All fields other than year/month/day are ignored.</param>
    /// <param name="playerCount">Count of players to retrieve the leaderboard for.</param>
    /// <returns>The name of the leaderboard used for the passed date and player count.</returns>
    public static string GetLeaderboardName(DateTimeOffset dateTime, int playerCount)
    {
        return $"{dateTime.Year}_{dateTime.Month}_{dateTime.Day}_{playerCount}p";
    }

    public static void UploadScoreIfNecessary(SerializableClimb serializableClimb, ulong playerId, bool isVictory)
    {
        if (serializableClimb.DailyTime == null) throw new InvalidOperationException($"Tried to upload daily score of a non-daily climb!");
        DateTimeOffset dailyTime = serializableClimb.DailyTime!.Value;
        int score = ScoreUtility.CalculateScore(serializableClimb, playerId, isVictory);
        TaskHelper.RunSafely(UploadScore(dailyTime, score, serializableClimb.Players));
    }
}
