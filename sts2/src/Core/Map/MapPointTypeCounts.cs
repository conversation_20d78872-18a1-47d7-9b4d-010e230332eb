using System.Collections.Generic;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Map;

public class MapPointTypeCounts
{
    public HashSet<MapPointType> PointTypesThatIgnoreRules { get; init; } = [];

    public int NumOfElites { get; init; }
    public int NumOfShops { get; init; }
    public int NumOfUnknowns { get; init; }
    public int NumOfRests { get; init; }

    /// <summary>
    /// Whether the map generation MapPointType assignment ignores the rules or follows the rules.
    /// </summary>
    public bool ShouldIgnoreMapPointRulesForMapPointType(MapPointType pointType) => PointTypesThatIgnoreRules.Contains(pointType);

    public MapPointTypeCounts(Rng rng)
    {
        NumOfElites = rng.NextInt(5, 7);
        NumOfShops = 2;
        NumOfUnknowns = rng.NextGaussianInt(12, 1, 10, 14);
        NumOfRests = rng.NextGaussianInt(5, 1, 3, 6);
    }
}
