using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Localization.DynamicVars;

public class DamageVar : DynamicVar
{
    public const string defaultName = "Damage";

    public ValueProp Props { get; }

    public DamageVar(decimal damage, ValueProp props) : base(defaultName, damage)
    {
        Props = props;
    }

    public DamageVar(string name, decimal damage, ValueProp props) : base(name, damage)
    {
        Props = props;
    }
}