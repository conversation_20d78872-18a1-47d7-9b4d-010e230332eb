using System;
using Godot;

namespace MegaCrit.Sts2.Core.Localization;

public partial class LocTextLabel : RichTextLabel
{
    [Export]
    private string? _localizationTable;

    [Export]
    private string? _localizationKey;

    private LocString? _locString;

    public string? LocalizationTable
    {
        get => _localizationTable;
        set
        {
            if (_localizationTable == value) return;
            _localizationTable = value;
            _locString = null; // Invalidate the LocString cache
            UpdateLocalization();
        }
    }

    public string? LocalizationKey
    {
        get => _localizationKey;
        set
        {
            if (_localizationKey == value) return;
            _localizationKey = value;
            _locString = null; // Invalidate the LocString cache
            UpdateLocalization();
        }
    }

    private void UpdateLocalization()
    {
        if (_localizationTable == null) throw new InvalidOperationException("_localizationTable is null.");
        if (_localizationKey == null) throw new InvalidOperationException("_localizationKey is null.");

        _locString ??= new LocString(_localizationTable, _localizationKey);
        Text = _locString.GetFormattedText();
    }

    public override void _Ready()
    {
        UpdateLocalization();
    }
}
