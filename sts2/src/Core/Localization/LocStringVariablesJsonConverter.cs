using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Localization;

/// <summary>
/// Serializes the variables of a LocString to JSON.
/// Not all DynamicVar types are supported, so use with caution.
/// </summary>
public class LocStringVariablesJsonConverter : JsonConverter<Dictionary<string, object>>
{
    public override Dictionary<string, object> Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        Dictionary<string, SerializableDynamicVar>? deserialized = JsonSerializer.Deserialize<Dictionary<string, SerializableDynamicVar>>(ref reader, options);
        if (deserialized == null) throw new InvalidOperationException("Could not read LocString variables");

        Dictionary<string, object> varDict = [];

        foreach (KeyValuePair<string, SerializableDynamicVar> pair in deserialized)
        {
            varDict[pair.Key] = pair.Value.ToDynamicVar(pair.Key);
        }

        return varDict;
    }

    public override void Write(Utf8JsonWriter writer, Dictionary<string, object> varDict, JsonSerializerOptions options)
    {
        Dictionary<string, SerializableDynamicVar> serializableDict = [];

        foreach (KeyValuePair<string, object> pair in varDict)
        {
            SerializableDynamicVar? serializableVar = SerializableDynamicVar.FromDynamicVar(pair.Value);

            // For now, let's ignore variables that are not mappable. Ancient events have lots of variables that are
            // put into their titles but not used. We will get an exception on the other end, when deserializing, for
            // variables that were ignored and then used.
            if (serializableVar == null)
            {
                continue;
            }

            // All dynamic variables are subclassed from DynamicVar, but we want to be as specific as possible when
            // serializing the type, otherwise the type that gets roundtripped will be wrong
            if (serializableVar.Value.type == DynamicVarType.BaseDynamic && pair.Value.GetType() != typeof(DynamicVar))
            {
                continue;
            }

            serializableDict[pair.Key] = serializableVar.Value;
        }

        JsonSerializer.Serialize(writer, serializableDict, options);
    }
}
