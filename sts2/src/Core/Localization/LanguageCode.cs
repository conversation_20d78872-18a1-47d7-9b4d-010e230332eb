using System;

namespace MegaCrit.Sts2.Core.Localization;

public class LanguageCode
{
    public LanguageCode(string code)
    {
        if (string.IsNullOrEmpty(code) || code.Length < 2 || code.Length > 3)
        {
            throw new ArgumentException("Language code must be 2 to 3 characters long.");
        }

        Code = code.ToLowerInvariant();
    }

    // Returns the language code
    public string Code { get; }

    // Utility method to check if the language code is valid based on some criteria (can be expanded)
    public bool IsValid()
    {
        // For now, we just check if it's the correct length, but you can add more validations.
        return Code.Length is >= 2 and <= 3;
    }

    public override string ToString()
    {
        return Code;
    }

    // Any other utility methods or properties related to the language code can be added here
}