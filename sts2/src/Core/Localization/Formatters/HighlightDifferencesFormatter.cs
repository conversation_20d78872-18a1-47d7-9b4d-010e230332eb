using System;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using SmartFormat.Core.Extensions;

namespace MegaCrit.Sts2.Core.Localization.Formatters;

public class HighlightDifferencesFormatter : IFormatter
{
    public string Name
    {
        get => "diff";
        set => throw new NotImplementedException();
    }

    public bool CanAutoDetect { get; set; }

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        if (formattingInfo.CurrentValue is not DynamicVar dynamicVar) return false;

        formattingInfo.Write(dynamicVar.ToHighlightedString(false));
        return true;
    }
}