using System;
using System.Linq;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using SmartFormat.Core.Extensions;

namespace MegaCrit.Sts2.Core.Localization.Formatters;

public class StarIconsFormatter : IFormatter
{
    private const string _starIconPath = "res://images/packed/sprite_fonts/star_icon.png";
    public const string starIconSprite = $"[img]{_starIconPath}[/img]";

    public string Name
    {
        get => "starIcons";
        set => throw new NotImplementedException();
    }

    public bool CanAutoDetect { get; set; }

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        int count = formattingInfo.CurrentValue switch
        {
            DynamicVar dynamicVar => (int)dynamicVar.PreviewValue,
            decimal value => (int)value,
            int value => value,
            _ => throw new LocException($"Unknown value='{formattingInfo.CurrentValue}' type={formattingInfo.CurrentValue?.GetType()}")
        };

        string text = string.Concat(Enumerable.Repeat(starIconSprite, count));
        formattingInfo.Write(text);

        return true;
    }
}