using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using SmartFormat.Core.Extensions;
using SmartFormat.Core.Parsing;

namespace MegaCrit.Sts2.Core.Localization.Formatters;

public class ShowIfUpgradedFormatter : IFormatter
{
    public string Name
    {
        get => "show";
        set => throw new NotSupportedException("Setting the 'Names' property is not supported.");
    }

    public bool CanAutoDetect { get; set; }

    public bool TryEvaluateFormat(IFormattingInfo formattingInfo)
    {
        if (formattingInfo.CurrentValue is not IfUpgradedVar ifUpgradedVar) return false;

        IList<Format>? options = formattingInfo.Format?.Split('|');
        if (options == null) throw new LocException($"Format expression must contain at least 1 option. format={formattingInfo.Format}.");
        if (options.Count > 2) throw new LocException($"Format expression cannot contain more than 2 options. num_of_options={options.Count} format={formattingInfo.Format}.");

        Format upgraded = options[0];
        Format? normal = options.Count > 1 ? options[1] : null;

        switch (ifUpgradedVar.upgradeDisplay)
        {
            case UpgradeDisplay.Normal:
                formattingInfo.FormatAsChild(normal!, formattingInfo.CurrentValue);
                break;
            case UpgradeDisplay.Upgraded:
                formattingInfo.FormatAsChild(upgraded, formattingInfo.CurrentValue);
                break;
            case UpgradeDisplay.UpgradePreview:
                formattingInfo.Write("[green]");
                formattingInfo.FormatAsChild(upgraded, formattingInfo.CurrentValue);
                formattingInfo.Write("[/green]");
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(ifUpgradedVar.upgradeDisplay), $"Unexpected value: {ifUpgradedVar.upgradeDisplay}");
        }

        return true;
    }
}