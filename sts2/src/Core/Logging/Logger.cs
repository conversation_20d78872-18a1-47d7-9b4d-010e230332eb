using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

namespace MegaCrit.Sts2.Core.Logging;

public class Logger
{
    public static LogLevel GlobalLogLevel { get; set; } = LogLevel.Info;
    private static readonly object _lockObj = new(); // ensure thread-safety

    // This determines if the game is launched from the godot editor vs CI, Rider, exe, etc.
    private static readonly bool _isRunningFromGodotEditor = Environment.GetEnvironmentVariable("__GODOT_EDITOR_STOP_SHORTCUT__") != null;
    private static readonly ILogPrinter _logPrinter = _isRunningFromGodotEditor ? new EditorLogPrinter() : new ConsoleLogPrinter();

    public static readonly Dictionary<LogType, LogLevel> logLevelTypeMap = new() {
        { LogType.Network, LogLevel.Info },
        { LogType.Actions, LogLevel.Info },
        { LogType.Generic, LogLevel.Info },
        { LogType.GameSync, LogLevel.Info },
    };

    private readonly LogType _logType;

    public string? Context { get; set; }
    public event Action<LogLevel, string, int>? LogCallback;

    static Logger()
    {
        string[] args = Environment.GetCommandLineArgs();

        for (int i = 0; i < args.Length; i++)
        {
            if (args[i] != "-log") continue;

            if (!LogConsoleCmd.TryParseEnumCaseInsensitive(args[i + 1], out LogType? logType))
            {
                _logPrinter.Print(LogLevel.Error, $"Invalid log command line argument! Could not parse {args[i + 1]} as LogType", 1);
            }

            if (!LogConsoleCmd.TryParseEnumCaseInsensitive(args[i + 2], out LogLevel? logLevel))
            {
                _logPrinter.Print(LogLevel.Error, $"Invalid log command line argument! Could not parse {args[i + 2]} as LogLevel", 1);
            }

            logLevelTypeMap[logType!.Value] = logLevel!.Value;

            // This is early enough that we cannot use Log
            _logPrinter.Print(LogLevel.Info, $"Log level for {logType} set to {logLevel}", 1);
        }
    }

    public Logger(string? context, LogType logType)
    {
        Context = context;
        _logType = logType;
        LogCallback += Log.InvokeGlobalLogCallback;
    }

    public void LogMessage(LogLevel level, string text, int skipFrames)
    {
        skipFrames++; // Skip this method
        string textWithContext = Context != null ? $"[{Context}] {text}" : text;
        LogMessage(level, _logType, textWithContext, skipFrames);
    }

    public void LogMessage(LogLevel level, LogType type, string text, int skipFrames)
    {
        skipFrames++; // Skip this method
        // Check if the log level is enabled before processing the message
        LogLevel? currentLogLevel = Logger.logLevelTypeMap.TryGetValue(type, out LogLevel logLevel) ? logLevel : null;
        if (level < (currentLogLevel ?? Logger.GlobalLogLevel))
        {
            return;
        }

        lock (_lockObj)
        {
            // Print the log message using the appropriate log printer (EditorLogPrinter or ConsoleLogPrinter)
            _logPrinter.Print(level, text, skipFrames);
            LogCallback?.Invoke(level, text, skipFrames);
        }
    }

    /// <summary>
    /// Prints to stdout. It should be used when loading operations are happening.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void Load(string text, int skipFrames = 1) => LogMessage(LogLevel.Load, text, skipFrames);

    /// <summary>
    /// Prints to stdout. Debug information which is useful for debugging. It should be used for verbose text.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void Debug(string text, int skipFrames = 1) => LogMessage(LogLevel.Debug, text, skipFrames);

    /// <summary>
    /// Prints to stdout. Debug information which is useful for debugging. It should be used for verbose text.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void VeryDebug(string text, int skipFrames = 1) => LogMessage(LogLevel.VeryDebug, text, skipFrames);

    /// <summary>
    /// Prints to stdout. It should be used for general information which is useful for debugging.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void Info(string text, int skipFrames = 1) => LogMessage(LogLevel.Info, text, skipFrames);

    /// <summary>
    /// Prints to stderr without a stacktrace. It should be used for non-critical issues which we should be aware of
    /// or could indicate an issue.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void Warn(string text, int skipFrames = 1) => LogMessage(LogLevel.Warn, text, skipFrames);

    /// <summary>
    /// Prints a stacktrace to stderr. It should be used for critical issue which should also not block
    /// the continuation of the game.
    /// </summary>
    /// <param name="text"></param>
    /// <param name="skipFrames"></param>
    public void Error(string text, int skipFrames = 1) => LogMessage(LogLevel.Error, text, skipFrames);

    public static void SetLogLevelForType(LogType type, LogLevel? logLevel)
    {
        if (logLevel != null)
        {
            logLevelTypeMap[type] = logLevel.Value;
        }
        else
        {
            logLevelTypeMap.Remove(type);
        }
    }
}
