using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class StatisticsEpoch : EpochModel
{
    public override EpochEra Era => EpochEra.Flourish2Civilization;
    public override int EraPosition => 0;

    public override void QueueUnlocks()
    {
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance!.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");
    }
}
