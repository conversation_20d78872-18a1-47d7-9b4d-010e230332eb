using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Potion3Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.Blight3Reclamation;
    public override int EraPosition => 2;

    public override void QueueUnlocks()
    {
        List<PotionModel> unlockedPotions = [];
        unlockedPotions.Add(ModelDb.Potion<FirePotion>());
        unlockedPotions.Add(ModelDb.Potion<FirePotion>());
        unlockedPotions.Add(ModelDb.Potion<FirePotion>());
        NTimelineScreen.Instance!.QueuePotionUnlock(unlockedPotions);
    }
}
