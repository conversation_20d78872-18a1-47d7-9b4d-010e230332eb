using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class SilentUnlock1Epoch : EpochModel
{
    public override EpochEra Era => EpochEra.Blight2Contagion;
    public override int EraPosition => 0;

    public override void QueueUnlocks()
    {
        List<CardModel> unlockedCards = [];
        unlockedCards.Add(ModelDb.Card<StrikeSilent>());
        unlockedCards.Add(ModelDb.Card<StrikeSilent>());
        unlockedCards.Add(ModelDb.Card<StrikeSilent>());
        NTimelineScreen.Instance!.QueueCardUnlock(unlockedCards);
    }
}
