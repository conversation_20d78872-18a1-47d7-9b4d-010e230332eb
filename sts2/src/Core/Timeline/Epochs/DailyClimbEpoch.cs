using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Nodes.Screens.Timeline;

namespace MegaCrit.Sts2.Core.Timeline.Epochs;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class DailyClimbEpoch : EpochModel
{
    public override EpochEra Era => EpochEra.Invitation3Vanishings;
    public override int EraPosition => 2;

    public override void QueueUnlocks()
    {
        LocString unlockText = new("epochs", $"{Id}.unlock");
        NTimelineScreen.Instance!.QueueMiscUnlock($"[center]{unlockText.GetFormattedText()}[/center]");
    }
}
