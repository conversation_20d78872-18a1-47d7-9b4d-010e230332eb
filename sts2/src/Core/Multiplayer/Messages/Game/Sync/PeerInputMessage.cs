using Godot;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;

/// <summary>
/// Message sent to update the input state of a remote peer.
/// Input state includes mouse position, mouse down state, and hover info.
/// </summary>
public class PeerInputMessage : INetMessage
{
    // Mouse position is represented as a normalized mouse position with respect to a 16:9 aspect ratio. If we have an
    // aspect ratio wider on either side, the mouse may go out-of-bounds, and we allow for this
    private static readonly QuantizeParams _quantizeParams = new(-3f, 3f, 16);

    public bool ShouldBroadcast => true;
    public NetTransferMode Mode => NetTransferMode.Unreliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    // Note that these fields are sent 30x a second, and as such, this message should serialize down to a few bytes. We
    // purposefully send many fields redundantly (i.e. even when they don't change) so that if packets are dropped due
    // to the unreliable protocol, peers will still have the updated state on the next update.
    public bool mouseDown;
    public bool isTargeting;
    public Vector2? netMousePos;
    public NetScreenType screenType;

    public HoveredModelData hoveredModelData;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteBool(mouseDown);
        writer.WriteBool(isTargeting);

        writer.WriteBool(netMousePos != null);

        if (netMousePos != null)
        {
            writer.WriteVector2(netMousePos.Value, _quantizeParams, _quantizeParams);
        }

        writer.WriteEnum(screenType);
        writer.Write(hoveredModelData);
    }

    public void Deserialize(PacketReader reader)
    {
        mouseDown = reader.ReadBool();
        isTargeting = reader.ReadBool();

        if (reader.ReadBool())
        {
            netMousePos = reader.ReadVector2(_quantizeParams, _quantizeParams);
        }

        screenType = reader.ReadEnum<NetScreenType>();
        hoveredModelData = reader.Read<HoveredModelData>();
    }
}
