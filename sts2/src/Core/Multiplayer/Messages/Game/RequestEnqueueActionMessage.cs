using System;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Game;

/// <summary>
/// Sent from clients to host when they initiate a GameAction.
/// The GameAction must not be enqueued until the host sends back an ActionEnqueuedMessage, to preserve determinism.
/// </summary>
public struct RequestEnqueueActionMessage : INetMessage, IClimbLocationTargetedMessage
{
    public bool ShouldBroadcast => false;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public ClimbLocation Location => location;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    public ClimbLocation location;
    public INetAction action;

    public void Serialize(PacketWriter writer)
    {
        writer.Write(location);
        writer.WriteByte((byte)action.ToId());
        writer.Write(action);
    }

    public void Deserialize(PacketReader reader)
    {
        location = reader.Read<ClimbLocation>();
        int id = reader.ReadByte();

        if (!ActionTypes.TryGetActionType(id, out Type? type))
        {
            throw new InvalidOperationException($"Received net action of type {id} that does not map to any type!");
        }

        action = (INetAction)Activator.CreateInstance(type!)!;
        action.Deserialize(reader);
    }

    public override string ToString() => $"{nameof(RequestEnqueueActionMessage)} location: {location} action: {action}";
}
