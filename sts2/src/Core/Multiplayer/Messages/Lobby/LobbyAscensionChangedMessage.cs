using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;

/// <summary>
/// Sent by the host when the chosen ascension level changes.
/// </summary>
public struct LobbyAscensionChangedMessage : INetMessage
{
    public bool ShouldBroadcast => true;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    public int ascension;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteInt(ascension);
    }

    public void Deserialize(PacketReader reader)
    {
        ascension = reader.ReadInt();
    }
}
