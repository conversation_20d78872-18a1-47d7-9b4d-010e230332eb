using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;

namespace MegaCrit.Sts2.Core.Multiplayer.Messages.Lobby;

/// <summary>
/// Sent from host to clients when a client has joined.
/// The newly joined client receives ClientSlotGrantedMessage and not this message.
/// </summary>
public struct PlayerJoinedMessage : INetMessage
{
    public bool ShouldBroadcast => false;
    public NetTransferMode Mode => NetTransferMode.Reliable;
    public LogLevel LogLevel => LogLevel.VeryDebug;

    public LobbyPlayer lobbyPlayer;

    public void Serialize(PacketWriter writer)
    {
        writer.Write(lobbyPlayer);
    }

    public void Deserialize(PacketReader reader)
    {
        lobbyPlayer = reader.Read<LobbyPlayer>();
    }
}
