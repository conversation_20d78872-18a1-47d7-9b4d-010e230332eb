using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Multiplayer;

/// <summary>
/// Responsible for synchronizing all players' combat states before combat begins.
/// </summary>
public class CombatStateSynchronizer : IDisposable
{
    private readonly INetGameService _netService;
    private readonly ClimbState _climbState;
    private readonly Dictionary<ulong, SerializablePlayer> _syncData = [];
    private SerializableClimbRngSet? _rngSet;
    private SerializableRelicGrabBag? _sharedRelicGrabBag;

    private readonly Logger _logger = new(nameof(CombatStateSynchronizer), LogType.GameSync);
    private TaskCompletionSource? _syncCompletionSource;

    // Currently only for in use for tests
    public bool IsDisabled { get; set; }

    public CombatStateSynchronizer(INetGameService netService, ClimbState climbState)
    {
        _netService = netService;
        _climbState = climbState;

        _netService.RegisterMessageHandler<SyncPlayerDataMessage>(OnSyncPlayerMessageReceived);
        _netService.RegisterMessageHandler<SyncRngMessage>(OnSyncRngMessageReceived);
    }

    public void Dispose()
    {
        _netService.UnregisterMessageHandler<SyncPlayerDataMessage>(OnSyncPlayerMessageReceived);
        _netService.UnregisterMessageHandler<SyncRngMessage>(OnSyncRngMessageReceived);
    }

    /// <summary>
    /// Handles a combat synchronization message from another peer.
    /// Remember that this can be called even before StartSync and must queue the data for the next time we sync.
    /// </summary>
    private void OnSyncPlayerMessageReceived(SyncPlayerDataMessage syncMessage, ulong senderId)
    {
        _logger.Debug($"Received sync player message from {senderId}");

        if (_syncData.ContainsKey(senderId))
        {
            _logger.Error($"Received two player sync messages from {senderId}! Ignoring the second one");
            return;
        }

        _syncData[senderId] = syncMessage.player;

        CheckSyncCompleted();
    }

    /// <summary>
    /// Handles a RNG synchronization message from the host.
    /// Remember that this can be called even before StartSync and must queue the data for the next time we sync.
    /// </summary>
    private void OnSyncRngMessageReceived(SyncRngMessage syncMessage, ulong senderId)
    {
        _logger.Debug($"Received sync RNG message from {senderId}");

        if (_rngSet != null)
        {
            _logger.Error($"Received two RNG sync messages from {senderId}! Ignoring the second one");
            return;
        }

        _rngSet = syncMessage.rng;
        _sharedRelicGrabBag = syncMessage.sharedRelicGrabBag;

        CheckSyncCompleted();
    }

    /// <summary>
    /// Sends a message syncing our combat state to all other players.
    /// This should be called early before beginning animations for entering a combat room. Then, WaitForSync should be
    /// called just before entering the combat room.
    /// Between this and WaitForSync, no player or RNG state should be modified.
    /// </summary>
    public void StartSync()
    {
        _logger.Debug("Broadcasting combat sync message to all peers");

        // No need to sync in singleplayer
        if (_netService.Type == NetGameType.Singleplayer || IsDisabled) return;
        if (_syncCompletionSource != null) throw new InvalidOperationException("StartSync called twice before WaitForSync!");

        SyncPlayerDataMessage playerMessage = default;
        Player player = LocalContext.GetMe(_climbState)!;
        playerMessage.player = player.ToSerializable();
        _netService.SendMessage(playerMessage);

        _syncData[playerMessage.player.NetId] = playerMessage.player;
        _syncCompletionSource = new TaskCompletionSource();

        // Host also synchronizes the entire state of the RNG
        if (_netService.Type == NetGameType.Host)
        {
            _rngSet = player.ClimbState.Rng.ToSerializable();
            _sharedRelicGrabBag = player.ClimbState.SharedRelicGrabBag.ToSerializable();

            SyncRngMessage rngMessage = default;
            rngMessage.rng = _rngSet;
            rngMessage.sharedRelicGrabBag = _sharedRelicGrabBag;
            _netService.SendMessage(rngMessage);
        }

        CheckSyncCompleted();
    }

    /// <summary>
    /// Waits for all other players' combat data to be received. StartSync must be called exactly once before this is called.
    /// </summary>
    public async Task WaitForSync()
    {
        _logger.Debug("Waiting to receive all sync messages from all clients");

        // No need to sync in singleplayer
        if (_netService.Type == NetGameType.Singleplayer || IsDisabled) return;
        if (_syncCompletionSource == null) throw new InvalidOperationException("StartSync must be called before WaitForSync!");

        await _syncCompletionSource.Task;

        foreach (KeyValuePair<ulong, SerializablePlayer> pair in _syncData)
        {
            Player? player = _climbState.GetPlayer(pair.Key);

            if (!LocalContext.IsMe(player))
            {
                player!.SyncWithSerializedPlayer(pair.Value);
            }
        }

        if (_netService.Type != NetGameType.Host)
        {
            if (_rngSet != null)
            {
                _climbState.Rng.LoadFromSerializable(_rngSet);
            }
            else if (_climbState.Players.Count > 1)
            {
                _logger.Error("There are two or more players and we are a client, but we never received the RNG set!");
            }

            if (_sharedRelicGrabBag != null)
            {
                _climbState.SharedRelicGrabBag.LoadFromSerializable(_sharedRelicGrabBag);
            }
            else if (_climbState.Players.Count > 1)
            {
                _logger.Error("There are two or more players and we are a client, but we never received the shared relic grab bag!");
            }
        }

        _syncData.Clear();
        _rngSet = null;
        _sharedRelicGrabBag = null;
        _syncCompletionSource = null;
    }

    /// <summary>
    /// Checks if synchronization is complete and sets the result on the sync completion source if we are all done.
    /// This should be called every time we receive some sync data from a peer.
    /// </summary>
    private void CheckSyncCompleted()
    {
        if (_syncCompletionSource == null) return;

        if (_netService.Type == NetGameType.Singleplayer || IsDisabled)
        {
            // In singleplayer, we don't need to wait for anything to be synchronized. Same in tests. Immediately
            // pretend we have received all sync data and exit
            _syncCompletionSource.SetResult();
            return;
        }

        // Otherwise, we need to check if synchronization has been completed.
        // If we are missing data from any players, then wait until we have all the data
        foreach (ulong playerId in ClimbManager.Instance.ClimbLobby!.ConnectedPlayerIds)
        {
            if (!_syncData.ContainsKey(playerId))
            {
                return;
            }
        }

        // If we are missing RNG data (arrives as a separate message), then wait for it to arrive
        if (_rngSet == null)
        {
            return;
        }

        // Otherwise, signal that we have received all combat sync data and are ready to proceed
        _syncCompletionSource.SetResult();
    }
}
