using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Multiplayer.Transport;
using MegaCrit.Sts2.Core.Multiplayer.Transport.ENet;
using MegaCrit.Sts2.Core.Multiplayer.Transport.Steam;
using MegaCrit.Sts2.Core.Platform;

namespace MegaCrit.Sts2.Core.Multiplayer;

public class NetHostGameService : INetHostHandler, INetGameService
{
    private NetHost? _netHost;
    private readonly NetMessageBus _messageBus = new();
    private List<NetClientData> _connectedPeers = [];

    public event Action<NetErrorInfo>? Disconnected;
    public event Action<ulong>? ClientConnected;
    public event Action<ulong, NetErrorInfo>? ClientDisconnected;

    public bool IsConnected => _netHost?.IsConnected ?? false;
    public IReadOnlyList<NetClientData> ConnectedPeers => _connectedPeers;
    public ulong NetId => _netHost?.NetId ?? throw new InvalidOperationException($"Tried to get NetId while not connected!");
    public PlatformType Platform { get; private set; }
    public NetHost? NetHost => _netHost;
    public NetGameType Type => NetGameType.Host;

    public NetErrorInfo? StartENetHost(ushort port, int maxClients)
    {
        ENetHost eHost = new(this);
        _netHost = eHost;
        return eHost.StartHost(port, maxClients);
    }

    public Task<NetErrorInfo?> StartSteamHost(int maxClients)
    {
        SteamHost host = new(this);
        _netHost = host;
        Platform = PlatformType.Steam;
        return host.StartHost(maxClients);
    }

    public void Update()
    {
        if (_netHost?.IsConnected ?? false)
        {
            _netHost.Update();
        }
    }

    public void SendMessage<T>(T message, ulong peerId) where T : INetMessage
    {
        // Note: I don't know if using one channel for reliable/unreliable is the right strategy. It seems like I was
        // having reliability issues when sending messages in mixed modes across the same channel, and this is an effort
        // to resolve that.
        SendMessageToClientInternal(message, peerId, message.Mode.ToChannelId(), null);
    }

    private void SendMessageToClientInternal<T>(T message, ulong peerId, int channel, ulong? overrideSenderId) where T : INetMessage
    {
        if (!IsConnected)
        {
            Log.Error($"Attempted to send message {message} while {this} is not connected!");
            return;
        }

        byte[] buffer = _messageBus.SerializeMessage(overrideSenderId ?? _netHost!.NetId, message, out int length);
        _netHost!.SendMessageToClient(peerId, buffer, length, message.Mode, channel);
    }

    public void SendMessage<T>(T message) where T : INetMessage
    {
        if (!IsConnected)
        {
            Log.Error($"Attempted to send message {message} while {this} is not connected!");
            return;
        }

        byte[] buffer = _messageBus.SerializeMessage(_netHost!.NetId, message, out int length);
        _netHost!.SendMessageToAll(buffer, length, message.Mode, message.Mode.ToChannelId());
    }

    public void RegisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage
    {
        _messageBus.RegisterMessageHandler(handler);
    }

    public void UnregisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage
    {
        _messageBus.UnregisterMessageHandler(handler);
    }

    public void OnPacketReceived(ulong senderId, byte[] packetBytes, NetTransferMode mode, int channel)
    {
        if (!_messageBus.TryDeserializeMessage(packetBytes, out INetMessage? message, out ulong? overrideSenderId))
        {
            Log.Error($"Tried to deserialize packet of size {packetBytes.Length} as message, but we were not able to!");
            return;
        }

        if (message!.ShouldBroadcast)
        {
            BroadcastMessage(message, senderId, channel, overrideSenderId!.Value);
        }

        senderId = overrideSenderId ?? senderId;
        _messageBus.SendMessageToAllHandlers(message, senderId);
    }

    private void BroadcastMessage<T>(T message, ulong excludePeerId, int channel, ulong overrideSenderId) where T : INetMessage
    {
        foreach (NetClientData clientData in _connectedPeers)
        {
            if (clientData.readyForBroadcasting && clientData.peerId != excludePeerId)
            {
                SendMessageToClientInternal(message, clientData.peerId, channel, overrideSenderId);
            }
        }
    }

    /// <summary>
    /// Starts sending broadcasted messages to a peer.
    /// When a peer first connects, messages that have ShouldBroadcast set are not sent to that peer. They are only sent
    /// to the newly connected peer after this method is called, passing the newly conneted peer's ID.
    /// This is used to prevent messages from being sent to a peer until the game-level connection flow has been completed.
    /// </summary>
    public void SetPeerReadyForBroadcasting(ulong peerId)
    {
        for (int i = 0; i < _connectedPeers.Count; i++)
        {
            if (_connectedPeers[i].peerId == peerId)
            {
                NetClientData data = _connectedPeers[i];
                data.readyForBroadcasting = true;
                _connectedPeers[i] = data;
            }
        }
    }

    public void DisconnectClient(ulong peerId, NetError reason, bool now = false)
    {
        _netHost!.DisconnectClient(peerId, reason, now);
    }

    public void Disconnect(NetError reason, bool now = false)
    {
        if (_netHost?.IsConnected ?? false)
        {
            _netHost.StopHost(reason, now);
        }
    }

    public void OnDisconnected(NetErrorInfo info)
    {
        Disconnected?.Invoke(info);
        Platform = PlatformType.None;
    }

    public void OnPeerConnected(ulong peerId)
    {
        _connectedPeers.Add(new NetClientData
        {
            peerId = peerId,
            readyForBroadcasting = false
        });

        ClientConnected?.Invoke(peerId);
    }

    public void OnPeerDisconnected(ulong peerId, NetErrorInfo info)
    {
        _connectedPeers.RemoveAll(p => p.peerId == peerId);
        ClientDisconnected?.Invoke(peerId, info);
    }
}
