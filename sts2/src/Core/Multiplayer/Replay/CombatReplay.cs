using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization.Metadata;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Multiplayer.Replay;

public class CombatReplay : IPacketSerializable
{
    private static readonly JsonSerializerOptions _jsonSerializerSettings = new()
    {
        WriteIndented = false,
        IncludeFields = true,
        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
        TypeInfoResolver = new DefaultJsonTypeInfoResolver { Modifiers = { JsonSerializeConditionAttribute.CheckJsonSerializeConditionsModifier } },
        Converters = { new ModelIdClimbSaveConverter() }
    };

    public string version = default!;
    public string gitCommit = default!;
    public uint modelIdHash;

    public List<uint> choiceIds = [];
    public uint nextActionId;
    public uint nextChecksumId;
    public uint nextHookId;
    public SerializableClimb serializableClimb = default!;
    public List<CombatReplayEvent> events = [];
    public List<ReplayChecksumData> checksumData = [];

    public void Serialize(PacketWriter writer)
    {
        writer.WriteString(version);
        writer.WriteString(gitCommit);
        writer.WriteUInt(modelIdHash);

        writer.WriteInt(choiceIds.Count);
        foreach (uint choiceId in choiceIds)
        {
            writer.WriteUInt(choiceId);
        }

        writer.WriteUInt(nextActionId);
        writer.WriteUInt(nextChecksumId);
        writer.WriteUInt(nextHookId);
        writer.WriteString(JsonSerializer.Serialize(serializableClimb, _jsonSerializerSettings));
        writer.WriteList(events);
        writer.WriteList(checksumData);
    }

    public void Deserialize(PacketReader reader)
    {
        version = reader.ReadString();
        gitCommit = reader.ReadString();
        modelIdHash = reader.ReadUInt();

        int choiceIdsCount = reader.ReadInt();
        for (int i = 0; i < choiceIdsCount; i++)
        {
            choiceIds.Add(reader.ReadUInt());
        }

        nextActionId = reader.ReadUInt();
        nextChecksumId = reader.ReadUInt();
        nextHookId = reader.ReadUInt();
        string climbSaveJson = reader.ReadString();
        serializableClimb = JsonSerializer.Deserialize<SerializableClimb>(climbSaveJson, _jsonSerializerSettings)!;
        events = reader.ReadList<CombatReplayEvent>();
        checksumData = reader.ReadList<ReplayChecksumData>();
    }
}
