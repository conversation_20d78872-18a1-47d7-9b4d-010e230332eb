using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Multiplayer.Game;

/// <summary>
/// Responsible for holding messages that are not destined for the current map point, and waiting until that map point
/// is entered before processing those messages.
/// We may be slow to transition to a new map point (e.g. because of loading delays), and messages from other peers may
/// come in before we're ready to receive them.
/// Note that it is important that queued messages are sent out in the order that they were received.
/// </summary>
public class ClimbLocationTargetedMessageBuffer
{
    private delegate void AnonymizedMessageHandlerDelegate(INetMessage message, ulong senderId);

    private readonly INetGameService _gameService;
    private readonly List<BlockedMessage> _messagesWaitingOnLocationChange = [];
    private readonly List<TypeAndMessageHandlers> _messageHandlers = [];
    private readonly HashSet<ClimbLocation> _visitedLocations = [];
    private readonly Logger _logger = new(nameof(ClimbLocationTargetedMessageBuffer), LogType.GameSync);

    // This might not be the same as Climb.CurrentLocation for brief periods of time! We may receive messages
    // after we change map points but before the room is ready to receive it, and this location should only change
    // after the room is ready to receive messages.
    public ClimbLocation CurrentLocation { get; private set; }

    private struct TypeAndMessageHandlers
    {
        // Type of message these handlers receive
        public Type messageType;

        // The handler that was passed to the INetGameService's Register method. This handler takes the message
        // and forwards it to the handlers list.
        public object netServiceHandler;

        // The message handlers
        public List<MessageHandler> handlers;
    }

    private struct MessageHandler
    {
        // The original handler that was passed to our Register method
        public object originalHandler;

        // Handler that may be called with messages of type messageType
        public AnonymizedMessageHandlerDelegate anonymizedHandler;
    }

    private struct BlockedMessage
    {
        // The location for which the message is destined
        public ClimbLocation location;
        public INetMessage message;
        public ulong senderId;
        public Type messageType;
    }

    public ClimbLocationTargetedMessageBuffer(INetGameService gameService)
    {
        _gameService = gameService;
        _visitedLocations.Add(CurrentLocation);
    }

    /// <summary>
    /// This should be called whenever the climb location changes. If messages are blocked waiting for a location change,
    /// then they will be sent to registered message handlers in the order that they were received.
    /// </summary>
    public void OnClimbLocationChanged(ClimbLocation location)
    {
        _logger.Debug($"Climb location changed to {location} (previously at: {CurrentLocation}), checking if we have enqueued messages");
        CurrentLocation = location;
        _visitedLocations.Add(CurrentLocation);

        for (int i = 0; i < _messagesWaitingOnLocationChange.Count; i++)
        {
            BlockedMessage blockedMessage = _messagesWaitingOnLocationChange[i];
            if (!_visitedLocations.Contains(blockedMessage.location)) continue;

            _logger.Debug($"Handling enqueued message {blockedMessage.message} of type {blockedMessage.messageType} from {blockedMessage.senderId}");
            CallHandlersOfType(blockedMessage.messageType, blockedMessage.message, blockedMessage.senderId);

            _messagesWaitingOnLocationChange.RemoveAt(i);
            i--;
        }

        if (_messagesWaitingOnLocationChange.Count > 0)
        {
            // Bug-related scenarios might be that a player went to a different map location or is not serializing the
            // correct map coordinate
            _logger.Error($"After transitioning to {location}, there are still {_messagesWaitingOnLocationChange.Count} messages for other locations. This is likely indicates a bug. Messages:\n{string.Join("\n", _messagesWaitingOnLocationChange)}");
        }
    }

    private void CallHandlersOfType(Type type, INetMessage message, ulong senderId)
    {
        foreach (TypeAndMessageHandlers typeAndHandlers in _messageHandlers)
        {
            if (typeAndHandlers.messageType == type)
            {
                foreach (MessageHandler handler in typeAndHandlers.handlers)
                {
                    handler.anonymizedHandler(message, senderId);
                }
            }
        }
    }

    /// <summary>
    /// Registers a message handler for map targeted messages.
    /// IClimbLocationTargetedMessages should be registered here instead of directly to the INetGameHandler; otherwise,
    /// they may be received at the wrong time.
    /// </summary>
    /// <param name="handler">
    /// The delegate to call when the message type is received or when we enter a new location and a message for that
    /// location is enqueued.
    /// </param>
    public void RegisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage, IClimbLocationTargetedMessage
    {
        _logger.VeryDebug($"Register message handler {handler} for {typeof(T)}");

        // First, check if we have already registered a handler in INetGameService for this type
        TypeAndMessageHandlers? typeAndHandlers = null;

        foreach (TypeAndMessageHandlers candidate in _messageHandlers)
        {
            if (candidate.messageType == typeof(T))
            {
                typeAndHandlers = candidate;
            }
        }

        // If no handler for this type was ever registered, register one now
        if (typeAndHandlers == null)
        {
            MessageHandlerDelegate<T> wrappedDelegate = HandleMessage;

            typeAndHandlers = new TypeAndMessageHandlers
            {
                messageType = typeof(T),
                netServiceHandler = wrappedDelegate,
                handlers = [],
            };

            _gameService.RegisterMessageHandler(wrappedDelegate);
            _messageHandlers.Add(typeAndHandlers.Value);
        }

        // Add a new targeted handler to our list of handlers
        typeAndHandlers.Value.handlers.Add(new MessageHandler
            {
                anonymizedHandler = AnonymousDelegate,
                originalHandler = handler,
            });

        return;

        void AnonymousDelegate(INetMessage message, ulong senderId) => handler.Invoke((T)message, senderId);
    }

    /// <summary>
    /// Unregisters a message handler for map targeted messages.
    /// </summary>
    public void UnregisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage, IClimbLocationTargetedMessage
    {
        for (int i = 0; i < _messageHandlers.Count; i++)
        {
            TypeAndMessageHandlers typeAndHandlers = _messageHandlers[i];

            if (typeAndHandlers.messageType != typeof(T)) continue;

            for (int j = 0; j < typeAndHandlers.handlers.Count; j++)
            {
                MessageHandler candidate = typeAndHandlers.handlers[j];

                if (candidate.originalHandler is MessageHandlerDelegate<T> typedHandler &&
                    typedHandler == handler)
                {
                    typeAndHandlers.handlers.RemoveAt(j);
                    j--;
                }
            }

            if (typeAndHandlers.handlers.Count <= 0)
            {
                _gameService.UnregisterMessageHandler((MessageHandlerDelegate<T>)typeAndHandlers.netServiceHandler);
                _messageHandlers.RemoveAt(i);
                i--;
            }
        }
    }

    /// <summary>
    /// Internal method which either sends the message to the handler, or enqueues it if the message is not destined
    /// for a location we have already visited.
    /// </summary>
    private void HandleMessage<T>(T message, ulong senderId) where T: INetMessage, IClimbLocationTargetedMessage
    {
        _logger.VeryDebug($"Handling map-targeted message {message} from {senderId} for location {message.Location}");

        if (_visitedLocations.Contains(message.Location))
        {
            CallHandlersOfType(typeof(T), message, senderId);
        }
        else
        {
            _logger.Debug($"Message {message} from {senderId} is for location {message.Location}, enqueueing it because we are currently at location {CurrentLocation}");

            BlockedMessage blockedMessage = new()
            {
                location = message.Location,
                message = message,
                messageType = message.GetType(),
                senderId = senderId
            };

            _messagesWaitingOnLocationChange.Add(blockedMessage);
        }
    }
}
