using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;

/// <summary>
/// Works with the PeerInputSynchronizer to synchronize the intent shown over the player's head during combat.
/// Note that this doesn't do any message sending itself - PeerInputSynchronizer does the message sending and handling
/// to reduce bandwidth. This is here only to separate out some of the state tracking from PeerInputSynchronizer.
/// </summary>
public class HoveredModelTracker
{
    private readonly PeerInputSynchronizer _inputSynchronizer;

    private CardModel? _localSelectedCard;
    private PotionModel? _localSelectedPotion;
    private CardModel? _localHoveredCard;
    private RelicModel? _localHoveredRelic;
    private PotionModel? _localHoveredPotion;

    private readonly List<AbstractModel?> _hoveredModels = [];
    private readonly IPlayerCollection _playerCollection;

    public event Action<ulong>? HoverChanged;

    public HoveredModelTracker(PeerInputSynchronizer inputSynchronizer, IPlayerCollection playerCollection)
    {
        _inputSynchronizer = inputSynchronizer;
        _playerCollection = playerCollection;

        foreach (Player _ in playerCollection.Players)
        {
            _hoveredModels.Add(null);
        }

        _inputSynchronizer.StateChanged += OnPlayerStateChanged;
        _inputSynchronizer.StateRemoved += OnPlayerStateRemoved;
    }

    private void OnPlayerStateChanged(ulong playerId)
    {
        Player player = _playerCollection.GetPlayer(playerId)!;
        int slot = _playerCollection.GetPlayerSlotIndex(player);

        HoveredModelData hoveredModelData = _inputSynchronizer.GetHoveredModelData(playerId);
        AbstractModel? model;

        switch (hoveredModelData.type)
        {
            case HoveredModelType.None:
                model = null;
                break;
            case HoveredModelType.Card:
                model = hoveredModelData.hoveredCombatCard?.ToCardModelOrNull();
                break;
            case HoveredModelType.Relic:
                model = hoveredModelData.hoveredRelicIndex < player.Relics.Count ? player.Relics[hoveredModelData.hoveredRelicIndex.Value] : null;
                break;
            case HoveredModelType.Potion:
                model = player.GetPotionAtSlotIndex(hoveredModelData.hoveredPotionIndex!.Value);
                break;
            default:
                throw new InvalidOperationException($"Unsupported hover type {hoveredModelData.type}");
        }

        // Cards and potions can be created during combat. If one player is running faster than another, then the hovered
        // model reference may not yet be instantiated on the receiving end. In this case, we fall back on the canonical
        // model.
        if (model == null && hoveredModelData.type != HoveredModelType.None)
        {
            model = ModelDb.GetById<AbstractModel>(hoveredModelData.hoveredModelId!);
        }

        AbstractModel? oldHovered = _hoveredModels[slot];

        if (oldHovered != model)
        {
            _hoveredModels[slot] = model;
            HoverChanged?.Invoke(playerId);
        }
    }

    private void OnPlayerStateRemoved(ulong playerId)
    {
        Player player = _playerCollection.GetPlayer(playerId)!;
        int slot = _playerCollection.GetPlayerSlotIndex(player);

        if (_hoveredModels[slot] != null)
        {
            _hoveredModels[slot] = null;
            HoverChanged?.Invoke(playerId);
        }
    }

    public AbstractModel? GetHoveredModel(ulong playerId)
    {
        return _hoveredModels[_playerCollection.GetPlayerSlotIndex(_playerCollection.GetPlayer(playerId)!)];
    }

    public void OnLocalCardSelected(CardModel cardModel)
    {
        _localSelectedCard = cardModel;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalCardDeselected()
    {
        _localSelectedCard = null;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalPotionSelected(PotionModel potionModel)
    {
        _localSelectedPotion = potionModel;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalPotionDeselected()
    {
        _localSelectedPotion = null;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalCardHovered(CardModel cardModel)
    {
        _localHoveredCard = cardModel;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalCardUnhovered()
    {
        _localHoveredCard = null;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalRelicHovered(RelicModel relicModel)
    {
        _localHoveredRelic = relicModel;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalRelicUnhovered()
    {
        _localHoveredRelic = null;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalPotionHovered(PotionModel potionModel)
    {
        _localHoveredPotion = potionModel;
        SynchronizeLocalHoveredModel();
    }

    public void OnLocalPotionUnhovered()
    {
        _localHoveredPotion = null;
        SynchronizeLocalHoveredModel();
    }

    private void SynchronizeLocalHoveredModel()
    {
        AbstractModel? model = (AbstractModel?)_localSelectedCard ??
            (AbstractModel?)_localSelectedPotion ??
            (AbstractModel?)_localHoveredCard ??
            (AbstractModel?)_localHoveredPotion ??
            _localHoveredRelic;

        _inputSynchronizer.SyncLocalHoveredModel(model);
    }
}
