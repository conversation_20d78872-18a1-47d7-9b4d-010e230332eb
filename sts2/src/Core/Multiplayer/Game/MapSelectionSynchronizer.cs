using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Multiplayer.Game;

public class MapSelectionSynchronizer
{
    private readonly INetGameService _netService;
    private readonly ActionQueueSynchronizer _actionQueueSynchronizer;
    private readonly ClimbState _climbState;

    // This may be different than Climb.CurrentMapCoord, because it changes immediately when we send out the
    // MoveToMapCoordAction.
    private ClimbLocation _acceptingVotesFromSource;

    // This list is indexed by player slot ID (i.e. same order as _climb.Players)
    private readonly List<MapVote?> _votes = [];

    private readonly Logger _logger = new("MapSelectionSynchronizer", LogType.GameSync);

    // Determines what map points are rolled in multiplayer when multiple map points have the same number of votes.
    // This is only rolled on the host. Do not depend on the RNG being deterministic across peers.
    private readonly Rng _multiplayerMapPointSelection;

    /// <summary>
    /// Each time we generate a map, this counter goes up. It is used to keep track of multiplayer map votes and to
    /// discard map votes that are for old maps.
    /// </summary>
    public int MapGenerationCount { get; private set; }

    // Second argument is previously voted for map coord (if any), third is new map coord
    public event Action<Player, MapVote?, MapVote?>? PlayerVoteChanged;

    // This is called when the player vote is cancelled due to an action that wasn't caused by the player.
    // This needs to be treated specially because the NMapScreen usually takes local authority over displaying the votes.
    public event Action<Player>? PlayerVoteCancelled;

    public event Action? PlayerVotesCleared;

    public MapSelectionSynchronizer(INetGameService netService, ActionQueueSynchronizer actionQueueSynchronizer, ClimbState climbState)
    {
        _netService = netService;
        _actionQueueSynchronizer = actionQueueSynchronizer;
        _climbState = climbState;
        _multiplayerMapPointSelection = new Rng(_climbState.Rng.Seed);
        OnClimbLocationChanged(_climbState.CurrentLocation);
    }

    public void PlayerVotedForMapCoord(Player player, ClimbLocation source, MapVote? destination)
    {
        if (_acceptingVotesFromSource != source)
        {
            _logger.Warn($"Received map vote from player {player.NetId} for source {source}, but we're currently accepting votes for {_acceptingVotesFromSource}");
            return;
        }

        if (destination?.mapGenerationCount < MapGenerationCount)
        {
            // This can happen in the following scenario:
            // - Player 1 takes Golden Compass
            // - Player 2 votes for map coordinate around the same time, but before they receive the message for the
            //   Golden Compass
            // Player 2's vote should be invalidated on both sides. This branch takes care of Player 1's side; OnMapChanged
            // takes care of the issue on Player 2's side.
            _logger.Warn($"Received map vote from player {player.NetId} for destination {destination}, but the map generation count is lower than our current: {MapGenerationCount}");
        }

        int slotIndex = _climbState.GetPlayerSlotIndex(player);
        MapVote? previousVote = _votes[slotIndex];
        _votes[slotIndex] = destination;

        PlayerVoteChanged?.Invoke(player, previousVote, destination);

        if (destination != null)
        {
            _logger.Debug($"Received vote to move to {destination} from player {player.NetId} (slot {slotIndex})");
        }
        else
        {
            _logger.Debug($"Received cancellation of vote from player {player.NetId} (slot {slotIndex})");
        }

        // Important that this check includes both host and singleplayer
        if (_votes.All(p => p != null && p.Value.mapGenerationCount == MapGenerationCount) &&
            _netService.Type != NetGameType.Client)
        {
            _logger.Debug("All votes received and we are host, choosing coordinate");
            MoveToHighestVotedMapCoord();
        }
    }

    public MapVote? GetVote(Player player)
    {
        return _votes[_climbState.GetPlayerSlotIndex(player)];
    }

    private void MoveToHighestVotedMapCoord()
    {
        if (_netService.Type == NetGameType.Client) throw new InvalidOperationException("Only host should be moving to new map point!");

        Dictionary<MapCoord, int> voteMap = [];

        foreach (MapVote? vote in _votes)
        {
            MapCoord votedCoord = vote!.Value.coord;
            int votes = voteMap.GetValueOrDefault(votedCoord, 0);
            voteMap[votedCoord] = votes + 1;
        }

        int highestVotes = -1;
        List<MapCoord> candidates = [];

        foreach (KeyValuePair<MapCoord, int> pair in voteMap)
        {
            if (pair.Value > highestVotes)
            {
                candidates.Clear();
                highestVotes = pair.Value;
            }

            if (pair.Value == highestVotes)
            {
                candidates.Add(pair.Key);
            }
        }

        MapCoord coord = _multiplayerMapPointSelection.NextItem(candidates);
        _acceptingVotesFromSource.coord = coord;

        _logger.Debug($"Moving to coordinate {coord} with {highestVotes} votes");
        MoveToMapCoordAction action = new(LocalContext.GetMe(_climbState)!, coord);
        _actionQueueSynchronizer.RequestEnqueue(action);
    }

    public void OnClimbLocationChanged(ClimbLocation location)
    {
        _acceptingVotesFromSource = location;
        _votes.Clear();

        for (int i = 0; i < _climbState.Players.Count; i++)
        {
            _votes.Add(null);
        }

        PlayerVotesCleared?.Invoke();
    }

    public void BeforeMapGenerated()
    {
        MapGenerationCount++;

        // Before the map is re-generated (but after the generation count goes up), we need to cancel all player votes
        // that are for a previous map.
        // It's likely that this is a bit overzealous and we could simply cancel all player votes, but on the off-chance
        // that I'm missing a scenario where a player vote could be received before the new map is generated, this is
        // safer.
        for (int i = 0; i < _votes.Count; i++)
        {
            MapVote? location = _votes[i];

            if (location != null && location.Value.mapGenerationCount < MapGenerationCount)
            {
                Player player = _climbState.Players[i];
                _logger.Debug($"Cancelling map vote for player {player.NetId} because the map has re-generated and their vote is old");
                _votes[i] = null;
                PlayerVoteCancelled?.Invoke(player);
            }
        }
    }
}
