using System;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Platform.Steam;

namespace MegaCrit.Sts2.Core.Multiplayer;

public class NetSingleplayerGameService : INetGameService
{
    public const int defaultNetId = 1;

    public bool IsConnected => true;
    public ulong NetId => defaultNetId;
    public NetGameType Type => NetGameType.Singleplayer;
    public PlatformType Platform => SteamInitializer.Initialized ? PlatformType.Steam : PlatformType.None;

    // Required by the interface
    #pragma warning disable 0067
    public event Action<NetErrorInfo>? Disconnected;
    #pragma warning restore 0067

    public void SendMessage<T>(T message, ulong playerId) where T : INetMessage
    {
    }

    public void SendMessage<T>(T message) where T : INetMessage
    {
    }

    public void RegisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage
    {
    }

    public void UnregisterMessageHandler<T>(MessageHandlerDelegate<T> handler) where T : INetMessage
    {
    }

    public void Update()
    {
    }

    public void Disconnect(NetError reason, bool now = false)
    {
    }
}
