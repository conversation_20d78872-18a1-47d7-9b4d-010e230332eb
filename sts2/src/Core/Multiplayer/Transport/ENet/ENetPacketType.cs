namespace MegaCrit.Sts2.Core.Multiplayer.Transport.ENet;

public enum ENetPacketType : byte
{
    // Handshake request sent as the first packet by a client
    HandshakeRequest,

    // Handshake response sent as the first packet to a client by the host
    HandshakeResponse,

    // Disconnection message sent by either host or client just before disconnecting
    Disconnection,

    // Message that should be forwarded to the application layer
    ApplicationMessage,
}
