using System;
using Godot;

namespace MegaCrit.Sts2.Core.Multiplayer.Transport.ENet;

public static class ENetUtil
{
    public static NetTransferMode ModeFromFlags(int flags)
    {
        if ((flags & ENetPacketPeer.FlagReliable) > 0)
        {
            return NetTransferMode.Reliable;
        }

        if ((flags & ENetPacketPeer.FlagUnreliableFragment) > 0)
        {
            return NetTransferMode.Unreliable;
        }

        throw new ArgumentOutOfRangeException($"Flags {flags} cannot be mapped to NetTransferMode!");
    }

    public static int FlagsFromMode(NetTransferMode mode)
    {
        return mode switch
        {
            NetTransferMode.Unreliable => (int)ENetPacketPeer.FlagUnreliableFragment,
            NetTransferMode.Reliable => (int)ENetPacketPeer.FlagReliable,
            _ => throw new ArgumentOutOfRangeException(nameof(mode), mode, null)
        };

    }
}
