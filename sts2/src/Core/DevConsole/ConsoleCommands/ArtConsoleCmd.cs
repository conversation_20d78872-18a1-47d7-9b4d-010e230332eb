using System.Collections.Generic;
using System.Linq;
using System.Text;
using Godot;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class ArtConsoleCmd : AbstractConsoleCmd
{
    private struct MissingArt
    {
        public AbstractModel Model { get; }
        public string IntendedPath { get; }
        public string Description { get; }

        public MissingArt(AbstractModel model, string intendedPath, string description)
        {
            Model = model;
            IntendedPath = intendedPath;
            Description = description;
        }
    }

    private static readonly string[] _types =
    [
        "affliction",
        "card",
        "enchantment",
        "power",
        "relic"
    ];

    public override string CmdName => "art";
    public override string Args => "<type:string>";
    public override string Description => $"Lists all the content of the specified type that is missing art. {TypesHint}";
    public override bool IsNetworked => false;

    private static string TypesHint => $"Types: {string.Join(", ", _types)}.";

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        // Argument validation
        if (args.Length < 1) return new CmdResult(false, $"{CmdName} requires a type. {TypesHint}");

        string typeName = args[0].ToLower();

        // Trim off the trailing "s" so people can enter "card" or "cards", "power" or "powers", etc.
        if (typeName.EndsWith('s'))
        {
            typeName = typeName[..^1];
        }

        List<MissingArt> entries = [];

        switch (typeName)
        {
            case "affliction":
                foreach (AfflictionModel affliction in ModelDb.DebugAfflictions)
                {
                    if (!affliction.HasOverlay)
                    {
                        entries.Add(new MissingArt(affliction, affliction.OverlayPath, affliction.Description.GetRawText()));
                    }
                }

                break;
            case "card":
                foreach (CardModel card in ModelDb.Cards)
                {
                    if (card is DeprecatedCard) continue;

                    foreach (string path in card.AllPortraitPaths)
                    {
                        // Consider beta/missing portrait paths to be "missing".
                        if (!ResourceLoader.Exists(path) || path == card.BetaPortraitPath || path == CardModel.MissingPortraitPath)
                        {
                            entries.Add(new MissingArt(card, card.PortraitPath, card.Description.GetRawText()));
                        }
                    }
                }

                break;
            case "enchantment":
                foreach (EnchantmentModel enchantment in ModelDb.DebugEnchantments)
                {
                    if (enchantment is DeprecatedEnchantment) continue;
                    if (enchantment.IconPath == enchantment.IntendedIconPath) continue;

                    entries.Add(new MissingArt(enchantment, enchantment.IntendedIconPath, enchantment.Description.GetRawText()));
                }

                break;
            case "power":
                foreach (PowerModel power in ModelDb.DebugPowers)
                {
                    if (power.IconPath == power.PackedIconPath) continue;

                    entries.Add(new MissingArt(power, power.PackedIconPath, power.Description.GetRawText()));
                }

                break;
            case "relic":
                foreach (RelicModel relic in ModelDb.Relics)
                {
                    if (relic.IconPath == relic.PackedIconPath) continue;

                    entries.Add(new MissingArt(relic, relic.PackedIconPath, relic.Description.GetRawText()));
                }

                break;
            default:
                return new CmdResult(false, $"'{args[0]}' is not a valid type. {TypesHint}");
        }

        string plural = entries.Count == 1 ? "" : "s";
        StringBuilder builder = new($"{entries.Count} {typeName}{plural} with missing art:\n");

        foreach (MissingArt missingArt in entries.OrderBy(m => m.Model.Id.Entry))
        {
            builder.AppendLine($"{missingArt.Model.Id.Entry}");
            builder.AppendLine($"* Intended Path: {missingArt.IntendedPath}");
            builder.AppendLine($"* Description: \"{missingArt.Description}\"");
        }

        return new CmdResult(true, builder.ToString());
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        // Autocomplete the first arg
        if (parsedArgs.Length == 1)
        {
            DevConsole.PartialComplete(parsedArgs[0], _types, ref inputBuffer, ref outputBuffer);
        }
    }
}
