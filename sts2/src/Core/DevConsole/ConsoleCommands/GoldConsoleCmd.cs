using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

/// <summary>
/// Console commands which manipulate the player's current gold.
/// </summary>
// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class GoldConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "gold";
    public override string Args => $"<amount:int>";
    public override string Description => "Manipulate player gold. Cha-ching!";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (args.Length < 1)
        {
            return new CmdResult(false, "An amount is required");
        }

        if (!int.TryParse(args[0], out int amount))
        {
            return new CmdResult(false, "First argument (the gold amount) must be an int.");
        }

        if (issuingPlayer == null || !ClimbManager.Instance.IsInProgress)
        {
            return new CmdResult(false, "A climb does not appear to be in progress");
        }

        Task task = PlayerCmd.GainGold(amount, issuingPlayer);
        return new CmdResult(task, true, $"'{amount}' gold added.");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
