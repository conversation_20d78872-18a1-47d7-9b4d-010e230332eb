using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class ApplyPowerConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "power";
    public override string Args => $"<id:string> <amount:int> <target-index:int>";
    public override string Description => "Grant power to given target at index.";
    public override bool IsNetworked => true;

    private static List<PowerModel>? _allPowers;

    private static IEnumerable<PowerModel> AllPowers
    {
        get
        {
            _allPowers ??= ReflectionHelper.GetSubtypes(typeof(PowerModel)).Select(ModelDb.DebugPower).ToList();
            return _allPowers;
        }
    }

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (!CombatManager.Instance.IsInProgress) return new CmdResult(false, "This doesn't appear to be a combat!");
        if (args.Length is < 3) return new CmdResult(false, "There must be 3 args.");
        if (!int.TryParse(args[1], out int amount)) return new CmdResult(false, "Arg 1 must be the amount of power to be applied.");

        string powerId = args[0].ToUpper();
        PowerModel? power = AllPowers.FirstOrDefault(c => c.Id.Entry == powerId);

        if (power == null)
        {
            return new CmdResult(false, $"The power id {powerId} does not exist.");
        }

        if (!int.TryParse(args[2], out int targetIndex)) return new CmdResult(false, "Arg 2 must be the target index if specified.");

        Creature creature = CombatManager.Instance.DebugOnlyGetState()!.Creatures.ElementAt(targetIndex);
        PowerModel? existingPower = creature.Powers.FirstOrDefault(p => p.GetType() == power.GetType());

        Task task;

        if (power.IsInstanced || existingPower == null)
        {
            task = PowerCmd.Apply(power.ToMutable(), creature, amount, null, null);
        }
        else
        {
            task = PowerCmd.ModifyAmount(existingPower, amount, null, null);
        }

        return new CmdResult(task, true, $"AppliedPower: [{string.Join(",", creature.IsPlayer ? "PLAYER" : creature.Monster!.Id.Entry)}]");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        if (parsedArgs.Length >= 2) return;

        List<string> names = AllPowers.Select(p => p.Id.Entry).ToList();

        if (parsedArgs.Length == 0 || string.IsNullOrWhiteSpace(parsedArgs[0]))
        {
            outputBuffer = string.Join('\n', names);
        }
        else
        {
            DevConsole.PartialComplete(parsedArgs.Last(), names, ref inputBuffer, ref outputBuffer);
        }
    }
}
