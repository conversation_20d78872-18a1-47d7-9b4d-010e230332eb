using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class UpgradeCardConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "upgrade";
    public override string Args => "<hand-index:int>";
    public override string Description => "Upgrade the target card based on it's hand position (0 is the left most).";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (!ClimbManager.Instance.IsInProgress) return new CmdResult(false, "A climb is currently not in progress!");
        int index;

        if (args.Length == 0)
        {
            index = 0;
        }
        else if (int.TryParse(args[0], out int value))
        {
            index = value;
        }
        else
        {
            index = 0;
        }

        CardPile hand = CardPileTarget.Hand.GetPile(issuingPlayer!);
        IReadOnlyList<CardModel> cards = hand.Cards;
        int handSize = cards.Count;
        if (index >= handSize)
        {
            return new CmdResult(false, $"The index={index} exceeds the hand size={handSize}.");
        }

        CardModel card = hand.Cards[index];
        if (card.CurrentUpgradeLevel == card.MaxUpgradeLevel)
        {
            return new CmdResult(false, $"The card at index={index} is already upgraded to " +
                $"max_level={card.MaxUpgradeLevel}!");
        }

        CardCmd.Upgrade(card);

        return new CmdResult(true, $"Upgraded '{card.Title}' at index '{index}' in hand.");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        if (!ClimbManager.Instance.IsInProgress || localPlayer == null) return;

        CardPile hand = CardPileTarget.Hand.GetPile(localPlayer);
        IReadOnlyList<CardModel> cards = hand.Cards;
        int handSize = cards.Count;
        List<string> range = Enumerable.Range(0, handSize - 1).Select(i => i.ToString()).ToList();
        DevConsole.PartialComplete(parsedArgs.Last(), range, ref inputBuffer, ref outputBuffer);
    }
}
