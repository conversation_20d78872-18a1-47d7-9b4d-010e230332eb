using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class DrawConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "draw";
    public override string Args => "<count:int>";
    public override string Description => "Draw X many cards.";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        int count;

        if (args.Length == 0)
        {
            count = 1;
        }
        else if (int.TryParse(args[0], out int value))
        {
            count = value;
        }
        else
        {
            return new CmdResult(false, "First argument is not an int");
        }

        if (count <= 0)
        {
            return new CmdResult(false, "Draw nothing?");
        }

        if (!ClimbManager.Instance.IsInProgress || issuingPlayer == null)
        {
            return new CmdResult(false, "A climb hasn't started");
        }

        Task task = DrawTask(issuingPlayer, count);
        return new CmdResult(task, true, $"Drawn '{count}' cards.");
    }

    private async Task DrawTask(Player player, int count)
    {
        HookPlayerChoiceContext playerChoiceContext = new(player, LocalContext.NetId!.Value);
        Task task = CardPileCmd.Draw(playerChoiceContext, count, player);
        await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
