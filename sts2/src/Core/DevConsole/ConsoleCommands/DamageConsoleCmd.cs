using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class DamageConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "damage";
    public override string Args => $"<amount:int> <target-index:int>";
    public override string Description => "Damage all enemies, or target creature if index is given (0 is player).";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        if (!CombatManager.Instance.IsInProgress) return new CmdResult(false, "This doesn't appear to be a combat!");
        if (args.Length is < 1 or > 2) return new CmdResult(false, "There must be 1 or 2 args.");
        if (!int.TryParse(args[0], out int amount)) return new CmdResult(false, "Arg 1 must be the amount of damage.");

        IEnumerable<Creature> creatures;
        CombatState combatState = CombatManager.Instance.DebugOnlyGetState()!;

        // Damage all enemies.
        if (args.Length < 2)
        {
            creatures = combatState.Enemies;
        }
        // Damage a specific creature.
        else if (int.TryParse(args[1], out int targetIndex))
        {
            creatures = [combatState.Creatures.ElementAt(targetIndex)];
        }
        else
        {
            return new CmdResult(false, "Arg 2 must be the target index if specified.");
        }

        IEnumerable<string> creatureNames = creatures.Select(c => c.IsPlayer ? "PLAYER" : c.Monster!.Id.Entry);

        Task task = DamageAndCheckWinCondition(creatures, amount);
        return new CmdResult(task, true, $"Damaged: [{string.Join(",", creatureNames)}]");
    }

    private async Task DamageAndCheckWinCondition(IEnumerable<Creature> creatures, decimal amount)
    {
        await CreatureCmd.Damage(creatures.ToList(), amount, DamageProps.nonCardUnpowered, null, null);
        await CombatManager.Instance.CheckWinCondition();
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs) { }
}
