using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.DevConsole.ConsoleCommands;

// ReSharper disable once UnusedType.Global
// Reason: Used in reflection
public class PotionConsoleCmd : AbstractConsoleCmd
{
    public override string CmdName => "potion";
    public override string Args => "<id:string>";
    public override string Description => "Adds potion to belt. Screaming snake case ('ENTROPIC_BREW', not 'Entropic Brew').";
    public override bool IsNetworked => true;

    public override CmdResult Process(Player? issuingPlayer, string[] args)
    {
        // Argument validation
        if (args.Length < 1) return new CmdResult(false, $"{CmdName} requires a potion name");
        if (!ClimbManager.Instance.IsInProgress) return new CmdResult(false, "A climb is not in progress.");

        string potionId = args[0].ToUpper();
        PotionModel? potion = ModelDb.AllPotions.FirstOrDefault(p => p.Id.Entry == potionId);

        if (potion == null)
        {
            return new CmdResult(false, $"Potion '{potionId}' not found");
        }

        PotionModel mutablePotion = potion.ToMutable();
        Task task = PotionCmd.TryToProcure(mutablePotion, issuingPlayer!);

        return new CmdResult(task, true, $"Added potion {potion.Id.Entry}");
    }

    public override void AutoComplete(Player? localPlayer, ref string inputBuffer, ref string outputBuffer, string[] parsedArgs)
    {
        List<string> names = ModelDb.AllPotions.Select(p => p.Id.Entry).ToList();

        if (parsedArgs.Length == 0 || string.IsNullOrWhiteSpace(parsedArgs[0]))
        {
            outputBuffer = string.Join("\n", names);
        }
        else if (parsedArgs.Length == 1)
        {
            DevConsole.PartialComplete(parsedArgs[0], names, ref inputBuffer, ref outputBuffer);
        }
        else
        {
            outputBuffer = string.Empty;
        }
    }
}
