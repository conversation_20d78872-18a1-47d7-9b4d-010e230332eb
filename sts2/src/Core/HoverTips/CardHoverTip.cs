using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.HoverTips;

public class CardHoverTip : IHoverTip
{
    public CardModel Card { get; }
    public string Id { get; }

    public bool IsSmart => true;
    public bool IsDebuff => false;
    public bool IsInstanced => false;

    public CardHoverTip(CardModel card)
    {
        if (!card.IsMutable)
        {
            // This is one of the only instances of CardModel.ToMutable() we allow outside of ICardCreators.
            // This is because CardModels shown in HoverTips will _never_ be added to a climb or combat, so we don't
            // have to worry about tracking or running hooks on them.
            // If I ever end up eating my words here, make sure to use an ICardCreator.
            Card = card.ToMutable();
        }
        else
        {
            Card = card;
        }

        Id = Card.Id.ToString();
    }
}
