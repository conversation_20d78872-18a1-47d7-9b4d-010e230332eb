using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Rooms;

public class EventRoom : AbstractRoom
{
    public override RoomType RoomType => RoomType.Event;

    /// <summary>
    /// The event that the player is doing in this room.
    /// Unlike CombatRoom.Encounter, we want this to be canonical, because we create a separate mutable copy for each
    /// player.
    /// </summary>
    public EventModel CanonicalEvent { get; }

    public Action<EventModel>? OnStart { private get; init; }

    private bool _isPreFinished;
    public override bool IsPreFinished => _isPreFinished;

    public EventRoom(EventModel eventModel)
    {
        eventModel.AssertCanonical();
        CanonicalEvent = eventModel;
    }

    public EventRoom(SerializableRoom serializableRoom)
    {
        CanonicalEvent = ModelDb.GetById<EventModel>(serializableRoom.EventId!);

        if (serializableRoom.IsPreFinished)
        {
            MarkPreFinished();
        }
    }

    public override async Task Enter(IClimbState? climbState)
    {
        await PreloadManager.LoadRoomEventAssets(CanonicalEvent);

        // Set the model ID here so that we can track which event was shown in the climb history.
        // Can be null if using dev cmd.
        MapPointHistoryEntry? entry = climbState?.CurrentMapPointHistoryEntry;
        if (entry != null)
        {
            entry.ModelId = CanonicalEvent.Id;
        }

        ClimbManager.Instance.EventSynchronizer.BeginEvent(CanonicalEvent, OnStart);

        if (IsPreFinished)
        {
            foreach (EventModel model in ClimbManager.Instance.EventSynchronizer.Events)
            {
                // There is no hard technical limitation on this, we would simply need to do a lot more event modification
                if (model is not AncientEventModel ancientEvent)
                {
                    throw new InvalidOperationException(
                        $"Tried to load into pre-finished event {model}! Only ancient events can be pre-finished."
                    );
                }

                ancientEvent.StartPreFinished();
            }
        }

        foreach (EventModel model in ClimbManager.Instance.EventSynchronizer.Events)
        {
            model.StateChanged += OnEventStateChanged;
        }

        NClimb.Instance?.SetCurrentRoom(NEventRoom.Create(ClimbManager.Instance.EventSynchronizer.GetLocalEvent()));
        await HookBus.Instance.AfterRoomEntered(this);
    }

    public override Task Exit(IClimbState? climbState)
    {
        // Shared events are combat-related, and we don't really trust end-of-combat rewards to be deterministic
        if (!CanonicalEvent.IsShared)
        {
            ClimbManager.Instance.ChecksumTracker.GenerateChecksum($"Exiting event room {CanonicalEvent.Id}", null);
        }

        ClimbManager.Instance.EventSynchronizer.GetLocalEvent().StateChanged -= OnEventStateChanged;

        return Task.CompletedTask;
    }

    public override Task Resume(AbstractRoom exitedRoom, IClimbState? climbState)
    {
        ClimbManager.Instance.EventSynchronizer.ResumeEvents(exitedRoom);
        NClimb.Instance!.SetCurrentRoom(NEventRoom.Create(ClimbManager.Instance.EventSynchronizer.GetLocalEvent())!);
        return Task.CompletedTask;
    }

    public override SerializableRoom ToSerializable()
    {
        SerializableRoom result = base.ToSerializable();
        result.EventId = CanonicalEvent.Id;
        result.IsPreFinished = IsPreFinished;

        return result;
    }

    public void MarkPreFinished()
    {
        _isPreFinished = true;
    }

    private void OnEventStateChanged(EventModel eventModel)
    {
        // We only save pre-finished Ancient events. Regular events restart if you save and load after finishing them.
        if (eventModel is not AncientEventModel) return;

        // In multiplayer, we must ensure that all players have finished the event before saving. Otherwise, some players
        // may miss out on taking rewards after load.
        foreach (EventModel model in ClimbManager.Instance.EventSynchronizer.Events)
        {
            if (!model.IsFinished) return;
        }

        // Save when Ancient events are finished, similar to when combat rooms are finished.
        MarkPreFinished();
        TaskHelper.RunSafely(SaveManager.Instance.SaveClimb(this));
    }
}
