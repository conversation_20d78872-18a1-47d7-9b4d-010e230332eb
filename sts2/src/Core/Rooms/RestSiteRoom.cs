using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Rooms;

public class RestSiteRoom : AbstractRoom
{
    public override RoomType RoomType => RoomType.RestSite;

    private RestSiteSynchronizer? _synchronizer;

    public IReadOnlyList<RestSiteOption> Options => _synchronizer?.GetLocalOptions() ?? [];

    public override async Task Enter(IClimbState? climbState)
    {
        _synchronizer = ClimbManager.Instance.RestSiteSynchronizer;
        _synchronizer.BeginRestSite();
        await PreloadManager.LoadRoomRestSite(Options);

        ShowRoomNode(climbState!);
        await HookBus.Instance.AfterRoomEntered(this);
    }

    public override Task Exit(IClimbState? climbState)
    {
        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("Exiting rest site room", null);
        return Task.CompletedTask;
    }

    public override Task Resume(AbstractRoom _, IClimbState? climbState)
    {
        ShowRoomNode(climbState!);
        return Task.CompletedTask;
    }

    private void ShowRoomNode(IClimbState climbState)
    {
        NClimb.Instance?.SetCurrentRoom(NRestSiteRoom.Create(this, climbState));
    }
}
