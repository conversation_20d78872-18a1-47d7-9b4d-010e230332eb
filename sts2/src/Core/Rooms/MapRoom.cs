using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Acts;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Rooms;

public class MapRoom : AbstractRoom
{
    public override RoomType RoomType => RoomType.Map;

    public override Task Enter(IClimbState? climbState)
    {
        if (TestMode.IsOn) return Task.CompletedTask;

        NMapRoom roomNode = NMapRoom.Create(
            climbState?.Act ?? ModelDb.Act<Overgrowth>(),
            climbState?.CurrentActIndex ?? 0
        )!;
        NClimb.Instance!.SetCurrentRoom(roomNode);

        // We intentionally don't call AfterRoomEntered here because the map room is not a "real" room.

        return Task.CompletedTask;
    }

    public override Task Exit(IClimbState? climbState) => Task.CompletedTask;
    public override Task Resume(AbstractRoom _, IClimbState? climbState) => throw new NotImplementedException();
}
