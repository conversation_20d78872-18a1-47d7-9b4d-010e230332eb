using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Rooms;

public class TreasureRoom : AbstractRoom
{
    private Player? _player;

    public int ActIndex { get; }

    public override RoomType RoomType => RoomType.Treasure;

    public TreasureRoom(int actIndex)
    {
        if (actIndex is < 0 or > 2) throw new ArgumentOutOfRangeException(nameof(actIndex), "must be between 0 and 2");

        ActIndex = actIndex;
    }

    public override async Task Enter(IClimbState? climbState)
    {
        await PreloadManager.LoadRoomTreasureAssets();
        _player = LocalContext.GetMe(climbState);
        NClimb.Instance?.SetCurrentRoom(NTreasureRoom.Create(this, climbState!)!);
        await HookBus.Instance.AfterRoomEntered(this);

        ClimbManager.Instance.TreasureRoomRelicSynchronizer.BeginRelicPicking();
    }

    public override Task Exit(IClimbState? climbState) => Task.CompletedTask;

    public override Task Resume(AbstractRoom _, IClimbState? climbState) => throw new NotImplementedException();

    public async Task GainGold(int gold)
    {
        ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedGold(gold);
        await PlayerCmd.GainGold(gold, _player!);
    }

    /// <summary>
    /// Offer rewards. Normally this does nothing but if extra rewards were added by a
    /// relic then this will show the rewards screen.
    /// </summary>
    public async Task DoExtraRewardsIfNeeded()
    {
        IReadOnlyList<Reward> rewards = await RewardsCmd.GenerateForRoom(_player!, this);

        if (rewards.Count > 0)
        {
            await RewardsCmd.Offer(_player!, rewards, true);
        }
    }

    public int GenerateGoldAmount()
    {
        return _player!.PlayerRng.Rewards.NextInt(42, 53);
    }
}
