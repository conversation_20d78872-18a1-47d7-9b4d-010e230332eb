using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Rooms;

public class VictoryRoom : AbstractRoom
{
    public override RoomType RoomType => RoomType.Victory;

    public override async Task Enter(IClimbState? climbState)
    {
        await PreloadManager.LoadRoomVictory();
        NClimb.Instance?.SetCurrentRoom(NVictoryRoom.Create(climbState!));
        await HookBus.Instance.AfterRoomEntered(this);
    }

    public override Task Exit(IClimbState? climbState) => Task.CompletedTask;
    public override Task Resume(AbstractRoom _, IClimbState? climbState) => throw new NotImplementedException();
}
