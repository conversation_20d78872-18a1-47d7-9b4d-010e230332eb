using System;
using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Rooms;

public class BackgroundAssets
{
    public string BackgroundScenePath { get; }
    public List<string> BgLayers { get; }
    public string FgLayer { get; }

    /* Casey: Not sure what this is but it's not used. Commented out
    public BackgroundAssets(Rng rng, string backgroundScenePath, IEnumerable<IEnumerable<string>> bgLayers, IEnumerable<string> fgLayers)
    {
        BackgroundScenePath = backgroundScenePath;
        BgLayers = SelectRandomBackgroundAssetLayers(rng, bgLayers);
        FgLayer = SelectRandomForegroundAssetLayers(rng, fgLayers);
    }
    */

    /// Creates the combat background for a particular encounter. If this encounter doesn't have a unique background,
    /// automatically pull the one for the act. Otherwise scrub through the the corresponding filepath and set up the
    /// background dynamically.
    /// file structure:
    /// ->scenes/backgrounds/{title}
    /// --> layers
    /// ---> {title}_bg_00_a.tscn
    /// ---> {title}_bg_01_a.tscn
    /// ---> {title}_bg_01_b.tscn
    /// ---> {title}_bg_02_a.tscn
    /// ---> {title}_bg_02_b.tscn
    /// ---> {title}_fg_a.tscn
    /// --> {title}_background.tscn
    ///
    /// notes:
    /// - the {title}_background.tscn file helps visually define how the layers are set up
    /// - the layer scenes (in the layer folder) are an asset optimization.
    /// - {title} must be exactly the same as the class name
    /// - layer files that share the same prefix (_bg_##, _fg_) are grouped together, and will be randomly chosen for the combat's background
    /// - you can have as many _bg_ layers as you want.
    /// - _bg_ layers are stacked from least to most
    /// - there is only 1 fg layer currently
    public BackgroundAssets(string title)
    {
        const string fgPrefix = "_fg_";
        const string bgPrefix = "_bg_";
        string dirName = $"res://scenes/backgrounds/{title}/layers";

        DirAccess dir = DirAccess.Open(dirName);
        if (dir == null)
        {
            throw new InvalidOperationException($"could not find directory {dirName}");
        }

        Dictionary<string, List<string>> bgPaths = new();
        List<string> fgPaths = [];

        dir.ListDirBegin();
        string fileName = dir.GetNext();

        while (fileName != "")
        {
            if (dir.CurrentIsDir())
            {
                throw new InvalidOperationException("there should be no other directories within the layers directory");
            }

            if (fileName.Contains(fgPrefix))
            {
                fgPaths.Add($"{dirName}/{fileName}");
            }
            else if (fileName.Contains(bgPrefix))
            {
                string suffix = fileName.Split(bgPrefix)[1];
                string layerNum = suffix.Split("_")[0];

                if (!bgPaths.ContainsKey(layerNum))
                {
                    bgPaths.Add(layerNum, []);
                }

                bgPaths[layerNum].Add($"{dirName}/{fileName}");
            }
            else
            {
                throw new InvalidOperationException($"files must either contain '{fgPrefix}' or '{bgPrefix}'");
            }

            fileName = dir.GetNext();
        }

        string backgroundPath = SceneHelper.GetScenePath($"backgrounds/{title}/{title}_background");

        BackgroundScenePath = backgroundPath;
        BgLayers = SelectRandomBackgroundAssetLayers(Rng.Chaotic, bgPaths);
        FgLayer = SelectRandomForegroundAssetLayers(Rng.Chaotic, fgPaths.ToArray());
    }

    public IEnumerable<string> AssetPaths => new[]
    {
        BackgroundScenePath,
        FgLayer
    }.Concat(BgLayers);

    private static List<string> SelectRandomBackgroundAssetLayers(Rng rng, Dictionary<string, List<string>> bgLayers)
    {
        List<string> layerAssetPaths = [];

        // Get the background layers
        foreach ((string? _, List<string> layer) in bgLayers.OrderBy(kv => kv.Key))
        {
            string layerPath = rng.NextItem(layer)!;
            layerAssetPaths.Add(layerPath);
        }

        return layerAssetPaths;
    }

    private static string SelectRandomForegroundAssetLayers(Rng rng, IEnumerable<string> fgLayer)
    {
        // Get the foreground layer
        return rng.NextItem(fgLayer)!;
    }
}
