using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using Steamworks;

namespace MegaCrit.Sts2.Core.ControllerInput;

public class SteamInputHandler : InputHandler
{
    private InputHandle_t? _currentControllerHandle;

    private readonly List<string> _pressedInputs = [];

    // Reusable InputEventAction to avoid allocation overhead
    private readonly Dictionary<StringName, InputEventAction> _inputEvents = new();

    // if we don't have a steam controller registered, fall back to Godot input
    private readonly InputHandler _fallbackHandler = new GodotInputHandler();

    // Cooldown timer to limit connection attempts
    private double _nextControllerCheckTime;

    // Cache for digital action handles to avoid redundant API calls every frame
    private readonly Dictionary<string, InputDigitalActionHandle_t> _digitalActionHandleCache = new();

    private readonly Dictionary<StringName, StringName> _inputMap = new()
    {
        { "Confirm", MegaInput.accept },
        { "End_Turn", MegaInput.endTurn },
        { "Cancel", MegaInput.cancel },
        { "Up", MegaInput.up },
        { "Down", MegaInput.down },
        { "Left", MegaInput.left },
        { "Right", MegaInput.right },
        { "Select", MegaInput.select },
        { "Tab_Left", MegaInput.tabLeft },
        { "Tab_Right", MegaInput.tabRight },
        { "View_Deck", MegaInput.viewDeck },
        { "Top_Panel", MegaInput.topPanel },
        { "View_Draw_Pile", MegaInput.viewDrawPile },
        { "View_Discard_Pile", MegaInput.viewDiscardPile },
        { "View_Exhaust_Pile", MegaInput.viewExhaustPile },
        { "View_Map", MegaInput.viewMap },
        { "Settings", MegaInput.settings },
    };

    public override async Task Init()
    {
        // wait a frame so Steamworks can get set up
        await NControllerManager.Instance!.ToSignal(NControllerManager.Instance.GetTree(), SceneTree.SignalName.ProcessFrame);

        // Initialize reusable InputEventAction objects for each input
        foreach (KeyValuePair<StringName, StringName> kvp in _inputMap)
        {
            _inputEvents[kvp.Value] = new InputEventAction
            {
                Action = kvp.Value
            };
        }

        // Only initialize Steam Input if Steamworks is initialized
        if (Platform.Steam.SteamInitializer.Initialized)
        {
            try
            {
                SteamInput.Init(false);

                // Pre-cache digital action handles
                foreach (StringName key in _inputMap.Keys)
                {
                    try
                    {
                        InputDigitalActionHandle_t handle = SteamInput.GetDigitalActionHandle(key);
                        _digitalActionHandleCache[key] = handle;
                    }
                    catch (System.InvalidOperationException ex)
                    {
                        Log.Error($"Failed to cache digital action handle for {key}: {ex.Message}");
                    }
                }
            }
            catch (System.InvalidOperationException ex)
            {
                Log.Error($"Failed to initialize Steam Input: {ex.Message}");
            }
        }
        else
        {
            Log.Warn("Cannot initialize Steam Input because Steamworks is not initialized. Falling back to standard input.");
        }

        await _fallbackHandler.Init();
    }

    public override void ProcessInput()
    {
        // Check if Steam is initialized before using Steam Input
        if (!Platform.Steam.SteamInitializer.Initialized)
        {
            _fallbackHandler.ProcessInput();
            return;
        }

        // Check if the state of controllers have changed
        double currentTime = Time.GetTicksMsec() / 1000.0;
        if (currentTime >= _nextControllerCheckTime)
        {
            // Set next check time to 1 second from now
            _nextControllerCheckTime = currentTime + 1.0;
            UpdateControllerConnections();
        }

        // If the controller is not connected, use the fallback controller handler
        if (_currentControllerHandle == null)
        {
            _fallbackHandler.ProcessInput();
        }
        else
        {
            try
            {
                SteamInput.RunFrame();
                ProcessDigitalInputs();
            }
            catch (System.InvalidOperationException ex)
            {
                Log.Error($"Error running Steam Input frame: {ex.Message}");
                _currentControllerHandle = null;
                _fallbackHandler.ProcessInput();
            }
        }
    }

    private void ProcessDigitalInputs()
    {
        foreach (KeyValuePair<StringName, StringName> kvp in _inputMap)
        {
            // Skip input processing if the handle wasn't cached during initialization
            // This eliminates the need for exception handling for each input
            if (!_digitalActionHandleCache.TryGetValue(kvp.Key, out InputDigitalActionHandle_t key))
            {
                Log.Error($"The input {kvp.Key} was not cached during initialization. Skipping...");
                continue;
            }

            // Get current button state
            bool isPressed = SteamInput.GetDigitalActionData(_currentControllerHandle!.Value, key).bState == 1;

            // Get the previous pressed state
            bool isCurrentlyPressed = _pressedInputs.Contains(kvp.Key);

            // Handle input presses when transitioning states
            if (isPressed && !isCurrentlyPressed)
            {
                // Only fire event when transitioning from released to pressed
                InputEventAction inputEvent = _inputEvents[kvp.Value];
                inputEvent.Pressed = true;
                Input.ParseInputEvent(inputEvent);
                _pressedInputs.Add(kvp.Key);
            }
            else if (!isPressed && isCurrentlyPressed)
            {
                // Only fire event when transitioning from pressed to released
                InputEventAction inputEvent = _inputEvents[kvp.Value];
                inputEvent.Pressed = false;
                Input.ParseInputEvent(inputEvent);
                _pressedInputs.Remove(kvp.Key);
            }
        }
    }

    private void UpdateControllerConnections()
    {
        try
        {
            InputHandle_t[] handles = new InputHandle_t[16];
            int connectedCount = SteamInput.GetConnectedControllers(handles);
            if (connectedCount == 0) // no controller detected
            {
                _currentControllerHandle = null;
                return;
            }

            _currentControllerHandle = handles[0];
            InputActionSetHandle_t actionSet = SteamInput.GetActionSetHandle("Controls");
            SteamInput.ActivateActionSet(_currentControllerHandle.Value, actionSet);
        }
        catch (System.InvalidOperationException ex)
        {
            // Handle the case where Steam API is not properly initialized
            Log.Error($"Failed to connect to Steam controller: {ex.Message}");
            _currentControllerHandle = null;
        }
    }
}
