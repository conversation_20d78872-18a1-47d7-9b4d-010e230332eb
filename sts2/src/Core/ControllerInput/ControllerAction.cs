namespace MegaCrit.Sts2.Core.ControllerInput;

public static class MegaInput
{
    public const string cancel = "ui_cancel";
    public const string up = "ui_up";
    public const string down = "ui_down";
    public const string left = "ui_left";
    public const string right = "ui_right";
    public const string accept = "ui_accept"; // confirming upgrades in rest sites, proceeding between rooms
    public const string select = "ui_select"; // ie selecting cards to be played, selecting rewards to take
    public const string tabLeft = "mega_settings_tab_left";
    public const string tabRight = "mega_settings_tab_right";
    public const string viewDeck = "mega_deck_shortcut";
    public const string topPanel = "mega_potion_shortcut";
    public const string viewDrawPile = "mega_draw_pile_shortcut";
    public const string viewDiscardPile = "mega_discard_pile_shortcut";
    public const string viewExhaustPile = "mega_exhaust_pile_shortcut";
    public const string viewMap = "mega_map_shortcut";
    public const string settings = "mega_options_shortcut";
    public const string endTurn = "mega_end_turn";

}
