using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Osty : MonsterModel
{
    public static Vector2 MinOffset => new(150, -75);
    public static Vector2 MaxOffset => new(250, -75);
    public static Vector2 ScaleRange => new(1, 2);

    public override int MinInitialHp => 1;
    public override int MaxInitialHp => 1;

    public const string pokeAnim = "attack_poke";

    public const string ostyAttackSfx = "event:/sfx/characters/osty/osty_attack";
    public override string DeathSfx => "event:/sfx/characters/osty/osty_die";
    public override bool HasDeathSfx => true;

    public override bool IsHealthBarVisible => Creature.IsAlive;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        MoveState nothingState = new("NOTHING_MOVE", _ => Task.CompletedTask);
        nothingState.FollowUpState = nothingState;

        return new MonsterMoveStateMachine([nothingState], nothingState);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleState = new(AnimState.idleAnim, true);
        AnimState castState = new(AnimState.castAnim);
        AnimState attackState = new(AnimState.attackAnim);
        AnimState pokeState = new(pokeAnim);
        AnimState hurtState = new(AnimState.hurtAnim);
        AnimState deathState = new(AnimState.dieAnim);
        AnimState deathLoopState = new("dead_loop", true);
        AnimState reviveState = new(AnimState.reviveAnim);

        idleState.AddBranch(SpineAnimator.hitTrigger, hurtState);

        castState.NextState = idleState;
        castState.AddBranch(SpineAnimator.hitTrigger, hurtState);

        attackState.NextState = idleState;
        attackState.AddBranch(SpineAnimator.hitTrigger, hurtState);

        pokeState.NextState = idleState;
        pokeState.AddBranch(SpineAnimator.hitTrigger, hurtState);

        hurtState.NextState = idleState;
        hurtState.AddBranch(SpineAnimator.hitTrigger, hurtState);

        deathState.NextState = deathLoopState;
        reviveState.NextState = idleState;

        SpineAnimator animator = new(idleState, spineController);

        animator.AddAnyState(SpineAnimator.attackTrigger, attackState);
        animator.AddAnyState(SpineAnimator.castTrigger, castState);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathState);
        animator.AddAnyState(pokeAnim, pokeState);

        // We do this here just in case we trigger revive before Osty plays his death animation.
        animator.AddAnyState(SpineAnimator.reviveTrigger, reviveState);

        return animator;
    }
}
