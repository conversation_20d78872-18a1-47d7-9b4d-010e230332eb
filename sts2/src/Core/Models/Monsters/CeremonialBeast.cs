using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Cards;
using MegaCrit.Sts2.Core.Nodes.Vfx.Utilities;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CeremonialBeast : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 245, 234);
    public override int MaxInitialHp => MinInitialHp;

    private int ButtDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 11);
    private int BasePlowAmount => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 45, 40);

    public override bool ShouldDisappearFromDoom => false;
    public override bool ShouldFadeAfterDeath => false;

    private const string _plowTrigger = "Plow";
    private const string _plowEndTrigger = "EndPlow";
    private const string _stunTrigger = "Stun";
    private const string _unStunTrigger = "Unstun";
    public const string plowHitTrigger = "PlowHit";

    private const string _plowSfx = "event:/sfx/enemy/enemy_attacks/ceremonial_beast/ceremonial_beast_plow";
    private const string _plowEndSfx = "event:/sfx/enemy/enemy_attacks/ceremonial_beast/ceremonial_beast_plow_end";
    private const string _shrillSfx = "event:/sfx/enemy/enemy_attacks/ceremonial_beast/ceremonial_beast_shrill";
    public const string stunSfx = "event:/sfx/enemy/enemy_attacks/ceremonial_beast/ceremonial_beast_stun";

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/ceremonial_beast/ceremonial_beast_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    private bool _isStunnedByPlowRemoval;

    private bool IsStunnedByPlowRemoval
    {
        get => _isStunnedByPlowRemoval;
        set
        {
            AssertMutable();
            _isStunnedByPlowRemoval = value;
        }
    }

    private bool ShouldPlayRegularHurtAnim => !IsStunnedByPlowRemoval && !Creature.Powers.OfType<Plow>().Any();

    private bool _inMidCharge;

    private bool InMidCharge
    {
        get => _inMidCharge;
        set
        {
            AssertMutable();
            _inMidCharge = value;
        }
    }

    private MoveState _stunState = default!;

    public MoveState StunState
    {
        get => _stunState;
        set
        {
            AssertMutable();
            _stunState = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();

        if (AscensionHelper.HasAscension(AscensionLevel.MightyBosses))
        {
            await PowerCmd.Apply<CeremonialBeastA1Regen>(Creature, 1, Creature, null);
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState buttState = new("BUTT_MOVE", ButtMove, new SingleAttackIntent(ButtDamage));
        MoveState plowState = new("PLOW_MOVE", PlowMove, new SingleAttackIntent(0), new DebuffIntent());
        MoveState cryState = new("CRY_MOVE", CryMove, new CardDebuffIntent());
        StunState = new("STUN_MOVE", StunnedMove, new StunIntent());


        buttState.FollowUpState = plowState;
        plowState.FollowUpState = cryState;
        StunState.FollowUpState = cryState;
        cryState.FollowUpState = buttState;

        states.Add(plowState);
        states.Add(buttState);
        states.Add(StunState);
        states.Add(cryState);

        return new MonsterMoveStateMachine(states, buttState);
    }

    private async Task ButtMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 1f);

        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NSpikeSplashVfx.Create(Creature, VfxColor.Cyan));
        NGame.Instance?.ScreenShake(ShakeStrength.Strong, ShakeDuration.Normal, 180f + Rng.Chaotic.NextFloat(-10f, 10f));

        await CreatureCmd.Damage(targets, ButtDamage, DamageProps.monsterMove, Creature, null);
        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slashPath);

        await Cmd.CustomScaledWait(0f, 0.4f, 0.4f);
        decimal plowAmount = BasePlowAmount + decimal.Floor((CombatState.RoundNumber + 1) / 3.0m) * 5;
        await PowerCmd.Apply<Plow>(Creature, plowAmount, Creature, null);
    }

    private async Task PlowMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_plowSfx);
        InMidCharge = true;
        Creature.GetPower<Plow>()!.MarkAlreadyAttacked();

        await CreatureCmd.TriggerAnim(Creature, _plowTrigger, 0f);

        await Cmd.Wait(0.5f);

        // Swooshy lines (pale green)
        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(
            NHorizontalLinesVfx.Create(new Color("BFFFC880"), 1.2f, false)
        );

        await Cmd.Wait(0.5f);

        // We add a bit of delay here to fake the hit, before actually applying the damage. This is to give the animation
        // a chance to leave the screen if we end up killing the ceremonial beast while its in this animation (with thorns)
        NCombatRoom.Instance?.RadialBlur(VfxPosition.Left);
        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.bluntPath);

        // Only play the line burst vfx on one of the players (as it's intense)
        foreach (Creature creature in targets)
        {
            NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NLineBurstVfx.Create(creature));
            break;
        }

        NGame.Instance?.ScreenShake(ShakeStrength.Strong, ShakeDuration.Normal, 180f + Rng.Chaotic.NextFloat(-10f, 10f));

        // The Plow power will add its own damage in the ModifyAttackDamageGiven hook.
        await CreatureCmd.Damage(targets, 0, DamageProps.monsterMove, Creature, null);
        NGame.Instance?.DoHitStop(ShakeStrength.Strong, ShakeDuration.Normal);
        InMidCharge = false;
        await Cmd.Wait(0.2f);

        await PowerCmd.Apply<Dexterity>(targets, -2, Creature, null);

        SfxCmd.Play(_plowEndSfx);
        await CreatureCmd.TriggerAnim(Creature, _plowEndTrigger, 0.0f);

        // add a manual wait here so that ceremonial beast gets back to its spot before the turn ends
        // no matter what speed the game is at
        await Cmd.Wait(0.5f);
        await PowerCmd.Remove<Plow>(Creature);
    }

    private async Task CryMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_shrillSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.6f);

        foreach (Creature target in targets)
        {
            Player player = target.Player ?? target.PetOwner!;
            IEnumerable<CardModel> cards = CardPile.GetCards(player, CardPileTarget.Draw, CardPileTarget.Discard);
            IEnumerable<CardModel> cardsToAfflict = ModelDb.Affliction<Ringing>().PickRandomTargets(player.ClimbState.Rng, cards, 5);
            await CardCmd.AfflictAndPreview<Ringing>(cardsToAfflict, 1);
        }
    }

    public async Task SetStunned()
    {
        IsStunnedByPlowRemoval = true;
        await CreatureCmd.TriggerAnim(Creature, _stunTrigger, 0.6f);
    }

    private async Task StunnedMove(IReadOnlyList<Creature> targets)
    {
        IsStunnedByPlowRemoval = false;
        await CreatureCmd.TriggerAnim(Creature, _unStunTrigger, 0.6f);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("shrill");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState plowStartAnim = new("plow");
        AnimState plowEndAnim = new("plow_end");
        AnimState plowEndDieAnim = new("plow_end_die");

        AnimState stunStart = new("stun");
        AnimState stunLoop = new("stun_loop", true);
        AnimState wakeUpAnim = new("wake_up");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        idleAnim.AddBranch(_plowTrigger, plowStartAnim);

        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        plowStartAnim.AddBranch(_plowEndTrigger, plowEndAnim);
        plowEndAnim.NextState = idleAnim;

        stunStart.NextState = stunLoop;
        stunLoop.AddBranch(SpineAnimator.hitTrigger, hurtAnim);

        wakeUpAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);
        animator.AddAnyState(_unStunTrigger, wakeUpAnim);

        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim, () => !InMidCharge);
        animator.AddAnyState(SpineAnimator.deathTrigger, plowEndDieAnim, () => InMidCharge);


        // have to define hit specifically because we don't want hurt animation to play while
        // doing its plow attack animation.
        idleAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        castAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        hurtAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        attackAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        stunStart.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        stunLoop.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);
        wakeUpAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim, () => ShouldPlayRegularHurtAnim);

        // Since Plow needs to branch the hurt state based on if it has worn off or not, only do default hit handling
        // if we don't have Plow on
        idleAnim.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        castAnim.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        hurtAnim.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        attackAnim.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        stunStart.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        stunLoop.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);
        wakeUpAnim.AddBranch(SpineAnimator.hitTrigger, stunStart, () => IsStunnedByPlowRemoval);

        animator.AddAnyState(_stunTrigger, stunStart);
        animator.AddAnyState(_plowTrigger, plowStartAnim);

        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);

        animator.AddAnyState(plowHitTrigger, hurtAnim);
        return animator;
    }
}
