using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PunchConstruct : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 60, 55);
    public override int MaxInitialHp => MinInitialHp;

    private int StrongPunchDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 14, 12);
    private int FastPunchDamage => 4;
    private int FastPunchRepeat => 2;

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    private bool _startsWithStrongPunch;

    public bool StartsWithStrongPunch
    {
        get => _startsWithStrongPunch;
        set
        {
            AssertMutable();
            _startsWithStrongPunch = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Artifact>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState readyState = new("READY_MOVE", ReadyMove, new DefendIntent());
        MoveState strongPunchState = new("STRONG_PUNCH_MOVE", StrongPunchMove, new SingleAttackIntent(StrongPunchDamage));
        MoveState fastPunchState = new("FAST_PUNCH_MOVE", FastPunchMove, new MultiAttackIntent(FastPunchDamage, FastPunchRepeat), new DebuffIntent());

        readyState.FollowUpState = strongPunchState;
        strongPunchState.FollowUpState = fastPunchState;
        fastPunchState.FollowUpState = readyState;

        states.Add(readyState);
        states.Add(fastPunchState);
        states.Add(strongPunchState);

        return new MonsterMoveStateMachine(states, StartsWithStrongPunch ? strongPunchState : readyState);
    }

    private async Task ReadyMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.GainBlock(Creature, 10, BlockProps.monsterMove, null);
    }

    private async Task StrongPunchMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(StrongPunchDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithHitFx(VfxCmd.bluntPath, AttackSfx)
            .Execute();
    }

    private async Task FastPunchMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(FastPunchDamage, FastPunchRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        await PowerCmd.Apply<Weak>(targets, 1, Creature, null);
    }
}
