using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Noisebot : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 24, 23);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 29, 28);

    private const int _noiseStatusCount = 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState stabState = new ("NOISE_MOVE", NoiseMove, new StatusIntent(_noiseStatusCount));
        stabState.FollowUpState = stabState;

        states.Add(stabState);
        return new MonsterMoveStateMachine(states, stabState);
    }

    private async Task NoiseMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.6f);

        // HACK: have to do this manually rather than CardPileCmd.AddAndPreview
        // because we need to preview cards going to 2 separate piles
        foreach (Creature target in targets)
        {
            Player player = target.Player ?? target.PetOwner!;

            CardPileAddResult[] statusCards = new CardPileAddResult[_noiseStatusCount];

            CardModel card1 = CombatState.CreateCard<Dazed>(player);
            statusCards[0] = await CardPileCmd.Add(card1, CardPileTarget.Discard);

            CardModel card2 = CombatState.CreateCard<Dazed>(player);
            statusCards[1] = await CardPileCmd.Add(card2, CardPileTarget.Draw, CardPilePosition.Random);

            if (LocalContext.IsMe(player))
            {
                CardCmd.PreviewCardPileAdd(statusCards);
                await Cmd.Wait(1f);
            }
        }
    }
}
