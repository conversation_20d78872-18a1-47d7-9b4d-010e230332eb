using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SlimedBerserker : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 339, 333);
    public override int MaxInitialHp => MinInitialHp;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;
    public override bool HasDeathSfx => true;

    protected override string CastSfx => "event:/sfx/enemy/enemy_attacks/slimed_berserker/slimed_berserker_buff";
    private string SlimeSfx => "event:/sfx/enemy/enemy_attacks/slimed_berserker/slimed_berserker_slime";

    private int PummelingDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 3, 2);
    private const int _pummelingRepeat = 4;

    private int SmotherDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 33, 30);

    private const int _leechingDrain = 3;

    private const int _vomitSlimeInDiscard = 10;

    private const string _hugTrigger = "Hug";
    private const string _vomitTrigger = "Vomit";

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Unstable>(Creature, 5, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState vomitIchorState = new("VOMIT_ICHOR_MOVE", VomitIchorMove, new StatusIntent(_vomitSlimeInDiscard));
        MoveState leechingHugState = new("LEECHING_HUG_MOVE", LeechingHugMove, new DebuffIntent(), new BuffIntent());

        MoveState furiousPummelingState = new("FURIOUS_PUMMELING_MOVE", FuriousPummelingMove, new MultiAttackIntent(PummelingDamage, _pummelingRepeat));
        MoveState smotherState = new("SMOTHER_MOVE", SmotherMove, new SingleAttackIntent(SmotherDamage));

        vomitIchorState.FollowUpState = smotherState;
        smotherState.FollowUpState = leechingHugState;
        leechingHugState.FollowUpState = furiousPummelingState;
        furiousPummelingState.FollowUpState = vomitIchorState;


        states.Add(vomitIchorState);
        states.Add(smotherState);
        states.Add(leechingHugState);
        states.Add(furiousPummelingState);

        return new MonsterMoveStateMachine(states, vomitIchorState);
    }

    private async Task VomitIchorMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(SlimeSfx);
        await CreatureCmd.TriggerAnim(Creature, _vomitTrigger, 0.7f);
        await CardPileCmd.AddToCombatAndPreview<Slimed>(targets, CardPileTarget.Discard, _vomitSlimeInDiscard, false);
    }

    private async Task LeechingHugMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, _hugTrigger, 0.65f);
        await PowerCmd.Apply<Weak>(targets, _leechingDrain, null, null);
        await PowerCmd.Apply<Strength>(Creature, _leechingDrain, Creature, null);
    }

    private async Task FuriousPummelingMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(PummelingDamage, _pummelingRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.2f)
            .WithAttackerFx(sfx: AttackSfx)
            .Execute();
    }

    private async Task SmotherMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SmotherDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.2f)
            .WithAttackerFx(sfx: AttackSfx)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState hugAnim = new("hug");
        AnimState vomitAnim = new("vomit");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        hugAnim.NextState = idleAnim;
        vomitAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(_hugTrigger, hugAnim);
        animator.AddAnyState(_vomitTrigger, vomitAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
