using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HauntedShip : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 63, 61);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 66, 64);

    private int RammingSpeedDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 11, 10);
    private int RammingSpeedStatusCount => 2;

    private int SwipeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 14, 13);
    private int StompDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 3, 3);
    private int StompRepeat => 3;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.ArmorBig;

    public override bool HasDeathSfx => false;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState rammingSpeedState = new("RAMMING_SPEED_MOVE", RammingSpeedMove, new SingleAttackIntent(RammingSpeedDamage), new StatusIntent(RammingSpeedStatusCount));
        MoveState swipeState = new("SWIPE_MOVE", SwipeMove, new SingleAttackIntent(SwipeDamage));
        MoveState stompState = new("STOMP_MOVE", StompMove, new MultiAttackIntent(StompDamage, StompRepeat));
        MoveState hauntState = new("HAUNT_MOVE", HauntMove, new DebuffIntent());

        RandomBranchState randState = new("RAND");

        rammingSpeedState.FollowUpState = randState;
        swipeState.FollowUpState = randState;
        stompState.FollowUpState = randState;
        hauntState.FollowUpState = randState;

        randState.AddBranch(rammingSpeedState, MoveRepeatType.CannotRepeat, (_, _) => CombatState.RoundNumber % 3 != 0 ? 1 : 0);
        randState.AddBranch(swipeState, MoveRepeatType.CannotRepeat, (_, _) => CombatState.RoundNumber % 3 != 0 ? 1 : 0);
        randState.AddBranch(stompState, MoveRepeatType.CannotRepeat, (_, _) => CombatState.RoundNumber % 3 != 0 ? 1 : 0);
        randState.AddBranch(hauntState, MoveRepeatType.CannotRepeat, (_, _) => CombatState.RoundNumber % 3 == 0 ? 1 : 0);

        states.Add(randState);
        states.Add(rammingSpeedState);
        states.Add(swipeState);
        states.Add(stompState);
        states.Add(hauntState);

        return new MonsterMoveStateMachine(states, rammingSpeedState);
    }

    private async Task RammingSpeedMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(RammingSpeedDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        await CardPileCmd.AddToCombatAndPreview<Dazed>(targets, CardPileTarget.Discard, RammingSpeedStatusCount, false);
    }

    private async Task SwipeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SwipeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task StompMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(StompDamage, StompRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task HauntMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Weak>(targets, 1, Creature, null);
        await PowerCmd.Apply<Frail>(targets, 1, Creature, null);
        await PowerCmd.Apply<Vulnerable>(targets, 1, Creature, null);

    }
}
