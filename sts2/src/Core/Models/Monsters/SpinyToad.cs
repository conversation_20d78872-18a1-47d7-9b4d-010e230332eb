using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SpinyToad : MonsterModel
{
    private static readonly LocString _croakLine = new("monsters", "SPINY_TOAD.moves.PROTRUDING_SPIKES.banter");

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 121, 116);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 124, 119);

    private const string _spikeTrigger = "Spiked";
    private const string _unSpikeTrigger = "Unspiked";

    private int LashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 16, 12);
    private const int _lashDexterity = -1;

    private const int _explosionRepeat = 5;
    private int ExplosionDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 5, 4);

    protected override string AttackSfx => "event:/sfx/enemy/enemy_attacks/spiny_toad/spiny_toad_lick";
    private const string _attackHeavySfx = "event:/sfx/enemy/enemy_attacks/spiny_toad/spiny_toad_explode";
    private const string _buffSfx = "event:/sfx/enemy/enemy_attacks/spiny_toad/spiny_toad_protrude";

    private bool _isSpiny;

    public bool IsSpiny
    {
        get => _isSpiny;
        set
        {
            AssertMutable();
            _isSpiny = value;
        }
    }

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/spiny_toad/spiny_toad_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState spikesState = new("PROTRUDING_SPIKES_MOVE", SpikesMove, new BuffIntent());
        MoveState explosionState = new("SPIKE_EXPLOSION_MOVE", ExplosionMove, new MultiAttackIntent(ExplosionDamage, _explosionRepeat));
        MoveState lashState1 = new("TONGUE_LASH_1_MOVE", LashMove, new SingleAttackIntent(LashDamage), new DebuffIntent());
        MoveState lashState2 = new("TONGUE_LASH_2_MOVE", LashMove, new SingleAttackIntent(LashDamage), new DebuffIntent());

        spikesState.FollowUpState = explosionState;
        explosionState.FollowUpState = lashState1;
        lashState1.FollowUpState = lashState2;
        lashState2.FollowUpState = spikesState;

        states.Add(spikesState);
        states.Add(explosionState);
        states.Add(lashState1);
        states.Add(lashState2);

        return new MonsterMoveStateMachine(states, spikesState);
    }

    public override Task AfterAddedToRoom()
    {
        base.AfterAddedToRoom();
        return Task.CompletedTask;
    }

    private async Task SpikesMove(IReadOnlyList<Creature> targets)
    {
        if (Rng.NextFloat() <= 0.5f)
        {
            TalkCmd.Play(_croakLine, Creature, 1.5f);
        }

        SfxCmd.Play(_buffSfx);

        await CreatureCmd.TriggerAnim(Creature, _spikeTrigger, 0.5f);
        IsSpiny = true;

        await PowerCmd.Apply<Thorns>(Creature, 5, Creature, null);
    }

    private async Task ExplosionMove(IReadOnlyList<Creature> targets)
    {
        IsSpiny = false;

        await DamageCmd.Attack(ExplosionDamage, _explosionRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_unSpikeTrigger, 0.7f)
            .WithAttackerFx(sfx: _attackHeavySfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Thorns>(Creature, -5, Creature, null);
        await Cmd.Wait(1f);
    }

    private async Task LashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(LashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Dexterity>(targets, _lashDexterity, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleSpikedAnim = new(AnimState.idleAnim, true);
        AnimState hurtSpikedAnim = new(AnimState.hurtAnim);
        AnimState dieSpikedAnim = new(AnimState.dieAnim);

        AnimState protrudeAnim = new("protrude");
        AnimState lickAnim = new("lick");
        AnimState explodeAnim = new("explode");

        AnimState idleNakedAnim = new("idle_naked_loop", true);
        AnimState hurtNakedAnim = new("hurt_naked");
        AnimState dieNakedAnim = new("die_naked");

        idleNakedAnim.AddBranch(_spikeTrigger, protrudeAnim);

        idleSpikedAnim.AddBranch(_unSpikeTrigger, explodeAnim);

        protrudeAnim.NextState = idleSpikedAnim;

        hurtNakedAnim.NextState = idleNakedAnim;
        hurtNakedAnim.AddBranch(_spikeTrigger, protrudeAnim);

        hurtSpikedAnim.NextState = idleSpikedAnim;
        hurtSpikedAnim.AddBranch(_unSpikeTrigger, explodeAnim);

        lickAnim.NextState = idleNakedAnim;
        explodeAnim.NextState = idleNakedAnim;

        SpineAnimator animator = new(idleNakedAnim, spineController);

        animator.AddAnyState(SpineAnimator.attackTrigger, lickAnim);

        animator.AddAnyState(SpineAnimator.hitTrigger, hurtNakedAnim, () => !IsSpiny);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtSpikedAnim, () => IsSpiny);

        animator.AddAnyState(SpineAnimator.deathTrigger, dieNakedAnim, () => !IsSpiny);
        animator.AddAnyState(SpineAnimator.deathTrigger, dieSpikedAnim, () => IsSpiny);

        return animator;
    }
}
