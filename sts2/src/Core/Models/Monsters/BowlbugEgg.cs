using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BowlbugEgg : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 32, 31);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 35, 34);

    private int BiteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int ProtectDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 4);
    private int ProtectBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int DefendBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 11, 9);

    private const string _spitSfx = "event:/sfx/enemy/enemy_attacks/workbug_egg/workbug_egg_spit";

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/workbug_egg/workbug_egg_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Insect;

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkeleton bugSkeleton = node.SpineController!.GetSkeleton();
        bugSkeleton.SetSkin(bugSkeleton.GetData().FindSkin("cocoon"));
        bugSkeleton.SetSlotsToSetupPose();

        SpineSprite cocoonNode = node.GetSpecialNode<SpineSprite>("Visuals/CocoonSlotNode/Cocoon");
        SpineSkeleton cocoonSkeleton = cocoonNode.GetSkeleton();
        cocoonSkeleton.SetSkin(cocoonSkeleton.GetData().FindSkin("egg1"));
        cocoonNode.GetAnimationState().SetAnimation("egg_idle_loop");
        cocoonSkeleton.SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<EggDrop>(Creature, 3, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState defendState = new("DEFEND_MOVE", DefendMove, new DefendIntent());
        MoveState protectState = new("PROTECT_MOVE", ProtectMove, new DefendIntent(), new SingleAttackIntent(ProtectDamage));
        MoveState biteState = new("BITE_MOVE", BiteMove, new SingleAttackIntent(BiteDamage));

        defendState.FollowUpState = protectState;
        protectState.FollowUpState = biteState;
        biteState.FollowUpState = protectState;

        states.Add(defendState);
        states.Add(protectState);
        states.Add(biteState);

        return new MonsterMoveStateMachine(states, defendState);
    }

    private async Task DefendMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.GainBlock(Creature, DefendBlock, BlockProps.monsterMove, null);
    }

    private async Task ProtectMove(IReadOnlyList<Creature> targets)
    {
        if (TestMode.IsOff)
        {
            // Set the bone position to the left-most player.
            Vector2? leftMostPlayerPos = null;

            foreach (Creature target in targets)
            {
                NCreature targetNode = NCombatRoom.Instance!.GetCreatureNode(target)!;

                if (leftMostPlayerPos == null || leftMostPlayerPos.Value.X > targetNode.GlobalPosition.X)
                {
                    leftMostPlayerPos = targetNode.GlobalPosition;
                }
            }

            NCreature creatureNode = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            SpineBoneNode bone = creatureNode.GetSpecialNode<SpineBoneNode>("Visuals/SpineBoneNode");
            bone.Position = Vector2.Right * (leftMostPlayerPos!.Value.X - creatureNode.GlobalPosition.X) * 4;
        }

        await DamageCmd.Attack(ProtectDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.8f)
            .WithAttackerFx(null, _spitSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
        await CreatureCmd.GainBlock(Creature, ProtectBlock, BlockProps.monsterMove, null);
    }

    private async Task BiteMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BiteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.3f)
            .WithAttackerFx(null, _spitSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("spit");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);


        // SetNextState automatically transitions to the next state on transition completion
        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);
        // AddAnyState means we always check this trigger no matter what the current state we are at
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);

        return animator;
    }
}
