using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class OwlMagistrate : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 345, 333);
    public override int MaxInitialHp => MinInitialHp;

    private int VerdictDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 39, 36);

    private int PeckAssaultDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 5, 4);
    private const int _peckAssaultRepeat = 5;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;
    public override bool HasDeathSfx => false;

    private const string _takeOffTrigger = "TakeOff";

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState magistrateScrutinyState = new("MAGISTRATE_SCRUTINY", MagistrateScrutinyMove, new DebuffIntent());
        MoveState peckAssaultState = new("PECK_ASSAULT", PeckAssaultMove, new MultiAttackIntent(PeckAssaultDamage, _peckAssaultRepeat));
        MoveState judicialFlightState = new("JUDICIAL_FLIGHT", JudicialFlightMove, new BuffIntent());
        MoveState verdictState = new("VERDICT", VerdictMove, new SingleAttackIntent(VerdictDamage), new DebuffIntent());


        magistrateScrutinyState.FollowUpState = peckAssaultState;
        peckAssaultState.FollowUpState = judicialFlightState;
        judicialFlightState.FollowUpState = verdictState;
        verdictState.FollowUpState = peckAssaultState;

        states.Add(magistrateScrutinyState);
        states.Add(peckAssaultState);
        states.Add(judicialFlightState);
        states.Add(verdictState);
        return new MonsterMoveStateMachine(states, magistrateScrutinyState);
    }

    private async Task MagistrateScrutinyMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Scrutiny>(targets, 2, Creature, null);
    }

    private async Task PeckAssaultMove(IReadOnlyList<Creature> targets)
    {
        NDebugAudioManager.Instance?.Play("owl_screech.ogg", 0.8f, PitchVariance.Medium);

        await DamageCmd.Attack(PeckAssaultDamage, _peckAssaultRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .OnlyPlayAnimOnce()
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task JudicialFlightMove(IReadOnlyList<Creature> targets)
    {
        NDebugAudioManager.Instance?.Play("owl_flight.ogg", 1f, PitchVariance.Medium);
        await CreatureCmd.TriggerAnim(Creature, _takeOffTrigger, 0);
        await Cmd.Wait(1.25f); // we do a manual pause here, so it isn't affected by play speed
        PowerModel power = (await PowerCmd.Apply<Soar>(Creature, 1, Creature, null))!;
        power.SkipNextDurationTick = true;
    }

    private async Task VerdictMove(IReadOnlyList<Creature> targets)
    {
        NDebugAudioManager.Instance?.Play("owl_flight.ogg", 1f, PitchVariance.Medium);
        NDebugAudioManager.Instance?.Play("owl_screech_big.ogg", 0.8f, PitchVariance.Medium);

        await DamageCmd.Attack(VerdictDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Scrutiny>(targets, 2, Creature, null);
        await Cmd.Wait(1f); // we do a manual pause here, so it isn't affected by play speed
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true) { BoundsContainer = "IdleBounds" };
        AnimState attackPeckAnim = new("attack_peck");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        AnimState takeOffAnim = new("take_off");
        AnimState idleFlyingAnim = new("fly_loop", true) { BoundsContainer = "FlyingBounds" };
        AnimState attackFlyingAnim = new("attack_dive");
        AnimState flyingHurtAnim = new("hurt_flying");
        AnimState flyingDeathAnim = new("die_flying");

        idleAnim.AddBranch(SpineAnimator.attackTrigger, attackPeckAnim);
        idleAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim);
        idleAnim.AddBranch(SpineAnimator.deathTrigger, deathAnim);

        attackPeckAnim.NextState = idleAnim;
        attackPeckAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim);
        attackPeckAnim.AddBranch(SpineAnimator.deathTrigger, deathAnim);

        hurtAnim.NextState = idleAnim;
        hurtAnim.AddBranch(SpineAnimator.attackTrigger, attackPeckAnim);
        hurtAnim.AddBranch(SpineAnimator.deathTrigger, deathAnim);
        hurtAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim);


        takeOffAnim.NextState = idleFlyingAnim;
        takeOffAnim.AddBranch(SpineAnimator.hitTrigger, flyingHurtAnim);
        takeOffAnim.AddBranch(SpineAnimator.deathTrigger, flyingDeathAnim);

        idleFlyingAnim.AddBranch(SpineAnimator.attackTrigger, attackFlyingAnim);
        idleFlyingAnim.AddBranch(SpineAnimator.hitTrigger, flyingHurtAnim);
        idleFlyingAnim.AddBranch(SpineAnimator.deathTrigger, flyingDeathAnim);

        flyingHurtAnim.NextState = idleFlyingAnim;
        flyingHurtAnim.AddBranch(SpineAnimator.attackTrigger, attackFlyingAnim);
        flyingHurtAnim.AddBranch(SpineAnimator.deathTrigger, flyingDeathAnim);

        attackFlyingAnim.NextState = idleAnim;
        attackFlyingAnim.AddBranch(SpineAnimator.attackTrigger, attackPeckAnim);
        attackFlyingAnim.AddBranch(SpineAnimator.deathTrigger, deathAnim);

        SpineAnimator animator = new(idleAnim, spineController);
        animator.AddAnyState(_takeOffTrigger, takeOffAnim);
        return animator;
    }
}
