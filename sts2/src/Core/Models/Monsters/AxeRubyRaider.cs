using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class AxeRubyRaider : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 21, 20);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 23, 22);

    private int SwingDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);
    private int SwingBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);
    private int BigSwingDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 12);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState swingState1 = new("SWING_1", SwingMove, new SingleAttackIntent(SwingDamage), new DefendIntent());
        MoveState swingState2 = new("SWING_2", SwingMove, new SingleAttackIntent(SwingDamage), new DefendIntent());
        MoveState bigSwingState = new("BIG_SWING", BigSwingMove, new SingleAttackIntent(BigSwingDamage));
        swingState1.FollowUpState = swingState2;
        swingState2.FollowUpState = bigSwingState;
        bigSwingState.FollowUpState = swingState1;

        states.Add(swingState1);
        states.Add(swingState2);
        states.Add(bigSwingState);

        return new MonsterMoveStateMachine(states, swingState1);
    }

    private async Task SwingMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SwingDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
        await CreatureCmd.GainBlock(Creature, SwingBlock, BlockProps.monsterMove, null);
    }

    private async Task BigSwingMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BigSwingDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }
}
