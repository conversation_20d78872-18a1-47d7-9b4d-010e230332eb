using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class EvilGas : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 82, 80);
    public override int MaxInitialHp => MinInitialHp;

    private int SuperGasBlastDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    private int _bloatAmount = 1;

    private int BloatAmount
    {
        get => _bloatAmount;
        set
        {
            AssertMutable();
            _bloatAmount = value;
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState advancedGasMove = new("ADVANCED_GAS_MOVE", AdvancedGasMove, new CardDebuffIntent());
        MoveState bloatState = new("BLOAT_MOVE", BloatMove, new SummonIntent());
        MoveState superGasBlastState = new("SUPER_GAS_BLAST_MOVE", SuperGasBlastMove, new SingleAttackIntent(SuperGasBlastDamage));

        advancedGasMove.FollowUpState = bloatState;
        bloatState.FollowUpState = superGasBlastState;
        superGasBlastState.FollowUpState = bloatState;

        states.Add(advancedGasMove);
        states.Add(superGasBlastState);
        states.Add(bloatState);

        return new MonsterMoveStateMachine(states, advancedGasMove);
    }

    private async Task AdvancedGasMove(IReadOnlyList<Creature> targets)
    {
        await Cmd.CustomScaledWait(0.4f, 0.4f, 0.4f);
        await PowerCmd.Apply<Smoggy>(targets, 1, Creature, null);
    }

    private async Task BloatMove(IReadOnlyList<Creature> targets)
    {
        await Cmd.CustomScaledWait(0.4f, 0.4f, 0.4f);

        for (int i = 0; i < BloatAmount; i++)
        {
            string slot = CombatState.Encounter!.GetNextSlot(CombatState);
            if (slot != "")
            {
                await CreatureCmd.Add<GasBomb>(CombatState, slot);
            }
        }

        BloatAmount = Math.Min(BloatAmount + 1, 5);
    }

    private async Task SuperGasBlastMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SuperGasBlastDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }
}
