using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TestSubject : MonsterModel
{
    private const int _baseTestSubjectNum = 8;

    public override LocString Title
    {
        get
        {
            LocString title = base.Title;
            title.Add("Count", SaveManager.Instance.ProgressSave.TestSubjectKills + _baseTestSubjectNum);
            return title;
        }
    }

    public override int MinInitialHp => FirstFormHp;
    public override int MaxInitialHp => MinInitialHp;

    public int FirstFormHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 111, 100);

    public int SecondFormHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 222, 200);
    public int ThirdFormHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 333, 300);

    private int JustAPinchDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private const int _justAPinchRepeat = 2;

    private int BodySlamDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 35, 30);
    private int EviscerateDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int EviscerateRepeat => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 3, 3);

    private const string _growthSpurtTrigger = "GrowthSpurtTrigger";
    private const string _bodySlamTrigger = "BodySlamTrigger";
    private const string _justAPinchTrigger = "JustAPinchTrigger";
    private const string _deadTrigger = "DeadTrigger";
    private const string _respawnTrigger = "RespawnTrigger";

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    // This gets set in GenerateMoveStateMachine, which is effectively a constructor.
    private MoveState _deadState = default!;

    private MoveState DeadState
    {
        get => _deadState;
        set
        {
            AssertMutable();
            _deadState = value;
        }
    }

    private int _respawns;

    private int Respawns
    {
        get => _respawns;
        set
        {
            AssertMutable();
            _respawns = value;
        }
    }

    public override bool ShouldDisappearFromDoom => Respawns >= 2;

    public async Task TriggerDeadState()
    {
        CombatState.ClimbState.ExtraFields.TestSubjectKills++;
        await CreatureCmd.TriggerAnim(Creature, _deadTrigger, 0f);

        // Start respawning.
        SetMoveImmediate(DeadState);
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();

        await PowerCmd.Apply<Adaptable>(Creature, 1, Creature, null);
        await ApplyBasePowers();
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        // Overall respawn move
        DeadState = new MoveState("RESPAWN_MOVE", RespawnMove, new HealIntent())
        {
            MustPerformOnceBeforeTransitioning = true
        };

        // First Form
        MoveState justAPinchState = new("JUST_A_PINCH_MOVE", JustAPinchMove, new MultiAttackIntent(JustAPinchDamage, _justAPinchRepeat), new StatusIntent(2));
        MoveState bodySlamState = new("BODY_SLAM_MOVE", BodySlam, new SingleAttackIntent(BodySlamDamage));
        ConditionalBranchState bodySlamFollowupState = new("BODY_SLAM_BRANCH");

        // Second Form
        MoveState growthSpurtState = new("GROWTH_SPURT_MOVE", GrowthSpurtMove, new DefendIntent(), new BuffIntent())
        {
            FollowUpState = justAPinchState
        };

        // Third Form
        MoveState eviscerateState = new("EVISCERATE_MOVE", EviscerateMove, new MultiAttackIntent(EviscerateDamage, EviscerateRepeat))
        {
            FollowUpState = growthSpurtState
        };

        justAPinchState.FollowUpState = bodySlamState;

        // Three conditionals for post-Body Slam move based on the form.
        bodySlamState.FollowUpState = bodySlamFollowupState;
        bodySlamFollowupState.AddState(justAPinchState, (_, _) => Respawns < 1);
        bodySlamFollowupState.AddState(growthSpurtState, (_, _) => Respawns < 2);
        bodySlamFollowupState.AddState(eviscerateState, (_, _) => Respawns == 2);

        DeadState.FollowUpState = justAPinchState;

        states.Add(DeadState);
        states.Add(justAPinchState);
        states.Add(bodySlamState);
        states.Add(bodySlamFollowupState);
        states.Add(growthSpurtState);
        states.Add(eviscerateState);

        return new MonsterMoveStateMachine(states, justAPinchState);
    }

    private async Task RespawnMove(IReadOnlyList<Creature> targets)
    {
        Respawns++;
        await CreatureCmd.TriggerAnim(Creature, _respawnTrigger, 0);
        await Cmd.Wait(0.8f); // we do manual wait here to time the growth with when the head pops up in the animation

        NCombatRoom.Instance?.GetCreatureNode(Creature)?.SetDefaultScaleTo(1 + Respawns * 0.1f, 0.1f);
        await Cmd.Wait(0.75f); // we do manual wait here so the animation finishes and the health bar bounds can be set before it appears again

        switch (Respawns)
        {
            case 1:
                await Revive(SecondFormHp);
                await ApplyBasePowers();

                // Second form also has Buffer.
                await PowerCmd.Apply<BufferPower>(Creature, 2, Creature, null);

                break;
            case 2:
                await Revive(ThirdFormHp);
                await ApplyBasePowers();
                // Third form also has Buffer and Enrage.
                await PowerCmd.Apply<BufferPower>(Creature, 2, Creature, null);
                await PowerCmd.Apply<Enrage>(Creature, 1, Creature, null);

                TalkCmd.Play(L10NMonsterLookup("TEST_SUBJECT.moves.THIRD_FORM.speakLine"), Creature);

                // Third form is the last form.
                await PowerCmd.Remove<Adaptable>(Creature);

                break;
        }
    }

    private async Task JustAPinchMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(JustAPinchDamage, _justAPinchRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_justAPinchTrigger, 0.2f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        foreach (Creature target in targets)
        {
            Player player = target.Player ?? target.PetOwner!;
            CardPileAddResult[] statusCards = new CardPileAddResult[2];

            CardModel card1 = CombatState.CreateCard<Wound>(player);
            statusCards[0] = await CardPileCmd.Add(card1, CardPileTarget.Discard);

            CardModel card2 = CombatState.CreateCard<Wound>(player);
            statusCards[1] = await CardPileCmd.Add(card2, CardPileTarget.Draw, CardPilePosition.Random);

            if (LocalContext.IsMe(player))
            {
                CardCmd.PreviewCardPileAdd(statusCards);
                await Cmd.Wait(1f);
            }
        }
    }

    private async Task BodySlam(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BodySlamDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_bodySlamTrigger, 0.2f)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task GrowthSpurtMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, _growthSpurtTrigger, 0.2f);
        await CreatureCmd.GainBlock(Creature, 20, BlockProps.monsterMove, null);
        await PowerCmd.Apply<Strength>(Creature, 3, Creature, null);
    }

    private async Task EviscerateMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(EviscerateDamage, EviscerateRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_justAPinchTrigger, 0.2f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task Revive(int baseRespawnHp)
    {
        AssertMutable();

        int scaledHp = baseRespawnHp * Creature.CombatState!.Players.Count;

        await CreatureCmd.SetMaxHp(Creature, scaledHp);
        await CreatureCmd.Heal(Creature, scaledHp);
    }

    private async Task ApplyBasePowers()
    {
        await PowerCmd.Apply<Malleable>(Creature, 3, Creature, null);

        if (AscensionHelper.HasAscension(AscensionLevel.MightyBosses))
        {
            await PowerCmd.Apply<Strength>(Creature, 1, Creature, null);
        }
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idle1Anim = new("idle_loop1", true);
        AnimState hurt1Anim = new("hurt1");
        AnimState attackDouble1Anim = new("attack_double1");
        AnimState attackBig1Anim = new("attack_big1");
        AnimState heal1Anim = new("heal1");
        AnimState knockout1Anim = new("knockout1");
        AnimState knockedOutLoop1Anim = new("knocked_out_loop1");
        AnimState regenerate1 = new("regenerate1") { BoundsContainer = "RespawnBounds1" };

        AnimState idle2Anim = new("idle_loop2", true);
        AnimState hurt2Anim = new("hurt2");
        AnimState attackDouble2Anim = new("attack_double2");
        AnimState attackBig2Anim = new("attack_big2");
        AnimState heal2Anim = new("heal2");
        AnimState knockout2Anim = new("knockout2");
        AnimState knockedOutLoop2Anim = new("knocked_out_loop2");
        AnimState regenerate2 = new("regenerate2") { BoundsContainer = "RespawnBounds2" };

        AnimState idle3Anim = new("idle_loop3", true);
        AnimState hurt3Anim = new("hurt3");
        AnimState attackDouble3Anim = new("attack_double3");
        AnimState attackBig3Anim = new("attack_big3");
        AnimState heal3Anim = new("heal3");
        AnimState dieAnim = new(AnimState.dieAnim);

        heal1Anim.NextState = idle1Anim;
        attackBig1Anim.NextState = idle1Anim;
        attackDouble1Anim.NextState = idle1Anim;
        hurt1Anim.NextState = idle1Anim;
        knockout1Anim.NextState = knockedOutLoop1Anim;
        regenerate1.NextState = idle2Anim;

        heal2Anim.NextState = idle2Anim;
        attackBig2Anim.NextState = idle2Anim;
        attackDouble2Anim.NextState = idle2Anim;
        hurt2Anim.NextState = idle2Anim;
        knockout2Anim.NextState = knockedOutLoop2Anim;
        regenerate2.NextState = idle3Anim;

        heal3Anim.NextState = idle3Anim;
        attackBig3Anim.NextState = idle3Anim;
        attackDouble3Anim.NextState = idle3Anim;
        hurt3Anim.NextState = idle3Anim;

        SpineAnimator animator = new(idle1Anim, spineController);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurt1Anim, () => Respawns == 0);
        animator.AddAnyState(_bodySlamTrigger, attackBig1Anim, () => Respawns == 0);
        animator.AddAnyState(_justAPinchTrigger, attackDouble1Anim, () => Respawns == 0);
        animator.AddAnyState(_growthSpurtTrigger, heal1Anim, () => Respawns == 0);
        animator.AddAnyState(_deadTrigger, knockout1Anim, () => Respawns == 0);
        animator.AddAnyState(_respawnTrigger, regenerate1, () => Respawns == 1);

        animator.AddAnyState(SpineAnimator.hitTrigger, hurt2Anim, () => Respawns == 1);
        animator.AddAnyState(_bodySlamTrigger, attackBig2Anim, () => Respawns == 1);
        animator.AddAnyState(_justAPinchTrigger, attackDouble2Anim, () => Respawns == 1);
        animator.AddAnyState(_growthSpurtTrigger, heal2Anim, () => Respawns == 1);
        animator.AddAnyState(_deadTrigger, knockout2Anim, () => Respawns == 1);
        animator.AddAnyState(_respawnTrigger, regenerate2, () => Respawns == 2);

        animator.AddAnyState(SpineAnimator.hitTrigger, hurt3Anim, () => Respawns == 2);
        animator.AddAnyState(_bodySlamTrigger, attackBig3Anim, () => Respawns == 2);
        animator.AddAnyState(_justAPinchTrigger, attackDouble3Anim, () => Respawns == 2);
        animator.AddAnyState(_growthSpurtTrigger, heal3Anim, () => Respawns == 2);
        animator.AddAnyState(SpineAnimator.deathTrigger, dieAnim);
        return animator;
    }
}
