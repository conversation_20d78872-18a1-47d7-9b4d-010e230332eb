using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GasBomb : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 12, 10);
    public override int MaxInitialHp => MinInitialHp;

    private int ExplodeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Minion>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState explodeState = new("EXPLODE_MOVE", ExplodeMove, new DeathBlowIntent(() => ExplodeDamage));

        states.Add(explodeState);

        return new MonsterMoveStateMachine(states, explodeState);
    }

    private async Task ExplodeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ExplodeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        // TODO: Give the creature a sec before it dies. Should be able to get rid of this once
        // we have animation
        await Cmd.Wait(0.25f);
        await CreatureCmd.Kill(Creature);
    }
}
