using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheObscura : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 129, 123);

    public override int MaxInitialHp => MinInitialHp;

    private int PiercingGazeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 11, 10);

    private int HardeningStrikeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int HardeningStrikeBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);

    public override bool HasDeathSfx => false;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState illusionState = new("ILLUSION_MOVE", IllusionMove, new SummonIntent());
        MoveState piercingGazeState = new("PIERCING_GAZE_MOVE", PiercingGazeMove, new SingleAttackIntent(PiercingGazeDamage));
        MoveState wailState = new("SAIL_MOVE", WailMove, new BuffIntent());
        MoveState hardeningStrikeState = new("HARDENING_STRIKE_MOVE", HardeningStrikeMove, new SingleAttackIntent(HardeningStrikeDamage), new DefendIntent());

        RandomBranchState randState = new("RAND");

        illusionState.FollowUpState = randState;

        piercingGazeState.FollowUpState = randState;
        wailState.FollowUpState = randState;
        hardeningStrikeState.FollowUpState = randState;

        randState.AddBranch(piercingGazeState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(wailState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(hardeningStrikeState, MoveRepeatType.CannotRepeat);

        states.Add(illusionState);
        states.Add(piercingGazeState);
        states.Add(wailState);
        states.Add(hardeningStrikeState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, illusionState);
    }

    private async Task IllusionMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.Add<Parafright>(CombatState, TheObscuraNormal.illusionSlot);
    }

    private async Task PiercingGazeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(PiercingGazeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task WailMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(Creature.CombatState!.GetTeammatesOf(Creature), 3, Creature, null);
    }

    private async Task HardeningStrikeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HardeningStrikeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CreatureCmd.GainBlock(Creature, HardeningStrikeBlock, BlockProps.monsterMove, null);
    }
}
