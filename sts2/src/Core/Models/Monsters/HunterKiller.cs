using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HunterKiller : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 126, 121);

    public override int MaxInitialHp => MinInitialHp;

    private int BiteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 19, 17);

    private const int _punctureRepeat = 3;
    private int PunctureDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 7);

    public override string TakeDamageSfx => "event:/sfx/enemy/enemy_attacks/hunter_killer/hunter_killer_hurt";
    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/hunter_killer/hunter_killer_die";

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState goopState = new("TENDERIZING_GOOP_MOVE", GoopMove, new DebuffIntent());
        MoveState biteState = new("BITE_MOVE", BiteMove, new SingleAttackIntent(BiteDamage));
        MoveState punctureState = new("PUNCTURE_MOVE", PunctureMove, new MultiAttackIntent(PunctureDamage, _punctureRepeat));

        RandomBranchState randState = new("RAND");

        goopState.FollowUpState = randState;
        biteState.FollowUpState = randState;
        punctureState.FollowUpState = randState;

        randState.AddBranch(biteState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(punctureState, 2);

        states.Add(goopState);
        states.Add(biteState);
        states.Add(punctureState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, goopState);
    }

    private async Task GoopMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.4f);
        await PowerCmd.Apply<Tender>(targets, 1, Creature, null);
    }

    private async Task BiteMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BiteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.bitePath)
            .Execute();
    }

    private async Task PunctureMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(PunctureDamage, _punctureRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }
}
