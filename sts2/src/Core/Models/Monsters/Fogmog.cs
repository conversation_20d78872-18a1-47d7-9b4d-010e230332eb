using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Fogmog : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 73, 71);
    public override int MaxInitialHp => MinInitialHp;

    private const string _summonTrigger = "Summon";
    private int SwipeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 10, 9);
    private int HeadbuttDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 17, 15);

    private const string _sporesSfx = "event:/sfx/enemy/enemy_attacks/fogmog/fogmog_summon";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Plant;


    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState illusionState = new("ILLUSION_MOVE", IllusionMove, new SummonIntent());
        MoveState swipeState = new("SWIPE_MOVE", SwipeMove, new SingleAttackIntent(SwipeDamage), new BuffIntent());
        MoveState swipeStateRandom = new("SWIPE_RANDOM_MOVE", SwipeMove, new SingleAttackIntent(SwipeDamage), new BuffIntent());
        MoveState headbuttState = new("HEADBUTT_MOVE", HeadbuttMove, new SingleAttackIntent(HeadbuttDamage));
        RandomBranchState branchState = new("BRANCH");

        branchState.AddBranch(swipeStateRandom, MoveRepeatType.CannotRepeat, (_, _) => 0.4f);
        branchState.AddBranch(headbuttState, MoveRepeatType.CannotRepeat, (_, _) => 0.6f);

        illusionState.FollowUpState = swipeState;
        swipeState.FollowUpState = branchState;
        swipeStateRandom.FollowUpState = headbuttState;
        headbuttState.FollowUpState = swipeState;

        states.Add(illusionState);
        states.Add(swipeState);
        states.Add(swipeStateRandom);
        states.Add(branchState);
        states.Add(headbuttState);

        return new MonsterMoveStateMachine(states, illusionState);
    }

    private async Task IllusionMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_sporesSfx);
        await CreatureCmd.TriggerAnim(Creature, _summonTrigger, 0.75f);
        await CreatureCmd.Add<EyeWithTeeth>(CombatState, FogmogNormal.illusionSlot);
    }

    private async Task SwipeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SwipeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Strength>(Creature, 1, Creature, null);
    }

    private async Task HeadbuttMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HeadbuttDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    // Fogmog requires a custom animator because he has a summon animation instead of a cast
    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);

        AnimState summonAnim = new("summon");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        summonAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new (idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);
        animator.AddAnyState(_summonTrigger, summonAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);

        return animator;
    }
}
