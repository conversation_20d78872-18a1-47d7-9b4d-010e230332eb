using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SlitheringStrangler : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 54, 53);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 56, 55);

    private int ThwackDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 7);
    private int LashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 13, 12);

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Plant;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = new();

        MoveState constrictState = new("CONSTRICT", ConstrictMove, new DebuffIntent());
        MoveState thwackState = new("TWACK", ThwackMove, new SingleAttackIntent(ThwackDamage), new DefendIntent());
        MoveState lashState = new("LASH", LashMove, new SingleAttackIntent(LashDamage));

        RandomBranchState rand = new("rand");

        constrictState.FollowUpState = rand;
        thwackState.FollowUpState = constrictState;
        lashState.FollowUpState = constrictState;

        rand.AddBranch(thwackState, MoveRepeatType.CanRepeatForever);
        rand.AddBranch(lashState, MoveRepeatType.CanRepeatForever);

        states.Add(rand);
        states.Add(thwackState);
        states.Add(constrictState);
        states.Add(lashState);

        return new MonsterMoveStateMachine(states, constrictState);
    }

    private async Task ConstrictMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.5f);
        await PowerCmd.Apply<Constrict>(targets, 3, Creature, null);
    }

    private async Task ThwackMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ThwackDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CreatureCmd.GainBlock(Creature, 5, BlockProps.monsterMove, null);
    }

    private async Task LashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(LashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.5f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }
}
