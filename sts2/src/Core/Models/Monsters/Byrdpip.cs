using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Byrdpip : MonsterModel
{
    public override int MinInitialHp => 9999;
    public override int MaxInitialHp => 9999;
    public override bool IsHealthBarVisible => false;

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        string skin = Creature.PetOwner!.GetRelic<Relics.Byrdpip>()!.Skin;
        SpineSkeleton skeleton = node.SpineController!.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        skeleton.SetSkin(skeletonData.FindSkin(skin));
        skeleton.SetSlotsToSetupPose();
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        MoveState nothingState = new("NOTHING_MOVE", _ => Task.CompletedTask);
        nothingState.FollowUpState = nothingState;

        return new MonsterMoveStateMachine([nothingState], nothingState);
    }
}