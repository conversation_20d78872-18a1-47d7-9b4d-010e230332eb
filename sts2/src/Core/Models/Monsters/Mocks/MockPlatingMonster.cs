using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters.Mocks;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MockPlatingMonster : MonsterModel
{
    public override LocString Title => L10NMonsterLookup("BIG_DUMMY.name");

    protected override string VisualsPath => SceneHelper.GetScenePath("creature_visuals/defect");

    public override int MinInitialHp => 9999;
    public override int MaxInitialHp => 9999;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState nothing = new("NOTHING", NothingMove, new HiddenIntent());
        nothing.FollowUpState = nothing;
        states.Add(nothing);

        return new MonsterMoveStateMachine(states, nothing);
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Plating>(Creature, 1, Creature, null);
    }

    private Task NothingMove(IReadOnlyList<Creature> targets) => Task.CompletedTask;
}
