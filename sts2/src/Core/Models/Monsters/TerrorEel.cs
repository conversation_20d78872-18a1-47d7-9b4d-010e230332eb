using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TerrorEel : MonsterModel
{
    private const string _thrashMoveId = "ThrashMove";
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 133, 131);
    public override int MaxInitialHp => MinInitialHp;

    private int CrashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 19, 17);

    private int ThrashDamage => 3;
    private int ThrashRepeat => 3;

    // This gets set in GenerateMoveStateMachine, which is effectively a constructor.
    private MoveState _terrorState = default!;

    public MoveState TerrorState
    {
        get => _terrorState;
        set
        {
            AssertMutable();
            _terrorState = value;
        }
    }

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Shriek>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState crashState = new("CRASH_MOVE", CrashMove, new SingleAttackIntent(CrashDamage));
        MoveState thrashState = new(_thrashMoveId, ThrashMove, new MultiAttackIntent(ThrashDamage, ThrashRepeat), new BuffIntent());

        MoveState stunState = new("STUN_MOVE", StunMove, new StunIntent());
        TerrorState = new("TERROR_MOVE", TerrorMove, new DebuffIntent());

        crashState.FollowUpState = thrashState;
        thrashState.FollowUpState = crashState;

        stunState.FollowUpState = TerrorState;
        TerrorState.FollowUpState = crashState;

        states.Add(crashState);
        states.Add(thrashState);

        states.Add(stunState);
        states.Add(TerrorState);

        return new MonsterMoveStateMachine(states, crashState);
    }

    private async Task CrashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(CrashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task ThrashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ThrashDamage, ThrashRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Vigor>(Creature, 7, Creature, null);
    }

    private Task StunMove(IReadOnlyList<Creature> targets)
    {
        return Task.CompletedTask;
    }

    private async Task TerrorMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Vulnerable>(targets, 99, Creature, null);
    }
}
