using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TwoTailedRat : MonsterModel
{
    private readonly LocString _banterLine = new("monsters", "TWO_TAILED_RAT.moves.CALL_FOR_BACKUP.banter");

    private static readonly string[] _barnacleOptions = ["barnacle1", "barnacle1", "barnacle3"];
    private static readonly string[] _headOptions = ["head1", "head2", "head3"];

    private const string _callForBackupMoveId = "CALL_FOR_BACKUP_MOVE";
    private const float _callForBackupChance = 0.75f;

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 24, 22);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 28, 26);

    private int ScratchDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int DiseaseBiteDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    private int _starterMoveIndex = -1;

    public int StarterMoveIndex
    {
        get => _starterMoveIndex;
        set
        {
            AssertMutable();
            _starterMoveIndex = value;
        }
    }

    private int _turnsUntilSummonable = 2;

    private int TurnsUntilSummonable
    {
        get => _turnsUntilSummonable;
        set
        {
            AssertMutable();
            _turnsUntilSummonable = value;
        }
    }

    private const string _summonTrigger = "Summon";

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin(Random.Rng.Chaotic.NextItem(_barnacleOptions)));
        compositeSkin.AddSkin(skeletonData.FindSkin(Random.Rng.Chaotic.NextItem(_headOptions)));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState scratchState = new("SCRATCH_MOVE", ScratchMove, new SingleAttackIntent(ScratchDamage));
        MoveState diseaseBiteState = new("DISEASE_BITE_MOVE", DiseaseBiteMove, new SingleAttackIntent(DiseaseBiteDamage), new StatusIntent(1));
        MoveState screechState = new("SCREECH_MOVE", ScreechMove, new DebuffIntent());
        MoveState callForBackupState = new(_callForBackupMoveId, CallForBackup, new SummonIntent());

        RandomBranchState randState = new("RAND");

        scratchState.FollowUpState = randState;
        diseaseBiteState.FollowUpState = randState;
        screechState.FollowUpState = randState;
        callForBackupState.FollowUpState = randState;

        const float callForBackupInverseChance = (1f - _callForBackupChance) / 3f; // there are 3 other moves, so divide the inverse evenly among them
        randState.AddBranch(scratchState, MoveRepeatType.CanRepeatForever, (_, _) => CanSummon() ? callForBackupInverseChance : 1);
        randState.AddBranch(diseaseBiteState, MoveRepeatType.CannotRepeat, (_, _) => CanSummon() ? callForBackupInverseChance : 1);
        randState.AddBranch(screechState, 3, MoveRepeatType.CannotRepeat, (_, _) => CanSummon() ? callForBackupInverseChance : 1);
        randState.AddBranch(callForBackupState, MoveRepeatType.UseOnlyOnce, (_, _) => CanSummon() ? _callForBackupChance : 0);

        states.Add(randState);
        states.Add(scratchState);
        states.Add(diseaseBiteState);
        states.Add(screechState);
        states.Add(callForBackupState);

        if (StarterMoveIndex == -1)
        {
            return new MonsterMoveStateMachine(states, randState);
        }
        else
        {
            MoveState initialState = (StarterMoveIndex % 3) switch
            {
                0 => scratchState,
                1 => diseaseBiteState,
                _ => screechState
            };
            return new MonsterMoveStateMachine(states, initialState);
        }
    }

    private async Task ScratchMove(IReadOnlyList<Creature> targets)
    {
        TurnsUntilSummonable--;

        await DamageCmd.Attack(ScratchDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.25f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task DiseaseBiteMove(IReadOnlyList<Creature> targets)
    {
        TurnsUntilSummonable--;

        await DamageCmd.Attack(DiseaseBiteDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.25f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CardPileCmd.AddToCombatAndPreview<Toxic>(targets, CardPileTarget.Discard, 1, false);
    }

    private async Task ScreechMove(IReadOnlyList<Creature> targets)
    {
        TurnsUntilSummonable--;
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.3f);
        await PowerCmd.Apply<Frail>(targets, 1, Creature, null);
    }

    private async Task CallForBackup(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, _summonTrigger, 0.3f);
        string openSlot = CombatState.Encounter!.GetNextSlot(CombatState);
        TalkCmd.Play(_banterLine, Creature);

        if (!string.IsNullOrEmpty(openSlot))
        {
            await Cmd.Wait(0.5f);
            await CreatureCmd.Add<TwoTailedRat>(CombatState, openSlot);
        }
    }

    private bool CanSummon()
    {
        if (TurnsUntilSummonable > 0) return false;
        if (string.IsNullOrEmpty(CombatState.Encounter?.GetNextSlot(CombatState))) return false;

        List<Creature> teammates = Creature.CombatState!.GetTeammatesOf(Creature).Where(c => c != Creature).ToList();

        foreach (Creature creature in teammates)
        {
            if (creature.Monster!.NextMove.Id.Equals(_callForBackupMoveId)) return false;
        }

        return true;
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new("debuff");
        AnimState summonAnim = new("summon");
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        summonAnim.NextState = idleAnim;
        castAnim.NextState = idleAnim;
        attackAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(_summonTrigger, summonAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim);

        return animator;
    }
}
