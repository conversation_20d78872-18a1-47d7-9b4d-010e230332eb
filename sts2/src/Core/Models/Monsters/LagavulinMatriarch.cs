using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LagavulinMatriarch : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 233, 222);
    public override int MaxInitialHp => MinInitialHp;

    private int SlashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 21, 19);
    private int DisembowelDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 10, 9);
    private int DisembowelRepeat => 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.ArmorBig;

    public const string slashMoveId = "SLASH_MOVE";


    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Plating>(Creature, 16, Creature, null);
        await PowerCmd.Apply<Asleep>(Creature, 3, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState sleepState = new ("SLEEP_MOVE", SleepMove, new SleepIntent());
        MoveState slashState = new (slashMoveId, SlashMove, new SingleAttackIntent(SlashDamage));
        MoveState disembowelState = new ("DISEMBOWEL_MOVE", DisembowelMove, new MultiAttackIntent(DisembowelDamage, DisembowelRepeat));
        MoveState soulSiphonState = new ("SOUL_SIPHON_MOVE", SoulSiphonMove, new DebuffIntent(), new BuffIntent());

        ConditionalBranchState sleepFollowUp = new ("SLEEP_BRANCH");

        sleepState.FollowUpState = sleepFollowUp;
        slashState.FollowUpState = disembowelState;
        disembowelState.FollowUpState = soulSiphonState;
        soulSiphonState.FollowUpState = slashState;

        sleepFollowUp.AddState(sleepState, (_, o) => o.HasPower<Asleep>());
        sleepFollowUp.AddState(slashState, (_, o) => !o.HasPower<Asleep>());

        states.Add(sleepFollowUp);
        states.Add(sleepState);
        states.Add(slashState);
        states.Add(disembowelState);
        states.Add(soulSiphonState);

        return new MonsterMoveStateMachine(states, sleepState);
    }

    private Task SleepMove(IReadOnlyList<Creature> targets)
    {
        return Task.CompletedTask;
    }

    private async Task SlashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(SlashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task DisembowelMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(DisembowelDamage, DisembowelRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task SoulSiphonMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(targets, -1, Creature, null);
        await PowerCmd.Apply<Dexterity>(targets, -1, Creature, null);
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }
}
