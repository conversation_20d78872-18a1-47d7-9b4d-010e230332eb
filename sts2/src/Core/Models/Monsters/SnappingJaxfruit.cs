using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SnappingJaxfruit : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 32, 31);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 37, 36);

    private int EnergyDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 4, 3);

    private const string _chargeTrigger = "Charge";

    private const string _chargedSfx = "event:/sfx/enemy/enemy_attacks/orb_plant/orb_plant_charged_loop";

    private const string _idleLoopSfx = "event:/sfx/enemy/enemy_attacks/orb_plant/orb_plant_idle_loop";

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Plant;

    private bool _isCharged;

    private bool IsCharged
    {
        get => _isCharged;
        set
        {
            AssertMutable();
            _isCharged = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Disperse>(Creature, 1, Creature, null);
        SfxCmd.PlayLoop(_idleLoopSfx);
    }

    public override void BeforeRemovedFromRoom()
    {
        SfxCmd.StopLoop(_idleLoopSfx);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState energyOrbState = new("ENERGY_ORB_MOVE", EnergyOrb, new SingleAttackIntent(EnergyDamage), new BuffIntent());
        energyOrbState.FollowUpState = energyOrbState;

        states.Add(energyOrbState);

        return new MonsterMoveStateMachine(states, energyOrbState);
    }

    public async Task EnergyOrb(IReadOnlyList<Creature> targets)
    {
        if (TestMode.IsOff)
        {
            NCreature creatureNode = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            Creature? target = LocalContext.GetMe(CombatState)?.Creature;
            creatureNode.GetSpecialNode<NSnappingJaxfruitVfx>("Visuals").SetTarget(target);
        }

        // SfxCmd.Play(CastSfx);
        IsCharged = true;

        await DamageCmd.Attack(EnergyDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.25f)
            .Execute();

        // SfxCmd.PlayLoop(_chargedSfx);
        IsCharged = false;
        await CreatureCmd.TriggerAnim(Creature, _chargeTrigger, 0.25f);
        await PowerCmd.Apply<Strength>(Creature, 1, Creature, null);
        // SfxCmd.StopLoop(_chargedSfx);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleLoop = new(AnimState.idleAnim, true);
        AnimState hurtState = new(AnimState.hurtAnim);
        AnimState deathState = new(AnimState.dieAnim);

        AnimState chargeUpState = new("charge_up");
        AnimState chargeUpLoop = new("charged_loop", true);
        AnimState chargeHurtState = new("hurt_charged");
        AnimState castState = new(AnimState.castAnim);

        hurtState.NextState = idleLoop;
        chargeUpState.NextState = chargeUpLoop;
        chargeHurtState.NextState = chargeUpLoop;
        castState.NextState = idleLoop;

        SpineAnimator animator = new(idleLoop, spineController);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathState);
        animator.AddAnyState(_chargeTrigger, chargeUpState);
        animator.AddAnyState(SpineAnimator.castTrigger, castState);
        animator.AddAnyState(SpineAnimator.hitTrigger, chargeHurtState, () => IsCharged);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtState, () => !IsCharged);

        return animator;
    }
}
