using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LeafSlimeM : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 31, 30);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 35, 34);

    private int ClumpDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private const int _stickyAmount = 2;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState clumpShotState = new("CLUMP_SHOT", ClumpShotMove, new SingleAttackIntent(ClumpDamage));
        MoveState stickyShotState = new("STICKY_SHOT", StickyShotMove, new StatusIntent(_stickyAmount));
        RandomBranchState randState = new("RAND");

        clumpShotState.FollowUpState = randState;
        stickyShotState.FollowUpState = randState;

        randState.AddBranch(clumpShotState, MoveRepeatType.CannotRepeat);
        randState.AddBranch(stickyShotState, 2);

        states.Add(clumpShotState);
        states.Add(stickyShotState);
        states.Add(randState);

        return new MonsterMoveStateMachine(states, stickyShotState);
    }

    private async Task ClumpShotMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ClumpDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slimeImpactVfxPath)
            .Execute();
    }

    private async Task StickyShotMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(CastSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 1);

        if (TestMode.IsOff)
        {
            // Set the bone position to the left-most player.
            Vector2? leftMostPlayerPos = null;

            foreach (Creature target in targets)
            {
                NCreature targetNode = NCombatRoom.Instance!.GetCreatureNode(target)!;

                if (leftMostPlayerPos == null || leftMostPlayerPos.Value.X > targetNode.GlobalPosition.X)
                {
                    leftMostPlayerPos = targetNode.VfxSpawnPosition;
                }
            }

            NCreature creatureNode = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
            SpineBoneNode bone = creatureNode.GetSpecialNode<SpineBoneNode>("Visuals/SpitTarget");
            bone.GlobalPosition = leftMostPlayerPos!.Value;
        }

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.slimeImpactVfxPath);
        await CardPileCmd.AddToCombatAndPreview<Slimed>(targets, CardPileTarget.Discard, _stickyAmount, false);
    }
}
