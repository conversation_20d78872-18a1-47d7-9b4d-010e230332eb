using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class WaspPrism : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 90, 85);
    public override int MaxInitialHp => MinInitialHp;

    private int BeamDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 16, 14);
    private int PulsatePowerAmount => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 3, 2);
    private int WhirlingSlashDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int WhirlingSlashBlock => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 9, 8);
    private int RadiateDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 7, 6);
    private int RadiateRepeat => 2;

    private bool _isFront;
    public override bool ShouldDisappearFromDoom => !Creature.HasPower<Volatile>();


    public bool IsFront
    {
        get => _isFront;
        set
        {
            AssertMutable();
            _isFront = value;
        }
    }
    private int _volatileDamage;

    private int VolatileDamage
    {
        get => _volatileDamage;
        set
        {
            AssertMutable();
            _volatileDamage = value;
        }
    }

    // This gets set in GenerateMoveStateMachine, which is effectively a constructor.
    private MoveState _volatileState = default!;

    private MoveState VolatileState
    {
        get => _volatileState;
        set
        {
            AssertMutable();
            _volatileState = value;
        }
    }

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Stone;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Volatile>(Creature, 15, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];
        MoveState beamState = new("BEAM_MOVE", BeamMove, new SingleAttackIntent(BeamDamage));
        MoveState pulsateState = new("PULSATE_MOVE", PulsateMove, new BuffIntent());
        MoveState whirlingSlashState = new("WHIRLING_SLASH_MOVE", WhirlingSlashMove, new SingleAttackIntent(WhirlingSlashDamage), new DefendIntent());
        MoveState radiateState = new("RADIATE_MOVE", RadiateMove, new MultiAttackIntent(RadiateDamage, RadiateRepeat));
        VolatileState = new MoveState("ABOUT_TO_BLOW_MOVE", VolatileMove, new StunIntent()) { MustPerformOnceBeforeTransitioning = true };
        MoveState explodeState = new("EXPLODE_MOVE", ExplodeMove, new DeathBlowIntent(() => VolatileDamage));

        beamState.FollowUpState = pulsateState;
        pulsateState.FollowUpState = whirlingSlashState;
        whirlingSlashState.FollowUpState = radiateState;
        radiateState.FollowUpState = beamState;

        VolatileState.FollowUpState = explodeState;
        explodeState.FollowUpState = explodeState;

        states.Add(beamState);
        states.Add(pulsateState);
        states.Add(whirlingSlashState);
        states.Add(radiateState);
        states.Add(explodeState);
        states.Add(VolatileState);

        return new MonsterMoveStateMachine(states, IsFront ? beamState : pulsateState);
    }

    private async Task BeamMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BeamDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task PulsateMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Strength>(Creature, PulsatePowerAmount, Creature, null);
    }

    private async Task WhirlingSlashMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(WhirlingSlashDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CreatureCmd.GainBlock(Creature, WhirlingSlashBlock, BlockProps.monsterMove, null);
    }

    private async Task RadiateMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(RadiateDamage, RadiateRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task VolatileMove(IReadOnlyList<Creature> targets)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);
        VolatileDamage = Creature.GetPowerAmount<Volatile>();
        await PowerCmd.Remove<Volatile>(Creature);

        if (creatureNode != null)
        {
            creatureNode.Visuals.Modulate = Colors.Red;
        }
    }

    private async Task ExplodeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(VolatileDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .Execute();

        // TODO: Give the creature a sec before it dies. Should be able to get rid of this once
        // we have animation
        await Task.Delay(500);
        await CreatureCmd.Kill(Creature);
    }

    public async Task TriggerAboutToBlowState()
    {
        await CreatureCmd.SetMaxHp(Creature, Really.bigNumber);
        await CreatureCmd.SetCurrentHp(Creature, Really.bigNumber);
        Creature.ShowsInfiniteHp = true;

        // Do final move before dying
        SetMoveImmediate(VolatileState);
    }
}
