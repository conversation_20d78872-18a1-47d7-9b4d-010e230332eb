using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class OneHpMonster : MonsterModel
{
    public override LocString Title => L10NMonsterLookup("BIG_DUMMY.name");

    public override int MinInitialHp => 1;
    public override int MaxInitialHp => 1;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState nothing = new("NOTHING", NothingMove, new HiddenIntent());
        nothing.FollowUpState = nothing;
        states.Add(nothing);

        return new MonsterMoveStateMachine(states, nothing);
    }

    private Task NothingMove(IReadOnlyList<Creature> targets) => Task.CompletedTask;
}