using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SoulFysh : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 191, 181);
    public override int MaxInitialHp => MinInitialHp;

    private int DeGasDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 18, 16);

    private int ScreamDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 14, 12);
    private int BeckonDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 6);
    private int BeckonMoveAmount => 2;
    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Magic;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Intangible>(Creature, 1, Creature, null);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState beckonState = new("BECKON_MOVE", BeckonMove, new SingleAttackIntent(BeckonDamage), new StatusIntent(BeckonMoveAmount));
        MoveState deGasState = new("DE_GAS_MOVE", DeGasMove, new SingleAttackIntent(DeGasDamage));
        MoveState fadeState = new("FADE_MOVE", FadeMove, new BuffIntent());
        MoveState screamState = new("SCREAM_MOVE", ScreamMove, new SingleAttackIntent(ScreamDamage), new DebuffIntent());

        RandomBranchState randState = new("RAND");

        beckonState.FollowUpState = randState;
        deGasState.FollowUpState = randState;
        fadeState.FollowUpState = screamState;
        screamState.FollowUpState = randState;

        // every 5 turns after turn 4 (4, 9, 13...) gain intangible
        randState.AddBranch(fadeState, MoveRepeatType.CannotRepeat, (_, _) => ((CombatState.RoundNumber - 4) % 5) == 0 ? 1 : 0);
        randState.AddBranch(beckonState, MoveRepeatType.CannotRepeat, (_, _) => ((CombatState.RoundNumber - 4) % 5) != 0 ? 1 : 0);
        randState.AddBranch(deGasState, MoveRepeatType.CannotRepeat, (_, _) => ((CombatState.RoundNumber - 4) % 5) != 0 ? 1 : 0);

        states.Add(randState);
        states.Add(beckonState);
        states.Add(deGasState);
        states.Add(screamState);
        states.Add(fadeState);

        return new MonsterMoveStateMachine(states, beckonState);
    }

    private async Task BeckonMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(BeckonDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.75f)
            .WithAttackerFx(sfx: CastSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        foreach (Creature target in targets)
        {
            Player player = target.Player ?? target.PetOwner!;

            CardPileAddResult[] statusCards = new CardPileAddResult[BeckonMoveAmount];

            for (int i = 0; i < BeckonMoveAmount; i++)
            {
                CardModel beckon = CombatState.CreateCard<Beckon>(player);
                statusCards[i] = await CardPileCmd.Add(beckon, CardPileTarget.Draw, CardPilePosition.Random);
            }

            if (LocalContext.IsMe(player))
            {
                CardCmd.PreviewCardPileAdd(statusCards);
                await Cmd.Wait(1f);
            }
        }
    }

    private async Task DeGasMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(DeGasDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task ScreamMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(ScreamDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await PowerCmd.Apply<Vulnerable>(targets, 2, Creature, null);
    }

    private async Task FadeMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Intangible>(Creature, 2, Creature, null);
    }
}
