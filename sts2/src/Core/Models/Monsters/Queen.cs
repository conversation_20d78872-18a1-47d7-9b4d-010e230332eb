using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Queen : MonsterModel
{
    private const string _queenTrackName = "queen_progress";

    protected override string AttackSfx => "event:/sfx/enemy/enemy_attacks/queen/queen_arms_attack";
    private const string _castSfx = "event:/sfx/enemy/enemy_attacks/queen/queen_cast";

    public override IEnumerable<string> AssetPaths => base.AssetPaths.Concat(ModelDb.Monster<TorchHeadAmalgam>().AssetPaths);

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 399, 386);
    public override int MaxInitialHp => MinInitialHp;

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Armor;

    private int OffWithYourHeadDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);
    private const int _offWithYourHeadRepeat = 5;

    private int StaffStrikeDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 12, 11);

    private bool _hasAmalgamDied;

    private bool HasAmalgamDied
    {
        get => _hasAmalgamDied;
        set
        {
            AssertMutable();
            _hasAmalgamDied = value;
        }
    }

    private Creature? _amalgam;

    private Creature? Amalgam
    {
        get => _amalgam;
        set
        {
            AssertMutable();
            _amalgam = value;
        }
    }

    private MoveState _burnBrightForMeState = default!;

    private MoveState BurnBrightForMeState
    {
        get => _burnBrightForMeState;
        set
        {
            AssertMutable();
            _burnBrightForMeState = value;
        }
    }

    private MoveState _enragedState = default!;

    private MoveState EnragedState
    {
        get => _enragedState;
        set
        {
            AssertMutable();
            _enragedState = value;
        }
    }

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        node.SpineController!.GetAnimationState().SetAnimation("tracks/writhe", true, 1);
    }

    public override void BeforeRemovedFromRoom()
    {
        // If we end because of a game over, keep the animation going.
        if (CombatState.ClimbState.IsGameOver) return;

        // Call this so the Queens inner hands stop writhing while she dies.
        // May be null if Queen is removed from the room before SetupSkins is called
        // or doom has already removed it from the room
        NCombatRoom.Instance!.GetCreatureNode(Creature)?.SpineController!.GetAnimationState().SetAnimation("tracks/empty", true, 1);
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        if (AscensionHelper.HasAscension(AscensionLevel.MightyBosses))
        {
            await PowerCmd.Apply<Intangible>(Creature, 1, Creature, null);
        }

        Creature.Died += AfterDeath;
    }

    private void AfterDeath(Creature _)
    {
        Creature.Died -= AfterDeath;
        NClimbMusicController.Instance?.UpdateMusicParameter(_queenTrackName, 5);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        // Pre Enrage State
        MoveState toMeState = new("TO_ME_MOVE", ToMeMove, new SummonIntent());
        MoveState puppetStringsState = new("PUPPET_STRINGS_MOVE", PuppetStringsMove, new CardDebuffIntent());
        MoveState youreMineNowState = new("YOUR_MINE_MOVE", YoureMineMove, new DebuffIntent());
        ConditionalBranchState youreMineNowFollowUpBranch = new("YOURE_MINE_NOW_BRANCH");

        BurnBrightForMeState = new("BURN_BRIGHT_FOR_ME_MOVE", BurnBrightForMeMove, new BuffIntent());
        ConditionalBranchState burnBrightForMeFollowUpBranch = new("BURN_BRIGHT_FOR_ME_BRANCH");

        MoveState soulSapState = new("SOUL_SAP", SoulSapMove, new DebuffIntent());
        ConditionalBranchState soulSapFollowupBranch = new("SOUL_SAP_BRANCH");

        MoveState staffStrikePhaseState = new("STAFF_STRIKE_MOVE", StaffStrikeMove, new SingleAttackIntent(StaffStrikeDamage));
        ConditionalBranchState staffStrikeFollowUpBranch = new("STAFF_STRIKE_BRANCH");


        // POST Amalgam Death State
        MoveState offWithYourHeadState = new("OFF_WITH_YOUR_HEAD_MOVE", OffWithYourHeadMove, new MultiAttackIntent(OffWithYourHeadDamage, _offWithYourHeadRepeat));
        EnragedState = new("ENRAGE_MOVE", EnrageMove, new BuffIntent());

        // opening moves
        toMeState.FollowUpState = puppetStringsState;
        puppetStringsState.FollowUpState = youreMineNowState;
        youreMineNowState.FollowUpState = youreMineNowFollowUpBranch;
        youreMineNowFollowUpBranch.AddState(BurnBrightForMeState, (_, _) => !HasAmalgamDied);
        youreMineNowFollowUpBranch.AddState(offWithYourHeadState, (_, _) => HasAmalgamDied);

        // PHASE 1.
        // we need the extra branch states here so that we can always transition to phase 2.

        BurnBrightForMeState.FollowUpState = burnBrightForMeFollowUpBranch;
        burnBrightForMeFollowUpBranch.AddState(soulSapState, (_, _) => !HasAmalgamDied);
        burnBrightForMeFollowUpBranch.AddState(offWithYourHeadState, (_, _) => HasAmalgamDied);

        soulSapState.FollowUpState = soulSapFollowupBranch;
        soulSapFollowupBranch.AddState(staffStrikePhaseState, (_, _) => !HasAmalgamDied);
        soulSapFollowupBranch.AddState(offWithYourHeadState, (_, _) => HasAmalgamDied);

        staffStrikePhaseState.FollowUpState = staffStrikeFollowUpBranch;
        staffStrikeFollowUpBranch.AddState(BurnBrightForMeState, (_, _) => !HasAmalgamDied);
        staffStrikeFollowUpBranch.AddState(offWithYourHeadState, (_, _) => HasAmalgamDied);

        // PHASE 2.
        offWithYourHeadState.FollowUpState = EnragedState;
        EnragedState.FollowUpState = offWithYourHeadState;

        states.Add(toMeState);
        states.Add(puppetStringsState);
        states.Add(youreMineNowState);
        states.Add(BurnBrightForMeState);
        states.Add(soulSapState);
        states.Add(staffStrikePhaseState);

        states.Add(burnBrightForMeFollowUpBranch);
        states.Add(staffStrikeFollowUpBranch);
        states.Add(youreMineNowFollowUpBranch);
        states.Add(soulSapFollowupBranch);

        states.Add(offWithYourHeadState);
        states.Add(EnragedState);

        return new MonsterMoveStateMachine(states, toMeState);
    }

    private async Task ToMeMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);

        Amalgam = await CreatureCmd.Add<TorchHeadAmalgam>(CombatState, QueenBoss.amalgamSlot);
        await PowerCmd.Apply<Minion>(Amalgam, 1, Creature, null);
        Amalgam.Died += AmalgamDeathResponse;
        NClimbMusicController.Instance?.UpdateMusicParameter(_queenTrackName, 1);
    }

    private async Task PuppetStringsMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        await PowerCmd.Apply<ChainsOfBinding>(targets, 3, Creature, null);
    }

    private async Task YoureMineMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        await PowerCmd.Apply<Frail>(targets, 4, Creature, null);
        await PowerCmd.Apply<Weak>(targets, 4, Creature, null);
        await PowerCmd.Apply<Vulnerable>(targets, 4, Creature, null);
    }

    private async Task BurnBrightForMeMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.8f);

        List<Creature> teammates = Creature.CombatState!.GetTeammatesOf(Creature).ToList();
        foreach (Creature teammate in teammates)
        {
            if (teammate != Creature)
            {
                await PowerCmd.Apply<Strength>(teammate, 3, Creature, null);
            }
        }
    }

    private async Task SoulSapMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        await PowerCmd.Apply<Strength>(targets, -1, Creature, null);
        await PowerCmd.Apply<Dexterity>(targets, -1, Creature, null);
    }

    private async Task StaffStrikeMove(IReadOnlyList<Creature> targets)
    {
        // This should be another attack that is not her special one. Should hit with her staff.
        await DamageCmd.Attack(StaffStrikeDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.castTrigger, 0.5f)
            .WithAttackerFx(sfx: _castSfx)
            .WithHitFx(VfxCmd.slashPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    private async Task OffWithYourHeadMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(OffWithYourHeadDamage, _offWithYourHeadRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.6f)
            .OnlyPlayAnimOnce()
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task EnrageMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_castSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.5f);
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }

    private void AmalgamDeathResponse(Creature _)
    {
        NClimbMusicController.Instance?.UpdateMusicParameter(_queenTrackName, 2);
        Amalgam!.Died -= AmalgamDeathResponse;
        if (Creature.IsDead) return;

        HasAmalgamDied = true;
        Amalgam = null;

        LocString line = L10NMonsterLookup("QUEEN.amalgamDeathSpeakLine");
        TalkCmd.Play(line, Creature, vfxColor: VfxColor.Purple);

        // if she is using BURN BRIGHT FOR ME. then automatically force ENRAGE. since she no longer has any amalgam to buff
        if (NextMove == BurnBrightForMeState)
        {
            SetMoveImmediate(EnragedState);
        }
    }
}
