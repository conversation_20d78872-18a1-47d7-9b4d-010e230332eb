using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ThievingHopper : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 78, 73);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 81, 76);

    public const string stunTrigger = "StunTrigger";

    private bool _isHovering;

    public bool IsHovering
    {
        get => _isHovering;
        set
        {
            AssertMutable();
            _isHovering = value;
        }
    }

    private const string _fleeTrigger = "Flee";
    private const string _hoverTrigger = "Hover";
    private const string _stealTrigger = "Steal";
    private const string _escapeMoveId = "ESCAPE_MOVE";
    protected override string AttackSfx => IsHovering ? "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_attack_hover" : "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_attack";
    private string FleeSfx => IsHovering ? "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_flee_hover" : "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_flee";
    private const string _stealSfx = "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_steal";
    private const string _takeOffSfx = "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_take_off";
    public const string hoverLoop = "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_hover_loop";

    public override string DeathSfx => "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_die";
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Insect;

    public override string TakeDamageSfx => IsHovering ? "event:/sfx/enemy/enemy_attacks/thieving_hopper/thieving_hopper_hurt_hover" : base.TakeDamageSfx;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<EscapeArtist>(Creature, 5, Creature, null);
    }

    public override void BeforeRemovedFromRoom()
    {
        SfxCmd.StopLoop(hoverLoop);
    }

    private int TheftDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 21, 19);
    private int HatTrickDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 26, 22);
    private int NabDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 16, 14);
    private const int _nabBlock = 11;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState thieveryState = new("THIEVERY_MOVE", ThieveryMove, new SingleAttackIntent(TheftDamage), new CardDebuffIntent());
        MoveState nabState = new("NAB_MOVE", NabMove, new SingleAttackIntent(NabDamage), new DefendIntent());
        MoveState hatTrickState = new("HAT_TRICK_MOVE", HatTrickMove, new SingleAttackIntent(HatTrickDamage));
        MoveState flutterState = new("FLUTTER_MOVE", FlutterMove, new BuffIntent());
        MoveState escapeState = new(_escapeMoveId, EscapeMove, new EscapeIntent());

        thieveryState.FollowUpState = flutterState;
        flutterState.FollowUpState = hatTrickState;
        hatTrickState.FollowUpState = nabState;
        nabState.FollowUpState = escapeState;
        escapeState.FollowUpState = escapeState;

        states.Add(thieveryState);
        states.Add(nabState);
        states.Add(hatTrickState);
        states.Add(flutterState);
        states.Add(escapeState);

        return new MonsterMoveStateMachine(states, thieveryState);
    }

    private async Task ThieveryMove(IReadOnlyList<Creature> targets)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);

        if (creatureNode != null)
        {
            // Set the bone position to the local player
            Creature targetCreature = LocalContext.GetMe(targets) ?? targets.First();
            NCreature? targetNode = NCombatRoom.Instance!.GetCreatureNode(targetCreature);
            SpineBoneNode bone = creatureNode.GetSpecialNode<SpineBoneNode>("Visuals/SpineBoneNode");
            bone.Position = Vector2.Right * (targetNode!.GlobalPosition.X - creatureNode.GlobalPosition.X);
        }

        // Only trigger animation/sound once
        await CreatureCmd.TriggerAnim(Creature, _stealTrigger, 0.25f);
        SfxCmd.Play(_stealSfx);

        List<CardModel> cardsToRemove = [];

        foreach (Creature target in targets)
        {
            // NOTE: we need to make sure that the card Thieving Hopper takes actually has a deck equivalent (exclude
            // things like shivs)
            List<CardModel> candidateCards = CardPile.GetCards(target.Player ?? target.PetOwner!, CardPileTarget.Draw, CardPileTarget.Discard)
                .Where(c => c.Rarity is CardRarity.Rare or CardRarity.Uncommon or CardRarity.Common && c.DeckVersion != null)
                .ToList();

            if (candidateCards.Count == 0)
            {
                candidateCards = CardPile.GetCards(target.Player ?? target.PetOwner!, CardPileTarget.Draw, CardPileTarget.Discard)
                    .Where(c => c.DeckVersion != null)
                    .ToList();
            }

            if (candidateCards.Count == 0)
            {
                // player has no cards in their combat deck
                // Suggestion: if you have no cards in your deck, hopper gives you a new card "Pity"
                continue;
            }

            candidateCards.StableShuffle(ClimbRng.CombatCardGeneration);

            CardModel cardToSteal = candidateCards.First();
            await CardPileCmd.RemoveFromCombat(cardToSteal);
            cardsToRemove.Add(cardToSteal);
        }

        await Cmd.Wait(0.75f);

        foreach (CardModel cardToSteal in cardsToRemove)
        {
            if (creatureNode != null && LocalContext.IsMine(cardToSteal))
            {
                Marker2D cardHolder = creatureNode.GetSpecialNode<Marker2D>("%StolenCardPos");
                NCard card = NCard.Create(cardToSteal)!;
                cardHolder.AddChildSafely(card);
                card.Position += card.Size * 0.5f;
                card.UpdateVisuals(CardPileTarget.Deck);
            }

            Swipe swipe = (Swipe)ModelDb.Power<Swipe>().ToMutable();
            swipe.Target = cardToSteal.Owner.Creature;
            swipe.StolenCard = cardToSteal;

            await PowerCmd.Apply(swipe, Creature, 1, Creature, null);
        }

        await DamageCmd.Attack(TheftDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();
    }

    private async Task NabMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(NabDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        await CreatureCmd.GainBlock(Creature, _nabBlock, BlockProps.monsterMove, null);
    }

    private async Task HatTrickMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(HatTrickDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.3f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    private async Task FlutterMove(IReadOnlyList<Creature> targets)
    {
        IsHovering = true;
        SfxCmd.Play(_takeOffSfx);
        SfxCmd.PlayLoop(hoverLoop);
        await CreatureCmd.TriggerAnim(Creature, _hoverTrigger, 0);
        await Cmd.Wait(1.25f); // we do a manual pause here, so it isn't affected by play speed

        await PowerCmd.Apply<Flutter>(Creature, 5, Creature, null);
    }

    private async Task EscapeMove(IReadOnlyList<Creature> targets)
    {
        // This hides the health bar
        NCombatRoom.Instance?.GetCreatureNode(Creature)?.ToggleIsInteractable(false);

        SfxCmd.Play(FleeSfx);
        await CreatureCmd.TriggerAnim(Creature, _fleeTrigger, 0.85f);

        if (IsHovering)
        {
            SfxCmd.StopLoop(hoverLoop);
            IsHovering = false;
        }

        foreach (PowerModel power in Creature.Powers)
        {
            if (power is not Swipe swipe) continue;

            if (swipe.StolenCard is { DeckVersion: not null })
            {
                await CardPileCmd.RemoveFromDeck(swipe.StolenCard.DeckVersion);
            }
        }

        await Cmd.Wait(1.5f);

        await CreatureCmd.Escape(Creature);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleLoop = new(AnimState.idleAnim, true);
        AnimState fleeGrounded = new("flee");
        AnimState fleeHover = new("flee_hover");
        AnimState hurtGrounded = new(AnimState.hurtAnim);
        AnimState hurtHover = new("hurt_hover");
        AnimState attackGrounded = new(AnimState.attackAnim);
        AnimState attackHover = new("attack_hover");
        AnimState deathAnim = new(AnimState.dieAnim);

        AnimState takeoffState = new("take_off");
        AnimState hoverLoopState = new("hover_loop", true) { BoundsContainer = "FlyingBounds" };

        AnimState stealAnim = new("steal");

        takeoffState.NextState = hoverLoopState;
        stealAnim.NextState = idleLoop;
        hurtGrounded.NextState = idleLoop;
        hurtHover.NextState = hoverLoopState;
        attackGrounded.NextState = idleLoop;
        attackHover.NextState = hoverLoopState;

        SpineAnimator animator = new(idleLoop, spineController);
        animator.AddAnyState(stunTrigger, idleLoop);
        animator.AddAnyState(_hoverTrigger, takeoffState);
        animator.AddAnyState(_stealTrigger, stealAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtHover, () => IsHovering);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtGrounded, () => !IsHovering);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackHover, () => IsHovering);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackGrounded, () => !IsHovering);
        animator.AddAnyState(_fleeTrigger, fleeHover, () => IsHovering);
        animator.AddAnyState(_fleeTrigger, fleeGrounded, () => !IsHovering);

        return animator;
    }
}
