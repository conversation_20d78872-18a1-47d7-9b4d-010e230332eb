using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MultiAttackMoveMonster : MonsterModel
{
    public const int repeat = 5;

    public override LocString Title => L10NMonsterLookup("BIG_DUMMY.name");
    public override int MinInitialHp => 999;
    public override int MaxInitialHp => 999;

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState poke = new("POKE", PokeMove, new MultiAttackIntent(1, repeat));
        poke.FollowUpState = poke;
        states.Add(poke);

        return new MonsterMoveStateMachine(states, poke);
    }

    private async Task PokeMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(1, repeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .Execute();
    }
}
