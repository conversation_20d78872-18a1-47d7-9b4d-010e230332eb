using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CubexConstruct : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 70, 65);
    public override int MaxInitialHp => MinInitialHp;

    private static readonly string[] _eyeOptions = ["diamondeye", "circleeye", "squareeye"];
    private static readonly string[] _mossOptions = ["moss1", "moss2", "moss3"];

    private const string _burrowTrigger = "Burrow";
    private const string _chargeTrigger = "Charge";
    private const string _attackEndTrigger = "AttackEnd";

    private int BlastDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 7);
    private int ExpelDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 6, 5);
    private const int _expelRepeats = 2;


    private const string _burrowSfx = "event:/sfx/enemy/enemy_attacks/cubex_construct/cubex_construct_burrow";
    private const string _chargedLoopSfx = "event:/sfx/enemy/enemy_attacks/cubex_construct/cubex_construct_charge_attack";

    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Stone;

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin(Rng.Chaotic.NextItem(_eyeOptions)));
        compositeSkin.AddSkin(skeletonData.FindSkin(Rng.Chaotic.NextItem(_mossOptions)));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await CreatureCmd.GainBlock(Creature, 13, BlockProps.monsterMove, null);
        Creature.CurrentHpChanged += OnHpChanged;
    }

    public override void BeforeRemovedFromRoom()
    {
        SfxCmd.SetParam(_chargedLoopSfx, "loop", 2);
        Creature.CurrentHpChanged -= OnHpChanged;
    }

    public void OnHpChanged(int oldHp, int newHp)
    {
        if (newHp < oldHp)
        {
            SfxCmd.SetParam(_chargedLoopSfx, "enemy_hurt", 1);
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState chargeUpState = new("CHARGE_UP_MOVE", ChargeUpMove, new BuffIntent());
        MoveState repeaterState = new("REPEATER_MOVE", RepeaterBlastMove, new SingleAttackIntent(BlastDamage), new BuffIntent());
        MoveState repeaterState2 = new("REPEATER_MOVE_2", RepeaterBlastMove, new SingleAttackIntent(BlastDamage), new BuffIntent());
        MoveState expellingBlast = new("EXPEL_BLAST", ExpelBlastMove, new MultiAttackIntent(ExpelDamage, _expelRepeats));
        MoveState submergeState = new("SUBMERGE_MOVE", SubmergeMove, new DefendIntent());

        chargeUpState.FollowUpState = repeaterState;
        repeaterState.FollowUpState = repeaterState2;
        repeaterState2.FollowUpState = expellingBlast;
        expellingBlast.FollowUpState = repeaterState;
        submergeState.FollowUpState = chargeUpState;

        states.Add(chargeUpState);
        states.Add(repeaterState);
        states.Add(repeaterState2);
        states.Add(expellingBlast);
        states.Add(submergeState);

        return new MonsterMoveStateMachine(states, chargeUpState);
    }

    private async Task ChargeUpMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.PlayLoop(_chargedLoopSfx);
        await CreatureCmd.TriggerAnim(Creature, _chargeTrigger, 0);
        await Cmd.Wait(0.75f); // we do a manual pause here, so it isn't affected by play speed
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }

    private async Task RepeaterBlastMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.SetParam(_chargedLoopSfx, "loop", 1);
        await Cmd.Wait(0.4f);

        await DamageCmd.Attack(BlastDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();

        SfxCmd.SetParam(_chargedLoopSfx, "loop", 0f);

        await Cmd.Wait(0.2f);
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);

        await CreatureCmd.TriggerAnim(Creature, _attackEndTrigger, 0);
    }

    private async Task ExpelBlastMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.SetParam(_chargedLoopSfx, "loop", 1);
        await Cmd.Wait(0.4f);

        await DamageCmd.Attack(ExpelDamage, _expelRepeats)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0f)
            .WithAttackerFx(sfx: AttackSfx)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();

        await Cmd.Wait(0.2f);
        await CreatureCmd.TriggerAnim(Creature, _attackEndTrigger, 0);
    }

    private async Task SubmergeMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(_burrowSfx);
        await CreatureCmd.TriggerAnim(Creature, _burrowTrigger, 0);
        await Cmd.Wait(1.25f); // we do a manual pause here, so it isn't affected by play speed

        await CreatureCmd.GainBlock(Creature, 15, BlockProps.monsterMove, null);
    }


    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState burrowedAnim = new("burrowed_loop", true) { BoundsContainer = "BurrowedBounds" };
        AnimState burrowedStartAnim = new("burrow");
        AnimState unburrowAnim = new("unburrow");

        AnimState idleAnim = new(AnimState.idleAnim, true) { BoundsContainer = "IdleBounds" };
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        AnimState hurtChargingAnim = new(AnimState.hurtAnim);
        AnimState chargeStartAnim = new("charge_start") { BoundsContainer = "ChargingBounds" };
        AnimState chargeLoopAnim = new("charge_loop", true);

        AnimState attackLoopAnim = new("attack_loop", true);
        AnimState attackEndAnim = new("attack_finish");

        // burrowed
        burrowedStartAnim.NextState = burrowedAnim;
        burrowedAnim.AddBranch(_chargeTrigger, unburrowAnim);
        unburrowAnim.NextState = chargeStartAnim;

        // idle >> burrowed and idle >> charging
        idleAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim);
        idleAnim.AddBranch(_burrowTrigger, burrowedStartAnim);

        hurtAnim.NextState = idleAnim;
        hurtAnim.AddBranch(SpineAnimator.hitTrigger, hurtAnim);
        hurtAnim.AddBranch(_burrowTrigger, burrowedStartAnim);

        // charging >> attack loop
        chargeStartAnim.NextState = chargeLoopAnim;
        chargeLoopAnim.AddBranch(SpineAnimator.hitTrigger, hurtChargingAnim);
        chargeLoopAnim.AddBranch(SpineAnimator.attackTrigger, attackLoopAnim);

        attackLoopAnim.AddBranch(_attackEndTrigger, attackEndAnim);

        attackEndAnim.NextState = chargeStartAnim;
        attackEndAnim.AddBranch(SpineAnimator.hitTrigger, hurtChargingAnim);

        // hit >> idle
        hurtChargingAnim.NextState = chargeLoopAnim;
        hurtChargingAnim.AddBranch(SpineAnimator.hitTrigger, hurtChargingAnim);
        hurtChargingAnim.AddBranch(SpineAnimator.attackTrigger, attackLoopAnim);

        SpineAnimator animator = new(burrowedStartAnim, spineController);
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim);

        return animator;
    }
}
