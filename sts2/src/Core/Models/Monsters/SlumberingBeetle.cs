using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SlumberingBeetle : MonsterModel
{
    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 86, 83);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 92, 88);

    public const string wakeUpTrigger = "WakeUp";
    private const string _rolloutTrigger = "Rollout";

    public const string rolloutMoveId = "ROLL_OUT_MOVE";
    private int RolloutDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 18, 16);

    private const string _rollSfx = "event:/sfx/enemy/enemy_attacks/slumbering_beetle/slumbering_beetle_roll";
    public const string wakeUp = "event:/sfx/enemy/enemy_attacks/slumbering_beetle/slumbering_beetle_wake_up";
    public const string sleepLoop = "event:/sfx/enemy/enemy_attacks/slumbering_beetle/slumbering_beetle_sleep_loop";

    private bool _isAwake;

    public bool IsAwake
    {
        get => _isAwake;
        set
        {
            AssertMutable();
            _isAwake = value;
        }
    }

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Plating>(Creature, 10, Creature, null);
        await PowerCmd.Apply<Slumber>(Creature, 3, Creature, null);

        // TODO: revisit once Clark adds the loop param to the sfx
        // SfxCmd.PlayLoop(sleepLoop);
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState snoreState = new("SNORE_MOVE", SnoreMove, new SleepIntent());
        MoveState rolloutState = new(rolloutMoveId, RolloutMove, new SingleAttackIntent(RolloutDamage), new BuffIntent());
        ConditionalBranchState snoreFollowUp = new("SNORE_NEXT");

        snoreState.FollowUpState = snoreFollowUp;
        snoreFollowUp.AddState(snoreState, (_, o) => o.HasPower<Slumber>());
        snoreFollowUp.AddState(rolloutState, (_, o) => !o.HasPower<Slumber>());
        rolloutState.FollowUpState = rolloutState;


        states.Add(snoreState);
        states.Add(snoreFollowUp);
        states.Add(rolloutState);

        return new MonsterMoveStateMachine(states, snoreState);
    }

    private Task SnoreMove(IReadOnlyList<Creature> targets)
    {
        // No-op
        return Task.CompletedTask;
    }

    private async Task RolloutMove(IReadOnlyList<Creature> targets)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(Creature);

        if (creatureNode != null)
        {
            // Set the bone position to the local player.
            NCreature playerNode = NCombatRoom.Instance!.GetCreatureNode(LocalContext.GetMe(CombatState)!.Creature)!;
            SpineBoneNode bone = creatureNode.GetSpecialNode<SpineBoneNode>("Visuals/SpineBoneNode");
            bone.Position = Vector2.Left * (creatureNode.GlobalPosition.X - playerNode.GlobalPosition.X);
        }

        await DamageCmd.Attack(RolloutDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_rolloutTrigger, 0.5f)
            .WithAttackerFx(sfx: _rollSfx)
            .WithHitFx(VfxCmd.bluntPath)
            .Execute();

        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState sleepLoopAnim = new("sleep_loop", true);
        AnimState wakeUpAnim = new("wake_up");

        AnimState idleLoopAnim = new(AnimState.idleAnim, true);
        AnimState castAnim = new(AnimState.castAnim);
        AnimState attackAnim = new(AnimState.attackAnim);
        AnimState rollAnim = new("roll");

        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState dieAnim = new(AnimState.dieAnim);

        wakeUpAnim.NextState = idleLoopAnim;
        attackAnim.NextState = idleLoopAnim;
        castAnim.NextState = idleLoopAnim;
        rollAnim.NextState = idleLoopAnim;
        hurtAnim.NextState = idleLoopAnim;

        SpineAnimator animator = new(sleepLoopAnim, spineController);
        animator.AddAnyState(wakeUpTrigger, wakeUpAnim);
        animator.AddAnyState(SpineAnimator.deathTrigger, dieAnim);
        animator.AddAnyState(SpineAnimator.attackTrigger, attackAnim);
        animator.AddAnyState(_rolloutTrigger, rollAnim);
        animator.AddAnyState(SpineAnimator.castTrigger, castAnim);
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim, () => IsAwake);

        return animator;
    }
}
