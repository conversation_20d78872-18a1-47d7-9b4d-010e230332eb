using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Toadpole : MonsterModel
{
    private static readonly string[] _eyeOptions = ["eye1", "eye2"];
    private static readonly string[] _patternOptions = ["pattern1", "pattern2"];

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 22, 21);

    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 26, 25);


    private bool _isFront;

    public bool IsFront
    {
        get => _isFront;
        set
        {
            AssertMutable();
            _isFront = value;
        }
    }

    private int SpikeSpitDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 3, 2);
    private int SpikeSpitRepeat => 3;
    private int WhirlDamage => AscensionHelper.GetValueIfAscension(AscensionLevel.DeadlyEnemies, 8, 7);

    private int SpikenAmount => 2;

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Slime;

    private const string _attackSingleTrigger = "AttackSingle";
    private const string _attackTripleTrigger = "AttackTriple";

    public override void SetupSkins()
    {
        NCreature node = NCombatRoom.Instance!.GetCreatureNode(Creature)!;
        SpineSkin compositeSkin = node.SpineController!.NewSkin("custom-skin");
        SpineSkeleton skeleton = node.SpineController.GetSkeleton();
        SpineSkeletonDataResource skeletonData = skeleton.GetData();

        compositeSkin.AddSkin(skeletonData.FindSkin(Rng.Chaotic.NextItem(_eyeOptions)));
        compositeSkin.AddSkin(skeletonData.FindSkin(Rng.Chaotic.NextItem(_patternOptions)));

        skeleton.SetSkin(compositeSkin);
        skeleton.SetSlotsToSetupPose();
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState spikeSpitMove = new("SPIKE_SPIT_MOVE", SpikeSpitMove, new MultiAttackIntent(SpikeSpitDamage, SpikeSpitRepeat));
        MoveState whirlMove = new("WHIRL_MOVE", WhirlMove, new SingleAttackIntent(WhirlDamage));
        MoveState spikenMove = new("SPIKEN_MOVE", SpikenMove, new BuffIntent());

        ConditionalBranchState initMoveState = new("INIT_MOVE");

        whirlMove.FollowUpState = spikenMove;
        spikenMove.FollowUpState = spikeSpitMove;
        spikeSpitMove.FollowUpState = whirlMove;

        initMoveState.AddState(whirlMove, (_, o) => !((Toadpole)o.Monster!).IsFront);
        initMoveState.AddState(spikenMove, (_, o) => ((Toadpole)o.Monster!).IsFront);

        states.Add(initMoveState);
        states.Add(spikenMove);
        states.Add(spikeSpitMove);
        states.Add(whirlMove);

        return new MonsterMoveStateMachine(states, initMoveState);
    }

    private async Task SpikeSpitMove(IReadOnlyList<Creature> targets)
    {
        await PowerCmd.Apply<Thorns>(Creature, -SpikenAmount, Creature, null);

        await DamageCmd.Attack(SpikeSpitDamage, SpikeSpitRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_attackTripleTrigger, 0.3f)
            .OnlyPlayAnimOnce()
            .WithHitFx(VfxCmd.bluntPath, AttackSfx)
            .Execute();
    }

    private async Task WhirlMove(IReadOnlyList<Creature> targets)
    {
        await DamageCmd.Attack(WhirlDamage, 1)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(_attackSingleTrigger, 0.15f)
            .WithHitFx(VfxCmd.bluntPath, AttackSfx)
            .Execute();
    }

    private async Task SpikenMove(IReadOnlyList<Creature> targets)
    {
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.castTrigger, 0.2f);
        await PowerCmd.Apply<Thorns>(Creature, SpikenAmount, Creature, null);
    }

    public override SpineAnimator GenerateSpineAnimator(SpineSprite spineController)
    {
        AnimState idleAnim = new(AnimState.idleAnim, true);
        AnimState buffAnim = new("buff");
        AnimState attackSingleAnim = new("attack_single");
        AnimState attackTripleAnim = new("attack_triple");
        AnimState hurtAnim = new(AnimState.hurtAnim);
        AnimState deathAnim = new(AnimState.dieAnim);

        AnimState idleBuffedAnim = new("idle_loop_buffed", true);
        AnimState attackSingleBuffedAnim = new("attack_single_buffed");
        AnimState attackTripleBuffedAnim = new("attack_triple");
        AnimState hurtBuffedAnim = new("hurt_buffed");
        AnimState deathBuffedAnim = new("die_buffed");

        buffAnim.NextState = idleBuffedAnim;
        attackSingleAnim.NextState = idleAnim;
        attackTripleAnim.NextState = idleAnim;
        hurtAnim.NextState = idleAnim;

        attackSingleBuffedAnim.NextState = idleBuffedAnim;
        attackTripleBuffedAnim.NextState = idleAnim;
        hurtBuffedAnim.NextState = idleBuffedAnim;
        deathBuffedAnim.NextState = idleBuffedAnim;

        SpineAnimator animator = new(idleAnim, spineController);

        animator.AddAnyState(SpineAnimator.castTrigger, buffAnim);
        animator.AddAnyState(_attackSingleTrigger, attackSingleAnim, () => !Creature.HasPower<Thorns>());
        animator.AddAnyState(_attackTripleTrigger, attackTripleAnim, () => !Creature.HasPower<Thorns>());
        animator.AddAnyState(SpineAnimator.deathTrigger, deathAnim, () => !Creature.HasPower<Thorns>());
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtAnim, () => !Creature.HasPower<Thorns>());

        animator.AddAnyState(_attackSingleTrigger, attackSingleBuffedAnim, () => Creature.HasPower<Thorns>());
        animator.AddAnyState(_attackTripleTrigger, attackTripleBuffedAnim, () => Creature.HasPower<Thorns>());
        animator.AddAnyState(SpineAnimator.deathTrigger, deathBuffedAnim, () => Creature.HasPower<Thorns>());
        animator.AddAnyState(SpineAnimator.hitTrigger, hurtBuffedAnim, () => Creature.HasPower<Thorns>());

        return animator;
    }
}
