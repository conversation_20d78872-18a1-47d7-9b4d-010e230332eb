using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.MonsterMoves.Intents;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Monsters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GremlinMerc : MonsterModel
{
    private static readonly LocString[] _stealLines =
    [
        new("monsters", "GREMLIN_MERC.moves.STEAL.banter1"),
        new("monsters", "GREMLIN_MERC.moves.STEAL.banter2")
    ];

    private static readonly LocString _heheLine = new("monsters", "GREMLIN_MERC.moves.HEHE.banter");

    public override int MinInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 51, 47);
    public override int MaxInitialHp => AscensionHelper.GetValueIfAscension(AscensionLevel.ToughEnemies, 53, 49);

    public override bool HasDeathSfx => false;
    public override DamageSfxType TakeDamageSfxType => DamageSfxType.Fur;

    private int GimmeDamage => 7;
    private int GimmeRepeat => 2;
    private int DoubleSmashDamage => 4;
    private int DoubleSmashRepeat => 2;

    public override async Task AfterAddedToRoom()
    {
        await base.AfterAddedToRoom();
        await PowerCmd.Apply<Surprise>(Creature, 1, Creature, null);

        foreach (Player player in Creature.CombatState!.Players)
        {
            Thievery thievery = (Thievery)ModelDb.Power<Thievery>().ToMutable();
            thievery.Target = player.Creature;
            await PowerCmd.Apply(thievery, Creature, 20, Creature, null);
        }
    }

    protected override MonsterMoveStateMachine GenerateMoveStateMachine()
    {
        List<MonsterState> states = [];

        MoveState gimmeState = new("GIMME_MOVE", GimmeMove, new MultiAttackIntent(GimmeDamage, GimmeRepeat));
        MoveState doubleSmashState = new("DOUBLE_SMASH_MOVE", DoubleSmashMove, new MultiAttackIntent(DoubleSmashDamage, DoubleSmashRepeat), new DebuffIntent());
        MoveState heheState = new("HEHE_MOVE", HeheMove, new BuffIntent(), new DefendIntent());

        gimmeState.FollowUpState = doubleSmashState;
        doubleSmashState.FollowUpState = heheState;
        heheState.FollowUpState = gimmeState;

        states.Add(gimmeState);
        states.Add(doubleSmashState);
        states.Add(heheState);

        return new MonsterMoveStateMachine(states, gimmeState);
    }

    private async Task GimmeMove(IReadOnlyList<Creature> targets)
    {
        LocString line = Random.Rng.Chaotic.NextItem(_stealLines)!;
        TalkCmd.Play(line, Creature);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.coinExplosionRegularPath);

        await DamageCmd.Attack(GimmeDamage, GimmeRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .OnlyPlayAnimOnce()
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        foreach (Thievery thievery in Creature.GetPowerInstances<Thievery>())
        {
            await thievery.Steal();
        }
    }

    private async Task DoubleSmashMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        LocString line = Random.Rng.Chaotic.NextItem(_stealLines)!;
        TalkCmd.Play(line, Creature);

        VfxCmd.PlayOnCreatureCenters(targets, VfxCmd.coinExplosionRegularPath);
        await DamageCmd.Attack(DoubleSmashDamage, DoubleSmashRepeat)
            .FromMonster(this)
            .TargetingAll(targets)
            .WithAttackerAnim(SpineAnimator.attackTrigger, 0.15f)
            .WithAttackerFx(null, AttackSfx)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        foreach (Thievery thievery in Creature.GetPowerInstances<Thievery>())
        {
            await thievery.Steal();
        }

        await PowerCmd.Apply<Weak>(targets, 1, Creature, null);
    }

    private async Task HeheMove(IReadOnlyList<Creature> targets)
    {
        SfxCmd.Play(AttackSfx);
        await CreatureCmd.TriggerAnim(Creature, SpineAnimator.attackTrigger, 0.15f);
        await CreatureCmd.GainBlock(Creature, 5, BlockProps.monsterMove, null);
        await PowerCmd.Apply<Strength>(Creature, 2, Creature, null);
        TalkCmd.Play(_heheLine, Creature);
    }
}
