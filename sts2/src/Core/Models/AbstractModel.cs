using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Exceptions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models;

public abstract class AbstractModel : IComparable<AbstractModel>
{
    public event Action<AbstractModel>? ExecutionFinished;
    public event Action? ChangedSortingOrder;

    public ModelId Id { get; }
    public bool IsMutable { get; private set; }
    public bool IsCanonical => !IsMutable;

    /// <summary>
    /// The category ID of the model, as reported by ModelIdSerializationCache.
    /// Used in the deterministic sort in DeterministicModelComparer for speed purposes; it's much faster to compare
    /// integers than strings.
    /// </summary>
    public int CategorySortingId { get; private set; }

    /// <summary>
    /// The entry ID of the model, as reported by ModelIdSerializationCache.
    /// Used in the deterministic sort in DeterministicModelComparer for speed purposes; it's much faster to compare
    /// integers than strings.
    /// </summary>
    public int EntrySortingId { get; private set; }

    protected AbstractModel()
    {
        Type type = GetType();

        if (ModelDb.Contains(type))
        {
            throw new DuplicateModelException(type);
        }

        Id = ModelDb.GetId(type);
    }

    public void InitId(ModelId id)
    {
        AssertCanonical();

        CategorySortingId = ModelIdSerializationCache.GetNetIdForCategory(Id.Category);
        EntrySortingId = ModelIdSerializationCache.GetNetIdForEntry(Id.Entry);
    }

    public virtual int CompareTo(AbstractModel? other)
    {
        if (ReferenceEquals(this, other)) return 0;
        if (ReferenceEquals(null, other)) return 1;

        return Id.CompareTo(other.Id);
    }

    public virtual void SortingOrderChanged()
    {
        ChangedSortingOrder?.Invoke();
    }

    /// <summary>
    /// Ensures that this model instance is mutable. Throws an exception if it's canonical.
    /// Use this in places where you want to ensure that you have a "real" model that you can use in combat and modify.
    ///
    /// WARNING: If you're getting an exception from here, don't just convert the model to mutable at the top level
    /// of the stack trace. Instead, find the root of where the model is coming from and convert it there, so the
    /// correct instance is used in the correct places.
    ///
    /// For example, canonical CardModels throw an exception if you try to add them to a pile, but you shouldn't just
    /// convert the CardModel to mutable in CardPile.AddCard(). Instead, find where that card was created and
    /// convert it there.
    /// </summary>
    /// <exception cref="CanonicalModelException"></exception>
    public void AssertMutable()
    {
        if (!IsMutable)
        {
            throw new CanonicalModelException(GetType());
        }
    }

    /// <summary>
    /// Ensures that this model instance is canonical. Throws an exception if it's mutable.
    /// Use this in places where you want a reference to the "concept" of a model. For example, CardPools only hold
    /// canonical CardModels.
    /// <exception cref="MutableModelException"></exception>
    /// </summary>
    public void AssertCanonical()
    {
        if (IsMutable)
        {
            throw new MutableModelException(GetType());
        }
    }

    /// <summary>
    /// Get a "clone" of this model, preserving its mutability status.
    /// This is useful in more backend-ish areas of the codebase, where a model can either be mutable or canonical,
    /// and we don't want to create a new mutable clone if we currently have a reference to the canonical instance.
    /// </summary>
    /// <returns>
    /// If this instance of the model is canonical, this method just returns itself.
    /// If this instance of the model is mutable, it returns another mutable clone.
    /// </returns>
    public AbstractModel ClonePreservingMutability() => IsMutable ? MutableClone() : this;

    /// <summary>
    /// WARNING: You almost always want to use `ToMutable()` or `ClonePreservingMutability()` instead, since we usually
    /// don't want to make mutable clones of already-mutable models.
    ///
    /// Get a mutable "clone" of this model.
    /// This is useful in very generalized spots where we could either have a mutable or canonical model, and we want a
    /// mutable clone regardless.
    /// </summary>
    /// <returns>A mutable clone of this model.</returns>
    public AbstractModel MutableClone()
    {
        AbstractModel clone = (AbstractModel)MemberwiseClone();
        clone.IsMutable = true;
        clone.DeepCloneFields();
        clone.AfterCloned();
        return clone;
    }

    protected virtual void DeepCloneFields() => AssertMutable();

    protected virtual void AfterCloned()
    {
        ExecutionFinished = null;
        ChangedSortingOrder = null;
    }

    public void InvokeExecutionFinished()
    {
        ExecutionFinished?.Invoke(this);
    }

    public virtual bool PreviewOutsideOfCombat => false;

    /// <summary>
    /// Whether or not this model should have combat hooks called on it.
    /// For example, AfterCardPlayed is only relevant in combat, and should be called on models that want to "listen" to
    /// the combat (powers, relics, cards in a combat pile, enchantments on those cards, etc.), but not on models that
    /// are disconnected from combat (cards in your deck, enchantments in your deck, etc.).
    /// Conversely, AfterRoomEntered is relevant outside of combat, and should be called on all models.
    /// Similarly, AfterDamageReceived is relevant outside of combat (since damage can be received in events and other
    /// non-combat rooms), so it should be called on all models.
    /// </summary>
    public abstract bool ShouldReceiveCombatHooks { get; }

    #region Before/After Hooks

    /// <summary>
    /// Runs after a player enters a new act.
    /// </summary>
    public virtual Task AfterActEntered() => Task.CompletedTask;

    /// <summary>
    /// Runs after a model prevents a card from being added to the player's deck.
    /// </summary>
    /// <param name="card">Card that would've been added.</param>
    public virtual Task AfterAddToDeckPrevented(CardModel card) => Task.CompletedTask;

    /// <summary>
    /// Runs before a creature attacks.
    /// For a multi-attack, this will run once before any hits are done (as opposed to BeforeDamageGiven, which will run
    /// before each hit).
    /// </summary>
    /// <param name="command">The attack command that will be executed.</param>
    public virtual Task BeforeAttack(AttackCommand command) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature attacks.
    /// For a multi-attack, this will run once after all hits are done (as opposed to AfterDamageGiven, which will run
    /// after each hit).
    /// </summary>
    /// <param name="command">The attack command that was executed.</param>
    public virtual Task AfterAttack(AttackCommand command) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature's block has been cleared at the beginning of their round.
    /// Combat-only hook.
    /// </summary>
    /// <param name="creature">Creature whose block was reset.</param>
    public virtual Task AfterBlockCleared(Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs before a creature gains block.
    /// Combat-only hook.
    /// </summary>
    /// <param name="creature">Creature that will gain block.</param>
    /// <param name="amount">Amount of block they will gain.</param>
    /// <param name="props">ValueProps for the block they will gain.</param>
    /// <param name="cardSource">Optional card that will add the block.</param>
    public virtual Task BeforeBlockGained(Creature creature, decimal amount, ValueProp props, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature gains block.
    /// Combat-only hook.
    /// </summary>
    /// <param name="creature">Creature that gained block.</param>
    /// <param name="amount">Amount of block they gained.</param>
    /// <param name="props">ValueProps for the block they gained.</param>
    /// <param name="cardSource">Optional card that added the block.</param>
    public virtual Task AfterBlockGained(Creature creature, decimal amount, ValueProp props, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature's block is completely removed.
    /// Prefer this over checking result.WasBlockBroken in AfterDamageReceived, as block can be broken by non-damage,
    /// like Expose.
    /// Combat-only hook.
    /// </summary>
    /// <param name="creature">Creature that gained block.</param>
    public virtual Task AfterBlockBroken(Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card moves from one pile to another.
    /// NOTE: The new pile can be determined by just checking card.Pile.
    /// </summary>
    /// <param name="card">Card that changed piles.</param>
    /// <param name="oldPileType">Type of the card's previous pile</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public virtual Task AfterCardChangedPiles(CardModel card, CardPileTarget oldPileType, AbstractModel? source) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use AfterCardChangedPiles instead of this.
    /// Runs after a card moves from one pile to another.
    /// Works just like AfterCardChangedPiles, but runs after it.
    /// NOTE: The new pile can be determined by just checking card.Pile.
    /// </summary>
    /// <param name="card">Card that changed piles.</param>
    /// <param name="oldPileType">Type of the card's previous pile</param>
    /// <param name="source">The model that initiated the card add, if applicable.</param>
    public virtual Task AfterCardChangedPilesLate(CardModel card, CardPileTarget oldPileType, AbstractModel? source) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card is discarded.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card that was discarded.</param>
    public virtual Task AfterCardDiscarded(PlayerChoiceContext choiceContext, CardModel card) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card is drawn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card that was drawn.</param>
    /// <param name="fromHandDraw">If this draw happened as part of the initial card draws at the start of your turn.</param>
    public virtual Task AfterCardDrawn(PlayerChoiceContext choiceContext, CardModel card, bool fromHandDraw) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card is put into a combat pile (see <see cref="CardPileTargetExtensions.IsCombatPile"/> details on combat piles).
    /// Combat-only hook.
    /// </summary>
    /// <param name="card">Card that was put into a combat pile.</param>
    public virtual Task AfterCardEnteredCombat(CardModel card) => Task.CompletedTask;


    /// <summary>
    /// Runs after a player generated a card for combat pile. This is different from AfterCardEnteredCombat
    /// because the player explicitly generated it themselves, excluding status cards that are applied by enemies.
    /// Combat-only hook.
    /// </summary>
    /// <param name="card">Card that was put into a combat pile.</param>
    /// <param name="addedByPlayer">If the card was generated by the player.</param>
    public virtual Task AfterCardGeneratedForCombat(CardModel card, bool addedByPlayer) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card is exhausted.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card that was exhausted.</param>
    /// <param name="causedByEthereal">Was this Exhaust caused by Ethereal?</param>
    public virtual Task AfterCardExhausted(PlayerChoiceContext choiceContext, CardModel card, bool causedByEthereal) => Task.CompletedTask;

    /// <summary>
    /// Runs before a card is automatically played by another card.
    /// Combat-only hook.
    /// </summary>
    /// <param name="card">Card that will be played.</param>
    /// <param name="target">Creature that will be targeted.</param>
    /// <param name="type">The method of autoplay.</param>
    public virtual Task BeforeCardAutoPlayed(CardModel card, Creature? target, AutoPlayType type) => Task.CompletedTask;

    /// <summary>
    /// Runs before a card is played.
    /// Combat-only hook.
    /// </summary>
    /// <param name="card">Card that will be played.</param>
    /// <param name="target">Creature that will be targeted.</param>
    /// <param name="playCount">
    /// The card's play count during this play. Usually 0, but when a card has an effect like Replay, the second play
    /// will have playCount=1, the third will have playCount=2, etc.
    /// </param>
    public virtual Task BeforeCardPlayed(CardModel card, Creature? target, int playCount) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card is played.
    /// Combat-only hook.
    /// </summary>
    /// <param name="context">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card that was played.</param>
    /// <param name="target">Creature that was targeted.</param>
    /// <param name="playCount">
    /// The card's play count during this play. Usually 0, but when a card has an effect like Replay, the second play
    /// will have playCount=1, the third will have playCount=2, etc.
    /// </param>
    public virtual Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use AfterCardPlayed instead of this.
    /// Runs after a card is played.
    /// Works just like AfterCardPlayed, but runs after it.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="card">Card that was played.</param>
    /// <param name="target">Creature that was targeted.</param>
    /// <param name="playCount">
    /// The card's play count during this play. Usually 0, but when a card has an effect like Replay, the second play
    /// will have playCount=1, the third will have playCount=2, etc.
    /// </param>
    public virtual Task AfterCardPlayedLate(PlayerChoiceContext choiceContext, CardModel card, Creature? target, int playCount) => Task.CompletedTask;

    /// <summary>
    /// Runs after a card has been retained.
    /// </summary>
    /// <param name="card">Card who has been retained.</param>
    public virtual Task AfterCardRetained(CardModel card) => Task.CompletedTask;

    /// <summary>
    /// Runs before combat starts.
    /// Note: This may sound like a combat-only hook, but combat start is relevant to non-combat models (like a deck
    /// card that transforms when you start your third combat).
    /// </summary>
    public virtual Task BeforeCombatStart() => Task.CompletedTask;

    /// <summary>
    /// Runs after combat ends.
    /// Note: This may sound like a combat-only hook, but combat end is relevant to non-combat models (like Guilty).
    /// </summary>
    /// <param name="room">The room where the combat ended.</param>
    public virtual Task AfterCombatEnd(CombatRoom room) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use AfterCombatVictory instead of this.
    /// Runs after combat ends in a player victory.
    /// Note: This will NOT run if the player escapes with something like Smoke Bomb.
    /// Note: This may sound like a combat-only hook, but combat victory is relevant to non-combat models (like a deck
    /// card that transforms after 3 combat victories).
    /// Works just like AfterCombatVictory, but runs before it.
    /// </summary>
    /// <param name="room">The room where the player won the combat.</param>
    public virtual Task AfterCombatVictoryEarly(CombatRoom room) => Task.CompletedTask;

    /// <summary>
    /// Runs after combat ends in a player victory.
    /// Note: This will NOT run if the player escapes with something like Smoke Bomb.
    /// Note: This may sound like a combat-only hook, but combat victory is relevant to non-combat models (like a deck
    /// card that transforms after 3 combat victories).
    /// </summary>
    /// <param name="room">The room where the player won the combat.</param>
    public virtual Task AfterCombatVictory(CombatRoom room) => Task.CompletedTask;

    /// <summary>
    /// Runs after a new creature is added to combat.
    /// </summary>
    /// <param name="creature">Creature that was added to combat.</param>
    public virtual Task AfterCreatureAddedToCombat(Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature's HP changes for any reason (damage, heal, HP loss, etc).
    /// </summary>
    /// <param name="creature">Creature whose HP changed.</param>
    /// <param name="delta">
    ///     Amount that the creature's HP changed by.
    ///     A negative amount represents damage, while a positive amount represents healing.
    /// </param>
    public virtual Task AfterCurrentHpChanged(Creature creature, decimal delta) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature might dealt damage.
    /// Note: Even if the actual damage amount is 0 (due to block or something else), this will still be called.
    /// </summary>
    /// <param name="dealer">Creature who dealt the damage.</param>
    /// <param name="result">Results from the damage they dealt.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="target">Creature who received the damage.</param>
    /// <param name="cardSource">Optional card that dealt the damage.</param>
    public virtual Task AfterDamageGiven(Creature? dealer, DamageResult result, ValueProp props, Creature target, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs before a creature might receive damage.
    /// Note: Even if the actual damage amount will be 0 (due to Weak or something else), this will still be called.
    /// </summary>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="amount">Amount of damage they will receive.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="dealer">Creature who will be dealt the damage.</param>
    /// <param name="cardSource">Optional card that will deal the damage.</param>
    public virtual Task BeforeDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature might receive damage.
    /// Note: Even if the actual damage amount is 0 (due to block or something else), this will still be called.
    /// </summary>
    /// <param name="target">Creature who received the damage.</param>
    /// <param name="result">Results from the damage they received.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="dealer">Creature who dealt the damage.</param>
    /// <param name="cardSource">Optional card that dealt the damage.</param>
    public virtual Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs before a creature dies.
    /// Note: This will run even if some effect (like Fairy in a Bottle) would prevent the creature's death.
    /// </summary>
    /// <param name="creature">Creature who is about to die.</param>
    public virtual Task BeforeDeath(Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs after a creature dies.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="creature">Creature who died.</param>
    /// <param name="deathAnimLength">
    /// Number of seconds the creature's death animation will last. Good for delaying visuals until the animation
    /// is over.
    /// </param>
    public virtual Task AfterDeath(PlayerChoiceContext choiceContext, Creature creature, float deathAnimLength) => Task.CompletedTask;


    /// <summary>
    /// Runs after a creature is killed by Doom
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="creature">Creature who died.</param>
    public virtual Task AfterDiedToDoom(PlayerChoiceContext choiceContext, Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs after the player's energy is reset at the beginning of their turn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="player">Player whose energy is reset.</param>
    public virtual Task AfterEnergyReset(Player player) => Task.CompletedTask;

    /// <summary>
    /// Runs before a card is removed from your deck.
    /// </summary>
    /// <param name="card">Card that is being removed.</param>
    public virtual Task BeforeCardRemoved(CardModel card) => Task.CompletedTask;

    /// <summary>
    /// Runs before a player's hand is flushed at the end of their turn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player whose hand will be flushed.</param>
    public virtual Task BeforeFlush(PlayerChoiceContext choiceContext, Player player) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use BeforeFlush instead of this.
    /// Runs before a player's hand is flushed at the end of their turn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player whose hand will be flushed.</param>
    public virtual Task BeforeFlushLate(PlayerChoiceContext choiceContext, Player player) => Task.CompletedTask;

    /// <summary>
    /// Runs before the player draws their hand at the beginning of their turn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="player">The player who is about to draw cards.</param>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="combatState">The CombatState that the hand draw will occur in.</param>
    public virtual Task BeforeHandDraw(Player player, PlayerChoiceContext choiceContext, CombatState combatState) => Task.CompletedTask;

    /// <summary>
    /// Runs the player gains gold
    /// </summary>
    /// <param name="player">Player who gained the gold.</param>
    public virtual Task AfterGainedGold(Player player) => Task.CompletedTask;

    /// <summary>
    /// Runs after the player's hand becomes empty.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player whose hand was emptied.</param>
    public virtual Task AfterHandEmptied(PlayerChoiceContext choiceContext, Player player) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player purchase an item from the shop.
    /// </summary>
    /// <param name="player">Player who purchased the item.</param>
    /// <param name="itemPurchased">Item that was purchased.</param>
    /// <param name="cost">Amount of gold  that the item was purchased for.</param>
    public virtual Task AfterItemPurchased(Player player, MerchantEntry itemPurchased, int cost) => Task.CompletedTask;

    /// <summary>
    /// Runs after a map is generated for an act.
    /// </summary>
    /// <param name="map">The generated map.</param>
    /// <param name="actIndex">The act index for which the map was generated.</param>
    public virtual Task AfterMapGenerated(ActMap map, int actIndex) => Task.CompletedTask;

    /// <summary>
    /// Runs after the amount of block was modified.
    /// Combat-only hook.
    /// </summary>
    /// <param name="modifiedAmount">The amount of block after all modifications have been applied.</param>
    public virtual Task AfterModifyingBlockAmount(decimal modifiedAmount) => Task.CompletedTask;

    /// <summary>
    /// Runs after the play count of a card was modified.
    /// </summary>
    /// <param name="card">Card whose play count was modified.</param>
    public virtual Task AfterModifyingCardPlayCount(CardModel card) => Task.CompletedTask;

    /// <summary>
    /// Runs after the trigger count of an orb was modified.
    /// </summary>
    /// <param name="orb">Orb whose trigger count was modified.</param>
    public virtual Task AfterModifyingOrbPassiveTriggerCount(OrbModel orb) => Task.CompletedTask;

    /// <summary>
    /// Runs after the play count of a card was modified.
    /// </summary>
    public virtual Task AfterModifyCardRewardOptions() => Task.CompletedTask;

    /// <summary>
    /// Runs after the amount of damage was modified.
    /// </summary>
    public virtual Task AfterModifyingDamageAmount() => Task.CompletedTask;

    /// <summary>
    /// Runs after the number of cards drawn at the beginning of the player's turn was modified.
    /// Combat-only hook.
    /// </summary>
    public virtual Task AfterModifyingHandDraw() => Task.CompletedTask;

    /// <summary>
    /// Runs after card draw was prevented.
    /// Combat-only hook.
    /// </summary>
    public virtual Task AfterPreventingDraw() => Task.CompletedTask;

    /// <summary>
    /// Runs after the amount of HP a creature lost was modified.
    /// </summary>
    public virtual Task AfterModifyingHpLost() => Task.CompletedTask;

    /// <summary>
    /// Runs after a power has its amount modified during application.
    /// Combat-only hook.
    /// </summary>
    /// <param name="power">Power whose amount was modified.</param>
    public virtual Task AfterModifyingPowerAmountReceived(PowerModel power) => Task.CompletedTask;

    /// <summary>
    /// Runs after a power has its amount modified during application.
    /// Combat-only hook.
    /// </summary>
    /// <param name="power">Power whose amount was modified.</param>
    public virtual Task AfterModifyingPowerAmountGiven(PowerModel power) => Task.CompletedTask;

    /// <summary>
    /// Runs after a list of rewards has its contents modified.
    /// </summary>
    public virtual Task AfterModifyingRewards() => Task.CompletedTask;

    /// <summary>
    /// Runs before we offer rewards to the player. i.e. after combat.
    /// </summary>
    /// <param name="player">Player we are offering the rewards to.</param>
    /// <param name="rewards">Rewards being offered.</param>
    public virtual Task BeforeOfferingRewards(Player player, IReadOnlyList<Reward> rewards) => Task.CompletedTask;

    /// <summary>
    /// Runs after player channeled an orb
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="player">Player who channeled the orb.</param>
    /// <param name="orb">Orb that was channeled.</param>
    public virtual Task AfterOrbChanneled(PlayerChoiceContext choiceContext, Player player, OrbModel orb) => Task.CompletedTask;

    /// <summary>
    /// Runs after player evokes a orb
    /// </summary>
    /// <param name="orb">Orb that was evoked.</param>
    /// <param name="targets">who the orb affected</param>
    public virtual Task AfterOrbEvoked(OrbModel orb, IEnumerable<Creature> targets) => Task.CompletedTask;

    /// <summary>
    /// Runs after Osty attacks.
    /// </summary>
    /// <param name="osty">The Osty creature that attacked.</param>
    public virtual Task AfterOstyAttacked(Creature osty) => Task.CompletedTask;

    /// <summary>
    /// Runs after Osty is revived.
    /// </summary>
    /// <param name="osty">The osty that was revived</param>
    public virtual Task AfterOstyRevived(Creature osty) => Task.CompletedTask;


    /// <summary>
    /// Runs before a potion is used.
    /// Note: This only invokes for relics when out of combat.
    /// </summary>
    /// <param name="potion">Potion that will be used.</param>
    /// <param name="target">Creature that will be targeted.</param>
    public virtual Task BeforePotionUsed(PotionModel potion, Creature? target) => Task.CompletedTask;

    /// <summary>
    /// Runs after a potion is used.
    /// Note: This only invokes for relics when out of combat.
    /// </summary>
    /// <param name="potion">Potion that was used.</param>
    /// <param name="target">Creature that was targeted.</param>
    public virtual Task AfterPotionUsed(PotionModel potion, Creature? target) => Task.CompletedTask;

    /// <summary>
    /// Runs after a potion is discarded.
    /// Note: This only invokes for relics when out of combat.
    /// </summary>
    /// <param name="potion">Potion that was used.</param>
    public virtual Task AfterPotionDiscarded(PotionModel potion) => Task.CompletedTask;

    /// <summary>
    /// Runs after a potion is procured.
    /// Note: This only invokes for relics when out of combat.
    /// </summary>
    /// <param name="potion">Potion that was used.</param>
    public virtual Task AfterPotionProcured(PotionModel potion) => Task.CompletedTask;

    /// <summary>
    /// Runs before a power's amount is changed, whether it's a new power being added or an existing power's amount being
    /// changed.
    /// Combat-only hook.
    /// </summary>
    /// <param name="power">Power that will be applied.</param>
    /// <param name="amount">Amount of the power that will be added.</param>
    /// <param name="target">The creature that the power will be added to</param>
    /// <param name="applier">
    /// (Optional) The creature that will change the power amount. Null if the change is not caused by a creature (usually
    /// in tests).
    /// </param>
    /// <param name="cardSource">Optional card that is changing the power amount.</param>
    public virtual Task BeforePowerAmountChanged(PowerModel power, decimal amount, Creature target, Creature? applier, CardModel? cardSource) => Task.CompletedTask;

    /// <summary>
    /// Runs after a power's amount is changed, whether it's a new power being added or an existing power's amount being
    /// changed.
    /// Combat-only hook.
    /// </summary>
    /// <param name="power">Power that was applied.</param>
    /// <param name="amount">Amount of the power that was added</param>
    /// <param name="applier">
    /// (Optional) The creature that changed the power amount. Null if the change was not caused by a creature (usually
    /// in tests).
    /// </param>
    public virtual Task AfterPowerAmountChanged(PowerModel power, decimal amount, Creature? applier) => Task.CompletedTask;

    /// <summary>
    /// Runs after a power prevents the creature from clearing block.
    /// Combat-only hook.
    /// </summary>
    /// <param name="preventer">Model that prevented the block clear.</param>
    /// <param name="creature">Creature whose block clear was prevented.</param>
    public virtual Task AfterPreventingBlockClear(AbstractModel preventer, Creature creature) => Task.CompletedTask;

    /// <summary>
    /// Runs after this model prevents a creature's death.
    /// </summary>
    /// <param name="creature">Creature whose death was prevented.</param>
    public virtual Task AfterPreventingDeath(Creature creature) => Task.CompletedTask;

    /// <summary>
    /// After player rests at a rest site.
    /// </summary>
    /// <param name="player">Player that rested.</param>
    public virtual Task AfterPlayerRested(Player player) => Task.CompletedTask;

    /// <summary>
    /// After player smiths at a rest site.
    /// </summary>
    /// <param name="player">Player that smithed.</param>
    public virtual Task AfterPlayerSmithed(Player player) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player successfully takes a reward from the reward screen.
    /// </summary>
    /// <param name="player">Player who took the reward.</param>
    /// <param name="reward">The reward taken.</param>
    public virtual Task AfterRewardTaken(Player player, Reward reward) => Task.CompletedTask;

    /// <summary>
    /// Runs before a player enters a room.
    /// </summary>
    /// <param name="room">Room that will be entered.</param>
    public virtual Task BeforeRoomEntered(AbstractRoom room) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player enters a room.
    /// For "start of combat" effects that should execute before the start of the first turn (like applying powers), you
    /// should use this hook with a "room is CombatRoom" check.
    /// For "start of combat" effects that should execute after the start of the first turn (like dealing damage or
    /// gaining block), you should use <see cref="AfterTurnStart"/> with a RoundNumber check instead.
    /// </summary>
    /// <param name="room">Room that was entered.</param>
    public virtual Task AfterRoomEntered(AbstractRoom room) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player shuffles their draw pile.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="shuffler">Player who shuffled.</param>
    public virtual Task AfterShuffle(PlayerChoiceContext choiceContext, Player shuffler) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player spends stars on something.
    /// Combat-only hook.
    /// </summary>
    /// <param name="amount">Amount of stars that were spent.</param>
    /// <param name="spender">Player that spent the stars.</param>
    public virtual Task AfterStarsSpent(int amount, Player spender) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player gains stars.
    /// Combat-only hook.
    /// </summary>
    /// <param name="amount">Amount of stars that were gained.</param>
    /// <param name="gainer">Player that gained the stars.</param>
    public virtual Task AfterStarsGained(int amount, Player gainer) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player triggers a Forge effect, increasing the damage of Sovereign Blade.
    /// Combat-only hook.
    /// </summary>
    /// <param name="amount">Amount of forge that was triggered.</param>
    /// <param name="forger">Player that triggered the forge.</param>
    public virtual Task AfterForged(decimal amount, Player forger) => Task.CompletedTask;

    /// <summary>
    /// Runs after a player summons.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="summoner">Player that summoned.</param>
    /// <param name="amount">Amount that was summoned.</param>
    public virtual Task AfterSummoning(PlayerChoiceContext choiceContext, Player summoner, decimal amount) => Task.CompletedTask;

    /// <summary>
    /// Runs after the player takes an extra turn.
    /// Combat-only hook.
    /// <param name="player">Player that took the extra turn.</param>
    /// </summary>
    public virtual Task AfterTakingExtraTurn(Player player) => Task.CompletedTask;

    /// <summary>
    /// WARNING: ONLY DO VFX/SFX THINGS IN THIS HOOK. It is not meant for things that affect game state.
    /// Runs after attempting to target this creature with a card is blocked.
    /// </summary>
    /// <param name="blocker">Creature who blocked the targeting.</param>
    public virtual Task AfterTargetingBlockedVfx(Creature blocker) => Task.CompletedTask;

    /// <summary>
    /// Runs before the start of a side's turn, before any effects like energy reset or card draw.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn ended.</param>
    public virtual Task BeforeTurnStart(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use AfterTurnStart instead of this.
    /// Runs after the start of a side's turn.
    /// For "start of combat" effects that should execute after the start of the first turn (like dealing damage or
    /// gaining block), you should use this hook with a RoundNumber check.
    /// For "start of combat effects that should execute before the start of the first turn (like applying powers), you
    /// should use <see cref="AfterRoomEntered"/> with a "room is CombatRoom" check instead.
    /// Works just like AfterTurnStart, but runs before it.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn started.</param>
    public virtual Task AfterTurnStartEarly(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// Runs after the start of a side's turn.
    /// For "start of combat" effects that should execute after the start of the first turn (like dealing damage or
    /// gaining block), you should use this hook with a RoundNumber check.
    /// For "start of combat effects that should execute before the start of the first turn (like applying powers), you
    /// should use <see cref="AfterRoomEntered"/> with a "room is CombatRoom" check instead.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn started.</param>
    public virtual Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use BeforeTurnEnd or BeforeTurnEndEarly instead of this.
    /// Runs before the end of a side's turn.
    /// Works just like BeforeTurnEnd and BeforeTurnEndEarly, but runs before either of them.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn ended.</param>
    public virtual Task BeforeTurnEndVeryEarly(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// CAREFUL! You should usually use BeforeTurnEnd instead of this.
    /// Runs before the end of a side's turn.
    /// Works just like BeforeTurnEnd, but runs before it.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn ended.</param>
    public virtual Task BeforeTurnEndEarly(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// Runs before the end of a side's turn.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn ended.</param>
    public virtual Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    /// <summary>
    /// Runs after the end of a side's turn.
    /// Note: Enemy-damaging effects (Bedlam Beacon, The Bomb, etc) should NOT go in here. Put them in BeforeTurnEnd
    /// instead. Self-damaging effects (Acid Dust, Magic Bomb, etc) can go in here.
    /// Combat-only hook.
    /// </summary>
    /// <param name="choiceContext">The context that is signalled in the event of a player choice.</param>
    /// <param name="side">Side whose turn ended.</param>
    public virtual Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side) => Task.CompletedTask;

    #endregion

    #region Modify Hooks

    /// <summary>
    /// Modify the number of times the currently-being-played card will be played.
    /// Good for "Your next card is played twice" effects.
    /// </summary>
    /// <param name="card">The card that is being played.</param>
    /// <param name="playCount">The number of times it should be played.</param>
    /// <param name="target">Creature this card is targeting</param>
    /// <returns></returns>
    public virtual int ModifyCardPlayCount(CardModel card, Creature? target, int playCount) => playCount;

    /// <summary>
    /// Modify the number of times an orb's passive will fire.
    /// Doesn't trigger if the passive is fired manually (ie loop).
    /// </summary>
    /// <param name="orb">Orb whose passive is being triggered.</param>
    /// <param name="triggerCount">The number of times the orb's passive should be triggered.</param>
    /// <returns></returns>
    public virtual int ModifyOrbPassiveTriggerCounts(OrbModel orb, int triggerCount) => triggerCount;

    /// <summary>
    /// Modify the cards available in the reward card pool.
    /// You may remove cards, add cards, or add duplicate cards (to increase the chances of that card showing up).
    /// </summary>
    /// <param name="player">The player who is being offered rewards.</param>
    /// <param name="options">The original options in the card pool.</param>
    /// <param name="source">What the card rewards are being generated for.</param>
    /// <returns>The modified card pool.</returns>
    public virtual IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source) => options;

    /// <summary>
    /// CAREFUL! You should usually use ModifyCardRewardCardPool instead of this.
    /// Modify the cards available in the reward card pool.
    /// You may remove cards, add cards, or add duplicate cards (to increase the chances of that card showing up).
    /// </summary>
    /// <param name="player">The player who is being offered rewards.</param>
    /// <param name="options">The original options in the card pool.</param>
    /// <param name="source">What the card rewards are being generated for.</param>
    /// <returns>The modified card pool.</returns>
    public virtual IEnumerable<CardModel> ModifyCardRewardCardPoolLate(Player player, IEnumerable<CardModel> options, CardCreationSource source) => options;

    /// <summary>
    /// Modify the odds of a card reward being offered upgraded.
    /// </summary>
    /// <param name="player">Player who is being offered rewards.</param>
    /// <param name="card">Card reward.</param>
    /// <param name="odds">Initial odds of it being upgraded.</param>
    /// <returns>New odds of it being upgraded.</returns>
    public virtual decimal ModifyCardRewardUpgradeOdds(Player player, CardModel card, decimal odds) => odds;

    /// <summary>
    /// CAREFUL! You should usually use ModifyDamageGiven instead of this.
    /// Modify the amount of damage that will be dealt by the dealer.
    /// Works just like ModifyDamageGiven, but runs before it.
    /// </summary>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageGivenEarly(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource) => amount;

    /// <summary>
    /// Modify the amount of damage that will be dealt by the dealer.
    /// </summary>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource) => amount;

    /// <summary>
    /// CAREFUL! You should usually use ModifyDamageGiven instead of this.
    /// Modify the amount of damage that will be dealt by the dealer.
    /// Works just like ModifyDamageGiven, but runs after it.
    /// </summary>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageGivenLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource) => amount;

    /// <summary>
    /// CAREFUL! You should usually use ModifyDamageGiven instead of this.
    /// Modify the amount of damage that will be dealt by the dealer.
    /// Works just like ModifyDamageGivenLate, but runs after it.
    /// </summary>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageGivenVeryLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource) => amount;

    /// <summary>
    /// CAREFUL! You should usually use ModifyDamageReceived instead of this.
    /// Modify the amount of damage that will be received by the target.
    /// Works just like ModifyDamageReceived, but runs before it.
    /// </summary>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageReceivedEarly(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource) => amount;

    /// <summary>
    /// Modify the amount of damage that will be received by the target.
    /// </summary>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageReceived(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource) => amount;

    /// <summary>
    /// CAREFUL! You should usually use ModifyDamageReceived instead of this.
    /// Modify the amount of damage that will be received by the target.
    /// Works just like ModifyDamageReceived, but runs after it.
    /// </summary>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="amount">Original amount of damage.</param>
    /// <param name="props">ValueProp for damage.</param>
    /// <param name="dealer">Creature who will deal the damage.</param>
    /// <param name="cardSource">Card that will be dealing the damage.</param>
    /// <returns>New amount of damage to be dealt.</returns>
    public virtual decimal ModifyDamageReceivedLate(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource) => amount;

    /// <summary>
    /// Modify the dungeon map before it is set up.
    /// </summary>
    /// <param name="climbState">Climb state.</param>
    /// <param name="map">The generated map.</param>
    /// <param name="actIndex">The act index for which the map was generated.</param>
    public virtual ActMap ModifyGeneratedMap(ClimbState climbState, ActMap map, int actIndex) => map;

    /// <summary>
    /// CAREFUL! You should usually use ModifyGeneratedMap instead of this.
    /// Modify the dungeon map before it is set up.
    /// </summary>
    /// <param name="climbState">Climb state.</param>
    /// <param name="map">The generated map.</param>
    /// <param name="actIndex">The act index for which the map was generated.</param>
    public virtual ActMap ModifyGeneratedMapLate(ClimbState climbState, ActMap map, int actIndex) => map;

    /// <summary>
    /// Modify the amount of cards the player should draw at the start of their turn.
    /// </summary>
    /// <param name="player">Player who will draw the cards.</param>
    /// <param name="count">Original number of cards that would be drawn.</param>
    /// <returns>New amount of cards to be drawn.</returns>
    public virtual decimal ModifyHandDraw(Player player, decimal count) => count;

    /// <summary>
    /// CAREFUL! You should usually use ModifyHandDraw instead of this.
    /// Modify the amount of cards the player should draw at the start of their turn.
    /// </summary>
    /// <param name="player">Player who will draw the cards.</param>
    /// <param name="count">Original number of cards that would be drawn.</param>
    /// <returns>New amount of cards to be drawn.</returns>
    public virtual decimal ModifyHandDrawLate(Player player, decimal count) => count;

    /// <summary>
    /// Modify the amount to be healed.
    /// </summary>
    /// <param name="creature">Creature who will be healing.</param>
    /// <param name="amount">The original amount that would be healed.</param>
    /// <returns>The new amount to be healed.</returns>
    public virtual decimal ModifyHealAmount(Creature creature, decimal amount) => amount;

    /// <summary>
    /// Modify the amount of HP that will be lost by the target.
    /// This runs when damage is being dealt, AFTER block is applied.
    /// </summary>
    /// <param name="target">Creature who will lose HP.</param>
    /// <param name="amount">Original amount of HP to be lost.</param>
    /// <param name="props">ValueProp for amount.</param>
    /// <param name="dealer">Creature who will be causing the HP loss.</param>
    /// <param name="cardSource">Card that will be causing the HP loss.</param>
    /// <returns>New amount of HP to be lost.</returns>
    public virtual decimal ModifyHpLost(Creature target, decimal amount, ValueProp props, Creature? dealer, CardModel? cardSource) => amount;

    /// <summary>
    /// Modify the max energy a player has.
    /// </summary>
    /// <param name="player">Player who we are modifying.</param>
    /// <param name="amount">Current amount of max energy they have.</param>
    public virtual decimal ModifyMaxEnergy(Player player, decimal amount) => amount;

    /// <summary>
    /// Modify the cards available in the merchant's card pool.
    /// You may remove cards, add cards, or add duplicate cards (to increase the chances of that card showing up).
    /// </summary>
    /// <param name="player">The player who is at the merchant.</param>
    /// <param name="options">The original options in the card pool.</param>
    /// <returns>The modified card pool.</returns>
    public virtual IEnumerable<CardModel> ModifyMerchantCardPool(Player player, IEnumerable<CardModel> options) => options;

    /// <summary>
    /// Modify the rarity of a merchant card entry
    /// </summary>
    /// <param name="player">The player who is at the merchant.</param>
    /// <param name="rarity">Original rarity of the merchant card entry.</param>
    /// <returns>New rarity.</returns>
    public virtual CardRarity ModifyMerchantCardRarity(Player player, CardRarity rarity) => rarity;

    /// <summary>
    /// Modify the cards created to sell at the merchant.
    /// </summary>
    /// <param name="player">Player who is at the merchant</param>
    /// <param name="cards">
    ///     CardCreationResults created to sell at the merchant.
    ///     Note that they could already have been modified by another source.
    /// </param>
    public virtual void ModifyMerchantCardCreationResults(Player player, List<CardCreationResult> cards) { }

    /// <summary>
    /// Modify the cost of items at the merchant.
    /// </summary>
    /// <param name="player">The player who is at the merchant.</param>
    /// <param name="entry">the merchant entry we are modifying the price for</param>
    /// <param name="cost">Original cost of the merchant item.</param>
    /// <returns>New cost of the merchant item.</returns>
    public virtual decimal ModifyMerchantPrice(Player player, MerchantEntry entry, decimal cost) => cost;

    /// <summary>
    /// Modify orb value for this creature.
    /// </summary>
    /// <param name="player">Player who owns the orb.</param>
    /// <param name="value">Original value that the orb would use for its action.</param>
    /// <returns>New value that the orb should use for its action.</returns>
    public virtual decimal ModifyOrbValue(Player player, decimal value) => value;

    /// <summary>
    /// Modify the amount of times that Osty will attack.
    /// </summary>
    /// <param name="amount">Original number of times the attack will be performed.</param>
    /// <returns>New number of times the attack will be performed.</returns>
    public virtual decimal ModifyOstyAttackCount(decimal amount) => amount;

    /// <summary>
    /// Modify the odds of a potion being offered in a rewards screen.
    /// </summary>
    /// <param name="player">Player who is receiving the reward.</param>
    /// <param name="roomType">Type of room that the reward is being offered in.</param>
    /// <param name="odds">Initial odds of a potion being offered.</param>
    /// <returns>New odds of a potion being offered.</returns>
    public virtual decimal ModifyPotionRewardOdds(Player player, RoomType roomType, decimal odds) => odds;

    /// <summary>
    /// Modify the amount of a power that will be applied by a creature.
    /// </summary>
    /// <param name="power">The power that will be applied.</param>
    /// <param name="giver">Creature who will be applying the power.</param>
    /// <param name="amount">Original amount of the power to be applied.</param>
    /// <param name="target">Optional creature who the power will be applied to.</param>
    /// <param name="cardSource">Optional card that applied the power.</param>
    /// <returns>New amount of the power to be applied.</returns>
    public virtual decimal ModifyPowerAmountGiven(PowerModel power, Creature giver, decimal amount, Creature? target, CardModel? cardSource) => amount;

    /// <summary>
    /// Modify the amount to be healed at the rest site.
    /// </summary>
    /// <param name="creature">Creature who will be healing (usually the player, but Osty when playing Necrobinder).</param>
    /// <param name="amount">The original amount that would be healed.</param>
    /// <returns>The new amount to be healed.</returns>
    public virtual decimal ModifyRestSiteHealAmount(Creature creature, decimal amount) => amount;

    /// <summary>
    /// Modify the amount of a Summon.
    /// </summary>
    /// <param name="summoner">Player who is doing the summoning.</param>
    /// <param name="amount">Amount that is being summoned.</param>
    /// <param name="source">
    /// The model that this Summon came from. For example, <see cref="Bodyguard"/> and <see cref="BoundPhylactery"/>
    /// pass themselves here.
    /// Null if the Summon did not come from any model (generally only relevant in tests).
    /// </param>
    /// <returns>The new amount to be summoned.</returns>
    public virtual decimal ModifySummonAmount(Player summoner, decimal amount, AbstractModel? source) => amount;

    /// <summary>
    /// Modify the creature that will receive unblocked damage.
    /// </summary>
    /// <param name="target">Creature who will receive the damage.</param>
    /// <param name="amount">Original amount of damage to be dealt.</param>
    /// <param name="props">ValueProp for amount.</param>
    /// <param name="dealer">Creature who will be dealing the damage.</param>
    /// <returns>New creature to receive the unblocked damage.</returns>
    public virtual Creature ModifyUnblockedDamageTarget(Creature target, decimal amount, ValueProp props, Creature? dealer) => target;

    /// <summary>
    /// Modify the current event for an event room
    /// </summary>
    /// <param name="currentEvent">The currently rolled event.</param>
    /// <returns>New event</returns>
    public virtual EventModel ModifyNextEvent(EventModel currentEvent) => currentEvent;

    /// <summary>
    /// Modify the room types that can be rolled when the player enters an unknown location.
    /// </summary>
    /// <param name="roomTypes">Room types that can be rolled.</param>
    /// <returns>New set of room types that can be rolled.</returns>
    public virtual IReadOnlySet<RoomType> ModifyUnknownMapPointRoomTypes(IReadOnlySet<RoomType> roomTypes) => roomTypes;

    /// <summary>
    /// When a room type is not chosen as the resolved room for an unknown map point, this modifies the increased odds
    /// that it will show up the next time.
    /// </summary>
    /// <param name="roomType">The room type whose odds are being increased.</param>
    /// <param name="oddsIncrease">The amount the room would increase odds without modification.</param>
    /// <returns>The modified odds increase.</returns>
    public virtual float ModifyOddsIncreaseForUnrolledRoomType(RoomType roomType, float oddsIncrease) => oddsIncrease;

    #endregion

    #region Try Modify Hooks

    /// <summary>
    /// Modify the amount of block that will be received by the target.
    /// </summary>
    /// <param name="target">Creature who will receive the block.</param>
    /// <param name="block">Initial amount of received block.</param>
    /// <param name="props">ValueProp for the block.</param>
    /// <param name="cardSource">Card that will be adding the block.</param>
    /// <param name="modifiedBlock">New amount of received block.</param>
    /// <returns>Whether or not the block amount was modified.</returns>
    public virtual bool TryModifyBlockReceived(Creature target, decimal block, ValueProp props, CardModel? cardSource, out decimal modifiedBlock)
    {
        modifiedBlock = block;
        return false;
    }

    /// <summary>
    /// CAREFUL! You should usually use TryModifyBlockReceivedLate instead of this.
    /// Modify the amount of block that will be received by the target.
    /// Works just like TryModifyBlockReceived, but runs after it.
    /// </summary>
    /// <param name="target">Creature who will receive the block.</param>
    /// <param name="block">Initial amount of received block.</param>
    /// <param name="props">ValueProp for the block.</param>
    /// <param name="cardSource">Card that will be adding the block.</param>
    /// <param name="modifiedBlock">New amount of received block.</param>
    /// <returns>Whether or not the block amount was modified.</returns>
    public virtual bool TryModifyBlockReceivedLate(Creature target, decimal block, ValueProp props, CardModel? cardSource, out decimal modifiedBlock)
    {
        modifiedBlock = block;
        return false;
    }

    /// <summary>
    /// Runs before a card is added to deck to give relics an opportunity to modify the card.
    /// Note that this can be run as part of transformation as well.
    /// The new owner can be determined by checking card.Owner.
    /// </summary>
    /// <param name="card">Card that was put into a deck.</param>
    /// <param name="newCard">New card that should be put into the deck instead.</param>
    public virtual bool TryModifyCardBeingAddedToDeck(CardModel card, out CardModel? newCard)
    {
        newCard = null;
        return false;
    }

    /// <summary>
    /// NOTE: You probably want to use TryModifyCardBeingAddedToDeck instead!
    /// Runs before a card is added to deck to give relics an opportunity to modify the card.
    /// Note that this can be run as part of transformation as well.
    /// The new owner can be determined by checking card.Owner.
    /// </summary>
    /// <param name="card">Card that was put into a deck.</param>
    /// <param name="newCard">New card that should be put into the deck instead.</param>
    public virtual bool TryModifyCardBeingAddedToDeckLate(CardModel card, out CardModel? newCard)
    {
        newCard = null;
        return false;
    }

    /// <summary>
    /// Modify the alternative options (options other than picking a card offered in a card reward).
    /// Ex: Skip, Heal +2 (Dream Catcher), Sacrifice (Pael Wing).
    /// </summary>
    /// <param name="player">Player to whom we are presenting the options.</param>
    /// <param name="cardReward">The reward that is being displayed.</param>
    /// <param name="alternatives">Card reward alternatives.</param>
    /// <returns>Whether the alternatives were modified.</returns>
    public virtual bool TryModifyCardRewardAlternatives(Player player, CardReward cardReward, List<CardRewardAlternative> alternatives) => false;

    /// <summary>
    /// Modifies the options for a card reward.
    /// </summary>
    /// <param name="player">The player receiving the rewards.</param>
    /// <param name="options">Options in the card reward.</param>
    /// <param name="source">How the original cards were created.</param>
    public virtual bool TryModifyCardRewardOptions(Player player, List<CardCreationResult> options, CardCreationSource source) => false;

    /// <summary>
    /// CAREFUL! You should usually use TryModifyCardRewardOptions instead of this.
    /// Modifies the options for a card reward.
    /// </summary>
    /// <param name="player">The player receiving the rewards.</param>
    /// <param name="options">Options in the card reward.</param>
    /// <param name="source">How the original cards were created.</param>
    public virtual bool TryModifyCardRewardOptionsLate(Player player, List<CardCreationResult> options, CardCreationSource source) => false;

    /// <summary>
    /// Modify the amount of energy that a card will cost.
    /// </summary>
    /// <param name="card">Card whose energy cost we're modifying.</param>
    /// <param name="originalCost">Original energy cost.</param>
    /// <param name="modifiedCost">Modified energy cost.</param>
    /// <returns>Whether the energy cost was modified</returns>
    public virtual bool TryModifyEnergyCost(CardModel card, decimal originalCost, out decimal modifiedCost)
    {
        modifiedCost = originalCost;
        return false;
    }

    /// <summary>
    /// Modify the amount of star that a card will cost.
    /// </summary>
    /// <param name="card">Card whose star cost we're modifying.</param>
    /// <param name="originalCost">Original star cost.</param>
    /// <param name="modifiedCost">Modified star cost.</param>
    /// <returns>Whether the energy cost was modified</returns>
    public virtual bool TryModifyStarCost(CardModel card, decimal originalCost, out decimal modifiedCost)
    {
        modifiedCost = originalCost;
        return false;
    }

    /// <summary>
    /// Modify the amount of a power that will be applied to the target.
    /// </summary>
    /// <param name="canonicalPower">Canonical power that will be applied.</param>
    /// <param name="target">Creature who the power will be applied to.</param>
    /// <param name="amount">Original amount of the power to be applied.</param>
    /// <param name="applier">Optional creature who will be applying the power.</param>
    /// <param name="modifiedAmount">New amount of the power to be applied.</param>
    /// <returns>Whether the amount was modified.</returns>
    public virtual bool TryModifyPowerAmountReceived(PowerModel canonicalPower, Creature target, decimal amount, Creature? applier, out decimal modifiedAmount)
    {
        modifiedAmount = amount;
        return false;
    }

    /// <summary>
    /// Modify the options offered to the player when they enter a rest site.
    /// </summary>
    /// <param name="player">Player who owns the rest site options</param>
    /// <param name="options">Rest site options</param>
    /// <returns>Whether the options were modified.</returns>
    public virtual bool TryModifyRestSiteOptions(Player player, ICollection<RestSiteOption> options) => false;

    /// <summary>
    /// Modify the rewards offered to the player after completing a particular room/encounter.
    /// </summary>
    /// <param name="player">The player being offered rewards.</param>
    /// <param name="rewards">The rewards offered to the player.</param>
    /// <param name="room">Room the rewards were found in.</param>
    /// <returns>Whether the rewards were modified.</returns>
    public virtual bool TryModifyRewards(Player player, List<Reward> rewards, AbstractRoom room) => false;

    /// <summary>
    /// CAREFUL! You should usually use TryModifyRewards instead of this.
    /// Modify the rewards offered to the player after completing a particular room/encounter.
    /// Works just like TryModifyRewards, but runs after it.
    /// </summary>
    /// <param name="player">The player being offered rewards.</param>
    /// <param name="rewards">Rewards</param>
    /// <param name="room">Room the rewards were found in.</param>
    /// <returns>Whether the rewards were modified.</returns>
    public virtual bool TryModifyRewardsLate(Player player, List<Reward> rewards, AbstractRoom room) => false;

    public virtual void TryModifyDrawShuffleOrder(Player player, List<CardModel> cards) { }

    /// <summary>
    /// Modify the text displayed by the rest site heal option.
    /// </summary>
    /// <param name="locString">A LocString to append to the rest site heal option.</param>
    /// <returns>True if a valid LocString was returned, false otherwise.</returns>
    public virtual bool TryAppendAdditionalRestSiteHealOptionText(out LocString? locString)
    {
        locString = null;
        return false;
    }

    #endregion

    #region Validation Hooks

    /// <summary>
    /// If the player is allowed upgrade this card.
    /// </summary>
    /// <param name="card">Card we are trying to upgrade.</param>
    public virtual bool CanUpgrade(CardModel card) => true;

    /// <summary>
    /// If the player is allowed to obtain potions
    /// </summary>
    /// <param name="potion">potion we are obtaining</param>
    /// <param name="player">Player trying to obtain the potion</param>
    public virtual bool CanProcurePotions(PotionModel potion, Player player) => true;

    /// <summary>
    /// If a card would be added to the player's deck, should it still be?
    /// </summary>
    /// <param name="card">Card to be added.</param>
    /// <returns>Whether to allow it.</returns>
    public virtual bool ShouldAddToDeck(CardModel card) => true;

    /// <summary>
    /// Should we allow the specified affliction to be added to this card?
    /// </summary>
    /// <param name="card">Card to be afflicted.</param>
    /// <param name="affliction">Affliction that would be added to the card.</param>
    /// <returns>Whether to allow the affliction.</returns>
    public virtual bool ShouldAfflict(CardModel card, AfflictionModel affliction) => true;

    /// <summary>
    /// Should we allow the player to speak to this Ancient?
    /// Used by things like the Wax Choker relic.
    /// </summary>
    /// <param name="player">Player speaking to the Ancient.</param>
    /// <param name="ancient">Ancient the player is trying to speak to.</param>
    /// <returns>Whether to allow it.</returns>
    public virtual bool ShouldAllowAncient(Player player, AncientEventModel ancient) => true;

    /// <summary>
    /// If an effect would normally hit a creature, should it still?
    /// See <see cref="Creature.IsHittable"/> for how hitting differs from targeting.
    /// </summary>
    /// <param name="creature">Creature who we want to hit.</param>
    /// <returns></returns>
    public virtual bool ShouldAllowHitting(Creature creature) => true;

    /// <summary>
    /// If a player tries to target a creature, should they be allowed to?
    /// </summary>
    /// <param name="target">Creature who we want to target.</param>
    /// <returns>Whether to allow player to target creature.</returns>
    public virtual bool ShouldAllowTargeting(Creature target) => true;

    /// <summary>
    /// Called after a reward is selected at the rewards screen to determine whether or not we should close the rewards screen.
    /// </summary>
    /// <param name="player">Player to whom we are presenting the options.</param>
    /// <param name="cardReward">The reward that is being displayed.</param>
    /// <returns>True to allow the player to continue to select rewards, false otherwise.</returns>
    public virtual bool ShouldAllowSelectingMoreRewards(Player player, CardReward cardReward) => false;

    /// <summary>
    /// If a creature's block is about to be cleared, should it still be cleared?
    /// </summary>
    /// <param name="creature">Creature whose block is about to be cleared.</param>
    /// <returns>Whether the creature's block should still be cleared.</returns>
    public virtual bool ShouldClearBlock(Creature creature) => true;

    /// <summary>
    /// If a creature is about to die, should they still?
    /// </summary>
    /// <param name="creature">Creature who is about to die.</param>
    /// <returns>Whether the creature should still die.</returns>
    public virtual bool ShouldDie(Creature creature) => true;

    /// <summary>
    /// CAREFUL! You should usually use ShouldDie instead of this.
    /// If a creature is about to die, should they still?
    /// Works just like ShouldDie, but runs after it.
    /// </summary>
    /// <param name="creature">Creature who is about to die.</param>
    /// <returns>Whether the creature should still die.</returns>
    public virtual bool ShouldDieLate(Creature creature) => true;

    /// <summary>
    /// After the player selects a Rest Site option, should we still disable the rest of them?
    /// </summary>
    /// <returns>Whether to disable remaining Rest Site options.</returns>
    public virtual bool ShouldDisableRemainingRestSiteOptions(Player player) => true;

    /// <summary>
    /// If a player is about to draw cards, should they still?
    /// </summary>
    /// <param name="player">Player is about to draw cards.</param>
    /// <param name="fromHandDraw">draw is part of the initial hand draw at the start of your turn.</param>
    /// <returns>Whether the player should still draw cards.</returns>
    public virtual bool ShouldDraw(Player player, bool fromHandDraw) => true;

    /// <summary>
    /// If Ethereal is about to trigger for a card, should it still?
    /// </summary>
    /// <param name="card">The card that is about to be Exhausted due to Ethereal.</param>
    /// <returns>Whether the card should still be Exhausted.</returns>
    public virtual bool ShouldEtherealTrigger(CardModel card) => true;

    /// <summary>
    /// If a player is about to flush their hand, should they still?
    /// </summary>
    /// <param name="player">Player who is about to flush their hand.</param>
    /// <returns>Whether the player should still flush their hand.</returns>
    public virtual bool ShouldFlush(Player player) => true;

    /// <summary>
    /// If a player is about to gain gold, should they still?
    /// </summary>
    /// <param name="amount">Amount of gold they would gain.</param>
    /// <param name="player">Player who is about to gain gold.</param>
    /// <returns>Whether the player should still gain gold.</returns>
    public virtual bool ShouldGainGold(decimal amount, Player player) => true;

    /// <summary>
    /// If a player is about to gain stars, should they still?
    /// </summary>
    /// <param name="amount">Amount of stars they would gain.</param>
    /// <param name="player">Player who is about to gain stars.</param>
    /// <returns>Whether the player should still gain stars.</returns>
    public virtual bool ShouldGainStars(decimal amount, Player player) => true;

    /// <summary>
    /// If a player is about to pay an energy cost that they don't have enough energy for, should they be allowed to
    /// spend stars to pay for the excess energy cost?
    /// </summary>
    /// <param name="player">Player who is about to spend energy.</param>
    /// <returns>Whether the player can spend stars to pay for the excess energy cost.</returns>
    public virtual bool ShouldPayExcessEnergyCostWithStars(Player player) => false;

    /// <summary>
    /// If a card is about to be played, should it still?
    /// </summary>
    /// <param name="card">Card that is about to be played</param>
    /// <param name="autoPlayType">The type of autoplay this card play is (ie played by echo form). None if its not an auto play.</param>
    /// <returns>Whether the card should still be played.</returns>
    public virtual bool ShouldPlay(CardModel card, AutoPlayType autoPlayType) => true;

    /// <summary>
    /// If the player's energy is about to be reset, should it be reset?
    /// If not, the player's max energy is still added on to the current amount.
    /// </summary>
    /// <returns>Whether the player's energy should be reset.</returns>
    public virtual bool ShouldPlayerResetEnergy(Player player) => true;

    /// <summary>
    /// If the player is attempting to proceed to the next point on the map, should they be allowed to?
    /// </summary>
    /// <returns>Whether to allow it.</returns>
    public virtual bool ShouldProceedToNextMapPoint() => true;

    /// <summary>
    /// If the player has just purchased something from the merchant, should the now-empty entry be refilled?
    /// </summary>
    /// <param name="entry">Entry that might be refilled.</param>
    /// <param name="player">Player who is at the merchant.</param>
    /// <returns>Whether the merchant entry should be refilled.</returns>
    public virtual bool ShouldRefillMerchantEntry(MerchantEntry entry, Player player) => false;

    /// <summary>
    /// At the merchant, should the player be allowed to remove cards?
    /// </summary>
    /// <param name="player">Player who is at the merchant.</param>
    /// <returns>True if the player should be allowed to remove cards, false otherwise.</returns>
    public virtual bool ShouldAllowMerchantCardRemoval(Player player) => true;

    /// <summary>
    /// If this creature just died, should it be removed from combat?
    /// Usually true, but things like reviving powers should return false so they can revive the creature later.
    /// </summary>
    /// <param name="creature">Creature that died.</param>
    /// <returns>Whether the merchant entry should be refilled.</returns>
    public virtual bool ShouldCreatureBeRemovedFromCombatAfterDeath(Creature creature) => true;

    /// <summary>
    /// If all enemies are dead, should we stop combat from ending?
    /// This should be set to true when all enemies are dead but new enemies are about to spawn.
    /// </summary>
    /// <returns>Whether combat should be marked as about to end</returns>
    public virtual bool ShouldStopCombatFromEnding() => false;

    /// <summary>
    /// If the player should be allowed to take an extra turn.
    /// </summary>
    /// <param name="player">Player ending turn.</param>
    /// <returns>Whether to allow it.</returns>
    public virtual bool ShouldTakeExtraTurn(Player player) => false;

    #endregion

    protected void NeverEverCallThisOutsideOfTests_SetIsMutable(bool isMutable)
    {
        if (TestMode.IsOff) throw new InvalidOperationException("You monster!");

        IsMutable = isMutable;
    }
}
