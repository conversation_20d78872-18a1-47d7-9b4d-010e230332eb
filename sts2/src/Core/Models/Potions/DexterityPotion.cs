using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DexterityPotion : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Common;
    public override PotionUsage Usage => PotionUsage.CombatOnly;
    public override ActionTarget Target => ActionTarget.AnyPlayer;

    protected override PotionBody Body => PotionBody.Diamond;
    protected override PotionOverlay Overlay => PotionOverlay.Bubbles;
    public override Color JuiceTint => new(0.4431373f, 0.8431373f, 0.227451f);
    public override Color OverlayTint => new(0.4431373f, 0.8431373f, 0.227451f);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new PowerVar<Dexterity>(2)
    ];

    public override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Dexterity>()];

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        AssertValidForTargetedPotion(target);
        await PowerCmd.Apply<Dexterity>(target, DynamicVars.Dexterity.BaseValue, Owner.Creature, null);
    }
}
