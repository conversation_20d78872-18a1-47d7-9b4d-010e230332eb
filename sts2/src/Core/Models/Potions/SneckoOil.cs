using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SneckoOil : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Rare;
    public override PotionUsage Usage => PotionUsage.CombatOnly;
    public override ActionTarget Target => ActionTarget.AnyPlayer;

    protected override PotionBody Body => PotionBody.Snecko;
    public override Color JuiceTint => new(0.3529412f, 0.6666667f, 0.1803922f);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(5)
    ];

    private int _testEnergyCostOverride = -1;

    public int TestEnergyCostOverride
    {
        get => _testEnergyCostOverride;
        set
        {
            TestMode.AssertOn();
            AssertMutable();

            _testEnergyCostOverride = value;
        }
    }

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        AssertValidForTargetedPotion(target);
        await CardPileCmd.Draw(choiceContext, DynamicVars.Cards.BaseValue, target.Player!);

        IEnumerable<CardModel> cards = CardPileTarget.Hand.GetPile(target.Player!).Cards.Where(c => !c.HasEnergyCostX);

        foreach (CardModel card in cards)
        {
            // If base cost < 0, it was probably initially unplayable (like a Curse).
            if (card.BaseEnergyCost >= 0)
            {
                card.SetEnergyCostThisTurn(NextEnergyCost());
            }
        }
    }

    private int NextEnergyCost()
    {
        if (TestEnergyCostOverride >= 0) return TestEnergyCostOverride;

        return Owner.ClimbState.Rng.CombatEnergyCosts.NextInt(4);
    }
}
