using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Clarity : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Uncommon;
    public override PotionUsage Usage => PotionUsage.CombatOnly;
    public override ActionTarget Target => ActionTarget.AnyPlayer;

    protected override PotionBody Body => PotionBody.Flask;

    public override Color JuiceTint => Colors.Black;
    public override Color OverlayTint => Colors.Black;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new PowerVar<DrawExtraCard>(3),
        new CardsVar(1)
    ];

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        AssertValidForTargetedPotion(target);
        await CardPileCmd.Draw(choiceContext, DynamicVars.Cards.BaseValue, target.Player!);
        await PowerCmd.Apply<DrawExtraCard>(target, DynamicVars[nameof(DrawExtraCard)].BaseValue, Owner.Creature, null);
    }
}
