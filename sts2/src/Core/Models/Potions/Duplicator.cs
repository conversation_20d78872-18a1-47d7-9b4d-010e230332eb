using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Duplicator : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Uncommon;
    public override PotionUsage Usage => PotionUsage.CombatOnly;

    protected override PotionBody Body => PotionBody.Card;
    protected override bool HasGradient => true;

    public override Color JuiceTint => Colors.White;

    // protected override string JuiceMaterialPath => "Assets/Materials/RainbowPotion.mat"; // TODO: Bring back JuiceMaterialPath
    public override Color GradientTint => Colors.White;

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        await PowerCmd.Apply<Duplication>(Owner.Creature, 1, Owner.Creature, null);
    }
}
