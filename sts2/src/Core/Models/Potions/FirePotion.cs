using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FirePotion : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Common;
    public override PotionUsage Usage => PotionUsage.CombatOnly;
    public override ActionTarget Target => ActionTarget.AnyEnemy;

    protected override PotionBody Body => PotionBody.Sphere;
    protected override bool HasGradient => true;

    public override Color JuiceTint => new(0.682353f, 0.1372549f, 0.2039216f);
    public override Color GradientTint => new(0.98f, 0.42f, 0.11f);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(20, DamageProps.nonCardUnpowered)
    ];

    protected override async Task OnUse(PlayerChoiceContext choiceContext, Creature? target)
    {
        AssertValidForTargetedPotion(target);

        // TODO: Animation
        // yield return new AttackVfxAction(ActionTarget.Target, AttackVfxAction.fireImpactVfx).Execute(context);
        DamageVar damage = DynamicVars.Damage;
        await CreatureCmd.Damage(target, damage.BaseValue, damage.Props, Owner.Creature, null);
    }
}
