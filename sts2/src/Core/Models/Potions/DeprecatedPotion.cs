using Godot;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Potions;

namespace MegaCrit.Sts2.Core.Models.Potions;

/// <summary>
/// Represents a potion that has been removed from the game. Mostly used for the climb history.
/// </summary>
// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeprecatedPotion : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.None;
    public override PotionUsage Usage => PotionUsage.CombatOnly;
    public override ActionTarget Target => ActionTarget.AnyEnemy;

    protected override PotionBody Body => PotionBody.Sphere;
    protected override bool HasGradient => true;

    public override Color JuiceTint => new(0.682353f, 0.1372549f, 0.2039216f);
    public override Color GradientTint => new(0.98f, 0.42f, 0.11f);
}
