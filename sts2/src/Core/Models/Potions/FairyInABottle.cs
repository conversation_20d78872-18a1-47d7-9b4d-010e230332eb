using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Potions;

namespace MegaCrit.Sts2.Core.Models.Potions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FairyInABottle : PotionModel
{
    public override PotionRarity Rarity => PotionRarity.Rare;
    public override PotionUsage Usage => PotionUsage.Automatic;

    protected override PotionBody Body => PotionBody.Fairy;
    protected override PotionOverlay Overlay => PotionOverlay.Sparkle;
    public override Color JuiceTint => new(0.05490196f, 0.6862745f, 0.6078432f);
    public override Color OverlayTint => Colors.White;

    public override bool CanBeGeneratedInCombat => false;

    public override bool ShouldDie(Creature creature)
    {
        if (creature != Owner.Creature) return true;

        return false;
    }

    public override async Task AfterPreventingDeath(Creature creature)
    {
        Remove();
        Owner.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(Owner.NetId).PotionUsed.Add(Id);
        await CreatureCmd.Heal(creature, creature.MaxHp * 0.3m);
    }
}
