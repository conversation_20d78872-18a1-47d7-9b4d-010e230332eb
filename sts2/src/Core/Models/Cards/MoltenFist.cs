using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MoltenFist : CardModel
{
    private const string _moltenFistVfxPath = "vfx/vfx_molten_fist";

    protected override IEnumerable<string> ExtraAssetPaths =>
    [
        SceneHelper.GetScenePath(_moltenFistVfxPath)
    ];

    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(10, DamageProps.card)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Vulnerable>()];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(vfx: _moltenFistVfxPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();

        int vulnerableAmount = target.IsAlive ? target.GetPowerAmount<Vulnerable>() : 0;

        if (vulnerableAmount > 0)
        {
            await PowerCmd.Apply<Vulnerable>(target, vulnerableAmount, Owner.Creature, this);
        }
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(4);
    }
}
