using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Combust : CardModel
{
    private const string _powerVarName = "EnemyDamage";

    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Power;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_powerVarName, 5)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        CombustPower? power = await PowerCmd.Apply<CombustPower>(Owner.Creature, DynamicVars[_powerVarName].BaseValue, Owner.Creature, this);

        // The Combust power should do 1 more self damage every time it's played.
        // We track this separately from power amount, since Combust+ scales enemy damage by increasing amount.
        power?.IncrementSelfDamage();
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_powerVarName].UpgradeValueBy(2);
    }
}
