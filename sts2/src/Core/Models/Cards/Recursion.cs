using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Recursion : CardModel
{
    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    public override bool EvokesOrbs => true;

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.Static(StaticHoverTip.Channeling)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        if (Owner.PlayerCombatState!.OrbQueue.Orbs.Count <= 0) return;

        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        OrbModel orb = (OrbModel)Owner.PlayerCombatState.OrbQueue.Orbs[0].ClonePreservingMutability();
        await OrbCmd.EvokeNext(choiceContext, Owner);

        await Cmd.Wait(0.25f);

        // NOTE: if we recurse on a dark orb, the spawned orb retains damage value of the original orb
        await OrbCmd.Channel(choiceContext, orb, Owner);
    }

    protected override void OnUpgrade()
    {
        UpgradeEnergyCostBy(-1);
    }
}
