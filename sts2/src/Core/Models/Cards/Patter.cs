using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Patter : CardModel
{
    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    public override bool GainsBlock => true;
    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new BlockVar(8, BlockProps.card),
        new PowerVar<Vigor>(2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Vigor>()];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await CreatureCmd.GainBlock(Owner.Creature, DynamicVars.Block, this);

        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        await PowerCmd.Apply<Vigor>(Owner.Creature, DynamicVars[nameof(Vigor)].IntValue, Owner.Creature, this);
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Block.UpgradeValueBy(2);
        DynamicVars[nameof(Vigor)].UpgradeValueBy(1);
    }
}