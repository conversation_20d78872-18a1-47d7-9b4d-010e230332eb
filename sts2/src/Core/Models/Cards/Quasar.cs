using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Nodes.CommonUi;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
namespace MegaCrit.Sts2.Core.Models.Cards;

public sealed class Quasar : CardModel
{
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Uncommon;
    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;
    public override int CanonicalEnergyCost => 0;

    public override int CanonicalStarCost => 1;

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        List<CardModel> options = CardFactory.GetDistinctForCombat(
            ModelDb.CardPool<ColorlessCardPool>().Cards,
            3,
            Owner.ClimbState.Rng.CombatCardGeneration
        ).Select(c => CombatState!.CreateCard(c, Owner)).ToList();

        if (IsUpgraded)
        {
            CardCmd.Upgrade(options, CardPreviewStyle.HorizontalLayout);
        }

        CardModel? card = await CardSelectCmd.FromChooseACardScreen(choiceContext, options, Owner, true);

        if (card != null)
        {
            await CardPileCmd.AddGeneratedCardToCombat(card, CardPileTarget.Hand, true);
        }
    }
}
