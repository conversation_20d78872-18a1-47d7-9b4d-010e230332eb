using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx.Cards;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Stomp : CardModel
{
    private const string _cardPlayThresholdKey = "CardPlayThreshold";

    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.All;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override bool ShouldGlowGoldInternal => ShouldBeFree();

    public override int CurrentEnergyCost
    {
        get
        {
            if (!IsInCombat) return base.CurrentEnergyCost;
            if (!ShouldBeFree()) return base.CurrentEnergyCost;

            return 0;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(7, DamageProps.card),
        new(_cardPlayThresholdKey, 3)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.AttackAnimDelay);

        IReadOnlyList<Creature> enemies = CombatState!.HittableEnemies;

        foreach (Creature enemy in enemies)
        {
            NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NSpikeSplashVfx.Create(enemy));
        }

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .TargetingAll(enemies)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(2);
    }

    private bool ShouldBeFree()
    {
        int attacksPlayedThisTurn = CombatManager.Instance.History.CardPlaysFinished.Count(e =>
            e.HappenedThisTurn(CombatState) &&
            e.Card.Type == CardType.Attack &&
            e.Card.Owner == Owner
        );

        // Don't count ourselves if we're currently being played
        if (Pile?.Type == CardPileTarget.Play)
        {
            attacksPlayedThisTurn--;
        }

        return attacksPlayedThisTurn >= DynamicVars[_cardPlayThresholdKey].IntValue;
    }
}
