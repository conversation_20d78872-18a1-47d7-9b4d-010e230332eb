using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HiddenGem : CardModel
{
    private const string _replayKey = "Replay";
    public override int CanonicalEnergyCost => 1;
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new IntVar(_replayKey, 2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [EnergyHoverTip, HoverTipFactory.Static(StaticHoverTip.ReplayStatic)];


    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        await CardPileCmd.Shuffle(choiceContext, Owner);

        IEnumerable<CardModel> validCards = CardPile.GetCards(Owner, CardPileTarget.Draw)
            .Where(c => !c.Keywords.Contains(CardKeyword.Unplayable)
                && c.Type is CardType.Attack or CardType.Skill or CardType.Power);

        CardModel? card = Owner.ClimbState.Rng.CombatCardSelection.NextItem(validCards);

        if (card != null)
        {
            card.ReplayCount += DynamicVars[_replayKey].IntValue;
            CardCmd.Preview(card);
        }
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_replayKey].UpgradeValueBy(1);
    }
}
