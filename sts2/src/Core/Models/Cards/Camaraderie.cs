using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Camaraderie : CardModel
{
    public override int CanonicalEnergyCost => 1;

    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new SummonVar(5),
        new PowerVar<Strength>(1)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.SummonDynamic, DynamicVars.Summon)];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        await OstyCmd.Summon(choiceContext, Owner, DynamicVars.Summon.BaseValue, this);
        await PowerCmd.Apply<Strength>(Owner.Osty!, DynamicVars.Strength.BaseValue, Owner.Creature, this);
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Summon.UpgradeValueBy(1);
        DynamicVars.Strength.UpgradeValueBy(1);
    }
}
