using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Shredder : CardModel
{
    public override int CanonicalEnergyCost => 2;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Random;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    private bool _testAllowDuplicates = true;

    public bool TestAllowDuplicates
    {
        private get => _testAllowDuplicates;
        set
        {
            TestMode.AssertOn();
            AssertMutable();

            _testAllowDuplicates = value;
        }
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.Static(StaticHoverTip.Channeling),
        HoverTipFactory.FromOrb<ScrapOrb>()
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(4, DamageProps.card),
        new RepeatVar(3)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        AttackCommand results = await DamageCmd.Attack(DynamicVars.Damage.BaseValue, DynamicVars.Repeat.IntValue)
            .FromCard(this)
            // If we are in testing mode, we guarantee that we never hit the same target twice.
            .TargetingRandom(CombatState!.HittableEnemies, TestAllowDuplicates)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();

        int targetsHit = results.Results.Select(r => r.Receiver).Distinct().Count();

        for (int i = 0; i < targetsHit; i++)
        {
            await OrbCmd.Channel<ScrapOrb>(choiceContext, Owner);
        }
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Repeat.UpgradeValueBy(1);
    }
}
