using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Shiv : CardModel
{
    private const string _hasFanOfKnives = "HasFanOfKnives";

    private bool HasFanOfKnives =>
        CombatManager.Instance.IsInProgress &&
        Owner.Creature.HasPower<FanOfKnivesPower>();

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Token;
    public override UiTargetEnemy TargetEnemy => HasFanOfKnives ? UiTargetEnemy.All : UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(4, DamageProps.card),
        new BoolVar(_hasFanOfKnives)
    ];

    public override int CanonicalEnergyCost => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        IReadOnlyList<Creature> enemies;

        if (HasFanOfKnives)
        {
            enemies = CombatState!.HittableEnemies;
        }
        else
        {
            ArgumentNullException.ThrowIfNull(target);
            enemies = [target];
        }

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .TargetingAll(enemies)
            .WithHitFx(VfxCmd.slashPath)
            .Execute();
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(2);
    }

    public override void RecalculateValues()
    {
        ((BoolVar)DynamicVars[_hasFanOfKnives]).BoolVal = HasFanOfKnives;
    }

    public static async Task<CardModel?> CreateInHand(Player owner, CombatState combatState) => (await CreateInHand(owner, 1, combatState)).FirstOrDefault();

    public static async Task<IEnumerable<CardModel>> CreateInHand(Player owner, int count, CombatState combatState)
    {
        if (count == 0) return [];
        if (CombatManager.Instance.IsAboutToEnd) return [];

        List<CardModel> shivs = [];

        for (int i = 0; i < count; i++)
        {
            shivs.Add(combatState.CreateCard<Shiv>(owner));
        }

        await CardPileCmd.AddGeneratedCardToCombat(shivs, CardPileTarget.Hand, true);

        return shivs;
    }
}
