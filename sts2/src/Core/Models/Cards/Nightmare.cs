using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Nodes.Vfx.Cards;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Nightmare : CardModel
{
    public override int CanonicalEnergyCost => 3;
    public override CardType Type => CardType.Skill;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;
    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Exhaust];

    protected override IEnumerable<string> ExtraAssetPaths => NNightmareHandsVfx.AssetPaths;

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        if (TestMode.IsOff)
        {
            NSmokyVignetteVfx? vignetteVfx = NSmokyVignetteVfx.Create(
                new Color(0.8f, 0.3f, 0.8f, 0.66f),
                new Color(0f, 0f, 4f, 0.33f)
            );

            // TODO: Need a better place for VFX rendering above select screens but under the Settings screen
            NGame.Instance!.CurrentClimbNode!.GlobalUi.AddChildSafely(vignetteVfx);
            NGame.Instance.CurrentClimbNode!.GlobalUi.AddChildSafely(NNightmareHandsVfx.Create());

            await Cmd.CustomScaledWait(0.1f, 0.25f, 0.25f);
        }

        await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);
        CardSelectorPrefs prefs = new(SelectionScreenPrompt, 1);
        IEnumerable<CardModel> cards = await CardSelectCmd.FromHand(choiceContext, Owner, prefs, null, this);

        CardModel? selectedCard = cards.FirstOrDefault();
        if (selectedCard == null) return;
        NightmarePower nightmare = (await PowerCmd.Apply<NightmarePower>(Owner.Creature, 3, Owner.Creature, this))!;
        nightmare.SetSelectedCard(selectedCard);
    }

    protected override void OnUpgrade()
    {
        UpgradeEnergyCostBy(-1);
    }
}
