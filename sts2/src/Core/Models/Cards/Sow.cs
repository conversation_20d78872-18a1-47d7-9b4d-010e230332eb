using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Sow : CardModel
{
    public override int CanonicalEnergyCost => 1;

    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.All;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(8, DamageProps.card)
    ];

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Retain];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .TargetingAll(CombatState!.HittableEnemies)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.heavyAttack)
            .Execute();
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(3);
    }
}
