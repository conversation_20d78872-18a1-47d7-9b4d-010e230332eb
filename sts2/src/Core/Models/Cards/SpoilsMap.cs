using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SpoilsMap : CardModel
{
    public override int CanonicalEnergyCost => -1;

    public override CardType Type => CardType.Quest;
    public override CardRarity Rarity => CardRarity.Quest;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.Self;

    public override int MaxUpgradeLevel => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Unplayable];

    private int _spoilsActIndex = -1;

    [SavedProperty]
    public int SpoilsActIndex
    {
        get => _spoilsActIndex;
        set
        {
            AssertMutable();
            _spoilsActIndex = value;
        }
    }

    public MapCoord? SpoilsCoord
    {
        get => SpoilsCoordSet ? new MapCoord { col = SpoilsCoordCol, row = SpoilsCoordRow } : null;
        set
        {
            AssertMutable();

            SpoilsCoordSet = value != null;
            SpoilsCoordCol = value?.col ?? 0;
            SpoilsCoordRow = value?.row ?? 0;
        }
    }

    // Used only for serialization purposes. Access these properties through SpoilsCoord instead
    [SavedProperty]
    private int SpoilsCoordCol { get; set; }

    [SavedProperty]
    private int SpoilsCoordRow { get; set; }

    [SavedProperty]
    private bool SpoilsCoordSet { get; set; }

    public override void AfterCreated()
    {
        SpoilsActIndex = Owner.ClimbState.CurrentActIndex + 1;
    }

    public override Task AfterMapGenerated(ActMap map, int actIndex)
    {
        if (Pile?.Type != CardPileTarget.Deck) return Task.CompletedTask;

        if (actIndex == SpoilsActIndex)
        {
            // We need to account for if the map has changed since the spoils coord was set.
            if (SpoilsCoord == null || !map.HasPoint(SpoilsCoord.Value) || map.GetPoint(SpoilsCoord.Value)!.PointType != MapPointType.Monster)
            {
                // Otherwise, this is a newly generated map and we should grab a random map point & put our spoils there.
                // We use our own RNG because we want this to generate the same point for all spoils maps generated for
                // each player in a multiplayer session.
                Rng rng = new(Owner.ClimbState.Rng.Seed + (uint)StringHelper.GetDeterministicHashCode(nameof(SpoilsMap)));
                IEnumerable<IGrouping<int, MapPoint>> spoilsMapPoints = map
                    .GetAllMapPoints()
                    .Where(p => p is { PointType: MapPointType.Monster, coord.row: >= 3 })
                    .GroupBy(p => p.coord.row);

                // Try to find an encounter on row 3 or 4.
                IEnumerable<IGrouping<int, MapPoint>> validRows = spoilsMapPoints.Where(g => g.Key is 3 or 4);

                // Otherwise, get the first valid row available.
                if (!validRows.Any())
                {
                    validRows = [spoilsMapPoints.First()];
                }

                MapPoint? point = rng.NextItem(validRows.SelectMany(g => g.ToArray()));

                if (point != null)
                {
                    SpoilsCoord = point.coord;
                    point.AddQuestCard(this);
                }
            }
            else
            {
                // If this card is being loaded from a save file, it's possible that SpoilsCoord has already been set
                // and we just need to associate this card with the right map point
                MapPoint? loadedPoint = map.GetPoint(SpoilsCoord.Value);
                if (loadedPoint == null)
                {
                    throw new InvalidOperationException(
                        $"Loaded a spoils map card with coordinate {SpoilsCoord}, but the generated map does not " +
                        $"contain that coordinate!"
                    );
                }

                loadedPoint.AddQuestCard(this);
            }
        }

        return Task.CompletedTask;
    }

    // When removed, if we had set some map point's quest card, remove it.
    public override Task BeforeCardRemoved(CardModel card)
    {
        if (card != this) return Task.CompletedTask;
        if (SpoilsActIndex != Owner.ClimbState.CurrentActIndex) return Task.CompletedTask;
        if (SpoilsCoord == null) return Task.CompletedTask;

        Owner.ClimbState.Map.GetPoint(SpoilsCoord.Value)?.RemoveQuestCard(this);

        return Task.CompletedTask;
    }

    public override bool TryModifyRewards(Player player, List<Reward> rewards, AbstractRoom room)
    {
        if (player != Owner) return false;
        if (Pile?.Type != CardPileTarget.Deck) return false;
        if (room is not CombatRoom) return false;
        if (Owner.ClimbState.CurrentMapCoord != SpoilsCoord) return false;

        List<Reward> newRewards = [];
        foreach (Reward reward in rewards)
        {
            switch (reward)
            {
                case GoldReward goldReward:
                    newRewards.Add(new GoldReward(goldReward.Amount * 2, player));
                    break;
                default:
                    newRewards.Add(reward);
                    break;
            }
        }

        newRewards.Add(new RelicReward(RelicRarity.Rare, player));
        newRewards.Add(new RelicReward(RelicRarity.Uncommon, player));

        rewards.Clear();
        rewards.AddRange(newRewards);
        return true;
    }

    public override async Task AfterModifyingRewards()
    {
        await CardPileCmd.RemoveFromDeck(this);
    }
}
