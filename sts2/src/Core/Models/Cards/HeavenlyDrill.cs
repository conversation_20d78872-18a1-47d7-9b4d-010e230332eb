using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HeavenlyDrill : CardModel
{
    private const string _extraDamageKey = "ExtraDamage";

    public override int CanonicalEnergyCost => 2;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Rare;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(8, DamageProps.card),
        new RepeatVar(0)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, DynamicVars.Repeat.IntValue)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    public override void RecalculateValues()
    {
        int starsSpentCount = CombatManager.Instance.History.Entries.OfType<StarsModifiedEntry>()
            .Where(e => e.Actor == Owner.Creature && e.HappenedThisTurn(CombatState) && e.Amount < 0)
            .Sum(e => -e.Amount);

        DynamicVars.Repeat.BaseValue = starsSpentCount;
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(2);
    }
}
