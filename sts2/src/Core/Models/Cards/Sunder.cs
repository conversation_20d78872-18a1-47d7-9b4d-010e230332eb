using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Commands.Builders;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Sunder : CardModel
{
    public override int CanonicalEnergyCost => 3;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(24, DamageProps.card),
        new EnergyVar(3)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        AttackCommand attack = await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.slashPath, tmpSfx: TmpSfx.slashAttack)
            .Execute();

        if (attack.Results.Any(r => r.WasTargetKilled))
        {
            await PlayerCmd.GainEnergy(DynamicVars.Energy.IntValue, Owner);
        }
    }

    protected override void OnUpgrade()
    {
        DynamicVars.Damage.UpgradeValueBy(8);
    }
}
