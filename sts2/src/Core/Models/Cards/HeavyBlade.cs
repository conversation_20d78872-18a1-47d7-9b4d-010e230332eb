using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class HeavyBlade : CardModel
{
    private const int _baseDamage = 14;
    private const string _strengthMultiplierKey = "StrengthMultiplier";

    public override int CanonicalEnergyCost => 2;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Common;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(_baseDamage, DamageProps.card),
        new(_strengthMultiplierKey, 3)
    ];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues();

        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.rockShatterPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_strengthMultiplierKey].UpgradeValueBy(2);
    }

    public override void RecalculateValues()
    {
        // -1 to leave room for Strength to apply its normal effect.
        int strengthMultiplier = DynamicVars[_strengthMultiplierKey].IntValue - 1;
        int strength = Owner.Creature.GetPowerAmount<Strength>();

        DynamicVars.Damage.BaseValue = _baseDamage + strengthMultiplier * strength;
    }
}
