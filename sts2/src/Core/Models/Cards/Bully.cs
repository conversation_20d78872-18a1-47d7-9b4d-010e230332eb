using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Bully : CardModel
{
    private const string _baseDamageKey = "BaseDamage";
    private const string _extraDamageKey = "ExtraDamage";
    private const string _enchantmentDamageKey = "EnchantmentDamage";

    public override int CanonicalEnergyCost => 0;
    public override CardType Type => CardType.Attack;
    public override CardRarity Rarity => CardRarity.Uncommon;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.Any;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_baseDamageKey, 4),
        new(_extraDamageKey, 1),
        // TODO: Come up with a system that handles this without an extra variable.
        new DamageVar(_enchantmentDamageKey, 0, DamageProps.card),
        new DamageVar(4, DamageProps.card),
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Vulnerable>()];

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        ArgumentNullException.ThrowIfNull(target);

        RecalculateValues(target);
        await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
            .FromCard(this)
            .Targeting(target)
            .WithHitFx(VfxCmd.bluntPath, tmpSfx: TmpSfx.bluntAttack)
            .Execute();
    }

    public override void RecalculateValues()
    {
        DynamicVars.Damage.BaseValue =
            DynamicVars[_baseDamageKey].BaseValue +
            DynamicVars[_enchantmentDamageKey].BaseValue;
    }

    public override void RecalculateValues(Creature target)
    {
        DynamicVars.Damage.BaseValue =
            DynamicVars[_baseDamageKey].BaseValue +
            DynamicVars[_enchantmentDamageKey].BaseValue +
            DynamicVars[_extraDamageKey].BaseValue * target.GetPowerAmount<Vulnerable>();
    }

    protected override void OnUpgrade()
    {
        DynamicVars[_extraDamageKey].UpgradeValueBy(1);
    }
}
