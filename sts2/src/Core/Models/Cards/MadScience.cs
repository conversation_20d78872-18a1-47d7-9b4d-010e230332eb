using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MadScience : CardModel
{
    public const int attackDamage = 15;
    public const int skillBlock = 13;
    private const string _powerAmountKey = "PowerAmount";
    private const string _powerTypeKey = "PowerType";
    private const string _bodyStrengthKey = "BodyStrength";
    private const string _flameVulnerableKey = "FlameVulnerable";
    private const string _heartEnergyKey = "HeartEnergy";
    private const string _iceWeakKey = "IceWeak";
    private const string _invulnerableBufferKey = "InvulnerableBuffer";
    private const string _mindCardsKey = "MindCards";
    private const string _shrinkingStrengthLossKey = "ShrinkingStrengthLoss";
    private const string _stabilityDexterityKey = "StabilityDexterity";
    private const string _vengeanceThornsKey = "VengeanceThorns";

    public override bool ShouldShowInCardLibrary => false;

    public override string PortraitPath => GetPortraitPath(TinkerTimeType);
    public override string BetaPortraitPath => MissingPortraitPath;

    public override string[] AllPortraitPaths =>
    [
        GetPortraitPath(CardType.Attack),
        GetPortraitPath(CardType.Skill),
        GetPortraitPath(CardType.Power)
    ];

    private string GetPortraitPath(CardType cardType)
    {
        return ImageHelper.GetImagePath($"atlases/card_atlas.sprites/event/{GetPortraitFilename(cardType)}.tres");
    }

    private string GetPortraitFilename(CardType cardType)
    {
        return cardType switch
        {
            CardType.Attack => "mad_science_attack",
            CardType.Skill => "mad_science_skill",
            CardType.Power => "mad_science_power",
            _ => throw new InvalidOperationException($"Mad Science is invalid type {TinkerTimeType}.")
        };
    }

    public override int CanonicalEnergyCost => 2;
    public override CardType Type => TinkerTimeType;
    public override CardRarity Rarity => CardRarity.Event;

    public override UiTargetEnemy TargetEnemy => TinkerTimeType == CardType.Attack ? UiTargetEnemy.Any : UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => TinkerTimeType == CardType.Attack ? UiTargetPlayer.None : UiTargetPlayer.Self;

    public override bool GainsBlock => TinkerTimeType == CardType.Skill;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(15, DamageProps.card),
        new BlockVar(13, BlockProps.card),
        new(_powerAmountKey, 2),
        new StringVar(_powerTypeKey),
        new PowerVar<Strength>(_bodyStrengthKey, 3),
        new PowerVar<Vulnerable>(_flameVulnerableKey, 2),
        new EnergyVar(_heartEnergyKey, 3),
        new PowerVar<Weak>(_iceWeakKey, 2),
        new(_invulnerableBufferKey, 1),
        new CardsVar(_mindCardsKey, 3),
        new(_shrinkingStrengthLossKey, 8),
        new PowerVar<Dexterity>(_stabilityDexterityKey, 3),
        new(_vengeanceThornsKey, 5)
    ];

    private CardType _tinkerTimeType;

    // Force order to -1 so that it always serializes before other properties which depend on it
    [SavedProperty(SerializationCondition.AlwaysSave, -1)]
    public CardType TinkerTimeType
    {
        get => _tinkerTimeType;
        set
        {
            AssertMutable();
            _tinkerTimeType = value;
        }
    }

    private ModelId? _tinkerTimePowerId;

    [SavedProperty(SerializationCondition.SaveIfNotTypeDefault)]
    public ModelId? TinkerTimePowerId
    {
        get => _tinkerTimePowerId;
        set
        {
            AssertMutable();
            _tinkerTimePowerId = value;
        }
    }

    private TinkerTime.CardWord[]? _tinkerTimeWords;

    [SavedProperty]
    public TinkerTime.CardWord[]? TinkerTimeWords
    {
        get => _tinkerTimeWords;
        set
        {
            AssertMutable();
            _tinkerTimeWords = value;
        }
    }

    private TinkerTime.CardFocus _tinkerTimeFocus;

    [SavedProperty]
    public TinkerTime.CardFocus TinkerTimeFocus
    {
        get => _tinkerTimeFocus;
        set
        {
            AssertMutable();
            _tinkerTimeFocus = value;
        }
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips
    {
        get
        {
            List<IHoverTip> hoverTips = [];

            if (Type == CardType.Power)
            {
                hoverTips.Add(ModelDb.GetById<PowerModel>(TinkerTimePowerId!).DumbHoverTip);
            }

            foreach (TinkerTime.CardWord word in TinkerTimeWords!)
            {
                hoverTips.AddRange(TinkerTime.GetCardWordHoverTips(word, Owner));
            }

            hoverTips.AddRange(TinkerTime.GetCardFocusHoverTips(TinkerTimeFocus, Type));

            return hoverTips;
        }
    }

    /// <summary>
    /// Update this card's DynamicVars and other internal state to match the choices made for it in the
    /// <see cref="TinkerTime"/> event.
    /// </summary>
    public void ApplyChoices()
    {
        if (TinkerTimePowerId != default)
        {
            string powerTitle = ModelDb.GetById<PowerModel>(TinkerTimePowerId).Title.GetFormattedText();
            ((StringVar)DynamicVars[_powerTypeKey]).StringValue = powerTitle;
        }

        ApplyFocus(TinkerTimeFocus);
    }

    protected override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        if (TargetEnemy == UiTargetEnemy.Any && target == null)
        {
            ArgumentNullException.ThrowIfNull(target);
        }

        switch (TinkerTimeType)
        {
            case CardType.Attack:
                await DamageCmd.Attack(DynamicVars.Damage.BaseValue, 1)
                    .FromCard(this)
                    .Targeting(target!)
                    .WithHitFx(VfxCmd.slashPath)
                    .Execute();

                break;
            case CardType.Skill:
                await CreatureCmd.GainBlock(Owner.Creature, DynamicVars.Block, this);
                break;
            case CardType.Power:
                await CreatureCmd.TriggerAnim(Owner.Creature, SpineAnimator.castTrigger, Owner.Character.CastAnimDelay);

                PowerModel? existingPower = Owner.Creature.GetPowerById(TinkerTimePowerId!);

                if (existingPower != null)
                {
                    await PowerCmd.ModifyAmount(
                        existingPower,
                        DynamicVars[_powerAmountKey].BaseValue,
                        Owner.Creature,
                        this
                    );
                }
                else
                {
                    await PowerCmd.Apply(
                        ModelDb.GetById<PowerModel>(TinkerTimePowerId!).ToMutable(),
                        Owner.Creature,
                        DynamicVars[_powerAmountKey].BaseValue,
                        Owner.Creature,
                        this
                    );
                }

                break;
            default:
                throw new ArgumentOutOfRangeException();
        }

        // TinkerTimeWords should've been set by the TinkerTime event, blow up if it wasn't.
        foreach (TinkerTime.CardWord word in TinkerTimeWords!)
        {
            await ExecuteWord(word, target, choiceContext);
        }

        await ExecuteFocus(TinkerTimeFocus);
    }

    protected override void OnUpgrade()
    {
        AddKeyword(CardKeyword.Innate);
    }

    protected override void AfterDowngraded() => ApplyChoices();
    protected override void AfterDeserialized() => ApplyChoices();

    protected override void AddExtraArgsToDescription(LocString description)
    {
        description.Add("CardType", TinkerTimeType.ToString());

        foreach (TinkerTime.CardWord word in Enum.GetValues(typeof(TinkerTime.CardWord)).Cast<TinkerTime.CardWord>())
        {
            description.Add(word.ToString(), TinkerTimeWords != null && TinkerTimeWords.Contains(word));
        }

        foreach (TinkerTime.CardFocus focus in Enum.GetValues(typeof(TinkerTime.CardFocus)).Cast<TinkerTime.CardFocus>())
        {
            description.Add(focus.ToString(), TinkerTimeFocus == focus);
        }
    }

    private async Task ExecuteWord(TinkerTime.CardWord word, Creature? target, PlayerChoiceContext choiceContext)
    {
        switch (word)
        {
            case TinkerTime.CardWord.Body:
                decimal bodyStrength = DynamicVars[_bodyStrengthKey].BaseValue;
                await PowerCmd.Apply<Strength>(Owner.Creature, bodyStrength, Owner.Creature, this);
                await PowerCmd.Apply<StrengthDown>(Owner.Creature, bodyStrength, Owner.Creature, this);
                break;
            case TinkerTime.CardWord.Flame:
                await PowerCmd.Apply<Vulnerable>(target!, DynamicVars[_flameVulnerableKey].BaseValue, Owner.Creature, this);
                break;
            case TinkerTime.CardWord.Heart:
                await PowerCmd.Apply<Energized>(Owner.Creature, DynamicVars[_heartEnergyKey].BaseValue, Owner.Creature, this);
                break;
            case TinkerTime.CardWord.Ice:
                await PowerCmd.Apply<Weak>(target!, DynamicVars[_iceWeakKey].BaseValue, Owner.Creature, this);
                break;
            case TinkerTime.CardWord.Invulnerable:
                await PowerCmd.Apply<BufferPower>(Owner.Creature, DynamicVars[_invulnerableBufferKey].IntValue, Owner.Creature, this);
                break;
            case TinkerTime.CardWord.Mind:
                await CardPileCmd.Draw(choiceContext, DynamicVars[_mindCardsKey].IntValue, Owner);
                break;
            case TinkerTime.CardWord.Soul:
                CardModel canonicalCard = CardFactory.GetDistinctForCombat(
                    Owner.Character.CardPool.Cards,
                    1,
                    Owner.ClimbState.Rng.CombatCardGeneration
                ).First();
                CardModel mutableCard = CombatState!.CreateCard(canonicalCard, Owner);
                mutableCard.SetToFreeThisTurn();
                await CardPileCmd.Add(mutableCard, CardPileTarget.Hand);

                break;
            case TinkerTime.CardWord.Stability:
                decimal stabilityDexterity = DynamicVars[_stabilityDexterityKey].BaseValue;
                await PowerCmd.Apply<Dexterity>(Owner.Creature, stabilityDexterity, Owner.Creature, this);
                await PowerCmd.Apply<DexterityDown>(Owner.Creature, stabilityDexterity, Owner.Creature, this);

                break;
            case TinkerTime.CardWord.Vengeance:
                await PowerCmd.Apply<Thorns>(Owner.Creature, DynamicVars[_vengeanceThornsKey].BaseValue, Owner.Creature, this);
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(word), word, null);
        }
    }

    private async Task ExecuteFocus(TinkerTime.CardFocus focus)
    {
        switch (focus)
        {
            case TinkerTime.CardFocus.Focus:
                IReadOnlyList<CardModel> drawPile = CardPileTarget.Draw.GetPile(Owner).Cards;
                if (drawPile.Count != 0)
                {
                    CardModel? oldCard = Owner.ClimbState.Rng.CombatCardSelection.NextItem(drawPile);

                    if (oldCard is { IsRemovable: true })
                    {
                        CardModel clone = CombatState!.CloneCard(this);
                        await CardCmd.Transform(oldCard, clone, true);
                    }
                }

                break;
            case TinkerTime.CardFocus.Power:
                // No-op, handled in ApplyFocus.
                break;
            case TinkerTime.CardFocus.Speed:
                // No-op, handled in ApplyFocus.
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(focus), focus, null);
        }
    }

    private void ApplyFocus(TinkerTime.CardFocus focus)
    {
        switch (focus)
        {
            case TinkerTime.CardFocus.Focus:
                // No-op, handled in OnPlay.
                break;
            case TinkerTime.CardFocus.Power:
                BaseEnergyCost++;

                foreach (DynamicVar dynamicVar in DynamicVars.Values)
                {
                    dynamicVar.BaseValue *= 2;
                }

                break;
            case TinkerTime.CardFocus.Speed:
                BaseEnergyCost--;

                if (TinkerTimeType != CardType.Power)
                {
                    AddKeyword(CardKeyword.Exhaust);
                }

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(focus), focus, null);
        }
    }
}
