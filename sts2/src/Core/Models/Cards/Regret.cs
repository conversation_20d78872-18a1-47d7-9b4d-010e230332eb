using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Regret : CardModel
{
    public override int CanonicalEnergyCost => -1;
    public override CardType Type => CardType.Curse;
    public override CardRarity Rarity => CardRarity.Curse;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override int MaxUpgradeLevel => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords => [CardKeyword.Unplayable];

    private int _cardsInHand;

    private int CardsInHand
    {
        get => _cardsInHand;
        set
        {
            AssertMutable();
            _cardsInHand = value;
        }
    }

    public override bool HasTurnEndInHandEffect => true;

    public override Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return Task.CompletedTask;
        if (Pile!.Type != CardPileTarget.Hand) return Task.CompletedTask;

        // We want to save this value before any on-turn-end-effect cards can be triggered and discarded.
        CardsInHand = Pile!.Cards.Count;

        return Task.CompletedTask;
    }

    public override async Task OnTurnEndInHand()
    {
        await CreatureCmd.Damage(Owner.Creature, CardsInHand, DamageProps.cardHpLoss, this);
        CardsInHand = 0;
    }
}