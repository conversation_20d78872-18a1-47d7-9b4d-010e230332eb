using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;

namespace MegaCrit.Sts2.Core.Models.Cards;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Doubt : CardModel
{
    public override int CanonicalEnergyCost => -1;
    public override CardType Type => CardType.Curse;
    public override CardRarity Rarity => CardRarity.Curse;

    public override UiTargetEnemy TargetEnemy => UiTargetEnemy.None;
    public override UiTargetPlayer TargetPlayer => UiTargetPlayer.None;

    public override int MaxUpgradeLevel => 0;

    public override IEnumerable<CardKeyword> CanonicalKeywords =>
    [
        CardKeyword.Unplayable
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new PowerVar<Weak>(1)
    ];

    public override bool HasTurnEndInHandEffect => true;

    public override async Task OnTurnEndInHand()
    {
        bool alreadyHasWeak = Owner.Creature.HasPower<Weak>();

        // Can be null if blocked by Artifact or something.
        PowerModel? weak = await PowerCmd.Apply<Weak>(Owner.Creature, DynamicVars.Weak.BaseValue, null, this);

        // Weak would normally wear off at the end of the monster side turn since it's being applied by the player,
        // so manually set it to last an extra turn.
        if (weak != null && !alreadyHasWeak)
        {
            weak.SkipNextDurationTick = true;
        }
    }
}
