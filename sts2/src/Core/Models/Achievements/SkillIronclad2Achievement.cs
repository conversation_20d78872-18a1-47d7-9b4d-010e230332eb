using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Achievements;

/// <summary>
/// Grants an achievement when a player deals a large amount of damage in a single blow.
/// </summary>
public class SkillIronclad2Achievement : AchievementModel
{
    private const int _damageRequirement = 999;

    public override Task AfterDamageGiven(Creature? dealer, DamageResult result, ValueProp props, Creature target, CardModel? cardSource)
    {
        if (!LocalContext.IsMe(dealer)) return Task.CompletedTask;
        if (result.UnblockedDamage < _damageRequirement) return Task.CompletedTask;

        AchievementsUtil.Unlock(Achievement.CharacterSkillIronclad2, dealer!.Player);
        return Task.CompletedTask;
    }
}
