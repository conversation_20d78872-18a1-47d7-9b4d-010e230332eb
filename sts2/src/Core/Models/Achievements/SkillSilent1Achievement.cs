using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Achievements;

/// <summary>
/// Grants an achievement when a player plays a large number of Sly cards from a single card.
/// </summary>
public class SkillSilent1Achievement : AchievementModel
{
    // The theory here is: if the player plays a bunch of sly cards before a card leaves the play zone, that means
    // they must have auto-played the sly cards using the first card that was played.
    // This might be subtly wrong, so change if needed
    private CardModel? _firstCardOnStack;
    private int _slyCardsPlayed;

    public override Task BeforeCardPlayed(CardModel card, Creature? target, int playCount)
    {
        if (!LocalContext.IsMine(card)) return Task.CompletedTask;

        if (_firstCardOnStack == null)
        {
            _firstCardOnStack = card;
        }

        return Task.CompletedTask;
    }

    public override Task BeforeCardAutoPlayed(CardModel card, Creature? target, AutoPlayType type)
    {
        if (!LocalContext.IsMine(card)) return Task.CompletedTask;
        if (type != AutoPlayType.SlyDiscard) return Task.CompletedTask;
        if (_firstCardOnStack == null) return Task.CompletedTask;

        _slyCardsPlayed++;

        if (_slyCardsPlayed >= 5)
        {
            AchievementsUtil.Unlock(Achievement.CharacterSkillSilent1, card.Owner);
        }

        return Task.CompletedTask;
    }

    public override Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (!LocalContext.IsMine(card)) return Task.CompletedTask;

        if (card == _firstCardOnStack)
        {
            _firstCardOnStack = null;
            _slyCardsPlayed = 0;
        }

        return Task.CompletedTask;
    }

    public override Task AfterRoomEntered(AbstractRoom room)
    {
        _firstCardOnStack = null;
        _slyCardsPlayed = 0;
        return Task.CompletedTask;
    }
}
