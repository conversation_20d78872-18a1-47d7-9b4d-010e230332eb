using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Platform;

namespace MegaCrit.Sts2.Core.Models.Achievements;

/// <summary>
/// Grants an achievement when the player obtains a large number of Stars.
/// </summary>
public class SkillRegent2Achievement : AchievementModel
{
    private const int _starThreshold = 20;

    public override Task AfterStarsGained(int amount, Player gainer)
    {
        if (!LocalContext.IsMe(gainer)) return Task.CompletedTask;
        if (gainer.PlayerCombatState?.Stars < _starThreshold) return Task.CompletedTask;

        AchievementsUtil.Unlock(Achievement.CharacterSkillRegent2, gainer);
        return Task.CompletedTask;
    }
}
