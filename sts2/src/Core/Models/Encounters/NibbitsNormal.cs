using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class NibbitsNormal : EncounterModel
{
    private const string _backSlot = "back";
    private const string _frontSlot = "front";

    public override IReadOnlyList<string> Slots =>
    [
        _frontSlot,
        _backSlot
    ];

    public override RoomType RoomType => RoomType.Monster;

    public override bool HasScene => true;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Nibbit>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        Nibbit frontMonster = (Nibbit)ModelDb.Monster<Nibbit>().ToMutable();
        frontMonster.IsFront = true;

        return
        [
            (frontMonster, _frontSlot),
            (ModelDb.Monster<Nibbit>().ToMutable(), _backSlot),
        ];
    }
}
