using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters.Mocks;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MockMonsterEncounter : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;
    public override bool IsDebugEncounter => true;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<BigDummy>()];
    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() => [(ModelDb.Monster<BigDummy>().ToMutable(), null)];
}
