using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GemEaterNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;


    private readonly IEnumerable<MonsterModel> _gemEaterAlly =
    [
        ModelDb.Monster<Seapunk>(),
        ModelDb.Monster<SludgeSpinner>()
    ];

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<GemEater>(), ModelDb.Monster<Seapunk>(), ModelDb.Monster<SludgeSpinner>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (Rng.NextItem(_gemEaterAlly)!.ToMutable(), null),
        (ModelDb.Monster<GemEater>().ToMutable(), null)
    ];
}
