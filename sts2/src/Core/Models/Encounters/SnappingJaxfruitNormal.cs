using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SnappingJaxfruitNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Mushroom];
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<SnappingJaxfruit>(), ModelDb.Monster<Flyconid>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<SnappingJaxfruit>().ToMutable(), null),
        (ModelDb.Monster<Flyconid>().ToMutable(), null),
    ];
}
