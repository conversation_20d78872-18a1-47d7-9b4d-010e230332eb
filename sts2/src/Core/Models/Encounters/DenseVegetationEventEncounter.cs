using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DenseVegetationEventEncounter : EncounterModel
{
    private const string _wrigglerSlotPrefix = "wriggler";

    public override RoomType RoomType => RoomType.Monster;

    public override IReadOnlyList<string> Slots =>
    [
        $"{_wrigglerSlotPrefix}1",
        $"{_wrigglerSlotPrefix}2",
        $"{_wrigglerSlotPrefix}3",
        $"{_wrigglerSlotPrefix}4"
    ];

    public override bool HasScene => true;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Wriggler>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        List<(MonsterModel, string?)> monsters = [];

        foreach (string slotName in Slots)
        {
            Wriggler wriggler = (Wriggler)ModelDb.Monster<Wriggler>().ToMutable();
            wriggler.StartStunned = false;
            monsters.Add((wriggler, slotName));
        }

        return monsters;
    }
}
