using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ScrollsOfBitingNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;

    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Scrolls];
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<ScrollOfBiting>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        ScrollOfBiting first = (ScrollOfBiting)ModelDb.Monster<ScrollOfBiting>().ToMutable();
        ScrollOfBiting second = (ScrollOfBiting)ModelDb.Monster<ScrollOfBiting>().ToMutable();
        ScrollOfBiting third = (ScrollOfBiting)ModelDb.Monster<ScrollOfBiting>().ToMutable();
        ScrollOfBiting fourth = (ScrollOfBiting)ModelDb.Monster<ScrollOfBiting>().ToMutable();

        // We want each scroll to start off with a different move, except for the fourth 1 which
        // is specified to always start with its buffed move instead.
        int offset = Rng.NextInt(3);
        first.StarterMoveIdx = offset;
        second.StarterMoveIdx = (offset + 1) % 3;
        third.StarterMoveIdx = (offset + 2) % 3;
        fourth.StarterMoveIdx = 2;

        return
        [
            (first, null),
            (second, null),
            (third, null),
            (fourth, null)
        ];
    }
}
