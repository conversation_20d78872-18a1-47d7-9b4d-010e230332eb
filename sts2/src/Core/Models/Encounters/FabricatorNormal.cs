using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FabricatorNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;

    private const string _fabricatorSlot = "fabricator";
    private const string _botSlotPrefix = "bot";

    public override bool HasScene => true;

    public override IReadOnlyList<string> Slots =>
    [
        $"{_botSlotPrefix}1",
        $"{_botSlotPrefix}2",
        _fabricatorSlot,
        $"{_botSlotPrefix}3",
        $"{_botSlotPrefix}4"
    ];

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Fabricator>(), ..Fabricator.defenseSpawns, ..Fabricator.aggroSpawns];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<Fabricator>().ToMutable(), _fabricatorSlot)
    ];

    public override float GetCameraScaling(CombatState combatState) => 0.85f;

    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 60f;
}
