using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ChompersNormal : EncounterModel
{
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Chomper];

    public override RoomType RoomType => RoomType.Monster;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Chomper>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        Chomper frontChomper = (Chomper)ModelDb.Monster<Chomper>().ToMutable();
        Chomper backChomper = (Chomper)ModelDb.Monster<Chomper>().ToMutable();

        backChomper.ScreamFirst = true;

        return
        [
            (frontChomper, null),
            (backChomper, null)
        ];
    }
}
