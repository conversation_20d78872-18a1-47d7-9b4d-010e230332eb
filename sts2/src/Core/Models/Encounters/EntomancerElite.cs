using System.Collections.Generic;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class EntomancerElite : EncounterModel
{
    public override RoomType RoomType => RoomType.Elite;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Monsters.Entomancer>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<Monsters.Entomancer>().ToMutable(), null)
    ];
}
