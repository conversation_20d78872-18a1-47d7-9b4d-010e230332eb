using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SoulFyshBoss : EncounterModel
{
    public override string BossNodePath => $"res://images/packed/map/placeholder/{Id.Entry.ToLower()}_icon.png";
    public override SpineSkeletonDataResource? BossNodeSpineResource => null;

    public override RoomType RoomType => RoomType.Boss;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<SoulFysh>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<SoulFysh>().ToMutable(), null),
    ];
}
