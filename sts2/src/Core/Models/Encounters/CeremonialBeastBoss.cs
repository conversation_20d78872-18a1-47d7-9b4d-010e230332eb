using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CeremonialBeastBoss : EncounterModel
{
    public override RoomType RoomType => RoomType.Boss;

    public override string CustomBgm => "event:/music/act1_boss_ceremonial_beast";

    public override float GetCameraScaling(CombatState combatState) => 0.9f;
    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;
    protected override bool HasCustomBackground => true;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<CeremonialBeast>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<CeremonialBeast>().ToMutable(), null)
    ];
}
