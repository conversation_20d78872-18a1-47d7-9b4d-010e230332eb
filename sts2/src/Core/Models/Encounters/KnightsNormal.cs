using System.Collections.Generic;
using System.Linq;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class KnightsNormal : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Knights];
    public override float GetCameraScaling(CombatState combatState) => 0.9f;

    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;

    public override IEnumerable<MonsterModel> AllPossibleMonsters =>
    [
        ModelDb.Monster<FlailKnight>(),
        ModelDb.Monster<MagiKnight>(),
        ModelDb.Monster<SpectralKnight>()
    ];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        return AllPossibleMonsters.Select(m => (m.ToMutable(), (string?)null)).ToList();
    }
}
