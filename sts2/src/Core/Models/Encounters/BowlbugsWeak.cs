using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BowlbugsWeak : EncounterModel
{
    public override RoomType RoomType => RoomType.Monster;
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Workers];
    public override bool IsWeak => true;

    private readonly MonsterModel[] _bugs =
    [
        ModelDb.Monster<BowlbugEgg>(),
        ModelDb.Monster<BowlbugGoop>(),
        ModelDb.Monster<BowlbugSilk>()
    ];

    public override IEnumerable<MonsterModel> AllPossibleMonsters => _bugs.Concat([ModelDb.Monster<BowlbugRock>()]);

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<BowlbugRock>().ToMutable(), "odd"),
        (Rng.NextItem(_bugs)!.ToMutable(), "even"),
    ];

    public override bool HasScene => true;
}
