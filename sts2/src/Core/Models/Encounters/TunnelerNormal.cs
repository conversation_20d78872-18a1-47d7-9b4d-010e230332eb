using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TunnelerNormal : EncounterModel
{
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Burrower, EncounterTag.Workers];

    private static readonly MonsterModel[] _bugs =
    [
        ModelDb.Monster<BowlbugEgg>(),
        ModelDb.Monster<BowlbugSilk>()
        // Rock bug purposefully excluded
    ];

    public override RoomType RoomType => RoomType.Monster;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Tunneler>(), .._bugs];
    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (Rng.NextItem(_bugs)!.ToMutable(), null),
        (ModelDb.Monster<Tunneler>().ToMutable(), null)
    ];
}
