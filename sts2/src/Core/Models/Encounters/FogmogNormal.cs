using System.Collections.Generic;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FogmogNormal : EncounterModel
{
    public const string illusionSlot = "illusion";
    public const string fogmogSlot = "fogmog";

    public override RoomType RoomType => RoomType.Monster;

    public override IReadOnlyList<string> Slots =>
    [
        illusionSlot,
        fogmogSlot
    ];

    public override bool HasScene => true;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<Fogmog>(), ModelDb.Monster<EyeWithTeeth>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<Fogmog>().ToMutable(), fogmogSlot)
    ];
}
