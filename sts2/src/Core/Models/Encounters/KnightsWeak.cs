using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class KnightsWeak : EncounterModel
{
    private static readonly MonsterModel[] _knights =
    [
        ModelDb.Monster<MagiKnight>(),
        ModelDb.Monster<FlailKnight>(),
        ModelDb.Monster<SpectralKnight>()
    ];

    public override RoomType RoomType => RoomType.Monster;
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Knights];
    public override bool IsWeak => true;
    public override IEnumerable<MonsterModel> AllPossibleMonsters => _knights;

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
        _knights.ToList()
            .StableShuffle(Rng)
            .Take(2)
            .Select(m => (m.ToMutable(), (string?)null))
            .ToList();

    public override float GetCameraScaling(CombatState combatState) => 0.9f;
}
