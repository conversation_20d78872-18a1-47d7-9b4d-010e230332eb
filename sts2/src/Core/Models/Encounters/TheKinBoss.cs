using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheKinBoss : EncounterModel
{
    public override RoomType RoomType => RoomType.Boss;

    public override bool HasScene => true;
    protected override bool HasCustomBackground => true;
    public override float GetCameraScaling(CombatState combatState) => 0.95f;
    public override Vector2 GetCameraOffset(CombatState combatState) => Vector2.Down * 50f;
    public override string CustomBgm => "event:/music/act1_boss_the_kin";
    public override string BossNodePath => $"res://images/packed/map/placeholder/{Id.Entry.ToLower()}_icon.png";
    public override SpineSkeletonDataResource? BossNodeSpineResource => null;

    public override IReadOnlyList<string> Slots =>
    [
        "slot1",
        "slot2",
        "slot3",
        "slot4",
        "leaderSlot",
    ];

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<KinFollower>(), ModelDb.Monster<KinPriest>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters()
    {
        KinFollower frontMonster = (KinFollower)ModelDb.Monster<KinFollower>().ToMutable();
        frontMonster.StartWithBoomerang = true;

        return
        [
            (frontMonster, "slot3"),
            (ModelDb.Monster<KinFollower>().ToMutable(), "slot4"),
            (ModelDb.Monster<KinPriest>().ToMutable(), "leaderSlot"),
        ];
    }
}
