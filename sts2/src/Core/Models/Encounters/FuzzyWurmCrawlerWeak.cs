using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Encounters;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Encounters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FuzzyWurmCrawlerWeak : EncounterModel
{
    public override IEnumerable<EncounterTag> Tags => [EncounterTag.Crawler];

    public override RoomType RoomType => RoomType.Monster;
    public override bool IsWeak => true;

    public override IEnumerable<MonsterModel> AllPossibleMonsters => [ModelDb.Monster<FuzzyWurmCrawler>()];

    protected override IReadOnlyList<(MonsterModel, string?)> GenerateMonsters() =>
    [
        (ModelDb.Monster<FuzzyWurmCrawler>().ToMutable(), null)
    ];
}
