using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SleightOfFleshPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    protected override IEnumerable<DynamicVar> CanonicalVars => [];

    public override async Task AfterPowerAmountChanged(PowerModel power, decimal amount, Creature? applier)
    {
        if (amount <= 0) return;
        if (power.GetTypeForAmount(amount) != PowerType.Debuff) return;
        if (!power.Owner.IsEnemy) return;
        if (applier != Owner) return;

        Flash();
        await CreatureCmd.Damage(power.Owner, Amount, DamageProps.nonCardUnpowered, Owner, null);
    }
}
