using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PossessPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Single;

    private class Data
    {
        public Dictionary<Creature, decimal> stolenStrength = new();
    }

    protected override object InitInternalData() => new Data();

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    private Dictionary<Creature, decimal> StolenStrength => GetInternalData<Data>().stolenStrength;

    public override Task AfterPowerAmountChanged(PowerModel power, decimal amount, Creature? applier)
    {
        if (applier != Owner) return Task.CompletedTask;
        if (!power.Owner.IsPlayer) return Task.CompletedTask;
        if (power is not Strength) return Task.CompletedTask;
        if (amount >= 0) return Task.CompletedTask;

        if (!StolenStrength.ContainsKey(power.Owner))
        {
            StolenStrength.Add(power.Owner, 0);
        }

        StolenStrength[power.Owner] += amount;
        return Task.CompletedTask;
    }

    public override async Task AfterDeath(PlayerChoiceContext choiceContext, Creature creature, float _)
    {
        if (creature != Owner) return;

        foreach (KeyValuePair<Creature, decimal> kvp in StolenStrength)
        {
            await PowerCmd.Apply<Strength>(kvp.Key, -kvp.Value, null, null);
        }
    }
}
