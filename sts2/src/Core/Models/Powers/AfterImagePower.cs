using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class AfterImagePower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    private class Data
    {
        /// <summary>
        /// Keep track of the cards we've seen played and the power amount at the time they were played.
        /// This lets After Image avoid triggering on cards that started play before it was applied, and avoid gaining
        /// extra block on multiple plays of After Image.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    public override Task BeforeCardPlayed(CardModel card, Creature? _, int playCount)
    {
        if (card.Owner.Creature != Owner) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        // Skip if the start of the card play wasn't seen.
        // This can happen if the card was played before AfterImagePower was applied (happens on After Image itself, or
        // if After Image was auto-played by another card).
        if (card.Owner.Creature != Owner) return;
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int block)) return;

        if (block <= 0) return;
        
        await CreatureCmd.GainBlock(Owner, block, BlockProps.nonCardUnpowered, null);
    }
}
