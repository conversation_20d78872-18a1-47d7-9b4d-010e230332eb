using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FlameBarrierPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override async Task AfterDamageReceived(Creature target, DamageResult _, ValueProp props, Creature? dealer, CardModel? __)
    {
        if (target != Owner) return;
        if (dealer == null) return; // We need a damage dealer here so we have someone to deal damage back to.
        if (!props.IsPoweredAttack()) return;

        // TODO: Animations
        // yield return new AttackVfxAction(ActionTarget.Target, AttackVfxAction.fireImpactVfx).Execute(context);

        await CreatureCmd.Damage(dealer, Amount, DamageProps.nonCardUnpowered, Owner, null);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        // Wait until the opponent's turn ends so this has a chance to trigger from their attacks.
        if (Owner.Side == side) return;

        await PowerCmd.Remove(this);
    }
}