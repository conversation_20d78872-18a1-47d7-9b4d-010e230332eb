using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MonologuePower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        /// <summary>
        /// Keep track of the cards we've seen played and the power amount at the time they were played.
        /// This lets Monologue avoid triggering on cards that started play before it was applied, and avoid gaining
        /// extra block on multiple plays of Monologue.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    public override Task BeforeCardPlayed(CardModel card, Creature? _, int playCount)
    {
        if (card.Owner.Creature != Owner) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? _, int playCount)
    {
        if (card.Owner != Owner.Player) return;

        // Skip if the start of the card play wasn't seen.
        // This can happen if the card was played before MonologuePower was applied (happens on Monologue itself, or
        // if Monologue was auto-played by another card).
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int strength)) return;

        await PowerCmd.Apply<Strength>(Owner, strength, Owner, null);
        await PowerCmd.Apply<StrengthDown>(Owner, strength, Owner, null);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        await PowerCmd.Remove(this);
    }
}
