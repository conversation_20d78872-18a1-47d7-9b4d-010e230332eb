using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Plating : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;
    public override bool ShouldScaleInMultiplayer => true;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    /// <summary>
    /// We want enemies that start with Plating to also start combat with block.
    /// </summary>
    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (Owner.IsPlayer) return Task.CompletedTask;

        // Relics that do combat operations (like gaining block) at the start of combat need to be implemented in
        // AfterTurnStart on round 1.
        if (Owner.CombatState!.RoundNumber != 1) return Task.CompletedTask;

        return CreatureCmd.GainBlock(Owner, Amount, BlockProps.nonCardUnpowered, null);
    }

    /// <summary>
    /// We do this in early so that it triggers before end-of-turn damage effects.
    /// </summary>
    public override async Task BeforeTurnEndEarly(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        Flash();
        await CreatureCmd.GainBlock(Owner, Amount, BlockProps.nonCardUnpowered, null);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        // Plating always ticks down at the end of the *enemy* turn, regardless of which side has it.
        // This resolves timing issues where relics that give Plating would last for one less turn than
        // intended.
        if (side != CombatSide.Enemy) return;

        await PowerCmd.Decrement(this);
    }
}
