using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Confused : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Single;

    private int _testEnergyCostOverride = -1;

    public int TestEnergyCostOverride
    {
        private get => _testEnergyCostOverride;
        set
        {
            TestMode.AssertOn();
            AssertMutable();

            _testEnergyCostOverride = value;
        }
    }

    public override Task AfterCardDrawn(PlayerChoiceContext choiceContext, CardModel card, bool fromHandDraw)
    {
        if (card.Owner != Owner.Player) return Task.CompletedTask;

        // If base cost < 0, it was probably initially unplayable (like a Curse).
        if (card.BaseEnergyCost < 0) return Task.CompletedTask;

        int randomCost = NextEnergyCost();
        card.SetEnergyCostThisCombat(randomCost);
        return Task.CompletedTask;
    }

    private int NextEnergyCost()
    {
        if (TestEnergyCostOverride >= 0) return TestEnergyCostOverride;

        return Owner.Player!.ClimbState.Rng.CombatEnergyCosts.NextInt(4);
    }
}
