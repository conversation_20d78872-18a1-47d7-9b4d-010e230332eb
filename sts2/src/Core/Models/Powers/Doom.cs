using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Animation;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Doom : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    // Doom uses the normal Amount label color instead of the debuff, since it has its own special visual marker on the
    // health bar.
    public override Color AmountLabelColor => _normalAmountLabelColor;

    public override async Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        // Only check for Doom at the end of the creature's turn
        if (side != Owner.Side) return;

        // Doom doesn't kill the creature if they have more HP than Doom (or if they have 0 HP).
        if (Owner.CurrentHp <= 0 || Owner.CurrentHp > Amount) return;

        // the immortal soul relic prevents doom from killing the player
        if (Owner.Player?.GetRelic<ImmortalSoul>() != null) return;

        // Cache combat state in case we lose it when the owner dies.
        CombatState combatState = CombatState;

        await PlayVfx(Owner);
        await CreatureCmd.Kill(Owner);
        await HookBus.Instance.AfterDiedToDoom(Owner, combatState);
    }

    public static async Task PlayVfx(Creature creature)
    {
        NCreature? creatureNode = NCombatRoom.Instance?.GetCreatureNode(creature);
        if (creatureNode == null) return;

        // Don't destroy the creature node if the creature's death should be prevented.
        bool shouldDisappearFromDoom = false;

        if (creature.IsMonster)
        {
            shouldDisappearFromDoom = HookBus.Instance.ShouldDie(creature, out _);
            shouldDisappearFromDoom = shouldDisappearFromDoom && creature.Monster!.ShouldDisappearFromDoom;
        }

        StartDoomAnim(creatureNode, shouldDisappearFromDoom);

        NDoomOverlayVfx? overlay = NDoomOverlayVfx.GetOrCreate();
        if (overlay != null && !overlay.IsInsideTree())
        {
            NCombatRoom.Instance!.CombatVfxContainer.AddChildSafely(overlay);
        }

        IEnumerable<Creature> livingTeammates = creature.CombatState!.GetTeammatesOf(creature).Where(c => c.IsAlive);

        if (shouldDisappearFromDoom)
        {
            if (livingTeammates.Count() == 1 && livingTeammates.First() == creature)
            {
                await Cmd.Wait(1.5f);
            }
            else
            {
                // If we have teammates, we don't want to have to wait for the whole animation to finish.
                await Cmd.Wait(0.25f);
            }
        }
    }

    private static void StartDoomAnim(NCreature creature, bool shouldDie)
    {
        Task? animDisableTask = null;

        if (shouldDie)
        {
            Tween animDisableUiTween = creature.AnimDisableUi();
            animDisableUiTween.TweenCallback(Callable.From(creature.QueueFreeSafely));
            animDisableTask = WaitForTween(animDisableUiTween);

            if (creature.HasSpineAnimation)
            {
                // This freezes the enemy in the middle of its hurt animation as it gets dragged
                // down by the portal.
                creature.SetAnimationTrigger(SpineAnimator.hitTrigger);

                // only do the freeze frame if it actually transitions to the hurt frame
                if (creature.SpineController!.GetAnimationState().GetCurrent(0).GetAnimation().GetName() == AnimState.hurtAnim)
                {
                    SpineTrackEntry spineTrackEntry = creature.SpineController!.GetAnimationState().GetCurrent(0);
                    spineTrackEntry.SetTrackTime(0.1f);
                    spineTrackEntry.SetTimeScale(0);
                }
            }

            // Manually remove the creature node from the combat room node so other effects that operate on creature
            // nodes don't see it.
            NCombatRoom.Instance?.RemoveCreatureNode(creature);
        }

        NDoomVfx? vfx = NDoomVfx.Create(
            creature.Visuals,
            creature.Hitbox.GlobalPosition,
            creature.Hitbox.Size,
            shouldDie
        );

        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(vfx);

        if (shouldDie)
        {
            creature.DeathAnimationTask = Task.WhenAll(animDisableTask!, vfx!.VfxTask!);
        }
    }

    private static async Task WaitForTween(Tween t)
    {
        await t.ToSignal(t, Tween.SignalName.Finished);
    }
}
