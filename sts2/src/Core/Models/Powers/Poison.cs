using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Poison : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    // Poison uses the normal Amount label color instead of the debuff, since it has its own special visual marker on
    // the health bar.
    public override Color AmountLabelColor => _normalAmountLabelColor;

    // The Accelerant power makes Poison trigger extra times, but it should never trigger more times than the amount
    // of Poison we have.
    // Since Accelerant is applied to players, enemy creatures need to look to them (their opponents) to figure out the trigger count.
    private int TriggerCount
    {
        get
        {
            IEnumerable<Creature> livingAllies = Owner.CombatState!.GetOpponentsOf(Owner).Where(c => c.IsAlive);
            return Math.Min(Amount, 1 + livingAllies.Sum(ally => ally.GetPowerAmount<AccelerantPower>()));
        }
    }

    // The Bubble Bubble power makes it so Poison doesn't decrement.
    // Since Bubble Bubble is applied to players, enemy creatures need to check them (their opponents) to figure out if they should decrement.
    private bool ShouldDecrementAtEndOfTurn => !Owner.CombatState!.GetOpponentsOf(Owner).Any(c => c.IsAlive && c.HasPower<BubbleBubblePower>());

    public int CalculateTotalDamageNextTurn()
    {
        decimal totalDamage = 0;
        int iterations = Math.Min(Amount, TriggerCount);

        // Note: We originally used the Triangular Number formula (https://en.wikipedia.org/wiki/Triangular_number)
        // as an optimization, but since each trigger needs to run the ModifyDamageReceived hook, we have to loop.
        for (int i = 0; i < iterations; i++)
        {
            decimal damage = Amount;

            // Example: if the enemy has 5 Poison and the player has 2 Accelerant, the damage will be 5, 4, and 3.
            if (ShouldDecrementAtEndOfTurn)
            {
                damage -= i;
            }

            damage = ApplyTrackingPowerModification(damage);

            // Give the creature a chance to modify the damage with powers like Intangible.
            // Example: if the enemy has Intangible, the above damage will be modified to 1, 1, and 1.
            damage = HookBus.Instance.ModifyDamageAmount(Owner, null, damage, DamageProps.nonCardHpLoss, null, false, out _);

            totalDamage += damage;
        }

        return (int)totalDamage;
    }

    public override async Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        int iterations = TriggerCount;

        for (int i = 0; i < iterations; i++)
        {
            decimal damage = ApplyTrackingPowerModification(Amount);
            await CreatureCmd.Damage(Owner, damage, DamageProps.nonCardHpLoss, null, null);

            if (Owner.IsAlive && ShouldDecrementAtEndOfTurn)
            {
                await PowerCmd.Decrement(this);
            }
        }
    }

    private decimal ApplyTrackingPowerModification(decimal damage)
    {
        IEnumerable<Creature> livingAllies = Owner.CombatState!.GetOpponentsOf(Owner).Where(c => c.IsAlive);
        foreach (Creature livingAlly in livingAllies)
        {
            if (livingAlly.HasPower<TrackingPower>())
            {
                damage = livingAlly.GetPower<TrackingPower>()!.ModifyPoisonDamage(Owner, damage);
            }
        }

        return damage;
    }
}
