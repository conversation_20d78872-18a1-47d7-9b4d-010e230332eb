using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TrackingPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Weak>(), HoverTipFactory.FromPower<Poison>()];

    public override decimal ModifyDamageGivenLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource)
    {
        if (!props.IsPoweredAttack()) return amount;
        if (cardSource == null) return amount;
        if (dealer != Owner && !Owner.Pets.Contains(dealer)) return amount;
        if (target == null || !target.HasPower<Weak>()) return amount;

        return amount + amount * Amount / 100m;
    }

    public decimal ModifyPoisonDamage(Creature target, decimal originalAmount)
    {
        if (!target.HasPower<Weak>()) return originalAmount;
        return originalAmount + originalAmount * Amount / 100m;
    }
}
