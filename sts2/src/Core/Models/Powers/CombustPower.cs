using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CombustPower : PowerModel
{
    private const string _selfDamageKey = "SelfDamage";

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        // This is cardHpLoss because it's procced by the combust card, and we want it to trigger Rupture.
        new DamageVar(_selfDamageKey, 0, DamageProps.cardHpLoss)
    ];

    public override async Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return;

        NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NFireSmokePuffVfx.Create(Owner));
        await Cmd.CustomScaledWait(0.2f, 0.4f);

        DamageVar selfDamageVar = (DamageVar)DynamicVars[_selfDamageKey];
        await CreatureCmd.Damage(Owner, selfDamageVar.BaseValue, selfDamageVar.Props, Owner, null);

        foreach (Creature creature in CombatState.HittableEnemies)
        {
            VfxCmd.PlayOnCreatureCenter(creature, VfxCmd.bluntPath);
        }

        await CreatureCmd.Damage(CombatState.HittableEnemies, Amount, DamageProps.nonCardUnpowered, Owner, null);

        // As Combust occurs before the enemy turn, wait for hurt animations, so they
        // don't overlap with attack animations.
        await Cmd.CustomScaledWait(0.2f, 0.3f);
    }

    public void IncrementSelfDamage()
    {
        AssertMutable();
        DynamicVars[_selfDamageKey].BaseValue++;
    }
}
