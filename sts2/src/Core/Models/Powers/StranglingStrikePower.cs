using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class StranglingStrikePower : PowerModel
{
    public override PowerType Type => PowerType.Debuff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        /// <summary>
        /// Keep track of the cards we've seen played and the power amount at the time they were played.
        /// This lets Strangling Strike avoid triggering on itself, especially when stacking.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    public override Task BeforeCardPlayed(CardModel card, Creature? _, int playCount)
    {
        if (card.Type != CardType.Attack) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Type != CardType.Attack) return;

        // Skip if the start of the card play wasn't seen.
        // This lets Strangling Strike avoid triggering on itself.
        // Also, if two Strangling Strikes are played in the same turn, the second one will use the amount from when
        // only 1 had been played.
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int amountWhenPlayed)) return;

        Flash();
        await CreatureCmd.Damage(Owner, amountWhenPlayed, DamageProps.nonCardHpLoss, null, null);
    }

    public override async Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        await PowerCmd.Remove(this);
    }
}
