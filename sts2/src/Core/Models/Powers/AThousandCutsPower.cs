using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class AThousandCutsPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        /// <summary>
        /// Keep track of the cards we've seen played and the power amount at the time they were played.
        /// This lets A Thousand Cuts avoid triggering on cards that started play before it was applied, and avoid
        /// dealing extra damage on multiple plays of A Thousand Cuts.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    public override Task BeforeCardPlayed(CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner.Player) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner.Player) return;

        // Skip if the start of the card play wasn't seen.
        // This can happen if the card was played before AThousandCutsPower was applied (happens on A Thousand Cuts
        // itself, or if A Thousand Cuts was auto-played by another card).
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int damage)) return;

        if (damage <= 0) return;

        await Cmd.CustomScaledWait(0.1f, 0.2f);

        foreach (Creature c in CombatState.HittableEnemies)
        {
            NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(NThinSliceVfx.Create(c, VfxColor.White));
        }

        await CreatureCmd.Damage(CombatState.HittableEnemies, damage, DamageProps.nonCardUnpowered, Owner, null);
    }
}
