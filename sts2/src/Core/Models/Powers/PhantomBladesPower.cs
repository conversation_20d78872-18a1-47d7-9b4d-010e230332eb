using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PhantomBladesPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.FromCard<Shiv>(),
        HoverTipFactory.FromKeyword(CardKeyword.Retain)
    ];

    public override Task AfterCardEnteredCombat(CardModel card)
    {
        if (card is not Shiv) return Task.CompletedTask;
        if (card.Owner != Owner.Player) return Task.CompletedTask;

        Flash();
        CardCmd.ApplyKeyword(card, CardKeyword.Retain);
        return Task.CompletedTask;
    }

    public override Task AfterApplied()
    {
        foreach (CardModel card in Owner.Player!.PlayerCombatState!.AllCards.Where(c => c is Shiv))
        {
            CardCmd.ApplyKeyword(card, CardKeyword.Retain);
        }
        return Task.CompletedTask;
    }

    public override decimal ModifyDamageGivenLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource)
    {
        if (!props.IsPoweredAttack()) return amount;
        if (cardSource is not Shiv) return amount;
        if (dealer != Owner) return amount;

        int shivsPlayed = CombatManager.Instance.History.CardPlaysFinished.Count(e =>
            e.Card is Shiv &&
            e.HappenedThisTurn(CombatState) &&
            e.Card.Owner.Creature == Owner
        );
        if (shivsPlayed == 1) return amount;

        return amount * Amount;
    }
}
