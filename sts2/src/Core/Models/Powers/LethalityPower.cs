using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LethalityPower : PowerModel
{
    private const string _damageIncrease = "DamageIncrease";

    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private int _timesTriggeredThisTurn;

    private int TimesTriggeredThisTurn
    {
        get => _timesTriggeredThisTurn;
        set
        {
            AssertMutable();
            _timesTriggeredThisTurn = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_damageIncrease, 1.5m)
    ];

    public override decimal ModifyDamageGivenLate(Creature? dealer, decimal amount, ValueProp props, Creature? target, CardModel? cardSource)
    {
        if (TimesTriggeredThisTurn >= Amount) return amount;

        // This power applies to both your and your pet's attacks, but no one else's.
        if (dealer != Owner && !Owner.Pets.Contains(dealer)) return amount;
        if (!props.IsPoweredAttack()) return amount;
        if (cardSource == null) return amount;

        return amount * DynamicVars[_damageIncrease].BaseValue;
    }

    public override Task AfterModifyingDamageAmount()
    {
        TimesTriggeredThisTurn++;
        return Task.CompletedTask;
    }

    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        TimesTriggeredThisTurn = 0;
        return Task.CompletedTask;
    }
}
