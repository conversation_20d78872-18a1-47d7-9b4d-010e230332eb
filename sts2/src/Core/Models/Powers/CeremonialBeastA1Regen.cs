using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CeremonialBeastA1Regen : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Single;
    public override bool IsVisible => false;

    public override async Task AfterDamageReceived(Creature target, DamageResult _, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return;
        if (target.CurrentHp > target.MaxHp * 0.66f) return;
        if (!AscensionHelper.HasAscension(AscensionLevel.MightyBosses)) return;

        await PowerCmd.Apply<Regen>(target, 9, null, null);
        await PowerCmd.Remove(this);
    }
}
