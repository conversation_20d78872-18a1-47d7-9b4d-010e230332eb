using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class NostalgiaPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private int _timesTriggeredThisTurn;

    private int TimesTriggeredThisTurn
    {
        get => _timesTriggeredThisTurn;
        set
        {
            AssertMutable();
            _timesTriggeredThisTurn = value;
        }
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (TimesTriggeredThisTurn >= Amount) return;
        if (card.IsDupe) return;
        if (card.Owner.Creature != Owner) return;
        if (card.Type is not (CardType.Attack or CardType.Skill)) return;

        // We still want Exhaust cards to Exhaust.
        if (card.Keywords.Contains(CardKeyword.Exhaust) || card.ExhaustOnNextPlay) return;

        Flash();
        await CardPileCmd.Add(card, CardPileTarget.Draw, CardPilePosition.Top);
        TimesTriggeredThisTurn++;
    }

    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        TimesTriggeredThisTurn = 0;
        return Task.CompletedTask;
    }
}
