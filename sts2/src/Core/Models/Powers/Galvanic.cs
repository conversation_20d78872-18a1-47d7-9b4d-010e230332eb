using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Afflictions;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Galvanic : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => HoverTipFactory.FromAffliction<Galvanized>(Amount);

    public override async Task AfterApplied()
    {
        foreach (Creature allies in Owner.CombatState!.Allies.ToList())
        {
            IEnumerable<CardModel> powers = allies.Player!.PlayerCombatState!.AllCards.Where(
                c => ModelDb.Affliction<Galvanized>().CanAfflict(c)
            );

            foreach (CardModel power in powers)
            {
                await CardCmd.Afflict<Galvanized>(power, Amount);
            }
        }
    }

    public override async Task AfterCardEnteredCombat(CardModel card)
    {
        if (card.Affliction != null) return;
        if (!ModelDb.Affliction<Galvanized>().CanAfflict(card)) return;

        await CardCmd.Afflict<Galvanized>(card, Amount);
    }
}
