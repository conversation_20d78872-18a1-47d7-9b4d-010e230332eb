using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Orbs;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class StormPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    private class Data
    {
        /// <summary>
        /// Keep track of the Power cards we've seen played and the power amount at the time they were played.
        /// This lets Storm avoid triggering on cards that started play before it was applied, and avoid channeling
        /// extra lightning on multiple plays of Storm.
        /// </summary>
        public readonly Dictionary<CardModel, int> amountsForPlayedCards = [];
    }

    protected override object InitInternalData() => new Data();

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.Static(StaticHoverTip.Channeling),
        HoverTipFactory.FromOrb<LightningOrb>()
    ];

    public override Task BeforeCardPlayed(CardModel card, Creature? _, int playCount)
    {
        if (card.Owner != Owner.Player) return Task.CompletedTask;
        if (card.Type != CardType.Power) return Task.CompletedTask;

        GetInternalData<Data>().amountsForPlayedCards.Add(card, Amount);
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner.Player) return;

        // Skip if the start of the card play wasn't seen.
        // This can happen if the card was played before StormPower was applied (happens on Storm itself, or if Storm
        // was auto-played by another card).
        if (!GetInternalData<Data>().amountsForPlayedCards.Remove(card, out int lightning)) return;

        if (lightning <= 0) return;

        Flash();

        for (int i = 0; i < lightning; i++)
        {
            await OrbCmd.Channel<LightningOrb>(context, Owner.Player);
        }
    }
}
