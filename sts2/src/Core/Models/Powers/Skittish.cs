using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Skittish : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override bool ShouldScaleInMultiplayer => true;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    private class Data
    {
        public CardModel? playedCard;
        public bool gainedBlockThisTurn;
    }

    protected override object InitInternalData() => new Data();

    public override Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (target != Owner) return Task.CompletedTask;
        if (!props.IsPoweredAttack()) return Task.CompletedTask;
        if (cardSource == null) return Task.CompletedTask;
        if (result.UnblockedDamage == 0) return Task.CompletedTask;
        if (GetInternalData<Data>().playedCard != null && cardSource != GetInternalData<Data>().playedCard) return Task.CompletedTask;
        if (GetInternalData<Data>().gainedBlockThisTurn) return Task.CompletedTask;

        GetInternalData<Data>().playedCard = cardSource;
        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card != GetInternalData<Data>().playedCard) return;
        GetInternalData<Data>().playedCard = null;
        GetInternalData<Data>().gainedBlockThisTurn = true;

        await CreatureCmd.GainBlock(Owner, Amount, BlockProps.nonCardUnpowered, null);

        NCombatRoom.Instance?.GetCreatureNode(Owner)?.SetDefaultScaleTo(0.25f, 0.75f);
    }

    public override Task AfterTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Side) return Task.CompletedTask;

        GetInternalData<Data>().gainedBlockThisTurn = false;
        return Task.CompletedTask;
    }
}
