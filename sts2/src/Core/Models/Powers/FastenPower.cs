using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class FastenPower : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    public override bool TryModifyBlockReceived(Creature target, decimal block, ValueProp props, CardModel? cardSource, out decimal modifiedBlock)
    {
        modifiedBlock = block;

        if (Owner != target) return false;
        if (!props.IsPoweredCardOrMonsterMoveBlock()) return false;
        if (!cardSource?.Tags.Contains(CardTag.Defend) ?? false) return false;

        modifiedBlock += Amount;
        return true;
    }

    public override Task AfterModifyingBlockAmount(decimal modifiedBlock)
    {
        Flash();
        return Task.CompletedTask;
    }
}
