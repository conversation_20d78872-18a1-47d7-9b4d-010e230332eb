using System;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Powers;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Powers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Leadership : PowerModel
{
    public override PowerType Type => PowerType.Buff;
    public override PowerStackType StackType => PowerStackType.Counter;

    public override decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? _, CardModel? __)
    {
        if (Owner == dealer) return amount;
        if (Owner.Side != dealer!.Side) return amount;
        if (!props.IsPoweredAttack()) return amount;

        return Math.Max(amount + Amount, 0);
    }
}
