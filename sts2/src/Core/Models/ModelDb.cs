using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Enchantments;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.Acts;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Models.Exceptions;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Models.Orbs;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Models;

public static class ModelDb
{
    // Updated 10/9/2023.
    //
    // This is the initial capacity of the content dictionary.
    //
    // 4096 is about 4x the number of content items we had when this was written, and should take up about 64kb in
    // memory, so it feels like a worthwhile tradeoff.
    //
    // If you see this message much later than the updated date, please double-check the amount of content we have
    // and update this capacity (and comment) accordingly.
    private const int _initialCapacity = 4096;

    private static readonly Dictionary<ModelId, AbstractModel> _contentById = new(_initialCapacity);

    /// <summary>
    /// Initializes the ModelDb.
    /// Note that the alternative is to initialize in a static initializer, but if we do this we don't control the time
    /// at which it gets initialized, and it usually happens at a bad time during gameplay.
    /// </summary>
    public static void Init()
    {
        foreach (Type modelType in ReflectionHelper.GetSubtypes<AbstractModel>())
        {
            ModelId id = GetId(modelType);

            // Some models can create other models through GetOrCreate (e.g. encounters need monster models)
            if (Contains(modelType)) continue;

            AbstractModel model = (AbstractModel)Activator.CreateInstance(modelType)!;
            _contentById[id] = model;
        }
    }

    /// <summary>
    /// Assigns IDs to all canonical models in the model database.
    /// This must happen after Init (i.e. the AbstractModel constructors); otherwise, there is a circular dependency
    /// between the static constructor and the ModelIdSerializationCache.
    /// </summary>
    public static void InitIds()
    {
        foreach (KeyValuePair<ModelId, AbstractModel> pair in _contentById)
        {
            pair.Value.InitId(pair.Key);
        }
    }

    public static ModelId GetId<T>() where T : AbstractModel => GetId(typeof(T));

    public static ModelId GetId(Type type)
    {
        return new ModelId(GetCategory(type), GetEntry(type));
    }

    // The category type of a model is the Type which inherits directly from AbstractModel.
    public static Type GetCategoryType(Type type)
    {
        Type categoryType = type;
        while (categoryType.BaseType! != typeof(AbstractModel))
        {
            categoryType = categoryType.BaseType!;
        }

        return categoryType;
    }

    public static string GetCategory(Type type)
    {
        return ModelId.SlugifyCategory(GetCategoryType(type).Name);
    }

    public static string GetEntry(Type type)
    {
        return StringHelper.Slugify(type.Name);
    }

    public static T? GetByIdOrNull<T>(ModelId id) where T : AbstractModel
    {
        if (_contentById.TryGetValue(id, out AbstractModel? model))
        {
            return (T)model;
        }

        foreach (Type subtype in ReflectionHelper.GetSubtypes<T>())
        {
            AbstractModel instance = DeserializeGetOrCreate(subtype);

            if (instance.Id == id)
            {
                return (T)instance;
            }
        }

        return null;
    }

    public static T GetById<T>(ModelId id) where T : AbstractModel
    {
        T? model = GetByIdOrNull<T>(id);
        return model ?? throw new ModelNotFoundException(id);
    }

    public static bool Contains(Type type)
    {
        return _contentById.ContainsKey(GetId(type));
    }

    private static T GetOrCreate<T>() where T : AbstractModel
    {
        ModelId id = GetId<T>();

        if (!_contentById.ContainsKey(id))
        {
            _contentById[id] = Activator.CreateInstance<T>();
        }

        return (T)_contentById[id];
    }

    private static AbstractModel GetOrCreate(Type type)
    {
        if (!type.IsSubclassOf(typeof(AbstractModel)))
        {
            throw new InvalidOperationException();
        }

        ModelId id = GetId(type);

        if (_contentById.TryGetValue(id, out AbstractModel? model)) return model;

        model = (AbstractModel)Activator.CreateInstance(type)!;
        _contentById[id] = model;

        return model;
    }

    /// <summary>
    /// Used only for our serialization and debug tools.
    /// </summary>
    /// <param name="type">Type to get a deserialized version of</param>
    /// <returns>Deserialized AbstractContent.</returns>
    private static AbstractModel DeserializeGetOrCreate(Type type)
    {
        ModelId id = GetId(type);

        if (!_contentById.TryGetValue(id, out AbstractModel? model))
        {
            if (Activator.CreateInstance(type) is not AbstractModel abstractModel)
            {
                throw new ModelNotFoundException(id);
            }

            model = abstractModel;
            _contentById[id] = model;
        }

        return model;
    }

    #region Afflictions

    public static T Affliction<T>() where T : AfflictionModel => GetOrCreate<T>();

    public static IEnumerable<AfflictionModel> DebugAfflictions => ReflectionHelper.GetSubtypes<AfflictionModel>().Select(t => (AfflictionModel)GetOrCreate(t));

    #endregion

    #region Enchantments

    public static T Enchantment<T>() where T : EnchantmentModel => GetOrCreate<T>();

    public static IEnumerable<EnchantmentModel> DebugEnchantments => ReflectionHelper.GetSubtypes<EnchantmentModel>().Select(t => (EnchantmentModel)GetOrCreate(t));

    /// <summary>
    /// A "safe" list of enchantments to pick random enchantments and amounts from.
    /// </summary>
    public static EnchantmentOption[] SharedEnchantments =>
    [
        new(Enchantment<Immune>(), 1, 1),
        new(Enchantment<Nimble>(), 1, 3),
        new(Enchantment<Rigid>(), 5, 5),
        new(Enchantment<Sharp>(), 1, 3),
    ];

    #endregion

    #region Cards

    public static T Card<T>() where T : CardModel => GetOrCreate<T>();

    public static IEnumerable<CardModel> Cards
    {
        get
        {
            return CardPools
                .SelectMany(p => p.Cards)
                .Concat(StartingDeckCards)
                .Distinct();
        }
    }

    public static IEnumerable<CardModel> StartingDeckCards => Characters.SelectMany(c => c.StartingDeck).Distinct();

    #endregion

    #region Card Pools

    public static T CardPool<T>() where T : CardPoolModel => GetOrCreate<T>();

    public static IEnumerable<CardPoolModel> CardPools => CharacterCardPools.Concat(SharedCardPools).Distinct();

    /// <summary>
    /// The card pools that are shared across all characters.
    /// WARNING: Do NOT add TestCardPool to this list, or test cards might accidentally appear in-game.
    /// </summary>
    public static IEnumerable<CardPoolModel> SharedCardPools =>
    [
        CardPool<ColorlessCardPool>(),
        CardPool<CurseCardPool>(),
        CardPool<DeprecatedCardPool>(),
        CardPool<EventCardPool>(),
        CardPool<QuestCardPool>(),
        CardPool<StatusCardPool>(),
        CardPool<MultiplayerCardPool>(),
        CardPool<TokenCardPool>()
    ];

    public static IEnumerable<CardPoolModel> CharacterCardPools => Characters.Select(c => c.CardPool);

    public static IEnumerable<CardPoolModel> UnlockedCharacterCardPools => UnlockedCharacters.Select(c => c.CardPool);

    #endregion

    #region Characters

    public static IEnumerable<CharacterModel> Characters =>
    [
        Character<Ironclad>(),
        Character<Silent>(),
        Character<Regent>(),
        Character<Necrobinder>(),
        Character<Defect>()
    ];

    public static IEnumerable<CharacterModel> UnlockedCharacters => SaveManager.Instance.GetUnlockedCharacters();

    public static T Character<T>() where T : CharacterModel => GetOrCreate<T>();

    #endregion

    #region Events

    public static T Event<T>() where T : EventModel => GetOrCreate<T>();

    public static IEnumerable<EventModel> SharedEvents =>
    [
        Event<CursedChoice>(),
        Event<DollRoom>(),
        Event<SelfHelpBook>(),
        Event<PotionCourier>(),
        Event<RanwidTheElder>(),
        Event<RelicTrader>(),
        Event<RoomFullOfCheese>(),
        Event<SlipperyBridge>(),
        Event<Symbiote>(),
        Event<TeaMaster>(),
        Event<TheFutureOfPotions>(),
        Event<TheLegendsWereTrue>(),
        Event<TrashHeap>(),
        Event<WelcomeToWongos>(),
        Event<WarHistorianRepy>()
    ];

    public static T AncientEvent<T>() where T : AncientEventModel => GetOrCreate<T>();

    public static IEnumerable<AncientEventModel> AncientEvents()
    {
        foreach (AncientEventModel ancient in SharedAncientEvents)
        {
            yield return ancient;
        }

        foreach (IEnumerable<AncientEventModel> ancients in Acts.Select(act => act.UniqueAncients))
        {
            foreach (AncientEventModel ancient in ancients)
            {
                yield return ancient;
            }
        }
    }

    public static IEnumerable<AncientEventModel> SharedAncientEvents =>
    [
        AncientEvent<Darv>()
    ];

    public static IEnumerable<EventModel> Events => Acts.SelectMany(act => act.Events).Concat(SharedEvents).Distinct();

    #endregion

    #region Monsters

    public static T Monster<T>() where T : MonsterModel => GetOrCreate<T>();

    /// <summary>
    /// Returns a list of every possible Monster in the game.
    /// </summary>
    public static IEnumerable<MonsterModel> Monsters => Acts.SelectMany(act => act.AllMonsters).Distinct();

    #endregion

    #region Encounters

    public static T Encounter<T>() where T : EncounterModel => GetOrCreate<T>();

    public static IEnumerable<EncounterModel> Encounters => Acts.SelectMany(act => act.Encounters).Distinct();

    #endregion

    #region Potions

    public static T Potion<T>() where T : PotionModel => GetOrCreate<T>();

    /// <summary>
    /// Get all the potions in the game.
    /// Be careful using this, it includes potions that you shouldn't be able to randomly roll for rewards.
    /// </summary>
    public static IEnumerable<PotionModel> AllPotions => SharedPotionPools
        .SelectMany(p => p.Potions)
        .Concat(Characters.SelectMany(character => character.PotionPool.Potions))
        .Distinct();

    #endregion

    #region Potion Pools

    public static T PotionPool<T>() where T : PotionPoolModel
    {
        return GetOrCreate<T>();
    }

    public static IEnumerable<PotionPoolModel> PotionPools => CharacterPotionPools.Concat(SharedPotionPools).Distinct();

    public static IEnumerable<PotionPoolModel> CharacterPotionPools => Characters.Select(c => c.PotionPool);

    /// <summary>
    /// Get the potion pools that are shared between all characters.
    /// </summary>
    private static IEnumerable<PotionPoolModel> SharedPotionPools =>
    [
        PotionPool<DeprecatedPotionPool>(),
        PotionPool<EventPotionPool>(),
        PotionPool<SharedPotionPool>(),
        PotionPool<TokenPotionPool>()
    ];

    #endregion

    #region Powers

    public static T Power<T>() where T : PowerModel => GetOrCreate<T>();

    public static IEnumerable<PowerModel> DebugPowers => ReflectionHelper.GetSubtypes<PowerModel>().Select(t => (PowerModel)GetOrCreate(t));

    public static PowerModel DebugPower(Type type) => (PowerModel)DeserializeGetOrCreate(type);

    #endregion

    #region Relics

    public static T Relic<T>() where T : RelicModel => GetOrCreate<T>();

    public static IEnumerable<RelicModel> Relics
    {
        get
        {
            CharacterModel[] characters = Characters.ToArray();

            return RelicPool<SharedRelicPool>().Relics
                .Concat(RelicPool<EventRelicPool>().Relics)
                .Concat(RelicPool<FallbackRelicPool>().Relics)
                .Concat(characters.SelectMany(character => character.RelicPool.Relics))
                .Concat(characters.SelectMany(character => character.StartingRelics))
                .Distinct()
                .OrderBy(r => r.Id.Entry);
        }
    }

    #endregion

    #region Relic Pools

    public static T RelicPool<T>() where T : RelicPoolModel
    {
        return GetOrCreate<T>();
    }

    public static IEnumerable<RelicPoolModel> RelicPools => CharacterRelicPools.Concat(NonCharacterRelicPools).Distinct();

    private static IEnumerable<RelicPoolModel> NonCharacterRelicPools =>
    [
        RelicPool<DeprecatedRelicPool>(),
        RelicPool<EventRelicPool>(),
        RelicPool<FallbackRelicPool>(),
        RelicPool<SharedRelicPool>()
    ];

    public static IEnumerable<RelicPoolModel> CharacterRelicPools => Characters.Select(c => c.RelicPool);

    #endregion

    #region Orbs

    public static T Orb<T>() where T : OrbModel
    {
        return GetOrCreate<T>();
    }

    public static OrbModel? DebugOrb(Type type)
    {
        try
        {
            return (OrbModel)DeserializeGetOrCreate(type);
        }
        catch
        {
            return null;
        }
    }

    public static IEnumerable<OrbModel> Orbs =>
    [
        Orb<LightningOrb>(),
        Orb<FrostOrb>(),
        Orb<DarkOrb>(),
        Orb<PlasmaOrb>()
    ];

    #endregion

    #region Acts

    public static T Act<T>() where T : ActModel => GetOrCreate<T>();

    public static IEnumerable<ActModel> Acts =>
    [
        Act<Overgrowth>(),
        Act<Hive>(),
        Act<Glory>(),
        Act<Underdocks>(),
    ];

    #endregion

    #region Singleton

    public static T Singleton<T>() where T : SingletonModel => GetOrCreate<T>();

    #endregion

    #region Achievement

    public static T Achievement<T>() where T : AchievementModel => GetOrCreate<T>();

    private static List<AchievementModel>? _achievements;

    public static IEnumerable<AchievementModel> Achievements
    {
        get
        {
            if (_achievements == null)
            {
                _achievements = [];

                foreach (Type type in ReflectionHelper.GetSubtypes<AchievementModel>())
                {
                    _achievements.Add((AchievementModel)GetOrCreate(type));
                }
            }

            return _achievements;
        }
    }

    #endregion

    #region Modifier

    public static T Modifier<T>() where T : ModifierModel => GetOrCreate<T>();

    public static IReadOnlyList<ModifierModel> GoodModifiers =>
    [
        Modifier<Draft>(),
        Modifier<SealedDeck>(),
        Modifier<Hoarder>(),
        Modifier<Specialized>(),
        Modifier<Insanity>(),
        Modifier<AllStar>(),
        Modifier<Flight>(),
        Modifier<Vintage>(),
        Modifier<Prismatic>(),
        Modifier<CharacterCards>(),
    ];

    public static IReadOnlyList<ModifierModel> BadModifiers =>
    [
        Modifier<DeadlyEvents>(),
        Modifier<CursedRun>(),
        Modifier<BigGameHunter>(),
        Modifier<Midas>(),
        Modifier<Murderous>(),
        Modifier<NightTerrors>(),
        Modifier<Terminal>(),
    ];

    public static IReadOnlyList<IReadOnlySet<ModifierModel>> MutuallyExclusiveModifiers =>
    [
        new HashSet<ModifierModel> { Modifier<SealedDeck>(), Modifier<Draft>(), Modifier<Insanity>() },
        new HashSet<ModifierModel> { Modifier<Prismatic>(), Modifier<CharacterCards>() }
    ];

    #endregion
}
