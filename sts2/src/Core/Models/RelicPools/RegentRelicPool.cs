using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RegentRelicPool : RelicPoolModel
{
    public override string EnergyColorName => Regent.energyColorName;

    protected override RelicModel[] GenerateRelics() =>
    [
        ModelDb.Relic<FencingManual>(),
        ModelDb.Relic<MyMantle>(),
        ModelDb.Relic<RoyalPerfume>(),
        ModelDb.Relic<StarrianTurbine>(),
        ModelDb.Relic<Starry>(),
        ModelDb.Relic<VitruvianMinion>()
    ];
}
