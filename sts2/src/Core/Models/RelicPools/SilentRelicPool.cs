using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.RelicPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SilentRelicPool : RelicPoolModel
{
    public override string EnergyColorName => Silent.energyColorName;

    protected override RelicModel[] GenerateRelics() =>
    [
        ModelDb.Relic<NinjaScroll>(),
        ModelDb.Relic<PaperKrane>(),
        ModelDb.Relic<RingOfTheSnake>(),
        ModelDb.Relic<SneckoSkull>(),
        ModelDb.Relic<TwistedFunnel>()
    ];
}
