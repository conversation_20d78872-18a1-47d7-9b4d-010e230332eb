using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Potions;

namespace MegaCrit.Sts2.Core.Models.PotionPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeprecatedPotionPool : PotionPoolModel
{
    public override string EnergyColorName => ColorlessCardPool.energyColorName;

    protected override PotionModel[] GeneratePotions() =>
    [
        ModelDb.Potion<DeprecatedPotion>()
    ];
}
