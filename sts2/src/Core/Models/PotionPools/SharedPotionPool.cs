using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Potions;

namespace MegaCrit.Sts2.Core.Models.PotionPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SharedPotionPool : PotionPoolModel
{
    public override string EnergyColorName => ColorlessCardPool.energyColorName;

    protected override PotionModel[] GeneratePotions() =>
    [
        ModelDb.Potion<AttackPotion>(),
        ModelDb.Potion<BeetleJuice>(),
        ModelDb.Potion<BlessingOfTheForge>(),
        ModelDb.Potion<BlockPotion>(),
        ModelDb.Potion<Clarity>(),
        ModelDb.Potion<ColorlessPotion>(),
        ModelDb.Potion<CureAll>(),
        ModelDb.Potion<DexterityPotion>(),
        ModelDb.Potion<DistilledChaos>(),
        ModelDb.Potion<DropletOfPrecognition>(),
        ModelDb.Potion<Duplicator>(),
        ModelDb.Potion<EnergyPotion>(),
        ModelDb.Potion<EntropicBrew>(),
        ModelDb.Potion<ExplosiveAmpoule>(),
        ModelDb.Potion<FairyInABottle>(),
        ModelDb.Potion<FirePotion>(),
        ModelDb.Potion<FlexPotion>(),
        ModelDb.Potion<Fortifier>(),
        ModelDb.Potion<FruitJuice>(),
        ModelDb.Potion<FyshOil>(),
        ModelDb.Potion<GamblersBrew>(),
        ModelDb.Potion<HeartOfIron>(),
        ModelDb.Potion<LiquidBronze>(),
        ModelDb.Potion<LiquidMemories>(),
        ModelDb.Potion<LuckyTonic>(),
        ModelDb.Potion<MazalethsGift>(),
        ModelDb.Potion<PowderedDemise>(),
        ModelDb.Potion<PowerPotion>(),
        ModelDb.Potion<RadiantTincture>(),
        ModelDb.Potion<RegenPotion>(),
        ModelDb.Potion<ScreamerPod>(),
        ModelDb.Potion<ShipInABottle>(),
        ModelDb.Potion<SkillPotion>(),
        ModelDb.Potion<SneckoOil>(),
        ModelDb.Potion<SpeedPotion>(),
        ModelDb.Potion<StableSerum>(),
        ModelDb.Potion<StrengthPotion>(),
        ModelDb.Potion<SwiftPotion>(),
        ModelDb.Potion<TouchOfInsanity>(),
        ModelDb.Potion<VulnerablePotion>(),
        ModelDb.Potion<WeakPotion>()
    ];
}
