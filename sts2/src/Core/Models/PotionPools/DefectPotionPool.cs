using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Potions;

namespace MegaCrit.Sts2.Core.Models.PotionPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DefectPotionPool : PotionPoolModel
{
    public override string EnergyColorName => Defect.energyColorName;

    protected override PotionModel[] GeneratePotions() =>
    [
        ModelDb.Potion<EssenceOfDarkness>(),
        ModelDb.Potion<FocusPotion>(),
        ModelDb.Potion<PotionOfCapacity>()
    ];
}
