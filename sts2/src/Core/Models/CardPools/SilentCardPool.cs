using Godot;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SilentCardPool : CardPoolModel
{
    public override string Title => "silent";
    public override string EnergyColorName => Silent.energyColorName;
    public override string CardFrameMaterialPath => "card_frame_green";
    public override Color DeckEntryCardColor => new("5EBD00");
    public override Color EnergyOutlineColor => new("1A6625");
    public override bool IsColorless => false;

    protected override CardModel[] GenerateCards() =>
    [
        ModelDb.Card<AThousandCuts>(),
        ModelDb.Card<Accelerant>(),
        ModelDb.Card<Accuracy>(),
        ModelDb.Card<Acrobatics>(),
        ModelDb.Card<Adrenaline>(),
        ModelDb.Card<AfterImage>(),
        ModelDb.Card<Anticipate>(),
        ModelDb.Card<Assassinate>(),
        ModelDb.Card<Backflip>(),
        ModelDb.Card<Backstab>(),
        ModelDb.Card<BladeDance>(),
        ModelDb.Card<BladeOfInk>(),
        ModelDb.Card<Blur>(),
        ModelDb.Card<BouncingFlask>(),
        ModelDb.Card<BubbleBubble>(),
        ModelDb.Card<BulletTime>(),
        ModelDb.Card<Burst>(),
        ModelDb.Card<CalculatedGamble>(),
        ModelDb.Card<Catalyst>(),
        ModelDb.Card<Caltrops>(),
        ModelDb.Card<CloakAndDagger>(),
        ModelDb.Card<CorrosiveWave>(),
        ModelDb.Card<Cower>(),
        ModelDb.Card<DaggerSpray>(),
        ModelDb.Card<DaggerThrow>(),
        ModelDb.Card<Dash>(),
        ModelDb.Card<DeadlyPoison>(),
        ModelDb.Card<DefendSilent>(),
        ModelDb.Card<Deflect>(),
        ModelDb.Card<Distraction>(),
        ModelDb.Card<DodgeAndRoll>(),
        ModelDb.Card<Envenom>(),
        ModelDb.Card<EscapePlan>(),
        ModelDb.Card<Execution>(),
        ModelDb.Card<Expose>(),
        ModelDb.Card<FanOfKnives>(),
        ModelDb.Card<Finisher>(),
        ModelDb.Card<Flechettes>(),
        ModelDb.Card<FlickFlack>(),
        ModelDb.Card<FlyingKnee>(),
        ModelDb.Card<FollowThrough>(),
        ModelDb.Card<Footwork>(),
        ModelDb.Card<GlassKnife>(),
        ModelDb.Card<Graceful>(),
        ModelDb.Card<GrandFinale>(),
        ModelDb.Card<HandTrick>(),
        ModelDb.Card<Haze>(),
        ModelDb.Card<HiddenDaggers>(),
        ModelDb.Card<InfiniteBlades>(),
        ModelDb.Card<LeadingStrike>(),
        ModelDb.Card<LegSweep>(),
        ModelDb.Card<Malaise>(),
        ModelDb.Card<MasterPlanner>(),
        ModelDb.Card<MementoMori>(),
        ModelDb.Card<Mirage>(),
        ModelDb.Card<Neutralize>(),
        ModelDb.Card<Nightmare>(),
        ModelDb.Card<NoxiousFumes>(),
        ModelDb.Card<Outmaneuver>(),
        ModelDb.Card<PiercingWail>(),
        ModelDb.Card<PoisonedStab>(),
        ModelDb.Card<Pounce>(),
        ModelDb.Card<PreciseCut>(),
        ModelDb.Card<Predator>(),
        ModelDb.Card<Prepared>(),
        ModelDb.Card<Reflex>(),
        ModelDb.Card<Ricochet>(),
        ModelDb.Card<ShadowStep>(),
        ModelDb.Card<Shadowmeld>(),
        ModelDb.Card<SharpEdge>(),
        ModelDb.Card<Skewer>(),
        ModelDb.Card<Slice>(),
        ModelDb.Card<Snakebite>(),
        ModelDb.Card<SneakyStrike>(),
        ModelDb.Card<Speedster>(),
        ModelDb.Card<StormOfSteel>(),
        ModelDb.Card<StranglingStrike>(),
        ModelDb.Card<StrikeSilent>(),
        ModelDb.Card<SuckerPunch>(),
        ModelDb.Card<Suppress>(),
        ModelDb.Card<Survivor>(),
        ModelDb.Card<Tactician>(),
        ModelDb.Card<ToolsOfTheTrade>(),
        ModelDb.Card<Tracking>(),
        ModelDb.Card<UncannyDodge>(),
        ModelDb.Card<UpMySleeve>(),
        ModelDb.Card<WellLaidPlans>(),
        ModelDb.Card<WraithForm>(),
    ];
}
