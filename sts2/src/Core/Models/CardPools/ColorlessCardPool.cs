using Godot;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ColorlessCardPool : CardPoolModel
{
    public const string energyColorName = "colorless";

    public override string Title => "colorless";
    public override string EnergyColorName => energyColorName;
    public override string CardFrameMaterialPath => "card_frame_colorless";
    public override Color DeckEntryCardColor => new("A3A3A3FF");

    public override bool IsColorless => true;

    protected override CardModel[] GenerateCards() =>
    [

        ModelDb.Card<Alchemize>(),
        ModelDb.Card<Anointed>(),
        ModelDb.Card<Apotheosis>(),
        ModelDb.Card<Automation>(),
        ModelDb.Card<BeatDown>(),
        ModelDb.Card<Bolas>(),
        ModelDb.Card<Calamity>(),
        ModelDb.Card<Catastrophe>(),
        ModelDb.Card<Contractility>(),
        ModelDb.Card<DarkShackles>(),
        ModelDb.Card<Discovery>(),
        ModelDb.Card<DramaticEntrance>(),
        ModelDb.Card<Entropy>(),
        ModelDb.Card<EternalArmor>(),
        ModelDb.Card<Equilibrium>(),
        ModelDb.Card<Fasten>(),
        ModelDb.Card<Finesse>(),
        ModelDb.Card<Fisticuffs>(),
        ModelDb.Card<FlashOfSteel>(),
        ModelDb.Card<GoldAxe>(),
        ModelDb.Card<HandOfGreed>(),
        ModelDb.Card<HiddenGem>(),
        ModelDb.Card<Impatience>(),
        ModelDb.Card<JackOfAllTrades>(),
        ModelDb.Card<Jackpot>(),
        ModelDb.Card<MasterOfStrategy>(),
        ModelDb.Card<Mayhem>(),
        ModelDb.Card<MindBlast>(),
        ModelDb.Card<Nostalgia>(),
        ModelDb.Card<Omnislice>(),
        ModelDb.Card<Order>(),
        ModelDb.Card<Panache>(),
        ModelDb.Card<PanicButton>(),
        ModelDb.Card<PrepTime>(),
        ModelDb.Card<Production>(),
        ModelDb.Card<Prowess>(),
        ModelDb.Card<Purity>(),
        ModelDb.Card<Rend>(),
        ModelDb.Card<Restlessness>(),
        ModelDb.Card<RollingBoulder>(),
        ModelDb.Card<Scrawl>(),
        ModelDb.Card<SecretTechnique>(),
        ModelDb.Card<SecretWeapon>(),
        ModelDb.Card<Shockwave>(),
        ModelDb.Card<Splash>(),
        ModelDb.Card<Stratagem>(),
        ModelDb.Card<TheBomb>(),
        ModelDb.Card<TheGambit>(),
        ModelDb.Card<ThinkingAhead>(),
        ModelDb.Card<ThrummingHatchet>(),
        ModelDb.Card<UltimateDefend>(),
        ModelDb.Card<UltimateStrike>()
    ];
}
