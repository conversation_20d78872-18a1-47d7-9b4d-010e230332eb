using Godot;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DeprecatedCardPool : CardPoolModel
{
    public override string Title => "token";
    public override string EnergyColorName => ColorlessCardPool.energyColorName;
    public override string CardFrameMaterialPath => "card_frame_colorless";

    public override Color DeckEntryCardColor => Colors.White;
    public override bool IsColorless => true;

    protected override CardModel[] GenerateCards() =>
    [
        ModelDb.Card<DeprecatedCard>()
    ];
}
