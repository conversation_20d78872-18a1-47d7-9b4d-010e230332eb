using Godot;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.CardPools;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TokenCardPool : CardPoolModel
{
    public override string Title => "token";
    public override string EnergyColorName => ColorlessCardPool.energyColorName;
    public override string CardFrameMaterialPath => "card_frame_colorless";

    // NOTE: Tokens don't show up in the Climb History screen so it's an ugly color
    public override Color DeckEntryCardColor => Colors.White;
    public override bool IsColorless => true;

    protected override CardModel[] GenerateCards() =>
    [
        ModelDb.Card<GiantRock>(),
        ModelDb.Card<Luminesce>(),
        ModelDb.Card<MinionDiveBomb>(),
        ModelDb.Card<MinionSacrifice>(),
        ModelDb.Card<Shiv>(),
        ModelDb.Card<Soul>(),
        ModelDb.Card<SovereignBlade>(),
        ModelDb.Card<SweepingGaze>()
    ];
}
