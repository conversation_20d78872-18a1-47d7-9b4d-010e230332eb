using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Rigid : EnchantmentModel
{
    public override bool HasExtraCardText => true;

    public override bool ShowAmount => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new BlockVar(0, BlockProps.card)
    ];
    
    public override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        await CreatureCmd.GainBlock(Card.Owner.Creature, DynamicVars.Block, Card);
    }
    
    public override void RecalculateValues()
    {
        DynamicVars.Block.BaseValue = Amount;
    }
}