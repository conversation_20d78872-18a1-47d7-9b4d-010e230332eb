using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Favored : EnchantmentModel
{
    public override bool CanEnchantCardType(CardType cardType) => cardType == CardType.Attack;

    /// <summary>
    /// NOTE: We use ModifyDamageGiven here instead of <see cref="EnchantmentModel.OnEnchant"/> because this damage
    /// modification is dynamic. Instead of changing the base amount of damage that its card does, it should modify the
    /// final damage after other effects (upgrades, etc.) have had a chance to modify the base.
    /// </summary>
    public override decimal ModifyDamageGiven(Creature? dealer, decimal amount, ValueProp props, Creature? _, CardModel? card)
    {
        if (card != Card) return amount;
        if (!props.IsPoweredAttack()) return amount;

        return amount * 2;
    }
}
