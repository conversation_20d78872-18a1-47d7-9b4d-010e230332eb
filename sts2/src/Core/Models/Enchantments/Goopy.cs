using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Goopy : EnchantmentModel
{
    public override bool ShowAmount => true;

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (Card.Pile?.Type != CardPileTarget.Deck) return;
        if (room is not RestSiteRoom) return;

        Amount--;

        if (Amount <= 0)
        {
            await CardPileCmd.RemoveFromDeck(Card);
        }
    }
}
