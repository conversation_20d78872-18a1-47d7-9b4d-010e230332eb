using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Sown : EnchantmentModel
{
    public override bool ShowAmount => true;

    public override Task AfterCombatVictory(CombatRoom _)
    {
        if (Card.Pile?.Type != CardPileTarget.Deck) return Task.CompletedTask;

        Amount--;

        if (Amount <= 0)
        {
            CardModel card = Card;
            CardCmd.ClearEnchantment(card);
            CardCmd.Enchant<Sprouted>(card, 1);

            NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
            if (vfx != null)
            {
                NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
            }
        }

        return Task.CompletedTask;
    }
}
