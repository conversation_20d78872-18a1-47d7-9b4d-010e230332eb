using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Enchantments;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SlumberingEssence : EnchantmentModel
{
    public override Task BeforeFlush(PlayerChoiceContext choiceContext, Player player)
    {
        if (player != Card.Owner) return Task.CompletedTask;
        if (Card.Pile?.Type != CardPileTarget.Hand) return Task.CompletedTask;

        Card.SetEnergyCostUntilPlayed(Card.CurrentEnergyCost - 1);
        return Task.CompletedTask;
    }
}
