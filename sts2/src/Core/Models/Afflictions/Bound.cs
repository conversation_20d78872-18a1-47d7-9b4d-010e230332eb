using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Afflictions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Bound : AfflictionModel
{
    public override bool HasExtraCardText => true;

    private bool _wasAlreadyUnplayable;

    private bool WasAlreadyUnplayable
    {
        get => _wasAlreadyUnplayable;
        set
        {
            AssertMutable();
            _wasAlreadyUnplayable = value;
        }
    }

    public override Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        foreach (CardModel combatCard in Card.Owner.PlayerCombatState!.AllCards)
        {
            if (combatCard.Affliction is not Bound) continue;

            combatCard.AddKeyword(CardKeyword.Unplayable);
        }

        return Task.CompletedTask;
    }

    public override void AfterApplied()
    {
        // If this card started out unplayable, keep track of that so we don't clear it at end of turn.
        WasAlreadyUnplayable = Card.Keywords.Contains(CardKeyword.Unplayable);

        // Note: This is to prevent an interaction with Hellraiser where Hellraiser plays the first card drawn in a turn.
        // Subsequent drawn cards that are Bound should start out unplayable. If we keep finding more edge cases like this,
        // we should probably make a IsPlayable hook or something similar and put this check there instead of relying on
        // the keyword to be added.
        bool boundCardPlayedThisTurn = CombatManager.Instance.History.CardPlaysStarted.Any(e =>
            e.HappenedThisTurn(CombatState) &&
            e.Card.Affliction is Bound
        );

        if (boundCardPlayedThisTurn)
        {
            Card.AddKeyword(CardKeyword.Unplayable);
        }
    }

    public override void BeforeRemoved()
    {
        // If this card was already unplayable before it was afflicted, exit early so we don't remove the keyword.
        if (WasAlreadyUnplayable) return;

        Card.RemoveKeyword(CardKeyword.Unplayable);
    }

    public override Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        CardCmd.ClearAffliction(Card);
        return Task.CompletedTask;
    }
}
