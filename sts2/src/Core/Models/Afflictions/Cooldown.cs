using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;

namespace MegaCrit.Sts2.Core.Models.Afflictions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Cooldown : AfflictionModel
{
    public override bool IsStackable => true;

    public override bool HasExtraCardText => true;

    public override bool ShouldPlay(CardModel card, AutoPlayType autoplayType)
    {
        if (autoplayType != AutoPlayType.None) return true;
        return card != Card;
    }

    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Card.Owner.Creature.Side) return Task.CompletedTask;

        Amount--;
        if (Amount <= 0)
        {
            CardCmd.ClearAffliction(Card);
        }

        return Task.CompletedTask;
    }
}
