using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Afflictions;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Galvanized : AfflictionModel
{
    public override bool IsStackable => true;
    public override bool CanAfflictCardType(CardType cardType) => base.CanAfflictCardType(cardType) && cardType == CardType.Power;

    public override async Task OnPlay(PlayerChoiceContext choiceContext, Creature? target)
    {
        VfxCmd.PlayOnCreature(Card.Owner.Creature, VfxCmd.lightningPath);
        await CreatureCmd.Damage(Card.Owner.Creature, Amount, DamageProps.nonCardUnpowered, null, null);
    }
}
