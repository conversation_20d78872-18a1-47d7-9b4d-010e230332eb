using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Audio.Debug;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.RelicPools;

namespace MegaCrit.Sts2.Core.Models.Characters;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Deprived : CharacterModel
{
    public override Color NameColor => StsColors.gold;
    public override int StartingHp => 100;
    public override int StartingGold => 99;
    public override int MaxEnergy => 100;

    private MockCardPool? _mockCardPool;

    public override CardPoolModel CardPool => _mockCardPool ?? ModelDb.CardPool<MockCardPool>();
    public override RelicPoolModel RelicPool => ModelDb.RelicPool<IroncladRelicPool>();
    public override PotionPoolModel PotionPool => ModelDb.PotionPool<IroncladPotionPool>();
    public override IEnumerable<CardModel> StartingDeck => [];
    public override IReadOnlyList<RelicModel> StartingRelics => [];
    public override IEnumerable<IReadOnlyList<CardModel>> CardBundles => [];

    public override float AttackAnimDelay => 0f;
    public override float CastAnimDelay => 0f;

    public override string CharacterSelectSfx => TmpSfx.heavyAttack;

    public void ResetMockCardPool() => _mockCardPool = null;

    public void AddToPool(CardModel card)
    {
        card.AssertCanonical();
        _mockCardPool ??= (MockCardPool)ModelDb.CardPool<MockCardPool>().ToMutable();
        _mockCardPool.Add(card);
    }

    public override Color MapDrawingColor => new("462996"); // This is the watcher color lol
}
