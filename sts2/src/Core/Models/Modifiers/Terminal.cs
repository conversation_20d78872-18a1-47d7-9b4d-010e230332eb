using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Terminal : ModifierModel
{
    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        // The modifier says "room", but I think players will interpret this as "map point", so only lose HP when
        // entering a new map point.
        if (ClimbState.BaseRoom == room)
        {
            foreach (Player player in ClimbState.Players)
            {
                await CreatureCmd.LoseMaxHp(player.Creature, 1);
            }
        }

        if (room is CombatRoom combatRoom)
        {
            foreach (Creature creature in combatRoom.CombatState.PlayerCreatures)
            {
                await PowerCmd.Apply<Plating>(creature, 5, null, null);
            }
        }
    }
}
