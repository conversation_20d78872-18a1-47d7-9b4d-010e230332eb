using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Insanity : ModifierModel
{
    public override bool ClearsPlayerDeck => true;

    public override Func<Task> GenerateNeowOption(EventModel eventModel) => () => ObtainCards(eventModel.Owner!, eventModel.Rng);

    private static async Task ObtainCards(Player player, Rng rng)
    {
        List<CardPileAddResult> results = [];

        for (int i = 0; i < 30; i++)
        {
            CardModel canonicalCard = rng.NextItem(player.Character.CardPool.Cards.Where(c => c.Rarity is CardRarity.Common or CardRarity.Uncommon or CardRarity.Rare))!;
            CardModel mutableCard = player.ClimbState.CreateCard(canonicalCard, player);
            CardPileAddResult result = await CardPileCmd.Add(mutableCard, CardPileTarget.Deck);
            results.Add(result);
        }

        foreach (CardPileAddResult result in results)
        {
            CardCmd.PreviewCardPileAdd(result, 1.2f, CardPreviewStyle.MessyLayout);
            await Cmd.CustomScaledWait(0.1f, 0.2f, 0.2f);
        }

        await Cmd.CustomScaledWait(0.6f, 1.2f, 1.2f);

        // Disable Pandora's Box from appearing (we have no starter cards)
        foreach (Player climbPlayer in player.ClimbState.Players)
        {
            climbPlayer.RelicGrabBag.Remove<PandorasBox>();
        }

        player.ClimbState.SharedRelicGrabBag.Remove<PandorasBox>();
    }
}
