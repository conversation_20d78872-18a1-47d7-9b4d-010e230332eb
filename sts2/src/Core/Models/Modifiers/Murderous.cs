using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Modifiers;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Murderous : ModifierModel
{
    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not CombatRoom combatRoom) return;

        foreach (Creature creature in combatRoom.CombatState.Creatures)
        {
            await PowerCmd.Apply<Strength>(creature, 3, null, null);
        }
    }
}
