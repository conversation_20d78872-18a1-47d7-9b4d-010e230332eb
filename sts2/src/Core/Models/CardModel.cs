using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Assets;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Localization.Formatters;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Exceptions;
using MegaCrit.Sts2.Core.Nodes.Cards;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models;

public abstract class CardModel : AbstractModel
{
    #region Events

    public event Action? AfflictionChanged;
    public event Action? EnchantmentChanged;
    public event Action? EnergyCostChanged;
    public event Action? KeywordsChanged;

    public event Action? ReplayCountChanged;

    public event Action? Played;
    public event Action? Drawn;
    public event Action? StarCostChanged;
    public event Action? Upgraded;

    #endregion

    #region Text

    public LocString TitleLocString => new("cards", $"{Id.Entry}.title");

    public string Title
    {
        get
        {
            LocString title = TitleLocString;

            if (!IsUpgraded)
            {
                return title.GetFormattedText();
            }

            if (MaxUpgradeLevel > 1)
            {
                return $"{title.GetFormattedText()}+{CurrentUpgradeLevel}";
            }

            return $"{title.GetFormattedText()}+";
        }
    }

    // This needs to be a LocString because we add lots of parameters to it
    public LocString Description => new("cards", $"{Id.Entry}.description");

    // Prompt for cards that show a selection screen (like Headbutt).
    protected LocString SelectionScreenPrompt
    {
        get
        {
            LocString prompt = new("cards", $"{Id.Entry}.selectionScreenPrompt");
            if (!prompt.Exists()) throw new InvalidOperationException($"No selection screen prompt for {Id}.");

            DynamicVars.AddTo(prompt);
            return prompt;
        }
    }

    #endregion

    #region General

    private enum DescriptionPreviewType
    {
        None = 0,
        Upgrade = 1
    }

    public virtual string PortraitPath => ImageHelper.GetImagePath($"atlases/card_atlas.sprites/{Pool.Title.ToLower()}/{Id.Entry.ToLower()}.tres");
    public string PortraitFallbackPath => ImageHelper.GetImagePath($"packed/card_portraits/{Pool.Title.ToLower()}/{Id.Entry.ToLower()}.png");
    public virtual string BetaPortraitPath => ImageHelper.GetImagePath($"atlases/card_atlas.sprites/{Pool.Title.ToLower()}/beta/{Id.Entry.ToLower()}.tres");
    public string BetaPortraitFallbackPath => ImageHelper.GetImagePath($"packed/card_portraits/{Pool.Title.ToLower()}/beta/{Id.Entry.ToLower()}.png");
    public static string MissingPortraitPath => ImageHelper.GetImagePath("atlases/card_atlas.sprites/beta.tres");
    public string AncientMissingPortraitPath => ImageHelper.GetImagePath("atlases/card_atlas.sprites/ancient_beta.tres");

    private bool HasPackedPortrait => ResourceLoader.Exists(PortraitPath);
    public bool HasPortrait => ResourceLoader.Exists(PortraitFallbackPath);
    public bool HasPackedBetaPortrait => ResourceLoader.Exists(BetaPortraitPath);
    public bool HasBetaPortrait => ResourceLoader.Exists(BetaPortraitFallbackPath);
    public Texture2D Portrait => PreloadManager.Cache.GetTexture2D(AllPortraitPaths.First());

    private string FramePath
    {
        get
        {
            CardType cardType;

            switch (Type)
            {
                case CardType.None:
                case CardType.Status:
                case CardType.Curse:
                    cardType = CardType.Skill;
                    break;
                case CardType.Quest:
                case CardType.Attack:
                case CardType.Skill:
                case CardType.Power:
                    cardType = Type;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            if (Rarity != CardRarity.Ancient)
            {
                return ImageHelper.GetImagePath($"packed/card_template/card_frame_{cardType.ToString().ToLower()}_s.png");
            }
            else
            {
                return ImageHelper.GetImagePath("packed/card_template/card_frame_ancient_s.png");
            }
        }
    }

    public CompressedTexture2D Frame => PreloadManager.Cache.GetCompressedTexture2D(FramePath);

    private string PortraitBorderPath
    {
        get
        {
            CardType cardType;

            switch (Type)
            {
                case CardType.None:
                case CardType.Status:
                case CardType.Curse:
                case CardType.Quest:
                    cardType = CardType.Skill;
                    break;
                case CardType.Attack:
                case CardType.Skill:
                case CardType.Power:
                    cardType = Type;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            return ImageHelper.GetImagePath($"packed/card_template/card_portrait_border_{cardType.ToString().ToLower()}_s.png");
        }
    }

    private string AncientTextBgPath
    {
        get
        {
            if (Rarity != CardRarity.Ancient)
            {
                throw new InvalidOperationException("This card is not an ancient card.");
            }

            CardType cardType;

            switch (Type)
            {
                case CardType.None:
                case CardType.Status:
                case CardType.Curse:
                    cardType = CardType.Skill;
                    break;
                case CardType.Quest:
                case CardType.Attack:
                case CardType.Skill:
                case CardType.Power:
                    cardType = Type;
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }

            return ImageHelper.GetImagePath($"packed/card_template/ancient_card_text_bg_{cardType.ToString().ToLower()}.png");
        }
    }

    public CompressedTexture2D AncientTextBg => PreloadManager.Cache.GetCompressedTexture2D(AncientTextBgPath);

    public CompressedTexture2D PortraitBorder => PreloadManager.Cache.GetCompressedTexture2D(PortraitBorderPath);
    private string EnergyIconPath => Pool.EnergyIconPath;
    public CompressedTexture2D EnergyIcon => PreloadManager.Cache.GetCompressedTexture2D(EnergyIconPath);

    protected IHoverTip EnergyHoverTip => HoverTipFactory.ForEnergy(this);

    private string BannerTexturePath
    {
        get
        {
            if (Rarity != CardRarity.Ancient)
            {
                return "res://images/packed/card_template/card_banner_s.png";
            }
            else
            {
                return "res://images/packed/card_template/card_banner_ancient_s.png";
            }
        }
    }


    public CompressedTexture2D BannerTexture => PreloadManager.Cache.GetCompressedTexture2D(BannerTexturePath);

    private string BannerMaterialPath
    {
        get
        {
            return Rarity switch
            {
                CardRarity.Uncommon => "res://materials/cards/banners/card_banner_uncommon_mat.tres",
                CardRarity.Rare => "res://materials/cards/banners/card_banner_rare_mat.tres",
                CardRarity.Curse => "res://materials/cards/banners/card_banner_curse_mat.tres",
                CardRarity.Status => "res://materials/cards/banners/card_banner_status_mat.tres",
                CardRarity.Event => "res://materials/cards/banners/card_banner_event_mat.tres",
                CardRarity.Quest => "res://materials/cards/banners/card_banner_quest_mat.tres",
                CardRarity.Ancient => "res://materials/cards/banners/card_banner_ancient_mat.tres",
                _ => "res://materials/cards/banners/card_banner_common_mat.tres"
            };
        }
    }

    public Material BannerMaterial => PreloadManager.Cache.GetMaterial(BannerMaterialPath);
    public Material FrameMaterial => Pool.FrameMaterial;

    public abstract CardType Type { get; }
    public abstract CardRarity Rarity { get; }

    private CardPoolModel? _pool;

    public CardPoolModel Pool
    {
        get
        {
            // If the pool is already cached, return it. A card's pool never changes at runtime.
            if (_pool != null) return _pool;

            // Try to find a real pool.
            _pool = ModelDb.CardPools.FirstOrDefault(pool => pool.CardIds.Contains(Id));
            if (_pool != null) return _pool;

            if (ModelDb.CardPool<MockCardPool>().CardIds.Contains(Id))
            {
                // Fall back to the mock pool.
                _pool = ModelDb.CardPool<MockCardPool>();
                return _pool;
            }

            // At this point, if we still can't find a card pool, something's wrong.
            throw new InvalidProgramException($"Card {this} is not in any card pool!");
        }
    }

    private Player? _owner;

    /// <summary>
    /// Get the Player that this card belongs to.
    /// Will technically be null on a canonical card model, but we should never be checking that, so we leave this as
    /// non-nullable for convenience.
    /// </summary>
    public Player Owner
    {
        get
        {
            AssertMutable();
            return _owner!;
        }
        set
        {
            AssertMutable();
            if (_owner != null && value != null) throw new InvalidOperationException($"Card {Id.Entry} already has an owner.");

            SortingOrderChanged();
            _owner = value;
        }
    }

    public CardPile? Pile => _owner?.Piles.FirstOrDefault(p => p.Cards.Contains(this));

    #endregion

    #region Energy Cost

    public abstract int CanonicalEnergyCost { get; }
    private bool _energyCostSet;

    // Only relevant if HasEnergyCostX is false.
    private int _baseEnergyCost;

    public int BaseEnergyCost
    {
        get
        {
            if (!IsMutable) return CanonicalEnergyCost;

            if (!_energyCostSet)
            {
                _baseEnergyCost = CanonicalEnergyCost;
                _energyCostSet = true;
            }

            return _baseEnergyCost;
        }

        protected set
        {
            AssertMutable();

            if (!HasEnergyCostX)
            {
                _baseEnergyCost = value;
                _energyCostSet = true;
            }

            EnergyCostChanged?.Invoke();
        }
    }

    /// <summary>
    /// Was this card's energy cost just recently upgraded?
    /// This is mainly used to show upgrade preview values in green.
    /// This should be cleared after the upgrade is complete.
    /// </summary>
    public bool WasEnergyCostJustUpgraded { get; private set; }

    private List<TemporaryCardCost> _temporaryEnergyCosts = [];
    public TemporaryCardCost? TemporaryEnergyCost => _temporaryEnergyCosts.LastOrDefault();

    /// <summary>
    /// Get this card's current energy cost.
    ///
    /// HOW DOES IT WORK?
    ///
    /// By default, we just use the card's base energy cost (the same one you'd see in the Card Library). However,
    /// many effects can set a temporary energy cost, either:
    ///
    /// * until the card is played (see King's Gambit)
    /// * for a single turn OR until the card is played, whichever comes first (see Infernal Blade)
    /// * for the entire combat (see Havoc).
    ///
    /// In rare cases, a card could have multiple temporary energy costs set. In this case, the most recent modification
    /// wins. However, we still need to keep track of all the temporary costs, because it's possible we'll need to go
    /// back to an older one when a newer one "wears off".
    ///
    /// For example, if a card has two temporary costs, one from King's Gambit ("until card played") and one from
    /// Infernal Blade ("single turn OR until card played"), and the turn ends _without_ playing the card,
    /// Infernal Blade's temporary cost should wear off, but King's Gambit's temporary cost should still apply, since
    /// the card wasn't played.
    ///
    /// We manage this complexity with a "stack" of temporary energy costs.
    /// </summary>
    public virtual int CurrentEnergyCost => TemporaryEnergyCost?.Cost ?? BaseEnergyCost;

    // Is this an energy X-bomb type card?
    public virtual bool HasEnergyCostX => false;

    private int _lastEnergySpent;

    /// <summary>
    /// The amount of energy most recently spent to play this card.
    /// Used when duplicating X-cost cards, to make sure the duplicates are played with the same value.
    /// </summary>
    public int LastEnergySpent
    {
        get => _lastEnergySpent;
        set
        {
            AssertMutable();
            _lastEnergySpent = value;
        }
    }

    #endregion

    #region Replay

    private int _replayCount;

    public int ReplayCount
    {
        get => _replayCount;
        set
        {
            AssertMutable();
            _replayCount = value;
            ReplayCountChanged?.Invoke();
        }
    }

    #endregion

    #region Star Cost

    public virtual int CanonicalStarCost => -1;
    private bool _starCostSet;

    // Only relevant if HasStarCostX is false.
    private int _baseStarCost;

    public int BaseStarCost
    {
        get
        {
            if (!IsMutable) return CanonicalStarCost;

            if (!_starCostSet)
            {
                _baseStarCost = CanonicalStarCost;
                _starCostSet = true;
            }

            return _baseStarCost;
        }

        private set
        {
            AssertMutable();

            if (!HasStarCostX)
            {
                _baseStarCost = value;
                _starCostSet = true;
            }

            StarCostChanged?.Invoke();
        }
    }

    /// <summary>
    /// Was this card's star cost just recently upgraded?
    /// This is mainly used to show upgrade preview values in green.
    /// This should be cleared after the upgrade is complete.
    /// </summary>
    public bool WasStarCostJustUpgraded { get; private set; }

    private List<TemporaryCardCost> _temporaryStarCosts = [];
    public TemporaryCardCost? TemporaryStarCost => _temporaryStarCosts.LastOrDefault();

    /// <summary>
    /// Get this card's current star cost.
    ///
    /// This works just like CurrentEnergyCost, but for stars, with one exception. If the card had no star cost (it was
    /// negative) and it is temporarily set to zero, then we still treat the card as if it has no star cost, so that
    /// it still doesn't show up on the card.
    /// </summary>
    public virtual int CurrentStarCost
    {
        get
        {
            int? tempCost = _temporaryStarCosts.LastOrDefault()?.Cost;

            if (tempCost != null)
            {
                if (tempCost == 0 && BaseStarCost < 0)
                {
                    // Even though temp cost has been set to zero, our base cost is already negative
                    // so just return the negative value
                    return BaseStarCost;
                }
                else
                {
                    // Temp cost is zero and base cost is non-zero
                    return tempCost.Value;
                }
            }
            else
            {
                // No temp cost, just return base cost
                return BaseStarCost;
            }
        }
    }

    // Is this a star X-bomb type card?
    public virtual bool HasStarCostX => false;

    private int _lastStarsSpent;

    /// <summary>
    /// The amount of stars most recently spent to play this card.
    /// Used when duplicating X-cost cards, to make sure the duplicates are played with the same value.
    /// </summary>
    public int LastStarsSpent
    {
        get => _lastStarsSpent;
        set
        {
            AssertMutable();
            _lastStarsSpent = value;
        }
    }

    #endregion

    #region Targeting

    // How to draw UI targeting elements for the enemy and player sides.
    public abstract UiTargetEnemy TargetEnemy { get; }
    public abstract UiTargetPlayer TargetPlayer { get; }
    public ActionTarget TargetType => ActionTargetExtensions.GetCompositeTarget(TargetEnemy, TargetPlayer);

    #endregion

    #region Keywords

    private HashSet<CardKeyword>? _keywords;

    public virtual IEnumerable<CardKeyword> CanonicalKeywords => [];

    public IReadOnlySet<CardKeyword> Keywords
    {
        get
        {
            if (_keywords != null) return _keywords;

            _keywords = [];
            _keywords.UnionWith(CanonicalKeywords);

            return _keywords;
        }
    }

    #endregion

    #region Tags

    private HashSet<CardTag>? _tags;

    /// <summary>
    /// This card's tags. See <see cref="CardTag"/> for details on how to use these.
    ///
    /// NOTE: The current implementation assumes a card's tags will never change. If we ever need to dynamically add or
    /// remove tags after creation, change this to work more like Keywords.
    /// </summary>
    public IEnumerable<CardTag> Tags => _tags ??= CanonicalTags;

    protected virtual HashSet<CardTag> CanonicalTags => [];

    #endregion

    #region Gameplay Logic

    private DynamicVarSet? _dynamicVars;

    public DynamicVarSet DynamicVars
    {
        get
        {
            _dynamicVars ??= new DynamicVarSet(CanonicalVars);
            return _dynamicVars;
        }
    }

    protected virtual IEnumerable<DynamicVar> CanonicalVars => [];

    private bool _exhaustOnNextPlay;

    /// <summary>
    /// This is used for cards like Havoc and Cinder to mark cards that should be sent to the exhaust pile instead
    /// of the discard pile on their next play.
    ///
    /// TODO: This might be kind of a hacky solution. We should look into other ways to pass this info along.
    /// </summary>
    public bool ExhaustOnNextPlay
    {
        get => _exhaustOnNextPlay;
        set
        {
            AssertMutable();
            _exhaustOnNextPlay = value;
        }
    }

    private bool _hasSingleTurnRetain;

    private bool HasSingleTurnRetain
    {
        get => _hasSingleTurnRetain;
        set
        {
            AssertMutable();
            _hasSingleTurnRetain = value;
        }
    }

    /// <summary>
    /// Should this card be retained this turn?
    /// True if the card has the Retain keyword, or if some effect like Well-Laid Plans has made it retain for a single
    /// turn and it's still that turn.
    /// </summary>
    public bool ShouldRetainThisTurn => Keywords.Contains(CardKeyword.Retain) || HasSingleTurnRetain;

    public bool HasEtherealThisTurn => Keywords.Contains(CardKeyword.Ethereal) || Keywords.Contains(CardKeyword.TemporaryEthereal);

    public EnchantmentModel? Enchantment { get; private set; }
    public AfflictionModel? Affliction { get; private set; }

    /// <summary>
    /// Whether or not playing this card can heal the player or their pets, either directly (like Feed) or by giving
    /// you something else that can heal you (like Alchemize giving you a Regen Potion).
    ///
    /// Used primarily to filter cards out of random in-combat generation effects, to avoid making annoying
    /// "optimal play" behavior.
    /// </summary>
    public virtual bool CanBeGeneratedInCombat => true;

    /// <summary>
    /// Whether or not playing this card will directly evoke an Orb.
    /// Applies to cards like Dualcast, which say "Evoke your next Orb".
    /// Does NOT apply to cards that may evoke an Orb as a side-effect (like Zap's channeling causing an Orb to evoke).
    ///
    /// Used primarily to visually update the numbers displayed on your Orbs to reflect their Evoke values while
    /// dragging a card.
    /// </summary>
    public virtual bool EvokesOrbs => false;

    /// <summary>
    /// Whether or not playing this card gains you block immediately.
    /// True for cards like Defend and Survivor.
    /// False for cards like Shadowmeld (which doesn't gain you block until you play an attack after).
    ///
    /// Used for things like filtering the Nimble enchantment's targets.
    /// Also automatically sets the block HoverTip.
    ///
    /// Note: Don't convert this into a CardTag. It's used by the system for automatic Osty targeting behavior.
    /// </summary>
    public virtual bool GainsBlock => false;

    /// <summary>
    /// Is this card one of the basic Strikes or Defends that you start the game with?
    ///
    /// Used for things like Pandora's Box that only operate on these specific starter cards.
    /// </summary>
    public virtual bool IsBasicStrikeOrDefend
    {
        get
        {
            if (Rarity != CardRarity.Basic) return false;
            if (Tags.Contains(CardTag.Strike)) return true;
            if (Tags.Contains(CardTag.Defend)) return true;

            return false;
        }
    }

    /// <summary>
    /// The card that this is a duplicate of. Null when this card is not a duplicate (which is most of the time).
    ///
    /// Dupes behave slightly differently from original cards in some situations. For example:
    /// * After playing a dupe, it is sent to the Limbo pile, rather than Discard/Exhaust.
    /// * Dupes cannot be further duplicated by effects like Duplication Potion.
    /// * If an X-cost card is duped, the dupe retains the X-value from the original.
    /// </summary>
    public CardModel? DupeOf { get; private set; }

    /// <summary>
    /// Whether or not this card is considered a duplicate. See <see cref="DupeOf"/> for more info on dupes.
    /// </summary>
    public bool IsDupe => DupeOf != null;

    public bool IsRemovable => !Keywords.Contains(CardKeyword.Eternal);

    public bool IsTransformable => IsRemovable || Pile is not { Type: CardPileTarget.Deck };

    public bool IsInCombat => IsMutable && Pile is { IsCombatPile: true };

    #endregion

    #region Upgrades

    private int _currentUpgradeLevel;

    public int CurrentUpgradeLevel
    {
        get => _currentUpgradeLevel;
        private set
        {
            AssertMutable();

            if (value > MaxUpgradeLevel)
            {
                throw new InvalidOperationException($"{Id} cannot be upgraded past its MaxUpgradeLevel.");
            }

            _currentUpgradeLevel = value;
        }
    }

    public virtual int MaxUpgradeLevel => 1;

    public bool IsUpgraded => CurrentUpgradeLevel > 0;

    public bool IsUpgradable
    {
        get
        {
            if (CurrentUpgradeLevel >= MaxUpgradeLevel) return false;

            return HookBus.Instance.CanUpgrade(this);
        }
    }

    private CardUpgradePreviewType _upgradePreviewType;

    // This is to facilitate having upgrade previews (that don't live in a pile)
    // reflect power values from the player in combat (i.e. armaments)
    public CardUpgradePreviewType UpgradePreviewType
    {
        get => _upgradePreviewType;
        set
        {
            AssertMutable();
            if (!value.IsPreview() && _upgradePreviewType.IsPreview())
            {
                throw new InvalidOperationException("A card cannot go to from being upgrade preview. Consider making a new card model instead.");
            }

            _upgradePreviewType = value;
        }
    }

    #endregion

    #region Checks

    /// <summary>
    /// Override this property to add extra conditions to check before allowing play.
    /// For example, Grand Finale is only playable if your draw pile is empty, so it would override this.
    /// </summary>
    protected virtual bool IsPlayable => true;

    /// <summary>
    /// Override this property to block a card from appearing in the Card Library screen.
    /// </summary>
    public virtual bool ShouldShowInCardLibrary => true;

    public bool ShouldGlowGold => ShouldGlowGoldInternal || (Enchantment?.ShouldGlowGold ?? false);
    public bool ShouldGlowRed => ShouldGlowRedInternal || (Enchantment?.ShouldGlowRed ?? false);

    /// <summary>
    /// Override this property to add conditions to check to determine whether to show a gold glow on this card.
    /// For example, Evil Eye adds 6 extra block if you've exhausted a card this turn, so it would override this.
    /// </summary>
    protected virtual bool ShouldGlowGoldInternal => false;

    /// <summary>
    /// Override this property to add conditions to check to determine whether to show a red glow on this card.
    /// For example, Normality should glow red when it blocks card plays.
    /// </summary>
    protected virtual bool ShouldGlowRedInternal => false;

    #endregion

    #region Overlay

    public virtual bool HasBuiltInOverlay => false;

    private string OverlayPath => SceneHelper.GetScenePath($"cards/overlays/{Id.Entry.ToLower()}");

    public Control CreateOverlay() => PreloadManager.Cache.GetScene(OverlayPath).Instantiate<Control>();

    #endregion

    private int? _floorAddedToDeck;

    public int? FloorAddedToDeck
    {
        get => _floorAddedToDeck;
        set
        {
            AssertMutable();
            _floorAddedToDeck = value;
        }
    }

    private Creature? _currentTarget;

    public Creature? CurrentTarget
    {
        get => _currentTarget;
        private set
        {
            AssertMutable();
            _currentTarget = value;
        }
    }

    private CardModel? _deckVersion;

    public CardModel? DeckVersion
    {
        get => _deckVersion;
        set
        {
            AssertMutable();
            _deckVersion = value;
        }
    }

    protected virtual IEnumerable<IHoverTip> ExtraHoverTips => [];

    public IEnumerable<IHoverTip> HoverTips
    {
        get
        {
            List<IHoverTip> tips = ExtraHoverTips.ToList();

            if (ReplayCount > 0)
            {
                tips.Add(HoverTipFactory.Static(StaticHoverTip.ReplayDynamic, [new DynamicVar("Times", ReplayCount)]));
            }

            if (EvokesOrbs)
            {
                tips.Add(HoverTipFactory.Static(StaticHoverTip.Evoke));
            }

            if (GainsBlock)
            {
                tips.Add(HoverTipFactory.Static(StaticHoverTip.Block));
            }

            foreach (CardKeyword cardKeyword in Keywords)
            {
                // TODO: if we end up needing another one of these linked keywords, we should refactor this out
                // into a small system
                tips.Add(HoverTipFactory.FromKeyword(cardKeyword));

                if (cardKeyword is CardKeyword.Ethereal or CardKeyword.TemporaryEthereal)
                {
                    tips.Add(HoverTipFactory.FromKeyword(CardKeyword.Exhaust));
                }
            }

            if (Enchantment != null)
            {
                tips.AddRange(Enchantment.HoverTips);
            }

            if (Affliction != null)
            {
                tips.Add(Affliction.HoverTip);
            }

            return tips.Distinct();
        }
    }

    private CardModel _canonicalInstance = default!;

    public CardModel CanonicalInstance
    {
        get => IsMutable ? _canonicalInstance : this;
        private set
        {
            AssertMutable();
            _canonicalInstance = value;
        }
    }

    /// <summary>
    /// The state of the climb that this card exists in.
    /// Null for cards that exist outside of a climb (in the Compendium, in preview HoverTips, etc.)
    /// </summary>
    public IClimbState? ClimbState => _owner?.ClimbState;

    /// <summary>
    /// The state of the combat that this card exists in.
    /// Null for cards that exist outside of a combat (deck cards, cards offered in rewards or at events, etc.)
    /// </summary>
    public CombatState? CombatState => _owner?.Creature.CombatState;

    /// <summary>
    /// The lowest-level scope that this card exists in.
    /// Combat takes precedence over climb, since all cards in a climb have a ClimbState, but cards that have been added
    /// to combat also have a CombatState.
    /// </summary>
    public ICardScope? CardScope => CombatState != null ? CombatState : ClimbState;

    public CardModel ToMutable()
    {
        AssertCanonical();
        CardModel clone = (CardModel)MutableClone();
        clone.CanonicalInstance = this;

        if (_owner != null)
        {
            clone.Owner = _owner;
        }

        return clone;
    }

    protected override void DeepCloneFields()
    {
        _keywords = [..Keywords];
        _dynamicVars = DynamicVars.Clone();
        _temporaryEnergyCosts = _temporaryEnergyCosts.ToList();
        _temporaryStarCosts = _temporaryStarCosts.ToList();

        if (Enchantment != null)
        {
            EnchantmentModel enchantmentClone = (EnchantmentModel)Enchantment.ClonePreservingMutability();

            // We do this instead of ClearEnchantmentInternal because we're not really clearing an enchantment, just
            // paving way for the clone.
            Enchantment = null;

            EnchantInternal(enchantmentClone, enchantmentClone.Amount);

            // Note: We don't call enchantmentClone.ModifyCard() here because the enchantment's modifications are
            // already reflected in the original card. Calling this would cause them to be doubled.
        }

        if (Affliction != null)
        {
            AfflictionModel clone = (AfflictionModel)Affliction.ClonePreservingMutability();

            // We do this instead of ClearAfflictionInternal because we're not really clearing an affliction, just
            // paving way for the clone.
            Affliction = null;

            AfflictInternal(clone, clone.Amount);
        }
    }

    protected override void AfterCloned()
    {
        base.AfterCloned();

        AfflictionChanged = null;
        DeckVersion = null;
        EnchantmentChanged = null;
        EnergyCostChanged = null;
        Played = null;
        StarCostChanged = null;
        Upgraded = null;
    }

    /// <summary>
    /// Extra logic that should be run after the card is created (NOT during deserialization).
    /// At this point, the card will have an owner, and will be in a CombatState or ClimbState.
    /// </summary>
    public virtual void AfterCreated() { }

    /// <summary>
    /// Extra logic that should be run after deserializing.
    /// This should rarely be overridden, just for very unusual cards like <see cref="MadScience"/>.
    /// </summary>
    protected virtual void AfterDeserialized()
    {
        // No-op in base.
    }

    protected void NeverEverCallThisOutsideOfTests_ClearOwner()
    {
        if (TestMode.IsOff) throw new InvalidOperationException("You monster!");

        _owner = null;
    }

    public void SetToFreeUntilPlayed()
    {
        if (CanonicalEnergyCost < 0) return; // this means it's probably unplayable or has alternate play conditions
        SetEnergyCostUntilPlayed(0);
        SetStarCostUntilPlayed(0);
    }

    public void SetToFreeThisTurn()
    {
        if (CanonicalEnergyCost < 0) return; // this means it's probably unplayable or has alternate play conditions
        SetEnergyCostThisTurn(0);
        SetStarCostThisTurn(0);
    }

    public void SetToFreeThisCombat()
    {
        if (CanonicalEnergyCost < 0) return; // this means it's probably unplayable or has alternate play conditions
        SetEnergyCostThisCombat(0);
        SetStarCostThisCombat(0);
    }

    public void SetEnergyCostUntilPlayed(int cost) => AddTemporaryEnergyCost(TemporaryCardCost.UntilPlayed(cost));
    public void SetEnergyCostThisTurn(int cost) => AddTemporaryEnergyCost(TemporaryCardCost.ThisTurn(cost));
    public void SetEnergyCostThisCombat(int cost) => AddTemporaryEnergyCost(TemporaryCardCost.ThisCombat(cost));

    /// <summary>
    /// This differs from CurrentEnergyCost because this skips temporary star costs that go away at the end of turn or when the card is played
    /// and gets to the persistent star cost during for this combat.
    /// </summary>
    public int GetEnergyCostThisCombat()
    {
        TemporaryCardCost? tempCost = _temporaryEnergyCosts.FirstOrDefault(c => c is
        {
            ClearsWhenTurnEnds: false,
            ClearsWhenCardIsPlayed: false
        });

        return tempCost?.Cost ?? BaseEnergyCost;
    }

    private void AddTemporaryEnergyCost(TemporaryCardCost cost)
    {
        AssertMutable();
        _temporaryEnergyCosts.Add(cost);
        EnergyCostChanged?.Invoke();
    }

    /// <summary>
    /// Upgrade the energy cost of this card by the specified amount.
    /// This is meant to be called in <see cref="CardModel.OnUpgrade"/> and <see cref="EnchantmentModel.OnEnchant"/>.
    /// </summary>
    /// <param name="addend">Amount to add to the current cost (usually negative).</param>
    public void UpgradeEnergyCostBy(int addend)
    {
        if (HasEnergyCostX) return;
        if (addend == 0) return;

        int oldCost = BaseEnergyCost;

        BaseEnergyCost = Math.Max(BaseEnergyCost + addend, 0);
        WasEnergyCostJustUpgraded = true;

        if (BaseEnergyCost < oldCost)
        {
            // If upgrading a card reduces its cost, don't let any temporary costs override it.
            _temporaryEnergyCosts.RemoveAll(c => c.Cost > BaseEnergyCost);
        }
    }

    public void SetStarCostUntilPlayed(int cost) => AddTemporaryStarCost(TemporaryCardCost.UntilPlayed(cost));
    public void SetStarCostThisTurn(int cost) => AddTemporaryStarCost(TemporaryCardCost.ThisTurn(cost));
    public void SetStarCostThisCombat(int cost) => AddTemporaryStarCost(TemporaryCardCost.ThisCombat(cost));

    // This differs from CurrentStarCost because this skips temporary star costs that go away at the end of turn or when the card is played
    // and gets to the persistent star cost during for this combat. Required for things like Solar Flare to update the number based on this value
    public int GetStarCostThisCombat() => _temporaryStarCosts.FirstOrDefault(cost => cost is { ClearsWhenTurnEnds: false, ClearsWhenCardIsPlayed: false })?.Cost ?? BaseStarCost;

    private void AddTemporaryStarCost(TemporaryCardCost cost)
    {
        AssertMutable();
        _temporaryStarCosts.Add(cost);
        StarCostChanged?.Invoke();
    }

    /// <summary>
    /// Upgrade the star cost of this card by the specified amount.
    /// This is meant to be called in OnUpgrade.
    /// </summary>
    /// <param name="addend">Amount to add to the current cost (usually negative).</param>
    protected void UpgradeStarCostBy(int addend)
    {
        if (HasStarCostX) throw new InvalidOperationException($"{nameof(UpgradeStarCostBy)} called on {Id.Entry} which has star cost X.");
        if (addend == 0) return;

        int oldCost = BaseStarCost;
        BaseStarCost += addend;
        WasStarCostJustUpgraded = true;

        if (BaseStarCost < oldCost)
        {
            // If upgrading a card reduces its cost, don't let any temporary costs override it.
            _temporaryStarCosts.RemoveAll(c => c.Cost > BaseStarCost);
        }
    }

    public void AddKeyword(CardKeyword keyword)
    {
        AssertMutable();
        _keywords!.Add(keyword);
        KeywordsChanged?.Invoke();
    }

    public void RemoveKeyword(CardKeyword keyword)
    {
        AssertMutable();
        _keywords!.Remove(keyword);
        KeywordsChanged?.Invoke();
    }

    /// <summary>
    /// Set this card to be retained this turn.
    /// Will be cleared at the end of the turn.
    /// </summary>
    public void GiveSingleTurnRetain()
    {
        HasSingleTurnRetain = true;
    }

    public string GetDescriptionForPile(CardPileTarget pileType)
    {
        return GetDescriptionForPile(pileType, DescriptionPreviewType.None);
    }

    public string GetDescriptionForUpgradePreview()
    {
        return GetDescriptionForPile(CardPileTarget.None, DescriptionPreviewType.Upgrade);
    }

    private string GetDescriptionForPile(CardPileTarget pileType, DescriptionPreviewType previewType)
    {
        LocString descriptionLoc = Description;
        DynamicVars.AddTo(descriptionLoc);
        AddExtraArgsToDescription(descriptionLoc);

        UpgradeDisplay upgradeDisplay;

        if (previewType == DescriptionPreviewType.Upgrade)
        {
            upgradeDisplay = UpgradeDisplay.UpgradePreview;
        }
        else if (IsUpgraded)
        {
            upgradeDisplay = UpgradeDisplay.Upgraded;
        }
        else
        {
            upgradeDisplay = UpgradeDisplay.Normal;
        }

        descriptionLoc.Add(new IfUpgradedVar(upgradeDisplay));

        bool onTable = pileType is CardPileTarget.Hand or CardPileTarget.Play;
        descriptionLoc.Add("OnTable", onTable);

        string energyPrefix = EnergyHelper.GetIconPrefix(this);
        descriptionLoc.Add("energyPrefix", energyPrefix);
        descriptionLoc.Add("singleStarIcon", StarIconsFormatter.starIconSprite);

        foreach (KeyValuePair<string, object> pair in descriptionLoc.Variables)
        {
            // Inject the color prefix into all energy vars in the description
            if (pair.Value is EnergyVar energyVar)
            {
                energyVar.ColorPrefix = energyPrefix;
            }
        }

        List<string> lines = [descriptionLoc.GetFormattedText()];

        LocString? enchantmentCardTxt = Enchantment?.DynamicExtraCardText;

        if (enchantmentCardTxt != null)
        {
            lines.Add($"[purple]{enchantmentCardTxt.GetFormattedText()}[/purple]");
        }

        LocString? afflictionCardTxt = Affliction?.DynamicExtraCardText;

        if (afflictionCardTxt != null)
        {
            lines.Add($"[purple]{afflictionCardTxt.GetFormattedText()}[/purple]");
        }

        // handle ethereal separately so that we don't dupe the Ethereal keyword
        // between CardKeywords.Ethereal and CardKeywords.TemporaryEthereal
        // To the player, these two keywords should look the same.
        if (HasEtherealThisTurn)
        {
            lines.Insert(0, CardKeyword.Ethereal.GetCardText());
        }

        // Add text for keywords that come before the card description.
        foreach (CardKeyword keyword in CardKeywordOrder.beforeDescription.Intersect(Keywords))
        {
            lines.Insert(0, keyword.GetCardText());
        }

        // replay is a pseudo card keyword.
        if (ReplayCount > 0)
        {
            LocString replayLoc = new("static_hover_tips", "REPLAY.extraText");
            replayLoc.Add("Times", ReplayCount);
            lines.Add($"{replayLoc.GetFormattedText()}");
        }

        // Add text for keywords that come after the card description.
        foreach (CardKeyword keyword in CardKeywordOrder.afterDescription.Intersect(Keywords))
        {
            lines.Add(keyword.GetCardText());
        }

        return string.Join('\n', lines.Where(l => !string.IsNullOrEmpty(l)));
    }

    /// <summary>
    /// Add an Enchantment to this card.
    /// </summary>
    /// <remarks>
    /// You should generally use CardCommands.Enchant instead of this method. If you do use this method, you may need to
    /// call <see cref="EnchantmentModel.ModifyCard"/> after.
    /// </remarks>
    /// <param name="enchantment">Enchantment to add.</param>
    /// <param name="amount">Amount to set on the added enchantment.</param>
    public void EnchantInternal(EnchantmentModel enchantment, decimal amount)
    {
        AssertMutable();
        enchantment.AssertMutable();

        Enchantment = enchantment;
        Enchantment.ApplyInternal(this, amount);
        EnchantmentChanged?.Invoke();
    }

    /// <summary>
    /// Add an Affliction to this card.
    /// </summary>
    /// <remarks>You should generally use CardCmd.Afflict instead of this method.</remarks>
    /// <param name="affliction">Affliction to add.</param>
    /// <param name="amount">Amount to set on the added affliction</param>
    /// <returns>Whether or not adding the affliction was successful.</returns>
    public void AfflictInternal(AfflictionModel affliction, decimal amount)
    {
        AssertMutable();
        affliction.AssertMutable();

        if (Affliction != null) throw new InvalidOperationException($"Attempted to afflict card {this} that was already afflicted! This is not allowed");

        Affliction = affliction;
        Affliction.Card = this;
        Affliction.Amount = (int)amount;

        // An Affliction should always be subscribed to hooks, even if it's on a card that's not in any pile.
        // This enables us to show card-value-modifying afflictions on selection screens and other places where no pile
        // has been set yet.
        HookBus.Instance.Subscribe(Affliction);

        AfflictionChanged?.Invoke();
    }

    public void ClearEnchantmentInternal()
    {
        if (Enchantment == null) return;

        AssertMutable();

        Enchantment.ClearInternal();
        Enchantment = null;

        EnchantmentChanged?.Invoke();
    }

    public void ClearAfflictionInternal()
    {
        AssertMutable();
        if (Affliction == null) return;

        Affliction.ClearInternal();
        Affliction = null;
        Owner.PlayerCombatState!.RecalculateCardValues();

        AfflictionChanged?.Invoke();
    }

    protected virtual void AddExtraArgsToDescription(LocString description)
    {
        // No-op by default.
    }

    /// <summary>
    /// Get this card's current energy cost, including all modifiers.
    /// Usually, this will just be the same as CurrentEnergyCost, but there are 2 exceptions:
    /// 1. X-cost cards will return the amount of energy that will be spent to play them instead of a 0 placeholder value.
    /// 2. Effects that modify energy costs (like the Free Skill power) will be reflected here.
    /// </summary>
    /// <returns>Current energy cost including modifiers.</returns>
    public int GetEnergyCostWithModifiers()
    {
        // X-cost cards always cost the amount of energy their owner has.
        if (HasEnergyCostX)
        {
            if (IsMutable)
            {
                return Owner.PlayerCombatState?.Energy ?? 0;
            }
            else
            {
                return 0;
            }
        }

        // Canonical cards are unaffected by modifiers.
        if (!IsMutable) return CurrentEnergyCost;

        // Call different hooks depending on if this card is in combat or not.
        if (Pile is { IsCombatPile: true })
        {
            return (int)HookBus.Instance.ModifyEnergyCostInCombat(this, CurrentEnergyCost);
        }
        else
        {
            return (int)HookBus.Instance.ModifyEnergyCostOutOfCombat(this, CurrentEnergyCost);
        }
    }

    /// <summary>
    /// Get this card's current star cost, including all modifiers.
    /// Usually, this will just be the same as CurrentStarCost, but there are 2 exceptions:
    /// 1. X-cost cards will return the amount of stars that will be spent to play them instead of a 0 placeholder value.
    /// 2. Effects that modify star costs will be reflected here.
    /// </summary>
    /// <returns>Current energy cost including modifiers.</returns>
    public int GetStarCostWithModifiers()
    {
        if (HasStarCostX)
        {
            return Owner.PlayerCombatState?.Stars ?? 0;
        }

        if (Pile is { IsCombatPile: true })
        {
            return (int)HookBus.Instance.ModifyStarCost(this, CurrentStarCost);
        }
        else
        {
            return CurrentStarCost;
        }
    }

    /// <summary>
    /// Does this card have an energy or star cost?
    /// </summary>
    /// <param name="includeModifiers">
    /// Whether to include modifiers in the cost.
    /// See <see cref="GetEnergyCostWithModifiers"/> for details.
    /// </param>
    public bool CostsEnergyOrStars(bool includeModifiers)
    {
        if (includeModifiers)
        {
            if (!HasEnergyCostX && GetEnergyCostWithModifiers() > 0) return true;
            if (!HasStarCostX && GetStarCostWithModifiers() > 0) return true;
        }
        else if (CurrentEnergyCost > 0 || CurrentStarCost > 0)
        {
            return true;
        }

        return false;
    }

    public void RemoveFromCurrentPile()
    {
        AssertMutable();
        Pile?.RemoveInternal(this);
    }

    public void EndOfPlayerTurnCleanup()
    {
        ExhaustOnNextPlay = false;
        HasSingleTurnRetain = false;

        CardCmd.RemoveKeyword(this, CardKeyword.TemporaryEthereal);

        if (_temporaryEnergyCosts.RemoveAll(c => c.ClearsWhenTurnEnds) > 0)
        {
            EnergyCostChanged?.Invoke();
        }

        if (_temporaryStarCosts.RemoveAll(c => c.ClearsWhenTurnEnds) > 0)
        {
            StarCostChanged?.Invoke();
        }
    }

    public virtual void AfterTransformedFrom() { }

    public virtual void AfterTransformedTo() { }

    // Hooks

    /// <summary>
    /// Override this method for when this card is played.
    /// </summary>
    /// <param name="choiceContext"></param>
    /// <param name="target">Creature that this card is targeting. Should be null for un-targeted cards.</param>
    protected virtual Task OnPlay(PlayerChoiceContext choiceContext, Creature? target) => Task.CompletedTask;

    /// <summary>
    /// Override this method to add VFX that should run the moment the mouse is released when playing this card.
    /// This is different from OnPlay, which is meant for game logic (dealing damage, gaining block, etc.), and won't
    /// run until the card play action is dequeued from the action queue.
    /// WARNING: Don't put any game logic in here! It might do things you don't expect.
    /// </summary>
    /// <param name="target">Creature that this card is targeting. Should be null for un-targeted cards.</param>
    public virtual Task OnEnqueuePlayVfx(Creature? target) => Task.CompletedTask;

    /// <summary>
    /// Override this method with logic for when this card is upgraded.
    /// </summary>
    protected virtual void OnUpgrade() { }

    /// <summary>
    /// Override this property to run this card's <see cref="OnTurnEndInHand"/> when the player ends the turn with this
    /// card in their hand.
    /// </summary>
    public virtual bool HasTurnEndInHandEffect => false;

    /// <summary>
    /// Override this method for when the player ends the turn with this card in their hand.
    /// NOTES:
    /// * You must also override <see cref="HasTurnEndInHandEffect"/> to return true.
    /// * While this method is being run, this card will be in the Play pile.
    /// * After this method is run, this card will be added to the Discard pile.
    /// </summary>
    public virtual Task OnTurnEndInHand() => Task.CompletedTask;

    public bool CanPlayTargeting(Creature? target)
    {
        if (!IsValidTarget(target)) return false;

        return CanPlay();
    }

    public bool CanPlay()
    {
        return CanPlay(out _, out _);
    }

    // TODO: Handle ignore unplayable.
    // TODO: Handle free card plays.
    public bool CanPlay(out UnplayableReason reason, out AbstractModel? preventer)
    {
        reason = UnplayableReason.None;

        if (Keywords.Contains(CardKeyword.Unplayable))
        {
            reason |= UnplayableReason.HasUnplayableKeyword;
        }

        if (!Owner.PlayerCombatState!.HasEnoughResourcesFor(this, out UnplayableReason resourceReason))
        {
            reason |= resourceReason;
        }

        if (TargetPlayer == UiTargetPlayer.Ally && Owner.Creature.CombatState!.PlayerCreatures.Count(c => c.IsAlive) <= 1)
        {
            reason |= UnplayableReason.NoLivingAllies;
        }

        if (!HookBus.Instance.ShouldPlay(this, out preventer, AutoPlayType.None))
        {
            reason |= UnplayableReason.BlockedByHook;
        }

        if (!IsPlayable)
        {
            reason |= UnplayableReason.BlockedByCardLogic;
        }

        return reason == UnplayableReason.None;
    }

    public bool IsValidTarget(Creature? target)
    {
        // If there's no target, it's valid unless the card's target type is "Any".
        if (target == null) return TargetEnemy != UiTargetEnemy.Any;

        if (!target.IsAlive) return false;

        // if its supposed to target an enemy, then the target creature cannot be on the same side
        if (TargetEnemy == UiTargetEnemy.Any) return target.Side != Owner.Creature.Side;

        // If the target type is another player, then the target creature must be on the same side.
        return TargetPlayer == UiTargetPlayer.Ally && target.Side == Owner.Creature.Side;
    }

    // TODO: Handle ignore unplayable.
    // TODO: Handle free card plays.
    public bool TryManualPlay(Creature? target)
    {
        // TODO: CanPlayTargeting should take an out IHookListener arg so we can allow blocking listeners to show a
        // speech bubble or whatever.
        if (CanPlayTargeting(target))
        {
            EnqueueManualPlay(target);
            return true;
        }
        else
        {
            // TODO: CardPlayBlocked?.Invoke(this, blocker) (no awaits here, just show a speech bubble for Normality)
            return false;
        }
    }

    private void EnqueueManualPlay(Creature? target)
    {
        TaskHelper.RunSafely(OnEnqueuePlayVfx(target));

        // TODO: Set a property on this card to prevent it from being played twice before the first one is dequeued.
        // Don't do this by checking what pile the card is in or anything, since there could be cases where we want to
        // be able to play it from piles other than your hand.
        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(new PlayCardAction(this, target));
    }

    public async Task SpendResources()
    {
        int currentEnergy = Owner.PlayerCombatState!.Energy;
        int energyToSpend = Math.Max(0, GetEnergyCostWithModifiers());
        int starsToSpend = Math.Max(0, GetStarCostWithModifiers());

        if (energyToSpend > currentEnergy && HookBus.Instance.ShouldPayExcessEnergyCostWithStars(Owner))
        {
            // For now, we assume that the only type of conversion we do is 1 energy -> 2 stars. If we ever have other
            // types of conversions, we can throw another hook in here.
            starsToSpend += (energyToSpend - currentEnergy) * 2;
            energyToSpend = currentEnergy;
        }

        await SpendEnergy(energyToSpend);
        await SpendStars(starsToSpend);
    }

    private Task SpendEnergy(int amount)
    {
        // Dupes keep the energy spent of their parent
        // (e.g. One-Two Punch on X-energy-cost card)
        if (!IsDupe)
        {
            LastEnergySpent = amount;
        }

        if (amount > 0)
        {
            Owner.PlayerCombatState!.LoseEnergy(Math.Max(0, amount));
        }

        return Task.CompletedTask;
    }

    private async Task SpendStars(int amount)
    {
        // Dupes keep the stars spent of their parent
        // (e.g. One-Two Punch on X-star-cost card)
        if (!IsDupe)
        {
            LastStarsSpent = amount;
        }

        if (amount > 0)
        {
            Owner.PlayerCombatState!.LoseStars(amount);
            await HookBus.Instance.AfterStarsSpent(amount, Owner);
        }
    }

    public async Task OnPlayWrapper(PlayerChoiceContext choiceContext, Creature? target)
    {
        // Push model to choice context to allow UI to display this card in the case of a player choice
        choiceContext.PushModel(this);

        await CombatManager.Instance.WaitForUnpause();

        CurrentTarget = target;
        await CardPileCmd.Add(this, CardPileTarget.Play);

        // If we are a power, we want to play our special VFX before activating the card effects. This would normally
        // occur in CardPileCmd.Add, but we need this to occur before card play, not after
        if (Type == CardType.Power)
        {
            NCard? node = NCard.FindOnTable(this);

            if (node != null)
            {
                NCardFlyPowerVfx swoosh = NCardFlyPowerVfx.Create(node)!;
                NCombatRoom.Instance?.CombatVfxContainer.AddChildSafely(swoosh);

                // This takes too long in fast mode, so we kick it off but use our own wait
                _ = TaskHelper.RunSafely(swoosh.PlayAnim());

                float duration = swoosh.GetDuration();
                await Cmd.CustomScaledWait(duration * 0.2f, duration, duration);
            }
        }

        int playCount = ReplayCount + 1;
        int cardPlayCount = HookBus.Instance.ModifyCardPlayCount(this, playCount, CurrentTarget, out List<AbstractModel> modifiers);
        await HookBus.Instance.AfterModifyingCardPlayCount(this, modifiers);

        for (int i = 0; i < cardPlayCount; i++)
        {
            // If the card is being played multiple times, do a scale bounce to indicate further plays.
            if (i > 0)
            {
                NCard? cardNode = NCard.FindOnTable(this);

                if (cardNode != null)
                {
                    await cardNode.AnimMultiCardPlay();
                }
            }

            await HookBus.Instance.BeforeCardPlayed(this, target, cardPlayCount);
            CombatManager.Instance.History.CardPlayStarted(this, target, i);

            await OnPlay(choiceContext, target);
            InvokeExecutionFinished();

            // If the card has an enchantment, run its logic.
            if (Enchantment != null)
            {
                await Enchantment.OnPlay(choiceContext, target);
                Enchantment.InvokeExecutionFinished();
            }

            // If the card has an affliction, run its logic.
            if (Affliction != null)
            {
                // Assign to a var in case it's removed while running.
                AfflictionModel affliction = Affliction;
                await affliction.OnPlay(choiceContext, target);
                affliction.InvokeExecutionFinished();
            }

            // We can move this out of here and split it up among the callers if we need to distinguish between different
            // types of plays (auto vs. manual, etc.).
            CombatManager.Instance.History.CardPlayFinished(this, target, i);

            if (CombatManager.Instance.IsInProgress)
            {
                await HookBus.Instance.AfterCardPlayed(choiceContext, this, target, cardPlayCount);
            }
        }

        await MoveCardToResultPileAfterPlay(choiceContext);
        await CombatManager.Instance.CheckForEmptyHand(choiceContext, Owner);

        // After we're fully done playing the card, clear any temporary energy/star costs.
        // Note: Even effects that say the card "costs N this _turn_" should clear when the card is played.
        // This is a niche effect (it only matters if you can get a played card back into your hand on the same turn) so
        // it's not worth putting in descriptions.
        if (_temporaryEnergyCosts.RemoveAll(c => c.ClearsWhenCardIsPlayed) > 0)
        {
            EnergyCostChanged?.Invoke();
        }

        if (_temporaryStarCosts.RemoveAll(c => c.ClearsWhenCardIsPlayed) > 0)
        {
            StarCostChanged?.Invoke();
        }

        CurrentTarget = null;
        Played?.Invoke();

        choiceContext.PopModel(this);
    }

    /// <summary>
    /// Send the card to the correct pile after being played.
    /// </summary>
    public async Task MoveCardToResultPileAfterPlay(PlayerChoiceContext choiceContext)
    {
        // If something already moved it out of the play pile, don't worry about moving it. (ex: Particle Wall)
        if (Pile?.Type != CardPileTarget.Play) return;

        if (IsDupe || Type == CardType.Power)
        {
            await CardPileCmd.RemoveFromCombat(this);
        }
        else if (ExhaustOnNextPlay || Keywords.Contains(CardKeyword.Exhaust))
        {
            await CardCmd.Exhaust(choiceContext, this);
        }
        else
        {
            await CardPileCmd.Add(this, CardPileTarget.Discard);
        }
    }

    /// <summary>
    /// Send the card to the correct pile after it was attempted to be played while unplayable.
    /// This is the same as MoveCardToResultPileAfterPlay with one important exception: Power cards do not get sent
    /// to Limbo, and instead get sent to the discard.
    /// </summary>
    public async Task MoveCardToResultPileWithoutPlaying(PlayerChoiceContext choiceContext)
    {
        // If something already moved it out of the play pile, don't worry about moving it. (ex: Particle Wall)
        if (Pile?.Type != CardPileTarget.Play) return;

        if (IsDupe)
        {
            await CardPileCmd.RemoveFromCombat(this);
        }
        else if (ExhaustOnNextPlay || Keywords.Contains(CardKeyword.Exhaust))
        {
            await CardCmd.Exhaust(choiceContext, this);
        }
        else
        {
            await CardPileCmd.Add(this, CardPileTarget.Discard);
        }
    }

    /// <summary>
    /// WARNING: If you're thinking of calling this from inside a model, you probably want
    /// <see cref="CardCmd.Upgrade(CardModel, CardPreviewStyle)"/> instead.
    ///
    /// Upgrade this card. This does not run any hooks.
    /// </summary>
    public void UpgradeInternal()
    {
        AssertMutable();

        CurrentUpgradeLevel++;
        OnUpgrade();
        Upgraded?.Invoke();
    }

    /// <summary>
    /// Finalize an upgrade after calling UpgradeInternal. This clears out state that is used for displaying an upgrade
    /// preview.
    /// </summary>
    public void FinalizeUpgradeInternal()
    {
        DynamicVars.FinalizeUpgrade();
        WasEnergyCostJustUpgraded = false;
        WasStarCostJustUpgraded = false;
    }

    public void DowngradeInternal()
    {
        AssertMutable();

        CurrentUpgradeLevel = 0;

        // We don't use ICardScope here because we're only creating this card in order to grab base values off it,
        // then it's thrown away forever and no hooks are run on it.
        CardModel baseCard = ModelDb.GetById<CardModel>(Id).ToMutable();
        _dynamicVars = baseCard.DynamicVars.Clone();
        _baseEnergyCost = baseCard.CurrentEnergyCost;
        _baseStarCost = baseCard.CurrentStarCost;
        _keywords = baseCard.Keywords.ToHashSet();

        // This should run before re-running Enchantment/Affliction application logic, since
        // those should run on a "clean" card.
        AfterDowngraded();

        // Re-apply the Enchantment, since resetting to base values can override its effects (for example, Soul's Power).
        Enchantment?.ModifyCard();

        // Re-apply the Affliction, since resetting to base values can override its effects.
        Affliction?.AfterApplied();

        Upgraded?.Invoke();
    }

    /// <summary>
    /// Gives cards a chance to add extra "cleanup" logic for downgrades.
    /// No-op by default.
    /// </summary>
    protected virtual void AfterDowngraded() { }

    public void InvokeDrawn() => Drawn?.Invoke();

    public CardModel CreateDupe()
    {
        // Never create dupes of dupes.
        if (IsDupe) return DupeOf!.CreateDupe();

        AssertMutable();

        CardModel dupe = CardScope!.CloneCard(this);
        dupe.DupeOf = this;
        dupe.RemoveKeyword(CardKeyword.Exhaust);

        return dupe;
    }

    public override bool ShouldReceiveCombatHooks => Pile is { IsCombatPile: true };

    /// <summary>
    /// Hook to give cards a heads-up when a game event happens that should cause them to recalculate values.
    /// Be careful not to trigger another RecalculateValues() call in here - very easy to create an infinite loop!
    /// </summary>
    public virtual void RecalculateValues() { }

    /// <summary>
    /// Hook to give cards a heads-up when targeting a creature causes them to recalculate values.
    /// </summary>
    /// <param name="target"></param>
    public virtual void RecalculateValues(Creature target)
    {
        RecalculateValues();
    }

    public SerializableCard ToSerializable()
    {
        AssertMutable();

        return new SerializableCard
        {
            Id = Id,
            CurrentUpgradeLevel = CurrentUpgradeLevel,
            Props = SavedProperties.From(this),
            Enchantment = Enchantment?.ToSerializable(),
            FloorAddedToDeck = FloorAddedToDeck
        };
    }

    /// <summary>
    /// Create a CardModel from a SerializableCard.
    /// Be careful calling this! Make sure all callers eventually use <see cref="ICardScope"/> to add the card to the
    /// correct scope.
    /// </summary>
    public static CardModel FromSerializable(SerializableCard save)
    {
        CardModel card;

        try
        {
            card = ModelDb.GetById<CardModel>(save.Id!).ToMutable();
        }
        catch (ModelNotFoundException)
        {
            card = ModelDb.Card<DeprecatedCard>().ToMutable();
        }

        save.Props?.Fill(card);

        if (save.FloorAddedToDeck != null)
        {
            card.FloorAddedToDeck = save.FloorAddedToDeck;
        }

        card.AfterDeserialized();

        // If we loaded a deprecated card, it cannot be enchanted nor upgraded
        if (card is not DeprecatedCard)
        {
            if (save.Enchantment != null)
            {
                // Use internal so we don't trigger any hooks.
                card.EnchantInternal(EnchantmentModel.FromSerializable(save.Enchantment), save.Enchantment.Amount);
                card.Enchantment!.ModifyCard();
                card.FinalizeUpgradeInternal();
            }

            for (int i = 0; i < save.CurrentUpgradeLevel; i++)
            {
                // Use internal so we don't trigger any hooks.
                card.UpgradeInternal();
                card.FinalizeUpgradeInternal();
            }
        }

        return card;
    }

    public override int CompareTo(AbstractModel? other)
    {
        if (ReferenceEquals(this, other)) return 0;
        if (ReferenceEquals(null, other)) return 1;

        // First, compare by ID.
        int baseCmp = base.CompareTo(other);
        if (baseCmp != 0) return baseCmp;

        CardModel otherCard = (CardModel)other;

        // Tiebreaker 1: Upgrade level.
        int upgradeCmp = CurrentUpgradeLevel.CompareTo(otherCard.CurrentUpgradeLevel);
        if (upgradeCmp != 0) return upgradeCmp;

        // Tiebreaker 2: Enchantment.
        // TODO: Enchantments
        // if (Enchantment != null && otherCard.Enchantment != null)
        // {
        //     int enchantmentCmp = Enchantment.CompareTo(otherCard.Enchantment);
        //     if (enchantmentCmp != 0) return enchantmentCmp;
        // }

        // At this point, consider the two cards equal.
        return 0;
    }

    public virtual IEnumerable<string> AllPortraitPaths
    {
        get
        {
            string path;

            // TODO: Get rid of these HasX calls once we have art for everything, since they do slow filesystem lookups.
            if (HasPackedPortrait)
            {
                path = PortraitPath;
            }
            else if (HasPortrait)
            {
                Log.Debug($"Portrait is not packed for {Title}");
                path = PortraitFallbackPath;
            }
            else if (HasPackedBetaPortrait)
            {
                Log.Debug($"Portrait is beta for {Title}");
                path = BetaPortraitPath;
            }
            else if (HasBetaPortrait)
            {
                Log.Debug($"Portrait is beta and not packed for {Title}");
                path = BetaPortraitFallbackPath;
            }
            else
            {
                Log.Debug($"Portrait is missing for {Title}");
                path = Rarity != CardRarity.Ancient ? MissingPortraitPath : AncientMissingPortraitPath;
            }

            return [path];
        }
    }

    public virtual IEnumerable<string> AssetPaths => BaseAssetPaths.Concat(ExtraAssetPaths);

    private IEnumerable<string> BaseAssetPaths
    {
        get
        {
            List<string> paths =
            [
                PortraitBorderPath,
                Pool.FrameMaterialPath,
                FramePath,
                EnergyIconPath,
                BannerMaterialPath
            ];

            paths.AddRange(AllPortraitPaths);

            if (HasBuiltInOverlay)
            {
                paths.Add(OverlayPath);
            }

            return paths;
        }
    }

    protected virtual IEnumerable<string> ExtraAssetPaths => [];
}
