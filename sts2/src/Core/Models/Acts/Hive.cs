using System.Collections.Generic;
using Godot;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Models.Acts;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Hive : ActModel
{
    public override IEnumerable<EncounterModel> Encounters =>
    [
        ModelDb.Encounter<BowlbugsNormal>(),
        ModelDb.Encounter<BowlbugsWeak>(),
        ModelDb.Encounter<ChompersNormal>(),
        ModelDb.Encounter<DecimillipedeElite>(),
        ModelDb.Encounter<EntomancerElite>(),
        ModelDb.Encounter<ExoskeletonsNormal>(),
        ModelDb.Encounter<HunterKillerNormal>(),
        ModelDb.Encounter<InfestedBoss>(),
        ModelDb.Encounter<InfestedPrismsElite>(),
        ModelDb.Encounter<KnowledgeDemonBoss>(),
        ModelDb.Encounter<LouseProgenitorNormal>(),
        ModelDb.Encounter<MytesNormal>(),
        ModelDb.Encounter<OvicopterNormal>(),
        ModelDb.Encounter<SlumberingBeetleNormal>(),
        ModelDb.Encounter<SpinyToadNormal>(),
        ModelDb.Encounter<TheInsatiableBoss>(),
        ModelDb.Encounter<TheObscuraNormal>(),
        ModelDb.Encounter<ThievingHopperWeak>(),
        ModelDb.Encounter<TunnelerNormal>(),
        ModelDb.Encounter<TunnelerWeak>(),
    ];

    public override IEnumerable<AncientEventModel> UniqueAncients =>
    [
        ModelDb.AncientEvent<Orobas>(),
        ModelDb.AncientEvent<Pael>(),
        ModelDb.AncientEvent<Tezcatara>()
    ];

    public override IEnumerable<EventModel> Events =>
    [
        ModelDb.Event<AmalgamatingAnvil>(), // TODO: make sure we add this to alt act 2 as well
        ModelDb.Event<BedlamBeacon>(),
        ModelDb.Event<Brainbug>(),
        ModelDb.Event<Bugslayer>(),
        ModelDb.Event<ColorfulPhilosophers>(),
        ModelDb.Event<InfestedAutomaton>(),
        ModelDb.Event<LivingCocoon>(),
        ModelDb.Event<TelepathicSpiders>(),
        ModelDb.Event<TheLanternKey>(),
        ModelDb.Event<FieldOfManSizedHoles>(),
    ];

    protected override int NumberOfWeakEncounters => 2;
    public override int NumberOfRooms => 14;

    public override string[] BgMusicOptions =>
    [
        "event:/music/act2_a1_v2",
        "event:/music/act2_a2_v2"
    ];

    public override string AmbientSfx => "event:/sfx/ambience/act2_ambience";

    // Replace once new naming convention comes into place for alternate acts
    public override string ChestSpineResourcePath => $"res://animations/backgrounds/treasure_room/chest_room_act_2_skel_data.tres";

    public override void ApplyDiscoveryOrderModifications(int index)
    {
        // TODO: manage discovery order for bosses and ancients
        // See https://app.clickup.com/10583894/v/dc/a2zup-2325/a2zup-26314.
    }

    #region Map

    public override Color MapTraveledColor => new("27221C");
    public override Color MapUntraveledColor => new("6E7750");
    public override Color MapBgColor => new("9B9562");

    public override MapPointTypeCounts GetMapPointTypes(Rng mapRng)
    {
        // We use this clone Rng so that we can temporarily peek ahead to get the MapPointType counts without altering the counter
        Rng cloneRng = new(mapRng.Seed, mapRng.Counter);
        MapPointTypeCounts counts = new(cloneRng);

        // Create new instance with modified Unknown count
        return new MapPointTypeCounts(mapRng)
        {
            // This act should have 1 less than the standard
            NumOfUnknowns = counts.NumOfUnknowns - 1
        };
    }

    #endregion
}
