using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GamblingChip : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    public override async Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return;
        if (Owner.Creature.CombatState!.RoundNumber > 1) return;

        List<CardModel> selection = (await CardSelectCmd.FromHandForDiscard(
            choiceContext,
            Owner,
            new CardSelectorPrefs(SelectionScreenPrompt, 0, Really.bigNumber),
            null,
            this
        )).ToList();
        if (selection.Count == 0) return;

        await CardCmd.DiscardAndDraw(choiceContext, selection, selection.Count);
    }
}
