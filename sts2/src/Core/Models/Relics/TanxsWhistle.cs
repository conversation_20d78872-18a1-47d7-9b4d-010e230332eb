using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TanxsWhistle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;
    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromCard<Whistle>()];

    public override async Task AfterObtained()
    {
        CardModel brightestFlame = Owner.ClimbState.CreateCard<Whistle>(Owner);
        CardPileAddResult result = await CardPileCmd.Add(brightestFlame, CardPileTarget.Deck);
        CardCmd.PreviewCardPileAdd([result], 2f);
    }
}
