using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RollingSphere : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(1)
    ];


    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (!CombatManager.Instance.IsInProgress) return;
        if (card.Type != CardType.Power) return;

        await CardPileCmd.Draw(context, DynamicVars.Cards.BaseValue, Owner);
    }
}
