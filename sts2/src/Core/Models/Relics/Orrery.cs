using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Orrery : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(5)
    ];

    public override async Task AfterObtained()
    {
        CardReward[] rewards = new CardReward[DynamicVars.Cards.IntValue];

        for (int i = 0; i < rewards.Length; i++)
        {
            rewards[i] = new CardReward(CardCreationSource.RegularEncounter, 3, Owner);
        }

        await RewardsCmd.Offer(Owner, rewards, false);
    }
}
