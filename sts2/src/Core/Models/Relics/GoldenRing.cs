using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Enchantments;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GoldenRing : RelicModel
{
    private const string _momentumKey = "Momentum";
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_momentumKey, 5)
    ];

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => HoverTipFactory.FromEnchantment<Momentum>(DynamicVars[_momentumKey].IntValue);

    public override async Task AfterObtained()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.EnchantSelectionPrompt, 1);

        Momentum canonicalMomentum = ModelDb.Enchantment<Momentum>();
        IEnumerable<CardModel> cards = await CardSelectCmd.FromDeckForEnchantment(Owner, canonicalMomentum, DynamicVars[_momentumKey].IntValue, prefs);

        foreach (CardModel card in cards)
        {
            CardCmd.Enchant(canonicalMomentum.ToMutable(), card, DynamicVars[_momentumKey].IntValue);
            CardCmd.Preview(card);
        }
    }
}
