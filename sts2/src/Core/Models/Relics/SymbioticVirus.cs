using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Orbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SymbioticVirus : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    private const string _darknessKey = "Dark";


    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_darknessKey, 1)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips =>
    [
        HoverTipFactory.Static(StaticHoverTip.Channeling),
        HoverTipFactory.FromOrb<DarkOrb>()
    ];

    public override async Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return;
        if (Owner.Creature.CombatState!.RoundNumber > 1) return;

        for (int i = 0; i < DynamicVars[_darknessKey].BaseValue; i++)
        {
            await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), Owner);
        }
    }
}
