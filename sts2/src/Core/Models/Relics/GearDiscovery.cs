using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GearDiscovery : BaseDiscoveryRelic
{
    public override CharacterModel Character => ModelDb.Character<Characters.Defect>();

    public override async Task AfterObtained()
    {
        await base.AfterObtained();

        if (Owner.BaseOrbSlotCount > 0) return;

        OrbCmd.IncreaseBaseOrbCount(Owner, 1);
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        await base.AfterRoomEntered(room);

        if (room is not CombatRoom) return;
        if (Owner.BaseOrbSlotCount > 0) return;

        await OrbCmd.AddSlots(Owner, 1);
    }
}
