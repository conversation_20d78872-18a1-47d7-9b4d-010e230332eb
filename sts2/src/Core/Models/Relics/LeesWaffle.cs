using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LeesWaffle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new MaxHpVar(7)
    ];

    public override async Task AfterObtained()
    {
        Creature creature = Owner.Creature;
        await CreatureCmd.GainMaxHp(creature, DynamicVars.MaxHp.BaseValue);
        await CreatureCmd.Heal(creature, creature.MaxHp - creature.CurrentHp);
    }
}