using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class VitruvianMinion : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromKeyword(CardKeyword.Exhaust)];


    // NOTE: all minion cards are generated. If we ever get minion cards in your permanent deck
    // we will have to revise this a bit.
    public override Task AfterCardEnteredCombat(CardModel card)
    {
        if (card.Owner != Owner) return Task.CompletedTask;
        if (!card.Tags.Contains(CardTag.Minion)) return Task.CompletedTask;
        if (!card.Keywords.Contains(CardKeyword.Exhaust)) return Task.CompletedTask;

        CardCmd.RemoveKeyword(card, CardKeyword.Exhaust);

        Flash();
        return Task.CompletedTask;
    }
}
