using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class IceCream : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    public override bool ShouldPlayerResetEnergy(Player player)
    {
        // Skip doing this on first turn
        if (player.Creature.CombatState!.RoundNumber == 1) return true;
        if (player != Owner) return true;

        return false;
    }
}
