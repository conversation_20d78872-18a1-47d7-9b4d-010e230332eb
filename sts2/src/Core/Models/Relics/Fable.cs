using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Fable : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    // Records cards we add to the deck so that we don't loop
    private readonly HashSet<CardModel> _cardsToSkip = [];

    public override async Task AfterCardChangedPiles(CardModel card, CardPileTarget oldPileType, AbstractModel? source)
    {
        if (oldPileType != CardPileTarget.None || card.Pile?.Type != CardPileTarget.Deck) return;
        if (card.Owner != Owner) return;
        if (source != null) return; // This was already cloned by another piece abstract model (ie hoarder), so don't clone it

        // Ensure that the card was not added by Hoarder
        if (_cardsToSkip.Contains(card))
        {
            _cardsToSkip.Remove(card);
            return;
        }

        Flash();
        CardModel clone = Owner.ClimbState.CloneCard(card);
        _cardsToSkip.Add(clone);
        CardPileAddResult result = await CardPileCmd.Add(clone, CardPileTarget.Deck, CardPilePosition.Bottom, this);
        CardCmd.PreviewCardPileAdd(result);
    }
}
