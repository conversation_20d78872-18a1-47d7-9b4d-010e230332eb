using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class MyMantle : RelicModel
{
    public override bool ShowCounter => true;
    public override int DisplayAmount => DynamicVars.Stars.IntValue - StarsSpent % DynamicVars.Stars.IntValue;

    private int _starsSpent;

    // We serialize this since it should carry over between combats.
    [SavedProperty]
    public int StarsSpent
    {
        get => _starsSpent;
        set
        {
            AssertMutable();
            _starsSpent = value;

            int starThreshold = DynamicVars.Stars.IntValue;
            Status = StarsSpent == starThreshold - 1 ? RelicStatus.Active : RelicStatus.Normal;

            InvokeDisplayAmountChanged();
        }
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StarsVar(10),
        new BlockVar(5, BlockProps.nonCardUnpowered)
    ];

    public override async Task AfterStarsSpent(int amount, Player spender)
    {
        if (spender != Owner) return;

        StarsSpent += amount;

        if (StarsSpent >= DynamicVars.Stars.IntValue)
        {
            Flash();
            await CreatureCmd.GainBlock(Owner.Creature, Mathf.FloorToInt((float)StarsSpent / DynamicVars.Stars.IntValue) * DynamicVars.Block.IntValue, BlockProps.nonCardUnpowered, null);
            StarsSpent %= DynamicVars.Stars.IntValue;
        }
        InvokeDisplayAmountChanged();
    }
}
