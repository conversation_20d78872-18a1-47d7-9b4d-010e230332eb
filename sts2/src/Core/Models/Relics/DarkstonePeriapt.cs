using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class DarkstonePeriapt : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new MaxHpVar(6)
    ];

    public override async Task AfterCardChangedPiles(CardModel card, CardPileTarget oldPileType, AbstractModel? source)
    {
        if (oldPileType != CardPileTarget.None) return;
        if (card.Pile?.Type != CardPileTarget.Deck) return;
        if (card.Owner != Owner) return;
        if (card.Type != CardType.Curse) return;

        Flash();
        await CreatureCmd.GainMaxHp(Owner.Creature, DynamicVars.MaxHp.BaseValue);
    }
}
