using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class OrnamentalFan : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    public override bool ShowCounter => true;
    public override int DisplayAmount => DynamicVars.Cards.IntValue - AttacksPlayedThisTurn % DynamicVars.Cards.IntValue;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3),
        new BlockVar(4, BlockProps.nonCardUnpowered)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.Block)];

    private int _attacksPlayedThisTurn;

    private int AttacksPlayedThisTurn
    {
        get => _attacksPlayedThisTurn;
        set
        {
            AssertMutable();
            _attacksPlayedThisTurn = value;

            int threshold = DynamicVars.Cards.IntValue;
            Status = AttacksPlayedThisTurn % threshold == threshold - 1 ? RelicStatus.Active : RelicStatus.Normal;

            InvokeDisplayAmountChanged();
        }
    }

    // Use BeforeTurnStart instead of AfterTurnStart so that effects like Hellraiser can proc it
    public override Task BeforeTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return Task.CompletedTask;

        AttacksPlayedThisTurn = 0;
        Status = RelicStatus.Normal;

        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (!CombatManager.Instance.IsInProgress) return;
        if (card.Type != CardType.Attack) return;

        AttacksPlayedThisTurn++;

        int threshold = DynamicVars.Cards.IntValue;

        if (AttacksPlayedThisTurn % threshold == 0)
        {
            Flash();
            await CreatureCmd.GainBlock(Owner.Creature, DynamicVars.Block, null);
        }
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        Status = RelicStatus.Normal;
        return Task.CompletedTask;
    }
}
