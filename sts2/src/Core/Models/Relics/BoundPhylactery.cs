using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BoundPhylactery : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Starter;
    public override bool SpawnsPets => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new SummonVar(5)
    ];

    private int _summonAmount;

    [SavedProperty]
    public int SummonAmount
    {
        get => DynamicVars.Summon.IntValue;
        private set
        {
            AssertMutable();
            if (_summonAmount == value) return;

            _summonAmount = value;
            DynamicVars.Summon.BaseValue = _summonAmount;
        }
    }

    protected override void DeepCloneFields()
    {
        base.DeepCloneFields();

        _summonAmount = DynamicVars.Summon.IntValue;
    }

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.Static(StaticHoverTip.SummonDynamic, DynamicVars.Summon)];

    public override async Task AfterObtained()
    {

        if (!CombatManager.Instance.IsInProgress) return;
        await SummonPet();
    }

    public override async Task BeforeCombatStart() => await SummonPet();

    private async Task SummonPet()
    {
        await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), Owner, DynamicVars.Summon.BaseValue, this);
    }
}
