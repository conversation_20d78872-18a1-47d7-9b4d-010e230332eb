using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class StoneCalendar : RelicModel
{
    private const string _damageTurnKey = "DamageTurn";

    public override RelicRarity Rarity => RelicRarity.Rare;

    public override bool ShowCounter => DisplayAmount > -1;

    public override int DisplayAmount
    {
        get
        {
            if (!CombatManager.Instance.IsInProgress) return -1;

            // Can't do anything after it's been activated.
            int roundNumber = Owner.Creature.CombatState!.RoundNumber;
            if (roundNumber > DynamicVars[_damageTurnKey].IntValue) return -1;

            return DynamicVars[_damageTurnKey].IntValue - roundNumber;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(52, DamageProps.nonCardUnpowered),
        new(_damageTurnKey, 7)
    ];

    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return Task.CompletedTask;

        if (Owner.Creature.CombatState!.RoundNumber == DynamicVars[_damageTurnKey].IntValue)
        {
            Status = RelicStatus.Active;
        }

        InvokeDisplayAmountChanged();
        return Task.CompletedTask;
    }

    public override async Task BeforeTurnEnd(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return;

        int damageTurn = DynamicVars[_damageTurnKey].IntValue;
        int roundNumber = Owner.Creature.CombatState!.RoundNumber;

        if (roundNumber > damageTurn)
        {
            Status = RelicStatus.Disabled;
        }
        else if (roundNumber < damageTurn)
        {
            Status = RelicStatus.Normal;
        }
        else
        {
            Status = RelicStatus.Disabled;

            Flash();
            await CreatureCmd.Damage(Owner.Creature.CombatState!.HittableEnemies, DynamicVars.Damage, Owner.Creature);
            InvokeDisplayAmountChanged();
        }
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        Status = RelicStatus.Normal;
        InvokeDisplayAmountChanged();

        return Task.CompletedTask;
    }

    public override Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not CombatRoom) return Task.CompletedTask;

        Status = RelicStatus.Normal;
        InvokeDisplayAmountChanged();

        return Task.CompletedTask;
    }
}
