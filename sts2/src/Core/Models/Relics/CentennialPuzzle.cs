using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class CentennialPuzzle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Common;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3)
    ];

    private bool _usedThisCombat;

    public bool UsedThisCombat
    {
        get => _usedThisCombat;
        private set
        {
            if (_usedThisCombat == value) return;
            AssertMutable();

            _usedThisCombat = value;
            Status = _usedThisCombat ? RelicStatus.Disabled : RelicStatus.Normal;
        }
    }

    public override async Task AfterDamageReceived(Creature target, DamageResult result, ValueProp props, Creature? dealer, CardModel? cardSource)
    {
        if (!CombatManager.Instance.IsInProgress) return;
        if (target != Owner.Creature) return;
        if (result.UnblockedDamage <= 0) return;
        if (UsedThisCombat) return;

        UsedThisCombat = true;

        for (int i = 0; i < DynamicVars.Cards.BaseValue; i++)
        {
            await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), Owner);
        }
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        UsedThisCombat = false;
        return Task.CompletedTask;
    }
}