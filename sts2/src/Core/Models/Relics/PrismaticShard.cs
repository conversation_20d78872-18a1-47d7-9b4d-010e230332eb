using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.Event
public sealed class PrismaticShard : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override Task AfterObtained()
    {
        if (Owner.BaseOrbSlotCount > 0) return Task.CompletedTask;

        OrbCmd.IncreaseBaseOrbCount(Owner, 1);
        return Task.CompletedTask;
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        await base.AfterRoomEntered(room);

        if (room is not CombatRoom) return;
        if (Owner.BaseOrbSlotCount > 0) return;

        await OrbCmd.AddSlots(Owner, 1);
    }

    public override IEnumerable<CardModel> ModifyCardRewardCardPool(Player player, IEnumerable<CardModel> options, CardCreationSource source)
    {
        if (Owner != player) return options;
        if (source == CardCreationSource.Custom) return options;

        List<CardModel> newOptions = [];

        List<CardPoolModel> validPools = ModelDb.UnlockedCharacterCardPools.ToList();

        newOptions.AddRange(validPools.SelectMany(pool => pool.Cards));
        return newOptions;
    }
}
