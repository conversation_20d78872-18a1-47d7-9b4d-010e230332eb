using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class GhostSeed : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromKeyword(CardKeyword.Ethereal)];

    public override Task AfterCardEnteredCombat(CardModel card)
    {
        if (!CanAffect(card)) return Task.CompletedTask;

        CardCmd.ApplyKeyword(card, CardKeyword.Ethereal);
        return Task.CompletedTask;
    }

    public override Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not CombatRoom) return Task.CompletedTask;

        IEnumerable<CardModel> cards = Owner.PlayerCombatState!.AllCards;
        foreach (CardModel card in cards)
        {
            if (CanAffect(card))
            {
                CardCmd.ApplyKeyword(card, CardKeyword.Ethereal);
            }
        }

        return Task.CompletedTask;
    }

    public bool CanAffect(CardModel card) => card.Rarity == CardRarity.Basic
        && (card.Tags.Contains(CardTag.Strike) || card.Tags.Contains(CardTag.Defend))
        && !card.Keywords.Contains(CardKeyword.Ethereal);
}
