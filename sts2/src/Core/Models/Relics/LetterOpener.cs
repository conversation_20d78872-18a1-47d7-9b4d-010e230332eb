using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class LetterOpener : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    public override bool ShowCounter => true;
    public override int DisplayAmount => DynamicVars.Cards.IntValue - SkillsPlayedThisTurn % DynamicVars.Cards.IntValue;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3),
        new DamageVar(5, DamageProps.nonCardUnpowered)
    ];

    private int _skillsPlayedThisTurn;

    private int SkillsPlayedThisTurn
    {
        get => _skillsPlayedThisTurn;
        set
        {
            AssertMutable();
            _skillsPlayedThisTurn = value;

            int threshold = DynamicVars.Cards.IntValue;
            Status = SkillsPlayedThisTurn % threshold == threshold - 1 ? RelicStatus.Active : RelicStatus.Normal;

            InvokeDisplayAmountChanged();
        }
    }

    public override Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != Owner.Creature.Side) return Task.CompletedTask;

        SkillsPlayedThisTurn = 0;
        Status = RelicStatus.Normal;

        return Task.CompletedTask;
    }

    public override async Task AfterCardPlayed(PlayerChoiceContext context, CardModel card, Creature? target, int playCount)
    {
        if (card.Owner != Owner) return;
        if (!CombatManager.Instance.IsInProgress) return;
        if (card.Type != CardType.Skill) return;

        SkillsPlayedThisTurn++;

        int threshold = DynamicVars.Cards.IntValue;

        if (SkillsPlayedThisTurn % threshold == 0)
        {
            Flash();
            await CreatureCmd.Damage(Owner.Creature.CombatState!.HittableEnemies, DynamicVars.Damage, Owner.Creature);
        }
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        Status = RelicStatus.Normal;
        return Task.CompletedTask;
    }
}
