using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BookOfFiveRings : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    public override bool ShowCounter => true;
    public override int DisplayAmount => DynamicVars.Cards.IntValue - CardsAdded % DynamicVars.Cards.IntValue;

    private int _cardsAdded;

    [SavedProperty]
    public int CardsAdded
    {
        get => _cardsAdded;
        set
        {
            AssertMutable();

            _cardsAdded = value;
            InvokeDisplayAmountChanged();
        }
    }

    private int CardsAddedSinceLastTrigger => CardsAdded % DynamicVars.Cards.IntValue;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(5),
        new HealVar(15)
    ];

    public override async Task AfterCardChangedPiles(CardModel card, CardPileTarget oldPileType, AbstractModel? source)
    {
        if (Owner.Creature.IsDead) return;
        if (card.Owner != Owner) return;
        if (card.Pile?.Type != CardPileTarget.Deck) return;

        CardsAdded++;

        if (CardsAddedSinceLastTrigger == 0)
        {
            Flash();
            await CreatureCmd.Heal(Owner.Creature, DynamicVars.Heal.BaseValue);
        }
    }
}
