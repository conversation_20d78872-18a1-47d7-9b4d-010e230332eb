using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Helpers.Models;
using MegaCrit.Sts2.Core.Nodes.CommonUi;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ToxicEgg : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Uncommon;

    public override bool TryModifyCardRewardOptionsLate(Player player, List<CardCreationResult> options, CardCreationSource source)
    {
        if (player != Owner) return false;
        EggRelicHelper.UpgradeValidCards(options, CardType.Skill, this);
        return true;
    }

    public override void ModifyMerchantCardCreationResults(Player player, List<CardCreationResult> cards)
    {
        if (player != Owner) return;
        EggRelicHelper.UpgradeValidCards(cards, CardType.Skill, this);
    }

    // This runs in the case that a card is added to the deck that is not from a reward or merchant
    public override bool TryModifyCardBeingAddedToDeck(CardModel card, out CardModel? newCard)
    {
        newCard = null;

        if (card.Owner != Owner) return false;
        if (card.Type != CardType.Skill) return false;
        if (!card.IsUpgradable) return false;

        newCard = Owner.ClimbState.CloneCard(card);
        CardCmd.Upgrade(newCard, CardPreviewStyle.None);
        return true;
    }
}
