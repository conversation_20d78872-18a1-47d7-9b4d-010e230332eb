using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class EmberTea : RelicModel
{
    private const string _combatsKey = "Combats";

    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool IsUsedUp => CombatsLeft <= 0;
    public override bool ShowCounter => true;
    public override int DisplayAmount => Math.Max(0, CombatsLeft);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_combatsKey, CombatsLeft),
        new PowerVar<Strength>(2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    private int _combatsLeft = 4;

    [SavedProperty]
    public int CombatsLeft
    {
        get => _combatsLeft;
        set
        {
            AssertMutable();

            _combatsLeft = value;
            DynamicVars[_combatsKey].BaseValue = _combatsLeft;
            InvokeDisplayAmountChanged();

            if (IsUsedUp)
            {
                Status = RelicStatus.Disabled;
            }
        }
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (IsUsedUp) return;
        if (room is not CombatRoom) return;

        Flash();
        await PowerCmd.Apply<Strength>(Owner.Creature, DynamicVars.Strength.BaseValue, null, null);
        CombatsLeft--;
    }
}
