using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class SeekingTentacle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    public override bool TryModifyRewardsLate(Player player, List<Reward> rewards, AbstractRoom room)
    {
        if (player != Owner) return false;

        foreach (CardReward cardReward in rewards.OfType<CardReward>())
        {
            cardReward.CanReroll = true;
        }

        return true;
    }
}
