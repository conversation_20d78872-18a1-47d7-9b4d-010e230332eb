using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Enchantments;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class OversizedNail : RelicModel
{
    private const string _sharpAmountKey = "SharpAmount";
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_sharpAmountKey, 2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => HoverTipFactory.FromEnchantment<Sharp>(DynamicVars[_sharpAmountKey].IntValue);

    public override bool TryModifyCardRewardOptionsLate(Player player, List<CardCreationResult> options, CardCreationSource source)
    {
        if (player != Owner) return false;

        EnchantValidCards(options);
        return true;
    }

    public override void ModifyMerchantCardCreationResults(Player player, List<CardCreationResult> cards)
    {
        if (player != Owner) return;
        EnchantValidCards(cards);
    }

    // This runs in the case that a card is added to the deck that is not from a reward or merchant
    public override bool TryModifyCardBeingAddedToDeck(CardModel card, out CardModel? newCard)
    {
        newCard = null;

        if (card.Owner != Owner) return false;
        if (!ModelDb.Enchantment<Sharp>().CanEnchant(card)) return false;

        newCard = EnchantCard(card);
        return true;
    }

    private void EnchantValidCards(List<CardCreationResult> options)
    {
        Sharp canonicalSharp = ModelDb.Enchantment<Sharp>();
        foreach (CardCreationResult entry in options)
        {
            CardModel card = entry.Card;

            if (!canonicalSharp.CanEnchant(card)) continue;
            CardModel enchanted = EnchantCard(card);

            entry.ModifyCard(enchanted, this);
        }
    }

    private CardModel EnchantCard(CardModel card)
    {
        CardModel enchanted = Owner.ClimbState.CloneCard(card);
        CardCmd.Enchant<Sharp>(enchanted, DynamicVars[_sharpAmountKey].BaseValue);

        return enchanted;
    }
}
