using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RedSkull : RelicModel
{
    private const string _hpThresholdKey = "HpThreshold";

    public override RelicRarity Rarity => RelicRarity.Common;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_hpThresholdKey, 50),
        new PowerVar<Strength>(3)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    private bool _strengthApplied;

    private bool StrengthApplied
    {
        get => _strengthApplied;
        set
        {
            AssertMutable();
            _strengthApplied = value;
        }
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not CombatRoom) return;

        await ModifyStrengthIfNecessary();
    }

    public override Task AfterCombatEnd(CombatRoom _)
    {
        // Relics persist between combats, so make sure to clean it up before the next combat.
        StrengthApplied = false;
        Status = RelicStatus.Normal;

        return Task.CompletedTask;
    }

    public override async Task AfterCurrentHpChanged(Creature creature, decimal _)
    {
        if (!CombatManager.Instance.IsInProgress) return;

        await ModifyStrengthIfNecessary();
    }

    private async Task ModifyStrengthIfNecessary()
    {
        Creature creature = Owner.Creature;
        bool aboveThreshold = creature.CurrentHp > creature.MaxHp * (DynamicVars[_hpThresholdKey].BaseValue / 100m);

        Status = aboveThreshold ? RelicStatus.Normal : RelicStatus.Active;
        decimal strength = DynamicVars.Strength.BaseValue;

        if (aboveThreshold && StrengthApplied)
        {
            Flash();

            // If the creature's health is above the threshold and strength has been applied, remove it.
            await PowerCmd.Apply<Strength>(creature, -strength, creature, null);
            StrengthApplied = false;
        }
        else if (!aboveThreshold && !StrengthApplied)
        {
            Flash();

            // If the creature's health is below the threshold and strength hasn't been applied yet, apply it.
            await PowerCmd.Apply<Strength>(creature, strength, creature, null);
            StrengthApplied = true;
        }
    }
}
