using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.CommonUi;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FragrantMushroom : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Event;

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(5)
    ];

    public override Task AfterObtained()
    {
        IEnumerable<CardModel> cards = CardPileTarget.Deck.GetPile(Owner).Cards
            .Where(c => c is { IsUpgradable: true })
            .ToList()
            .StableShuffle(Owner.ClimbState.Rng.Niche)
            .Take(DynamicVars.Cards.IntValue);

        foreach (CardModel card in cards)
        {
            CardCmd.Upgrade(card, CardPreviewStyle.MessyLayout);
        }

        return Task.CompletedTask;
    }
}
