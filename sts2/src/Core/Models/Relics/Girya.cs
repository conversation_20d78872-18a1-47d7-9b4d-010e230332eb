using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Entities.RestSite;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Girya : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Rare;

    public override bool ShowCounter => true;

    public override int DisplayAmount => TimesLifted;

    private int _timesLifted;

    [SavedProperty]
    public int TimesLifted
    {
        get => _timesLifted;
        set
        {
            AssertMutable();
            _timesLifted = value;
            InvokeDisplayAmountChanged();
        }
    }

    public const int maxLifts = 3;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Strength>()];

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (TimesLifted <= 0) return;
        if (room is not CombatRoom) return;

        Flash();
        await PowerCmd.Apply<Strength>(Owner.Creature, TimesLifted, Owner.Creature, null);
    }

    public override bool TryModifyRestSiteOptions(Player player, ICollection<RestSiteOption> options)
    {
        if (player != Owner) return false;
        if (TimesLifted >= maxLifts) return false;

        options.Add(new LiftRestSiteOption(player));
        return true;

    }
}
