using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Models.Enchantments;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Replicator : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    protected override IEnumerable<IHoverTip> ExtraHoverTips => HoverTipFactory.FromEnchantment<CloningCapsule>(4);

    public override async Task AfterObtained()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.EnchantSelectionPrompt, 1);
        IEnumerable<CardModel> cards = (await CardSelectCmd.FromDeckForEnchantment(Owner, ModelDb.Enchantment<CloningCapsule>(), 4, prefs));

        foreach (CardModel card in cards)
        {
            CardCmd.Enchant<CloningCapsule>(card, 4);
            CardCmd.Preview(card);
        }
    }
}
