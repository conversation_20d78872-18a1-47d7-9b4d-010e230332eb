using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class UsedNotepad : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Common;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3)
    ];

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (room.RoomType != RoomType.Boss) return;

        Flash();

        List<CardModel> cards = CardPileTarget.Draw.GetPile(Owner).Cards
            .Where(c => c.IsUpgradable)
            .ToList()
            .StableShuffle(Owner.ClimbState.Rng.CombatCardSelection)
            .Take(DynamicVars.Cards.IntValue)
            .ToList();

        CardCmd.Upgrade(cards, CardPreviewStyle.HorizontalLayout);
        CardCmd.Preview(cards);
        await Cmd.CustomScaledWait(0.5f, 1f, 1.2f);
    }
}
