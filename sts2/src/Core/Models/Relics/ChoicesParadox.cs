using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ChoicesParadox : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Ancient;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(5)
    ];

    public override async Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return;
        if (Owner.Creature.CombatState!.RoundNumber != 1) return;

        Flash();

        List<CardModel> mutableCards = CardFactory.GetDistinctForCombat(
            Owner.Character.CardPool.Cards,
            DynamicVars.Cards.IntValue,
            Owner.ClimbState.Rng.CombatCardGeneration
        ).Select(c => Owner.Creature.CombatState.CreateCard(c, Owner)).ToList();

        foreach (CardModel card in mutableCards)
        {
            CardCmd.Upgrade(card);
        }

        IEnumerable<CardModel> cards = await CardSelectCmd.FromSimpleGrid(
            choiceContext,
            mutableCards,
            Owner,
            new CardSelectorPrefs(L10NLookup("CHOICES_PARADOX.selectionScreenPrompt"), 1)
        );

        foreach (CardModel card in cards)
        {
            card.SetToFreeThisTurn();
            await CardPileCmd.AddGeneratedCardToCombat(card, CardPileTarget.Hand, true);
        }
    }
}
