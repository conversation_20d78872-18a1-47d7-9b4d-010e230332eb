using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Toolbox : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new CardsVar(3)
    ];

    public override async Task AfterTurnStart(PlayerChoiceContext choiceContext, CombatSide side)
    {
        if (side != CombatSide.Player) return;
        if (Owner.Creature.CombatState!.RoundNumber != 1) return;

        Flash();

        List<CardModel> canonicalCards = CardFactory.GetDistinctForCombat(
            ModelDb.CardPool<ColorlessCardPool>().Cards,
            DynamicVars.Cards.IntValue,
            Owner.ClimbState.Rng.CombatCardGeneration
        ).ToList();

        CardModel? canonicalCard = await CardSelectCmd.FromChooseACardScreen(choiceContext, canonicalCards, Owner, false);

        if (canonicalCard != null)
        {
            CardModel mutableCard = Owner.Creature.CombatState.CreateCard(canonicalCard, Owner);
            await CardPileCmd.AddGeneratedCardToCombat(mutableCard, CardPileTarget.Hand, true);
        }
    }
}
