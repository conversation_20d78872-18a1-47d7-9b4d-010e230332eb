using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Cauldron : RelicModel
{
    private const string _potionsKey = "Potions";

    private static readonly PotionModel[] _testPotions =
    [
        ModelDb.Potion<FlexPotion>(),
        ModelDb.Potion<WeakPotion>(),
        ModelDb.Potion<VulnerablePotion>(),
        ModelDb.Potion<StrengthPotion>(),
        ModelDb.Potion<DexterityPotion>()
    ];

    public override RelicRarity Rarity => RelicRarity.Shop;

    public override bool HasUponPickupEffect => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_potionsKey, 5)
    ];

    public override async Task AfterObtained()
    {
        await RewardsCmd.Offer(Owner, GenerateRewards(), false);
    }

    private IReadOnlyList<PotionReward> GenerateRewards()
    {
        int potionsCount = DynamicVars[_potionsKey].IntValue;
        PotionReward[] rewards = new PotionReward[potionsCount];

        if (TestMode.IsOn)
        {
            // Test mode, roll specified relics.
            for (int i = 0; i < rewards.Length; i++)
            {
                rewards[i] = new PotionReward(_testPotions[i % potionsCount].ToMutable(), Owner);
            }
        }
        else
        {
            // Game mode, roll random relics.
            for (int i = 0; i < rewards.Length; i++)
            {
                rewards[i] = new PotionReward(Owner);
            }
        }

        return rewards;
    }
}
