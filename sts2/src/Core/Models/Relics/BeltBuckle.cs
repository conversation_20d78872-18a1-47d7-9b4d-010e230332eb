using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Relics;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class BeltBuckle : RelicModel
{
    public override RelicRarity Rarity => RelicRarity.Shop;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new PowerVar<Dexterity>(2)
    ];

    protected override IEnumerable<IHoverTip> ExtraHoverTips => [HoverTipFactory.FromPower<Dexterity>()];

    public override async Task AfterObtained()
    {
        if (!CombatManager.Instance.IsInProgress) return;
        if (Owner.Potions.Any()) return;
        await ApplyDexterity();
    }

    public override async Task AfterRoomEntered(AbstractRoom room)
    {
        if (room is not CombatRoom) return;

        RefreshStatus();
        if (Owner.Potions.Any()) return;

        await ApplyDexterity();
    }

    public override Task AfterCombatEnd(CombatRoom room)
    {
        RefreshStatus();
        return Task.CompletedTask;
    }

    public override async Task AfterPotionProcured(PotionModel potion)
    {
        RefreshStatus();

        if (!CombatManager.Instance.IsInProgress) return;
        // If there's only one potion, then this potion is the first one we procured
        if (Owner.Potions.Count() > 1) return;
        await RemoveDexterity();
    }

    public override async Task AfterPotionDiscarded(PotionModel potion)
    {
        RefreshStatus();

        if (!CombatManager.Instance.IsInProgress) return;
        if (Owner.Potions.Any()) return;
        await ApplyDexterity();
    }

    public override async Task AfterPotionUsed(PotionModel potion, Creature? target)
    {
        RefreshStatus();

        if (!CombatManager.Instance.IsInProgress) return;
        if (Owner.Potions.Any()) return;
        await ApplyDexterity();
    }

    private async Task ApplyDexterity()
    {
        Flash();
        await PowerCmd.Apply<Dexterity>(Owner.Creature, DynamicVars.Dexterity.BaseValue, null, null);
    }

    private async Task RemoveDexterity()
    {
        Flash();
        Dexterity? dexterity = Owner.Creature.Powers.OfType<Dexterity>().FirstOrDefault();

        if (dexterity != null)
        {
            await PowerCmd.ModifyAmount(dexterity, -DynamicVars.Dexterity.BaseValue, null, null);
        }
    }

    private void RefreshStatus()
    {
        if (Owner.Potions.Any())
        {
            Status = RelicStatus.Disabled;
        }
        else
        {
            if (CombatManager.Instance.IsInProgress)
            {
                Status = RelicStatus.Active;
            }
            else
            {
                Status = RelicStatus.Normal;
            }
        }
    }
}
