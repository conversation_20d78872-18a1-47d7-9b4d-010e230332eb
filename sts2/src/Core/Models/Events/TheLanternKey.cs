using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheLanternKey : EventModel
{
    public override bool IsShared => true;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new GoldVar(0)
    ];

    public override void CalculateVars()
    {
        DynamicVars.Gold.BaseValue = Owner!.ClimbState.TotalFloor * 5;
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(ReturnTheKey, "THE_LANTERN_KEY.pages.INITIAL.options.RETURN_THE_KEY"),
        new(<PERSON><PERSON><PERSON><PERSON><PERSON>, "THE_LANTERN_KEY.pages.INITIAL.options.KEEP_THE_KEY")
    ];

    private async Task ReturnTheKey()
    {
        await PlayerCmd.GainGold(DynamicVars.Gold.BaseValue, Owner!);
        SetEventFinished(L10NLookup("THE_LANTERN_KEY.pages.DONE.options.RETURN_THE_KEY.description"));
    }

    private Task KeepTheKey()
    {
        SetEventState(L10NLookup("THE_LANTERN_KEY.pages.KEEP_THE_KEY.description"),
        [
            new EventOption(Fight, "THE_LANTERN_KEY.pages.KEEP_THE_KEY.options.FIGHT")
        ]);

        return Task.CompletedTask;
    }

    private Task Fight()
    {
        CombatRoom combat = new(ModelDb.Encounter<MysteriousKnightEventEncounter>().ToMutable(), Owner!.ClimbState);
        CardModel card = Owner.ClimbState.CreateCard<LanternKey>(Owner);
        combat.AddExtraReward(Owner!, new QuestCardReward(card, Owner!));
        EnterCombatRoomWithoutExitingEvent(combat);

        return Task.CompletedTask;
    }

    public override Task Resume(AbstractRoom _)
    {
        SetEventFinished(L10NLookup("THE_LANTERN_KEY.pages.FIGHT_FINISHED.description"));
        return Task.CompletedTask;
    }
}
