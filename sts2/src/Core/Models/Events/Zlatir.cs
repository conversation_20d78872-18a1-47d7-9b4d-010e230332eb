using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Zlatir : AncientEventModel
{
    private const decimal _immortalityMaxHpLossProportion = 0.2m;
    private const string _immortalityMaxHpLossKey = "ImmortalityMaxHpLoss";

    public override IEnumerable<EventOption> AllPossibleOptions => OptionPool.Append(AcknowledgementOption);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new MaxHpVar(_immortalityMaxHpLossKey, 0),
        new StringVar("BloodSoakedRoseDescription", ModelDb.Relic<BloodSoakedRose>().DynamicDescription.GetFormattedText()),
        new StringVar("CrimsonPendantDescription", ModelDb.Relic<CrimsonPendant>().DynamicDescription.GetFormattedText()),
    ];

    private IEnumerable<EventOption> OptionPool =>
    [
        new(Immortality, "ZLATIR.pages.INITIAL.options.IMMORTALITY", ModelDb.Relic<CrimsonPendant>().HoverTipsExcludingRelic),
        RelicOption<ZlatirsCape>("POWER"),
        new(Beauty, "ZLATIR.pages.INITIAL.options.BEAUTY", ModelDb.Relic<BloodSoakedRose>().HoverTipsExcludingRelic.Concat(HoverTipFactory.FromCardWithCardHoverTips<Enthralled>())),
        RelicOption<RubyEarrings>("STATUS"),
        RelicOption<PerpetualCoin>("WEALTH"),
        RelicOption<ChoicesParadox>("FREEDOM")
    ];

    // Special case option
    private EventOption AcknowledgementOption => RelicOption<SpecialSleeve>("ACKNOWLEDGEMENT");

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        List<EventOption> optionPools = OptionPool.ToList();

        // It enchants an attack, so if there isn't an attack, this choice shouldn't appear.
        if (Owner!.Deck.Cards.Any(c => ModelDb.Enchantment<Favored>().CanEnchant(c)))
        {
            optionPools.Add(AcknowledgementOption);
        }

        optionPools.UnstableShuffle(Rng);

        // ceil this to make sure that the localized value matches actual damage given.
        DynamicVars[_immortalityMaxHpLossKey].BaseValue = Math.Ceiling(_immortalityMaxHpLossProportion * Owner!.Creature.MaxHp);

        return optionPools.Take(3).ToList();
    }

    private async Task Immortality()
    {
        await CreatureCmd.LoseMaxHp(Owner!.Creature, DynamicVars[_immortalityMaxHpLossKey].BaseValue);
        await RelicCmd.Obtain<CrimsonPendant>(Owner!);
        Done();
    }

    private async Task Beauty()
    {
        await CardPileCmd.AddCursesToDeck(Enumerable.Repeat(ModelDb.Card<Enthralled>(), 1), Owner!);
        await RelicCmd.Obtain<BloodSoakedRose>(Owner!);
        Done();
    }
}
