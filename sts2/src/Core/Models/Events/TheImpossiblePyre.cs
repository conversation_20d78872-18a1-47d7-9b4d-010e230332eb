using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheImpossiblePyre : EventModel
{
    private const string _maxHpLossKey = "MaxHpLoss";

    public override bool IsAllowed(ClimbState climbState) => climbState.Players.All(p => p.Creature.CurrentHp <= p.Creature.MaxHp * 0.55m);

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new HealVar(0),
        new(_maxHpLoss<PERSON><PERSON>, 11),
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(Communion, "THE_IMPOSSIBLE_PYRE.pages.INITIAL.options.COMMUNION", HoverTipFactory.FromCardWithCardHoverTips<Arrhythmia>()),
        new(Harvest, "THE_IMPOSSIBLE_PYRE.pages.INITIAL.options.HARVEST"),
    ];

    public override void CalculateVars()
    {
        DynamicVars.Heal.BaseValue = Owner!.Creature.MaxHp - Owner.Creature.CurrentHp;
    }

    private async Task Communion()
    {
        await CreatureCmd.Heal(Owner!.Creature, DynamicVars.Heal.BaseValue);
        await CardPileCmd.AddCursesToDeck([ModelDb.Card<Arrhythmia>()], Owner!);

        SetEventFinished(L10NLookup("THE_IMPOSSIBLE_PYRE.pages.COMMUNION.description"));
    }

    private async Task Harvest()
    {
        await CreatureCmd.LoseMaxHp(Owner!.Creature, DynamicVars[_maxHpLossKey].IntValue);
        RelicModel relic = RelicFactory.PullNextRelicFromFront(Owner!).ToMutable();
        await RelicCmd.Obtain(relic, Owner!);

        SetEventFinished(L10NLookup("THE_IMPOSSIBLE_PYRE.pages.HARVEST.description"));
    }
}
