using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class AbyssalBaths : EventModel
{

    private int _minDamage = 2;

    private int MinDamage
    {
        get => _minDamage;
        set
        {
            AssertMutable();
            _minDamage = value;
        }
    }

    private int _maxDamage = 6;

    private int MaxDamage
    {
        get => _maxDamage;
        set
        {
            AssertMutable();
            _maxDamage = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new MaxHpVar(2),
        new DamageVar(0, DamageProps.nonCardHpLoss),
        new HealVar(6)
    ];

    public override void CalculateVars()
    {
        DynamicVars.Damage.BaseValue = Rng.NextInt(MinDamage, MaxDamage);
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new EventOption(Immerse, "ABYSSAL_BATHS.pages.INITIAL.options.IMMERSE").ThatDoesDamage(DynamicVars.Damage.IntValue),
        new(Abstain, "ABYSSAL_BATHS.pages.INITIAL.options.ABSTAIN"),
    ];

    private async Task Immerse()
    {
        await OnImmerse();

        SetEventState(L10NLookup($"ABYSSAL_BATHS.pages.IMMERSE.description"),
        [
            new EventOption(Linger, $"ABYSSAL_BATHS.pages.ALL.options.LINGER")
                .ThatDoesDamage(DynamicVars.Damage.IntValue),
            new EventOption(ExitBaths, $"ABYSSAL_BATHS.pages.ALL.options.EXIT_BATHS"),
        ]);
    }

    private async Task Abstain()
    {
        await CreatureCmd.Heal(Owner!.Creature, DynamicVars.Heal.IntValue);
        SetEventFinished(L10NLookup("ABYSSAL_BATHS.pages.ABSTAIN.description"));
    }

    private async Task Linger()
    {
        await OnImmerse();
        SetEventState(L10NLookup($"ABYSSAL_BATHS.pages.LINGER.description"),
        [
            new EventOption(Linger, $"ABYSSAL_BATHS.pages.ALL.options.LINGER")
                .ThatDoesDamage(DynamicVars.Damage.IntValue),
            new EventOption(ExitBaths, $"ABYSSAL_BATHS.pages.ALL.options.EXIT_BATHS"),
        ]);
    }

    private Task ExitBaths()
    {
        SetEventFinished(L10NLookup("ABYSSAL_BATHS.pages.EXIT_BATHS.description"));
        return Task.CompletedTask;
    }

    private async Task OnImmerse()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.Damage, null, null);
        await CreatureCmd.GainMaxHp(Owner!.Creature, DynamicVars.MaxHp.BaseValue);

        int increase = Rng.NextInt(1, 3);
        MinDamage += increase;
        MaxDamage += increase;
        DynamicVars.Damage.BaseValue = Rng.NextInt(MinDamage, MaxDamage);
    }
}
