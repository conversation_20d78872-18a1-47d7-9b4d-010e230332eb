using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class FieldOfManSizedHoles : EventModel
{
    public override bool IsAllowed(ClimbState climbState)
    {
        return climbState.Players.All(p =>
            CardPile.Get(CardPileTarget.Deck, p)!.Cards.Any(ModelDb.Enchantment<PerfectFit>().CanEnchant)
        );
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new HpLossVar(11),
        new GoldVar(75),
        new CardsVar(2),
        new StringVar("ResistCurse", ModelDb.Card<Normality>().Title),
        new StringVar("Enchantment", ModelDb.Enchantment<PerfectFit>().Title.GetFormattedText()),
        new StringVar("FindAnotherHoleCurse", ModelDb.Card<Injury>().Title)
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(Resist, "FIELD_OF_MAN_SIZED_HOLES.pages.INITIAL.options.RESIST", HoverTipFactory.FromCardWithCardHoverTips<Normality>()),
        new EventOption(EnterYourHole, "FIELD_OF_MAN_SIZED_HOLES.pages.INITIAL.options.ENTER_YOUR_HOLE", HoverTipFactory.FromEnchantment<PerfectFit>()).ThatDoesDamage(DynamicVars.HpLoss.IntValue),
        new(FindAnotherHole, "FIELD_OF_MAN_SIZED_HOLES.pages.INITIAL.options.FIND_ANOTHER_HOLE", HoverTipFactory.FromCardWithCardHoverTips<Injury>())
    ];

    private async Task Resist()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.RemoveSelectionPrompt, DynamicVars.Cards.IntValue);
        List<CardModel> cards = (await CardSelectCmd.FromDeckForRemoval(Owner!, prefs)).ToList();
        await CardPileCmd.RemoveFromDeck(cards);
        await CardPileCmd.AddCursesToDeck([ModelDb.Card<Normality>()], Owner!);
        SetEventFinished(L10NLookup("FIELD_OF_MAN_SIZED_HOLES.pages.RESIST.description"));
    }

    private async Task EnterYourHole()
    {
        CardModel? card = (await CardSelectCmd.FromDeckForEnchantment(
            Owner!,
            ModelDb.Enchantment<PerfectFit>(),
            1,
            new CardSelectorPrefs(CardSelectorPrefs.EnchantSelectionPrompt, 1)
        )).FirstOrDefault();

        if (card != null)
        {
            CardCmd.Enchant<PerfectFit>(card, 1);

            NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
            if (vfx != null)
            {
                NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
            }
        }

        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.HpLoss.IntValue, DamageProps.nonCardHpLoss, null, null);
        SetEventFinished(L10NLookup("FIELD_OF_MAN_SIZED_HOLES.pages.ENTER_YOUR_HOLE.description"));
    }

    private async Task FindAnotherHole()
    {
        RelicModel relic = RelicFactory.PullNextRelicFromFront(Owner!).ToMutable();
        await RelicCmd.Obtain(relic, Owner!);
        await PlayerCmd.GainGold(DynamicVars.Gold.IntValue, Owner!);
        await CardPileCmd.AddCursesToDeck([ModelDb.Card<Injury>()], Owner!);
        SetEventFinished(L10NLookup("FIELD_OF_MAN_SIZED_HOLES.pages.FIND_ANOTHER_HOLE.description"));
    }
}
