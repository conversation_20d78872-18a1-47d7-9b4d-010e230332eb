using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class StoneOfAllTime : EventModel
{
    private const string _drinkRandomPotionKey = "DrinkRandomPotion";
    private const string _drinkMaxHpGain = "DrinkMaxHpGain";
    private const string _pushHpLoss = "PushHpLoss";
    private const string _pushVigorousCardCountKey = "PushVigorousCardCount";
    private const string _pushVigorousAmountKey = "PushVigorousAmount";
    private const string _ramCardKey = "RamCard";

    private PotionModel? _drinkAndLiftPotion;
    private CardModel? _ramCard;

    private PotionModel? DrinkAndLiftPotion
    {
        get => _drinkAndLiftPotion;
        set
        {
            AssertMutable();
            _drinkAndLiftPotion = value;
        }
    }

    private CardModel? RamCard
    {
        get => _ramCard;
        set
        {
            AssertMutable();
            _ramCard = value;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_drinkRandomPotionKey),
        new(_drinkMaxHpGain, 8),
        new(_pushHpLoss, 6),
        new(_pushVigorousCardCountKey, 2),
        new(_pushVigorousAmountKey, 8),
        new StringVar(_ramCardKey)
    ];

    public override bool IsAllowed(ClimbState climbState)
    {
        return climbState.Players.All(player =>
        {
            IReadOnlyList<CardModel> deckCards = CardPile.Get(CardPileTarget.Deck, player)!.Cards;
            bool hasAtLeastTwoAttacks = deckCards.Count(c => ModelDb.Enchantment<Vigorous>().CanEnchant(c)) >= 2;

            return player.Potions.Any() && hasAtLeastTwoAttacks;
        });
    }

    protected override Task BeforeEventStarted()
    {
        Owner!.CanRemovePotions = false;
        return Task.CompletedTask;
    }

    public override void OnEventFinished()
    {
        Owner!.CanRemovePotions = true;
    }

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        DrinkAndLiftPotion = Rng.NextItem(Owner!.Potions);

        IEnumerable<CardModel> rareAttacks = CardPile.Get(CardPileTarget.Deck, Owner!)!.Cards.Where(c => c is { Type: CardType.Attack, Rarity: CardRarity.Rare });
        RamCard = Rng.NextItem(rareAttacks);

        EventOption drinkAndLift;
        EventOption push;
        EventOption ram;

        if (DrinkAndLiftPotion != null)
        {
            StringVar potionVar = (StringVar)DynamicVars[_drinkRandomPotionKey];
            potionVar.StringValue = DrinkAndLiftPotion.Title.GetFormattedText();

            drinkAndLift = new EventOption(DrinkAndLift, "STONE_OF_ALL_TIME.pages.INITIAL.options.DRINK_AND_LIFT", HoverTipFactory.FromPotion(DrinkAndLiftPotion));
        }
        else
        {
            drinkAndLift = new EventOption(null, "STONE_OF_ALL_TIME.pages.INITIAL.options.DRINK_AND_LIFT_LOCKED");
        }

        if (CardPile.Get(CardPileTarget.Deck, Owner!)!.Cards.Count(c => ModelDb.Enchantment<Vigorous>().CanEnchant(c)) >= DynamicVars[_pushVigorousCardCountKey].BaseValue)
        {
            push = new EventOption(
                Push,
                "STONE_OF_ALL_TIME.pages.INITIAL.options.PUSH",
                HoverTipFactory.FromEnchantment<Vigorous>(DynamicVars[_pushVigorousAmountKey].IntValue)
            ).ThatDoesDamage(DynamicVars[_pushHpLoss].BaseValue);
        }
        else
        {
            push = new EventOption(null, "STONE_OF_ALL_TIME.pages.INITIAL.options.PUSH_LOCKED");
        }

        if (RamCard != null)
        {
            StringVar ramCardVar = (StringVar)DynamicVars[_ramCardKey];
            ramCardVar.StringValue = RamCard.Title;

            ram = new EventOption(Ram, "STONE_OF_ALL_TIME.pages.INITIAL.options.RAM", new List<IHoverTip> { HoverTipFactory.FromCard(RamCard) }.Concat(HoverTipFactory.FromEnchantment<Duplex>()));
        }
        else
        {
            ram = new EventOption(null, "STONE_OF_ALL_TIME.pages.INITIAL.options.RAM_LOCKED");
        }

        return
        [
            drinkAndLift,
            push,
            ram
        ];
    }

    private async Task DrinkAndLift()
    {
        await PotionCmd.Discard(DrinkAndLiftPotion!);
        await CreatureCmd.GainMaxHp(Owner!.Creature, DynamicVars[_drinkMaxHpGain].BaseValue);

        SetEventFinished(L10NLookup("STONE_OF_ALL_TIME.pages.DRINK_AND_LIFT.description"));
    }

    private async Task Push()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars[_pushHpLoss].BaseValue, DamageProps.nonCardHpLoss, null, null);

        IEnumerable<CardModel> attackCards = CardPile.Get(CardPileTarget.Deck, Owner!)!.Cards
            .Where(c => ModelDb.Enchantment<Vigorous>().CanEnchant(c))
            .ToList()
            .StableShuffle(Rng)
            .Take(DynamicVars[_pushVigorousCardCountKey].IntValue);

        foreach (CardModel card in attackCards)
        {
            CardCmd.Enchant<Vigorous>(card, DynamicVars[_pushVigorousAmountKey].BaseValue);

            NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
            if (vfx != null)
            {
                NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
            }
        }

        SetEventFinished(L10NLookup("STONE_OF_ALL_TIME.pages.PUSH.description"));
    }

    private async Task Ram()
    {
        Duplex duplex = ModelDb.Enchantment<Duplex>();

        CardSelectorPrefs prefs = new(CardSelectorPrefs.EnchantSelectionPrompt, 1);
        CardModel card = (await CardSelectCmd.FromDeckForEnchantment(Owner!, duplex, 1, c => c != RamCard, prefs)).First();

        CardCmd.Enchant<Duplex>(card, 1);

        NCardEnchantVfx? vfx = NCardEnchantVfx.Create(card);
        if (vfx != null)
        {
            NClimb.Instance?.GlobalUi.CardPreviewContainer.AddChildSafely(vfx);
        }

        await CardPileCmd.RemoveFromDeck(RamCard!);

        SetEventFinished(L10NLookup("STONE_OF_ALL_TIME.pages.RAM.description"));
    }
}
