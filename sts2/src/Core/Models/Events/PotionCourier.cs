using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx.Events;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class PotionCourier : EventModel
{
    private const string _foulPotionsKey = "FoulPotions";

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(GrabPotions, "POTION_COURIER.pages.INITIAL.options.GRAB_POTIONS", HoverTipFactory.FromPotion<FoulPotion>()),
        new(<PERSON>ns<PERSON>, "POTION_COURIER.pages.INITIAL.options.RANSACK")
    ];

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_foulPotionsKey, 3)
    ];

    // Must be at least act 2
    public override bool IsAllowed(ClimbState climbState) => climbState.CurrentActIndex > 0;

    public override void OnRoomEnter()
    {
        NEventRoom.Instance!.Layout!.AddVfxAnchoredToPortrait(NPotionCourierVfx.Create());
    }

    private async Task GrabPotions()
    {
        List<PotionReward> rewards = [];

        for (int i = 0; i < DynamicVars[_foulPotionsKey].IntValue; i++)
        {
            rewards.Add(new PotionReward(ModelDb.Potion<FoulPotion>().ToMutable(), Owner!));
        }

        await RewardsCmd.Offer(Owner!, rewards, false);

        SetEventFinished(L10NLookup("POTION_COURIER.pages.GRAB_POTIONS.description"));
    }

    private async Task Ransack()
    {
        IEnumerable<PotionModel> options = Owner!.Character.PotionPool.Potions
            .Concat(ModelDb.PotionPool<SharedPotionPool>().Potions)
            .Where(p => p.Rarity == PotionRarity.Uncommon);

        PotionModel? potion = Owner!.PlayerRng.Rewards.NextItem(options);

        if (potion != null)
        {
            await RewardsCmd.Offer(Owner!, [new PotionReward(potion.ToMutable(), Owner)], false);
        }

        SetEventFinished(L10NLookup("POTION_COURIER.pages.RANSACK.description"));
    }
}
