using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TelepathicSpiders : EventModel
{
    private const string _meditationTechniquesCostKey = "MeditationTechniquesCost";
    private const string _mentalAcuityCostKey = "MentalAcuityCost";
    private const string _knowYourselfCostKey = "KnowYourselfCost";

    public override bool IsAllowed(ClimbState climbState) => climbState.Players.All(p =>
        p.Gold >= DynamicVars[_mentalAcuityCostKey].BaseValue
    );

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        int gold = Owner!.Gold;

        // Note: We currently allow Meditation Techniques to be selected even if you don't have enough gold.
        EventOption meditationTechniques = new(MeditationTechniques, "TELEPATHIC_SPIDERS.pages.INITIAL.options.MEDITATION_TECHNIQUES", HoverTipFactory.FromCardWithCardHoverTips<Enlightenment>());

        EventOption mentalAcuity;
        EventOption knowYourself;

        if (gold >= DynamicVars[_mentalAcuityCostKey].IntValue)
        {
            mentalAcuity = new EventOption(MentalAcuity, "TELEPATHIC_SPIDERS.pages.INITIAL.options.MENTAL_ACUITY");
        }
        else
        {
            mentalAcuity = CreateLockedOption();
        }

        if (gold >= DynamicVars[_knowYourselfCostKey].IntValue)
        {
            knowYourself = new EventOption(KnowYourself, "TELEPATHIC_SPIDERS.pages.INITIAL.options.KNOW_YOURSELF");
        }
        else
        {
            knowYourself = CreateLockedOption();
        }

        return [meditationTechniques, mentalAcuity, knowYourself];
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_meditationTechniquesCostKey, 50),
        new(_mentalAcuityCostKey, 125),
        new(_knowYourselfCostKey, 250)
    ];

    private async Task MeditationTechniques()
    {
        await PlayerCmd.LoseGold(DynamicVars[_meditationTechniquesCostKey].IntValue, Owner!);

        CardModel[] cards = new CardModel[2];

        for (int i = 0; i < cards.Length; i++)
        {
            cards[i] = Owner!.ClimbState.CreateCard<Enlightenment>(Owner);
        }

        IReadOnlyList<CardPileAddResult> results = await CardPileCmd.Add(cards, CardPileTarget.Deck);
        CardCmd.PreviewCardPileAdd(results);

        SetEventFinished(L10NLookup("TELEPATHIC_SPIDERS.pages.DONE.description"));
    }

    private async Task MentalAcuity()
    {
        await RemoveCardsAndProceed(DynamicVars[_mentalAcuityCostKey].IntValue, 1
        );
    }

    private async Task KnowYourself()
    {
        await RemoveCardsAndProceed(DynamicVars[_knowYourselfCostKey].IntValue, 2);
    }

    private async Task RemoveCardsAndProceed(int cost, int count)
    {
        IEnumerable<CardModel> cards = await CardSelectCmd.FromDeckForRemoval(
            Owner!,
            new CardSelectorPrefs(CardSelectorPrefs.RemoveSelectionPrompt, count)
        );

        await CardPileCmd.RemoveFromDeck(cards.ToList());
        await PlayerCmd.LoseGold(cost, Owner!);

        SetEventFinished(L10NLookup("TELEPATHIC_SPIDERS.pages.DONE.description"));
    }

    private static EventOption CreateLockedOption() => new(null, "TELEPATHIC_SPIDERS.pages.INITIAL.options.LOCKED");
}
