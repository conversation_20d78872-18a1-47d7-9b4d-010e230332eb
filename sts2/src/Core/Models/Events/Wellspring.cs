using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.Rewards;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class Wellspring : EventModel
{
    private const string _batheKey = "BatheCurses";

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new(_batheKey, 1),
    ];


    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(<PERSON><PERSON>, "WELLSPRING.pages.INITIAL.options.BOTTLE"),
        new(<PERSON><PERSON>, "WELLSPRING.pages.INITIAL.options.BATHE", HoverTipFactory.FromCardWithCardHoverTips<Guilty>())
    ];

    public override void OnRoomEnter()
    {
        // Places the rain vfx in between the portrait and event ui elements.
        Control rainVfx = NAdditiveFogVfx.Create();
        NEventRoom.Instance!.VfxContainer.AddChildSafely(rainVfx);
    }

    private async Task Bottle()
    {
        IEnumerable<PotionModel> options = Owner!.Character.PotionPool.Potions
            .Concat(ModelDb.PotionPool<SharedPotionPool>().Potions);

        PotionModel? potion = Owner!.PlayerRng.Rewards.NextItem(options);

        if (potion != null)
        {
            await RewardsCmd.Offer(Owner!, [new PotionReward(potion.ToMutable(), Owner)], false);
        }

        SetEventFinished(L10NLookup("WELLSPRING.pages.BOTTLE.description"));
    }

    private async Task Bathe()
    {
        CardSelectorPrefs prefs = new(CardSelectorPrefs.RemoveSelectionPrompt, 1);
        List<CardModel> cards = (await CardSelectCmd.FromDeckForRemoval(Owner!, prefs)).ToList();
        await CardPileCmd.RemoveFromDeck(cards);

        await AddGuilty(DynamicVars[_batheKey].IntValue);

        SetEventFinished(L10NLookup("WELLSPRING.pages.BATHE.description"));
    }

    private async Task AddGuilty(int amount)
    {
        await CardPileCmd.AddCursesToDeck(Enumerable.Repeat(ModelDb.Card<Guilty>(),amount), Owner!);
    }
}
