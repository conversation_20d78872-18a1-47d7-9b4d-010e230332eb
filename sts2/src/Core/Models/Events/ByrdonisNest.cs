using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class ByrdonisNest : EventModel
{
    private const string _cardKey = "Card";

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new(Eat, "BYRDONIS_NEST.pages.INITIAL.options.EAT"),
        new(Take, "BYRDONIS_NEST.pages.INITIAL.options.TAKE", HoverTipFactory.FromCardWithCardHoverTips<ByrdonisEgg>())
    ];

    public override IEnumerable<string> AssetPaths
    {
        get
        {
            List<string> paths = base.AssetPaths.ToList();
            paths.Add(NByrdonisNestVfx.ScenePath);
            return paths;
        }
    }

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new MaxHpVar(7),
        new StringVar(_cardKey, ModelDb.Card<ByrdonisEgg>().Title)
    ];

    public override void OnRoomEnter()
    {
        NEventRoom.Instance!.Layout!.AddVfxAnchoredToPortrait(NByrdonisNestVfx.Create());
    }

    private async Task Eat()
    {
        await CreatureCmd.GainMaxHp(Owner!.Creature, DynamicVars.MaxHp.BaseValue);

        SetEventFinished(L10NLookup("BYRDONIS_NEST.pages.EAT.description"));
    }

    private async Task Take()
    {
        CardModel card = Owner!.ClimbState.CreateCard<ByrdonisEgg>(Owner);
        CardPileAddResult result = await CardPileCmd.Add(card, CardPileTarget.Deck);
        CardCmd.PreviewCardPileAdd(result, 2f);

        SetEventFinished(L10NLookup("BYRDONIS_NEST.pages.TAKE.description"));
    }
}
