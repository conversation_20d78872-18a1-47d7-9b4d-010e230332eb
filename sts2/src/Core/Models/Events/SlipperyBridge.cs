using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Vfx;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class SlipperyBridge : EventModel
{
    private const string _randomCardKey = "RandomCard";
    private const string _hpLossKey = "HpLoss";
    private const string _overcomeLocKey = "SLIPPERY_BRIDGE.pages.INITIAL.options.OVERCOME";
    private const int _initialHpLoss = 3;

    private int _numberOfHoldOns;
    private CardModel? _randomCardToLose;

    private int NumberOfHoldOns
    {
        get => _numberOfHoldOns;
        set
        {
            AssertMutable();
            _numberOfHoldOns = value;
        }
    }

    private CardModel? RandomCardToLose
    {
        get => _randomCardToLose;
        set
        {
            AssertMutable();
            _randomCardToLose = value;
        }
    }

    private int CurrentHpLoss => _initialHpLoss + NumberOfHoldOns;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_randomCardKey),
        new(_hpLossKey, CurrentHpLoss)
    ];

    public override bool IsAllowed(ClimbState climbState) => climbState.TotalFloor > 6 && climbState.Players.All(p => p.Deck.Cards.Any(c => c.IsRemovable));

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        GetNewRandomCard();

        return
        [
            new EventOption(Overcome, _overcomeLocKey, HoverTipFactory.FromCard(RandomCardToLose!)),
            new EventOption(HoldOn, "SLIPPERY_BRIDGE.pages.INITIAL.options.HOLD_ON_0").ThatDoesDamage(CurrentHpLoss)
        ];
    }

    public override void OnRoomEnter()
    {
        // Places the rain vfx in between the portrait and event ui elements.
        Node2D rainVfx = NRainVfx.Create();
        NEventRoom.Instance!.VfxContainer.AddChildSafely(rainVfx);
    }

    private void GetNewRandomCard()
    {
        List<CardModel> candidatesForRemoval;

        if (RandomCardToLose == null)
        {
            // First card is not a starter card unless that's all the player has
            candidatesForRemoval = Owner!.Deck.Cards.Where(c => c.Rarity != CardRarity.Basic).ToList();
        }
        else
        {
            // Later cards must be cards that are of a different type than the already selected cards, but
            // can be starter cards
            candidatesForRemoval = Owner!.Deck.Cards.Where(c => c.GetType() != RandomCardToLose.GetType()).ToList();
        }

        // Remove non-removable cards as candidates
        candidatesForRemoval.RemoveAll(c => !c.IsRemovable);

        if (candidatesForRemoval.Count == 0)
        {
            // Entire deck is starter or repeats, so, whatever
            candidatesForRemoval = Owner!.Deck.Cards.Where(c => c.IsRemovable).ToList();
        }

        RandomCardToLose = Rng.NextItem(candidatesForRemoval);

        StringVar randomCardVar = (StringVar)DynamicVars[_randomCardKey];
        randomCardVar.StringValue = RandomCardToLose!.Title;
    }

    private async Task Overcome()
    {
        await CardPileCmd.RemoveFromDeck(RandomCardToLose!);
        SetEventFinished(L10NLookup("SLIPPERY_BRIDGE.pages.OVERCOME.description"));
    }

    private async Task HoldOn()
    {
        await CreatureCmd.Damage(Owner!.Creature, CurrentHpLoss, DamageProps.nonCardHpLoss, null, null);

        NumberOfHoldOns++;
        DynamicVars[_hpLossKey].BaseValue = CurrentHpLoss;

        GetNewRandomCard();

        // We do NumberOfHoldOns - 1 for currentSuffix because we're calculating the current page number, but we've
        // already incremented the number to ensure that we have the latest HP loss value.
        string currentSuffix = GetHoldOnSuffix(NumberOfHoldOns - 1);

        // Note: Although these options are all the same, the metrics server uses these as keys. Since we want to know
        //  how many people hold on 1-7 times, we cannot merge these into one key.
        string nextSuffix = GetHoldOnSuffix(NumberOfHoldOns);
        string holdOnOptionKey = $"SLIPPERY_BRIDGE.pages.HOLD_ON_{currentSuffix}.options.HOLD_ON_{nextSuffix}";

        SetEventState(L10NLookup($"SLIPPERY_BRIDGE.pages.HOLD_ON_{currentSuffix}.description"),
        [
            // The "Overcome" option always uses the same text and doesn't need to be distinguished by the metrics
            // server in the same way as "Hold On", so we just keep using the INITIAL page one for simplicity's sake.
            new EventOption(Overcome, _overcomeLocKey, HoverTipFactory.FromCard(RandomCardToLose!)),
            new EventOption(HoldOn, holdOnOptionKey).ThatDoesDamage(CurrentHpLoss)
        ]);
    }

    private string GetHoldOnSuffix(int holdOnNumber) => holdOnNumber < 7 ? holdOnNumber.ToString() : "LOOP";
}
