using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models.Relics;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public class Darv : AncientEventModel
{
    private struct ValidRelicSet
    {
        public readonly Func<Player, bool> filter;
        public readonly RelicModel[] relics;

        public ValidRelicSet(Func<Player, bool> filter, RelicModel[] relics)
        {
            this.filter = filter;
            this.relics = relics;
        }

        public ValidRelicSet(RelicModel[] relics)
        {
            filter = (_) => true;
            this.relics = relics;
        }
    }

    private static readonly List<ValidRelicSet> _validRelicSets =
    [
        new([ModelDb.Relic<Astrolabe>()]),
        new([ModelDb.Relic<BlackStar>()]),
        new([ModelDb.Relic<CallingBell>()]),
        new([ModelDb.Relic<ElectrifiedShard>()]),
        new([ModelDb.Relic<EmptyCage>()]),
        new([ModelDb.Relic<LoomingFruit>()]),
        new([ModelDb.Relic<OddlyHeavyStone>()]),
        new(
            // These modifiers remove all starter cards, so don't allow Pandora's Box to show up when they're around
            owner => !owner.ClimbState.Modifiers.Any(m => m.ClearsPlayerDeck),
            [ModelDb.Relic<PandorasBox>()]
        ),
        new([ModelDb.Relic<RunicPyramid>()]),
        new([ModelDb.Relic<SneckoEye>()]),
        new(
            owner => owner.ClimbState.CurrentActIndex == 1,
            [ModelDb.Relic<Ectoplasm>(), ModelDb.Relic<Sozu>()]
        ),
        new(
            owner => owner.ClimbState.CurrentActIndex == 2,
            [ModelDb.Relic<PhilosophersStone>(), ModelDb.Relic<VelvetChoker>()]
        )
    ];

    public override IEnumerable<EventOption> AllPossibleOptions => _validRelicSets
        .SelectMany(s => s.relics)
        .Select(r => RelicOption(r))
        // Dusty Tome is manually appended here because it has different random choice logic from the other options.
        .Concat([RelicOption(ModelDb.Relic<DustyTome>())]);

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        List<EventOption> ancientRelicOptions = _validRelicSets
            .Where(rs => rs.filter.Invoke(Owner!))
            .Select(rs => RelicOption(Rng.NextItem(rs.relics)!))
            .ToList()
            .UnstableShuffle(Rng);

        List<EventOption> options;

        if (Rng.NextBool())
        {
            // Half of the time, offer 2 random ancient relics and a random ancient card.
            options = ancientRelicOptions.Take(2).ToList();

            DustyTome dustyTome = (DustyTome)ModelDb.Relic<DustyTome>().ToMutable();
            if (Owner != null)
            {
                // required for game info
                dustyTome.SetupForPlayer(Owner!);
            }

            options.Add(RelicOption(dustyTome, "LOST_ART"));
        }
        else
        {
            // The other half of the time, offer 3 random ancient relics.
            options = ancientRelicOptions.Take(3).ToList();
        }

        return options;
    }
}
