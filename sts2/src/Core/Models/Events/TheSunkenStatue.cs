using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class TheSunkenStatue : EventModel
{
    private const string _relicKey = "Relic";
    private const string _hpLossKey = "HpLoss";

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new StringVar(_relicKey, ModelDb.Relic<SwordOfStone>().Title.GetFormattedText()),
        new GoldVar(111),
        new(_hpLossKey, 7)
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions() =>
    [
        new EventOption(GrabSword, "THE_SUNKEN_STATUE.pages.INITIAL.options.GRAB_SWORD", HoverTipFactory.FromRelic<SwordOfStone>()),
        new EventOption(DiveIntoWater, "THE_SUNKEN_STATUE.pages.INITIAL.options.DIVE_INTO_WATER")
            .ThatDoesDamage(DynamicVars[_hpLossKey].BaseValue)
    ];

    private async Task GrabSword()
    {
        await RelicCmd.Obtain<SwordOfStone>(Owner!);

        SetEventFinished(L10NLookup("THE_SUNKEN_STATUE.pages.GRAB_SWORD.description"));
    }

    private async Task DiveIntoWater()
    {
        await PlayerCmd.GainGold(DynamicVars.Gold.BaseValue, Owner!);
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars[_hpLossKey].BaseValue, DamageProps.nonCardHpLoss, null, null);

        SetEventFinished(L10NLookup("THE_SUNKEN_STATUE.pages.DIVE_INTO_WATER.description"));
    }
}
