using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.CardSelection;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Events;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.HoverTips;
using MegaCrit.Sts2.Core.Localization.DynamicVars;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;

namespace MegaCrit.Sts2.Core.Models.Events;

// ReSharper disable once ClassNeverInstantiated.Global
// Reason: Instantiated via reflection.
public sealed class RoomFullOfCheese : EventModel
{
    public override bool IsAllowed(ClimbState climbState) => climbState.CurrentActIndex < 2;

    protected override IEnumerable<DynamicVar> CanonicalVars =>
    [
        new DamageVar(14, DamageProps.nonCardHpLoss)
    ];

    protected override IReadOnlyList<EventOption> GenerateInitialOptions()
    {
        List<EventOption> options = [];

        options.Add(new EventOption(Gorge, "ROOM_FULL_OF_CHEESE.pages.INITIAL.options.GORGE"));

        if (Owner!.Relics.OfType<ChosenCheese>().Any())
        {
            options.Add(new EventOption(Search, "ROOM_FULL_OF_CHEESE.pages.INITIAL.options.SEARCH_LOCKED"));
        }
        else
        {
            options.Add(new EventOption(Search, "ROOM_FULL_OF_CHEESE.pages.INITIAL.options.SEARCH", HoverTipFactory.FromRelic<ChosenCheese>()).ThatDoesDamage(DynamicVars.Damage.BaseValue));
        }

        return options;
    }

    private async Task Gorge()
    {
        Player player = Owner!;

        List<CardCreationResult> cards = CardFactory.CreateForReward(player, player.Character.CardPool.Cards.Where(c => c.Rarity == CardRarity.Common), 8).ToList();
        CardSelectorPrefs prefs = new(L10NLookup("ROOM_FULL_OF_CHEESE.pages.GORGE.selectionScreenPrompt"), 2);
        IEnumerable<CardModel> selectedCards = await CardSelectCmd.FromSimpleGridForRewards(new NullPlayerChoiceContext(), cards, player, prefs);

        foreach (CardModel selectedCard in selectedCards)
        {
            CardPileAddResult result = await CardPileCmd.Add(selectedCard, CardPileTarget.Deck);
            CardCmd.PreviewCardPileAdd(result);
        }

        SetEventFinished(L10NLookup("ROOM_FULL_OF_CHEESE.pages.GORGE.description"));
    }

    private async Task Search()
    {
        await CreatureCmd.Damage(Owner!.Creature, DynamicVars.Damage, null, null);
        await RelicCmd.Obtain<ChosenCheese>(Owner!);

        SetEventFinished(L10NLookup("ROOM_FULL_OF_CHEESE.pages.SEARCH.description"));
    }
}
