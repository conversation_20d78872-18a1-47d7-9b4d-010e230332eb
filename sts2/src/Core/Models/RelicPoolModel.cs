using System.Collections.Generic;
using System.Linq;

namespace MegaCrit.Sts2.Core.Models;

public abstract class RelicPoolModel : AbstractModel, IPoolModel
{
    public abstract string EnergyColorName { get; }

    private RelicModel[]? _relics;

    public IEnumerable<RelicModel> Relics => _relics ??= GenerateRelics();

    private HashSet<ModelId>? _relicIds;
    public IEnumerable<ModelId> RelicIds => _relicIds ??= Relics.Select(c => c.Id).ToHashSet();

    protected abstract RelicModel[] GenerateRelics();

    public override bool ShouldReceiveCombatHooks => false;
}
