using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Combat;

/// <summary>
/// An object containing all of the state that represents the current combat.
///
/// Some state details are derived from higher-level objects. For example:
/// * All players in the combat state are derived by checking the Player property of all the creatures.
/// * All cards in the combat state are derived by checking the combat piles of all those players.
/// </summary>
public class CombatState : ICardScope
{
    /// <summary>
    /// The state of the climb that this combat exists in.
    /// Will be <see cref="Climbs.ClimbState"/> in gameplay and <see cref="NullClimbState"/> in some test/debug scenarios.
    /// </summary>
    public IClimbState ClimbState { get; }

    /// <summary>
    /// Fired whenever the arrangement of creatures in the combat changes. Specifically, when:
    /// * A creature is added.
    /// * A creature is removed.
    /// * A creature's index changes.
    /// </summary>
    public event Action<CombatState>? CreaturesChanged;

    private readonly List<Creature> _allies = [];
    private readonly List<Creature> _enemies = [];

    /// <summary>
    /// This is the ID that will be assigned to the next spawned creature's CombatId field.
    /// If we receive an action targeting a creature with an ID less than this one, then it must either be in the creature
    /// list or it had died at some point in the past.
    /// If we receive an action targeting a creature with an ID greater than or equal to this one, then it has yet to
    /// spawn (or there is some other error).
    /// </summary>
    private uint _nextCreatureId;

    /// <summary>
    /// Get all creatures on the Allies side.
    /// </summary>
    public IReadOnlyList<Creature> Allies => _allies;

    /// <summary>
    /// Get all creatures on the Enemies side.
    /// </summary>
    public IReadOnlyList<Creature> Enemies => _enemies;

    /// <summary>
    /// Get all creatures in the combat on all sides.
    /// </summary>
    public IReadOnlyList<Creature> Creatures => _allies.Concat(_enemies).ToList();

    /// <summary>
    /// Get all the player creatures in the combat.
    /// </summary>
    public IReadOnlyList<Creature> PlayerCreatures => Creatures.Where(c => c.IsPlayer).ToList();

    /// <summary>
    /// Get all players in the combat.
    /// </summary>
    public IReadOnlyList<Player> Players => PlayerCreatures.Select(c => c.Player!).ToList();

    private readonly EncounterModel? _encounter;

    /// <summary>
    /// The round of combat that we're on in this combat.
    /// A "round" encompasses both the player's turn and the enemy's turn.
    /// This starts at 1, so it should never be 0.
    /// </summary>
    public int RoundNumber { get; set; }

    /// <summary>
    /// The side that is active in this combat.
    /// A round starts with the player side being active, then switches to the enemy side after all players have ended
    /// their turn.
    /// When the enemy turn ends, the current side changes back to the player side, and the round number is incremented.
    /// </summary>
    public CombatSide CurrentSide { get; set; }

    public EncounterModel? Encounter
    {
        get => _encounter;
        private init
        {
            value?.AssertMutable();
            _encounter = value;
        }
    }

    /// <summary>
    /// A list of creatures that escaped (were removed without dying) during the encounter. Used when rewards are given.
    /// </summary>
    public List<Creature> EscapedCreatures { get; private set; } = [];

    /// <summary>
    /// All cards that have been created within this state.
    /// This allows us to keep track of "floating" cards that have not been added to any piles (like fake cards in
    /// upgrade previews).
    /// </summary>
    private readonly List<CardModel> _allCards = [];

    public CombatState(EncounterModel? encounter = null, IClimbState? climbState = null)
    {
        encounter?.AssertMutable();
        Encounter = encounter;
        RoundNumber = 1;
        CurrentSide = CombatSide.Player;
        ClimbState = climbState ?? NullClimbState.Instance;
    }

    #region Public card-related methods

    /// <summary>
    /// See <see cref="ICardScope.CreateCard{T}"/>.
    /// </summary>
    public T CreateCard<T>(Player owner) where T : CardModel => (T)CreateCard(ModelDb.Card<T>(), owner);

    /// <summary>
    /// See <see cref="ICardScope.CreateCard"/>.
    /// </summary>
    public CardModel CreateCard(CardModel canonicalCard, Player owner)
    {
        CardModel card = canonicalCard.ToMutable();
        AddCard(card, owner);
        card.AfterCreated();

        return card;
    }

    /// <summary>
    /// See <see cref="ICardScope.CloneCard"/>.
    /// </summary>
    public CardModel CloneCard(CardModel mutableCard)
    {
        CardModel card = (CardModel)mutableCard.ClonePreservingMutability();
        AddCard(card);

        return card;
    }

    /// <summary>
    /// See <see cref="ICardScope.AddCard"/>.
    /// </summary>
    public void AddCard(CardModel card, Player owner)
    {
        card.Owner = owner;
        AddCard(card);
    }

    public void RemoveCard(CardModel card)
    {
        _allCards.Remove(card);
        card.Owner = null!;
    }

    /// <summary>
    /// Does this state contain the specified card?
    /// </summary>
    public bool ContainsCard(CardModel card) => _allCards.Contains(card);

    #endregion

    #region Public creature-related methods

    /// <summary>
    /// Add a player's creature on the Allies side.
    /// </summary>
    public void AddPlayer(Player player)
    {
        AttachCreature(player.Creature, _allies);
    }

    public Creature CreateCreature(MonsterModel monster, CombatSide side, string? slot)
    {
        monster.AssertMutable();
        monster.ClimbRng = ClimbState.Rng;

        Creature creature = new(monster, side, slot);
        List<Creature> sideCreatures = side == CombatSide.Player ? _allies : _enemies;

        if (side == CombatSide.Enemy)
        {
            creature.SetUniqueMonsterHpValue(sideCreatures, ClimbState.Rng.Niche);
            creature.ScaleMonsterHpForMultiplayer(Players.Count, ClimbState.CurrentActIndex);
        }

        AttachCreature(creature, sideCreatures);

        monster.Rng = new Rng((uint)(
            ClimbState.Rng.Seed +
            ClimbState.CurrentMapCoord?.col ?? 0 +
            ClimbState.CurrentMapCoord?.row ?? 0 +
            ClimbState.CurrentActIndex +
            creature.CombatId!.Value
        ));

        return creature;
    }

    /// <summary>
    /// Call this to remove a creature that escaped rather than dying.
    /// </summary>
    public void CreatureEscaped(Creature creature)
    {
        EscapedCreatures.Add(creature);
        RemoveCreature(creature);
    }

    public void RemoveCreature(Creature creature)
    {
        if (creature.CombatState != this) throw new InvalidOperationException("Creature is in a different combat.");

        if (_enemies.Contains(creature))
        {
            _enemies.Remove(creature);
        }
        else if (_allies.Contains(creature))
        {
            _allies.Remove(creature);
        }
        else
        {
            throw new InvalidOperationException($"Removed creature '{creature}' was not found.");
        }

        creature.CombatState = null;
        CreaturesChanged?.Invoke(this);
    }

    public bool ContainsCreature(Creature creature) => _allies.Contains(creature) || _enemies.Contains(creature);

    public bool ContainsMonster<T>() where T : MonsterModel => _enemies.Any(c => c.Monster is T);

    /// <summary>
    /// Get the creature with the specified combat ID. Null if not found.
    /// </summary>
    public Creature? GetCreature(uint? combatId)
    {
        if (combatId == null) return null;
        return Creatures.FirstOrDefault(c => c.CombatId == combatId);
    }

    /// <summary>
    /// Get the creature with the specified combat ID.
    /// If the creature doesn't exist, keep checking for a while before timing out and returning null.
    /// </summary>
    /// <param name="combatId">Combat ID of the creature to get.</param>
    /// <param name="timeoutSec">How long to wait for the creature to appear.</param>
    /// <returns>Specified Creature, or null if it doesn't exist and we've waited long enough.</returns>
    public async Task<Creature?> GetCreatureAsync(uint? combatId, double timeoutSec)
    {
        // Cards without targets should immediately get a null target
        if (combatId == null) return null;

        // Try to get the creature. If it exists, return it immediately.
        Creature? target = GetCreature(combatId);
        if (target != null) return target;

        // If the creature does not exist, and the ID was for a creature that has been spawned, then assume the creature
        // had already died at some point.
        if (combatId < _nextCreatureId) return null;

        // Otherwise, it's possible that a multiplayer peer is ahead of us. We need to wait for the creature to be
        // spawned (e.g. by the Infested Phrog dying).
        TaskCompletionSource<Creature> completionSource = new();
        CreaturesChanged += OnCreaturesChanged;

        // Issue a timeout if the creature doesn't show up within a few seconds. This should never happen under
        // normal circumstances, but if there's a bug, the timeout might be valuable so that we're not hanging
        // around waiting forever.
        Task timeoutTask = GodotTimerTask(timeoutSec);
        Task firstCompletedTask = await Task.WhenAny(completionSource.Task, timeoutTask);

        CreaturesChanged -= OnCreaturesChanged;

        if (firstCompletedTask == timeoutTask)
        {
            throw new InvalidOperationException($"Timed out waiting for creature with target index {combatId} to spawn!");
        }

        // The task is already complete, but using await is a cheap way to re-throw the exception if there is one.
        return await completionSource.Task;

        void OnCreaturesChanged(CombatState _)
        {
            Creature? candidate = GetCreature(combatId);
            if (candidate == null) return;

            completionSource.SetResult(candidate);
        }
    }

    /// <summary>
    /// Get all the creatures on the specified side.
    /// </summary>
    public IReadOnlyList<Creature> GetCreaturesOnSide(CombatSide side) => side == CombatSide.Enemy ? Enemies : Allies;

    /// <summary>
    /// Get all the creatures on the currently-active side.
    /// </summary>
    public IReadOnlyList<Creature> CreaturesOnCurrentSide => GetCreaturesOnSide(CurrentSide);

    /// <summary>
    /// Get all opponents of a creature.
    /// </summary>
    public IReadOnlyList<Creature> GetOpponentsOf(Creature creature) => GetCreaturesOnSide(creature.Side.GetOppositeSide());

    /// <summary>
    /// Get all teammates of a creature, including the creature itself.
    /// </summary>
    public IReadOnlyList<Creature> GetTeammatesOf(Creature creature) => GetCreaturesOnSide(creature.Side);

    /// <summary>
    /// Get all hittable enemies.
    /// See <see cref="Creature.IsHittable"/> for a definition.
    ///
    /// NOTE: We shouldn't add too many methods like this, this one is just extremely common because it's used for AOE
    /// and random attack targeting.
    /// </summary>
    public IReadOnlyList<Creature> HittableEnemies => Enemies.Where(e => e.IsHittable).ToList();

    #endregion

    #region Public player-related methods

    public Player? GetPlayer(ulong playerId) => Players.FirstOrDefault(p => p.NetId == playerId);

    #endregion

    public IEnumerable<AbstractModel> AllHookListeners()
    {
        foreach (Creature creature in Creatures)
        {
            foreach (PowerModel power in creature.Powers)
            {
                yield return power;
            }
        }

        foreach (Player player in Players)
        {
            foreach (RelicModel relic in player.Relics)
            {
                yield return relic;
            }

            foreach (CardModel deckCard in player.Deck.Cards)
            {
                yield return deckCard;

                if (deckCard.Enchantment != null)
                {
                    yield return deckCard.Enchantment;
                }
            }

            foreach (CardModel combatCard in player.PlayerCombatState!.AllCards)
            {
                yield return combatCard;

                if (combatCard.Affliction != null)
                {
                    yield return combatCard.Affliction;
                }

                if (combatCard.Enchantment != null)
                {
                    yield return combatCard.Enchantment;
                }
            }
        }
    }

    public void SortEnemiesBySlotName()
    {
        if (Encounter == null) return;

        _enemies.Sort((a, b) => Encounter.Slots.IndexOf(a.SlotName) - Encounter.Slots.IndexOf(b.SlotName));
    }

    public void SetEnemyIndex(Creature creature, int index)
    {
        if (Encounter!.Slots.Any())
        {
            throw new InvalidOperationException("Cannot modify turn order of a combat with pre-set slots");
        }

        if (!Enemies.Contains(creature))
        {
            throw new ArgumentException("Creature must be a valid enemy to change its turn order.");
        }

        _enemies.Remove(creature);
        _enemies.Insert(Mathf.Min(index, _enemies.Count - 1), creature);
    }

    private void AddCard(CardModel card)
    {
        card.AssertMutable();
        if (card.CombatState != this) throw new InvalidOperationException($"Card {card.Id.Entry} combat state is set to a different combat.");

        _allCards.Add(card);
    }

    private void AttachCreature(Creature creature, List<Creature> side)
    {
        if (creature.CombatState != null) throw new InvalidOperationException("Creature is already in a combat.");

        if (ContainsCreature(creature))
        {
            throw new InvalidOperationException(
                "Creature is already in this combat, and has no CombatState set. Something is very wrong."
            );
        }

        creature.CombatId = _nextCreatureId;
        _nextCreatureId++;

        creature.CombatState = this;
        side.Add(creature);

        CreaturesChanged?.Invoke(this);
    }

    private static async Task GodotTimerTask(double timeSec)
    {
        SceneTreeTimer timer = ((SceneTree)Engine.GetMainLoop()).CreateTimer(timeSec);
        await timer.ToSignal(timer, SceneTreeTimer.SignalName.Timeout);
    }
}
