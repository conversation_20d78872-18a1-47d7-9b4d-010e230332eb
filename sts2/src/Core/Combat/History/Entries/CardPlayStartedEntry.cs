using System.Diagnostics.CodeAnalysis;
using System.Text;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Potions;

namespace MegaCrit.Sts2.Core.Combat.History.Entries;

public class CardPlayStartedEntry : CombatHistoryEntry
{
    /// <summary>
    /// The card that is being played.
    /// </summary>
    public CardModel Card { get; }

    /// <summary>
    /// The creature that the card is targeting.
    /// Null for cards that target no creatures or all creatures.
    /// </summary>
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public Creature? Target { get; }

    /// <summary>
    /// The card's play count during this play.
    /// Normally 0, but when a card has Replay or <see cref="Duplicator"/> or something, the second play will
    /// have PlayCount 1, the third will have PlayCount 2, etc.
    /// </summary>
    public int PlayCount { get; }

    public override string Description
    {
        get
        {
            StringBuilder builder = new($"{Actor.Player!.Character.Id.Entry} started playing {Card.Id.Entry}");

            if (Target != null)
            {
                // Assumes cards only ever target monsters.
                // Make this check for players if that ever changes.
                builder.Append($" targeting {Target.Monster!.Id.Entry}");
            }

            return builder.ToString();
        }
    }

    public CardPlayStartedEntry(CardModel card, Creature? target, int playCount, int roundNumber, CombatSide currentSide, CombatHistory history) :
        base(card.Owner.Creature, roundNumber, currentSide, history)
    {
        Card = card;
        Target = target;
        PlayCount = playCount;
    }
}
