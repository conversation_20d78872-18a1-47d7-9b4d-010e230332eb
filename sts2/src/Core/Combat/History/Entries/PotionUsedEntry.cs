using System.Diagnostics.CodeAnalysis;
using System.Text;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Combat.History.Entries;

public class PotionUsedEntry : CombatHistoryEntry
{
    [SuppressMessage("<PERSON>Sharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public PotionModel Potion { get; }

    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public Creature? Target { get; }

    public override string Description
    {
        get
        {
            StringBuilder builder = new($"{Actor.Player!.Character.Id.Entry} drank {Potion.Id.Entry}");

            if (Target != null)
            {
                // Assumes potions only ever target monsters.
                // Make this check for players if that ever changes.
                builder.Append($" targeting {Target.Monster!.Id.Entry}");
            }

            return builder.ToString();
        }
    }

    public PotionUsedEntry(PotionModel potion, Creature? target, int roundNumber, CombatSide currentSide, CombatHistory history) :
        base(potion.Owner.Creature, roundNumber, currentSide, history)
    {
        Potion = potion;
        Target = target;
    }
}
