using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Text;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.MonsterMoves.MonsterMoveStateMachine;

namespace MegaCrit.Sts2.Core.Combat.History.Entries;

public class MonsterPerformedMoveEntry : CombatHistoryEntry
{
    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public MonsterModel Monster { get; }

    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public MoveState Move { get; }

    [SuppressMessage("ReSharper", "MemberCanBePrivate.Global", Justification = "Public API")]
    public IEnumerable<Creature>? Targets { get; }

    public override string Description
    {
        get
        {
            StringBuilder builder = new($"{Monster.Id.Entry} performed {Move.Id}");

            if (Targets != null)
            {
                builder.Append($" targeting {string.Join(",", Targets.Select(GetTargetName))}");
            }

            return builder.ToString();
        }
    }

    public MonsterPerformedMoveEntry(MonsterModel monster, MoveState move, IEnumerable<Creature>? targets, int roundNumber, CombatSide currentSide, CombatHistory history) :
        base(monster.Creature, roundNumber, currentSide, history)
    {
        Monster = monster;
        Move = move;
        Targets = targets;
    }

    private static string GetTargetName(Creature creature)
    {
        return creature.IsPlayer ? creature.Player!.Character.Id.Entry : creature.Monster!.Id.Entry;
    }
}