using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat.History;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Nodes;
using MegaCrit.Sts2.Core.Nodes.Audio;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Nodes.CommonUi;
using MegaCrit.Sts2.Core.Nodes.Ftue;
using MegaCrit.Sts2.Core.Nodes.HoverTips;
using MegaCrit.Sts2.Core.Nodes.Rooms;
using MegaCrit.Sts2.Core.Nodes.Screens.Map;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Combat;

public class CombatManager
{
    public const int baseHandDrawCount = 5;

    public static CombatManager Instance { get; } = new();

    /// <summary>
    /// Fired after combat is set up.
    /// Note that this happens a little bit before combat actually begins.
    /// </summary>
    public event Action<CombatState>? CombatSetUp;

    /// <summary>
    /// Fired when combat ends.
    /// </summary>
    public event Action<CombatRoom>? CombatEnded;

    /// <summary>
    /// Fired when combat is won.
    /// </summary>
    public event Action<CombatRoom>? CombatWon;

    /// <summary>
    /// Fired whenever the arrangement of creatures in the combat changes. Specifically, when:
    /// * A creature is added.
    /// * A creature is removed.
    /// * A creature's position changes.
    /// </summary>
    public event Action<CombatState>? CreaturesChanged;

    /// <summary>
    /// Fired whenever a new turn starts.
    /// </summary>
    public event Action<CombatState>? TurnStarted;

    /// <summary>
    /// Fired whenever a player ends their turn. Remember that, in multiplayer, this is not the same as switching to the
    /// enemy's turn.
    /// </summary>
    public event Action<Player, bool>? PlayerEndedTurn;

    /// <summary>
    /// Fired whenever a player un-does the end of their turn.
    /// </summary>
    public event Action<Player>? PlayerUnendedTurn;

    /// <summary>
    /// Fired when all players have fully committed to ending turn and all player actions are done (including end of turn
    /// hooks like Well-Laid Plans), but before the player hand flush.
    /// </summary>
    public event Action<CombatState>? AboutToSwitchToEnemyTurn;

    /// <summary>
    /// Fired when the local player's actions become disabled or enabled.
    /// </summary>
    public event Action<CombatState>? PlayerActionsDisabledChanged;

    private readonly HashSet<Player> _playersReadyToEndTurn = [];
    private readonly HashSet<Player> _playersReadyToBeginEnemyTurn = [];

    private bool _playerTakingExtraTurn;

    private CombatState? _state;

    /// <summary>
    /// THIS IS TEMPORARY AND SHOULD ONLY BE USED IN TESTS
    /// </summary>
    /// <returns></returns>
    public CombatState? DebugOnlyGetState() => _state;

    public bool IsPaused { get; private set; }

    /// <summary>
    /// Set to true when the player should not be able to interact with their hand or any potions.
    /// </summary>
    private bool _playerActionsDisabled;

    public bool PlayerActionsDisabled
    {
        get => _playerActionsDisabled;
        private set
        {
            if (_playerActionsDisabled != value)
            {
                _playerActionsDisabled = value;
                PlayerActionsDisabledChanged?.Invoke(_state!);
            }
        }
    }

    /// <summary>
    /// Set to true in the time between when all players are ready to begin the enemy turn and when the enemy turn begins.
    /// </summary>
    public bool EndingPlayerTurnPhaseTwo { get; private set; }

    /// <summary>
    /// Set to true in the time during phase one of the end of the player's turn.
    /// </summary>
    public bool EndingPlayerTurnPhaseOne { get; private set; }

    public CombatStateTracker StateTracker { get; }
    public CombatHistory History { get; }

    /// <summary>
    /// Is the combat currently in progress?
    /// True when the combat is done being initialized and has fully started.
    /// False when:
    /// * The combat is first being initialized.
    /// * The combat is ending (the last monster has been killed).
    /// * We're in a non-combat room.
    /// </summary>
    public bool IsInProgress { get; private set; }

    /// <summary>
    /// Is the combat about to end?
    /// True when combat is in progress but all the enemies are dead, and there is nothing stopping combat from ending
    /// (e.g. Phrog Parasite spawning in new enemies).
    /// False when
    /// * Combat is in progress and 1+ enemies are still alive.
    /// * Combat is not in progress.
    /// </summary>
    public bool IsAboutToEnd
    {
        get
        {
            // Combat has already ended.
            if (!IsInProgress) return false;

            // There are living enemies.
            if (_state != null && _state.Enemies.Any(e => e.IsAlive)) return false;

            // Something is stopping combat from ending (probably spawning new enemies).
            if (HookBus.Instance.ShouldStopCombatFromEnding()) return false;

            return true;
        }
    }

    private CombatManager()
    {
        History = new CombatHistory();
        StateTracker = new CombatStateTracker(this);
    }

    public void SetUpCombat(CombatState state)
    {
        if (_state != null) throw new InvalidOperationException("Make sure to reset the combat before setting up a new one.");

        _state = state;
        StateTracker.SetState(state);

        foreach (Player player in state.Players)
        {
            player.BeforeCombatStart(state);
        }

        NetCombatCardDb.Instance.StartCombat(state.Players);

        foreach (Creature creature in state.Creatures)
        {
            AddCreature(creature);
        }

        CombatSetUp?.Invoke(state);
    }

    public void AfterCombatRoomLoaded()
    {
        TaskHelper.RunSafely(StartCombatInternal());
    }

    public async Task StartCombatInternal()
    {
        if (_state!.Encounter!.HasBgm)
        {
            NClimbMusicController.Instance?.PlayCustomMusic(_state!.Encounter.CustomBgm);
        }

        // Run all initial creature logic
        foreach (Creature creature in _state!.Creatures)
        {
            await AfterCreatureAdded(creature);
        }

        // Pause the action queue so that multiplayer actions received don't begin until combat has been fully set up.
        ClimbManager.Instance.ActionExecutor.Pause();

        // Initialize the action queue executor for the beginning of combat, pausing the queue, so any queued actions
        // are sent only after the player turn begins.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotPlayPhase);

        IsInProgress = true;
        await HookBus.Instance.BeforeCombatStart();

        NClimbMusicController.Instance?.UpdateTrack();

        // Don't show the combat banner if the Combat Basics ftue is going to show up
        if (SaveManager.Instance.SeenFtue(NCombatRulesFtue.id))
        {
            NCombatRoom.Instance?.AddChildSafely(NCombatStartBanner.Create());
        }

        await Cmd.CustomScaledWait(0.5f, 1f, 1.2f);

        await StartTurn();

        if (!SaveManager.Instance.SeenFtue(NCombatRulesFtue.id))
        {
            NModalContainer.Instance?.Add(NCombatRulesFtue.Create()!);
        }
    }

    private async Task StartTurn(Func<Task>? actionDuringEnemyTurn = null)
    {
        if (!IsInProgress) return;

        await HookBus.Instance.BeforeTurnStart(_state!.CurrentSide, _state);

        if (_state.CurrentSide == CombatSide.Player)
        {
            // Allow player hand to be interacted with.
            PlayerActionsDisabled = false;

            _playersReadyToEndTurn.Clear();
            _playersReadyToBeginEnemyTurn.Clear();

            if (_state.RoundNumber != 1)
            {
                NCombatRoom.Instance?.AddChildSafely(NPlayerTurnBanner.Create(_state.RoundNumber));
            }

            if (!_playerTakingExtraTurn)
            {
                foreach (Creature creature in _state!.Enemies)
                {
                    creature.PrepareForNextTurn(_state.PlayerCreatures);
                }
            }

            _playerTakingExtraTurn = false;
        }
        else
        {
            NCombatRoom.Instance?.AddChildSafely(NEnemyTurnBanner.Create()!);
        }

        await Cmd.CustomScaledWait(0.5f, 0.8f, 1.2f);

        // Block clear happens in here.
        foreach (Creature creature in _state.CreaturesOnCurrentSide)
        {
            await creature.AfterTurnStart(_state.RoundNumber, _state.CurrentSide);
        }

        // Call the AfterBlockCleared hook only after all creatures have cleared block. It used to be in Creature.
        // AfterTurnStart, but that caused Beacon of Hope to trigger, then for the other player who gained block to clear
        // the block gained.
        foreach (Creature creature in _state.CreaturesOnCurrentSide)
        {
            await HookBus.Instance.AfterBlockCleared(creature);
        }

        if (_state.CurrentSide == CombatSide.Player)
        {
            foreach (Player player in _state.Players)
            {
                HookPlayerChoiceContext playerChoiceContext = new(player, LocalContext.NetId!.Value);
                Task task = SetupPlayerTurn(player, playerChoiceContext);
                await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);
            }
        }

        await HookBus.Instance.AfterTurnStart(_state.CurrentSide, _state);

        if (_state.CurrentSide == CombatSide.Player)
        {
            foreach (Player player in _state.Players)
            {
                await player.PlayerCombatState!.OrbQueue.AfterTurnStart();
            }

            ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After player turn start", null);

            // Set already-dead players to ready so they don't have to end turn
            foreach (Player player in _state.Players)
            {
                if (player.Creature.IsDead)
                {
                    SetReadyToEndTurn(player, true);
                }
            }

            // Check in case anything killed the enemies at the start of the player turn (i.e. Kingly Punch).
            await CheckWinCondition();

            // Unpause the action executor, which is paused at the start of combat and at the end of the player turn.
            // Note: This is different from CombatManager.Unpause().
            ClimbManager.Instance.ActionExecutor.Unpause();

            // Set to player turn, allowing actions to flow again.
            ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.PlayPhase);

            TurnStarted?.Invoke(_state);
        }
        else
        {
            TurnStarted?.Invoke(_state);

            ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After enemy turn start", null);

            await WaitForUnpause();

            // Check in case anything killed the enemies at the start of turn (i.e. Poison).
            await CheckWinCondition();
            await ExecuteEnemyTurn(actionDuringEnemyTurn);
        }
    }

    /// <summary>
    /// Executes the turn start sequence for a given player.
    /// If the player's turn start executes a player choice (e.g. Mayhem plays Cosmic Indifference), then the entire
    /// sequence is paused for this player. However, other players' turn start sequences may continue, and they may
    /// play cards while this is occuring.
    /// </summary>
    /// <param name="player">The player whose turn to setup.</param>
    /// <param name="playerChoiceContext">The player choice context to pass to hooks that take it.</param>
    private async Task SetupPlayerTurn(Player player, HookPlayerChoiceContext playerChoiceContext)
    {
        if (player.Creature.IsDead) return;

        if (HookBus.Instance.ShouldPlayerResetEnergy(player))
        {
            player.PlayerCombatState!.ResetEnergy();
        }
        else
        {
            player.PlayerCombatState!.AddMaxEnergyToCurrent();
        }

        await HookBus.Instance.AfterEnergyReset(player);
        await HookBus.Instance.BeforeHandDraw(player, playerChoiceContext, _state!);

        decimal handDraw = HookBus.Instance.ModifyHandDraw(player, baseHandDrawCount, out IEnumerable<AbstractModel> modifiers);
        await HookBus.Instance.AfterModifyingHandDraw(modifiers);

        if (_state!.RoundNumber == 1)
        {
            CardPile drawPile = CardPileTarget.Draw.GetPile(player);

            // Find all the cards with an enchantment that forces them to the bottom and move them to the
            // bottom.
            List<CardModel> bottomCards = drawPile
                .Cards
                .Where(c => c.Enchantment is { ShouldStartAtBottomOfDrawPile: true })
                .ToList();

            foreach (CardModel bottomCard in bottomCards)
            {
                drawPile.MoveToBottomInternal(bottomCard);
            }

            // Find all the Innate cards (_without_ an enchantment that forces them to the bottom) and move them
            // to the top.
            List<CardModel> innateCards = drawPile
                .Cards
                .Where(c => c.Keywords.Contains(CardKeyword.Innate))
                .Except(bottomCards)
                .ToList();

            foreach (CardModel innateCard in innateCards)
            {
                drawPile.MoveToTopInternal(innateCard);
            }

            // If there are more innate cards than the normal hand draw size, allow extra cards to be drawn...
            handDraw = Math.Max(handDraw, innateCards.Count);

            // ...but not past the maximum hand size.
            handDraw = Math.Min(handDraw, CardPile.maxCardsInHand);
        }

        await CardPileCmd.Draw(playerChoiceContext, handDraw, player, true);
    }

    /// <summary>
    /// Called in EndPlayerTurnAction to indicate that the player is ready to execute end-of-turn events.
    /// </summary>
    /// <param name="player">The player that readied up.</param>
    /// <param name="canBackOut">In multiplayer, notes if the player is allowed to back out of ending their turn.</param>
    public void SetReadyToEndTurn(Player player, bool canBackOut)
    {
        _playersReadyToEndTurn.Add(player);
        PlayerEndedTurn?.Invoke(player, canBackOut);

        if (AllPlayersReadyToEndTurn())
        {
            // We do not wish this to block the shared queue while
            // end of turn hooks are running, so we execute this as its own task
            Log.LogMessage(LogLevel.Debug, LogType.GameSync, "All players ready to end turn");
            TaskHelper.RunSafely(AfterAllPlayersReadyToEndTurn());
        }
    }

    public void UndoReadyToEndTurn(Player player)
    {
        _playersReadyToEndTurn.Remove(player);

        if (LocalContext.IsMe(player))
        {
            PlayerActionsDisabled = false;
        }

        PlayerUnendedTurn?.Invoke(player);
    }

    /// <summary>
    /// Call this when the end turn button is pressed to disable local player actions until the start of the next turn.
    /// In multiplayer, this prevents the player from playing cards after they have ended turn.
    /// In both SP and MP, this prevents the player from playing cards before the AfterTurnStart hook has run.
    /// It's important that we do this when the end turn button is pressed, instead of when the EndTurnAction is
    /// processed, because the player might try to execute actions while the end turn action is waiting in the queue.
    /// This is a little fragile; if actions do slip through in MP, it has the potential to cause a state divergence.
    /// Revisit if needed - we might need to discard actions on the host side (which ends up being way more complicated).
    /// </summary>
    public void OnEndedTurnLocally()
    {
        PlayerActionsDisabled = true;
    }

    /// <summary>
    /// Called in ReadyToBeginEnemyTurnAction to indicate that the player is ready to switch to the monster turn (or
    /// extra player turn, if necessary). Note that this is called automatically, not as part of a player action.
    /// </summary>
    /// <param name="player">The player that is ready to switch sides.</param>
    public void SetReadyToBeginEnemyTurn(Player player)
    {
        // If we receive this action after combat ends, do not execute it, otherwise we will re-pause the player queue
        // and map vote actions will not be executed.
        // Note that there is no condition under which this should currently happen - this is more of a failsafe.
        if (!IsInProgress) Log.Error("Trying to set player ready to begin enemy turn, but combat is over!");

        _playersReadyToBeginEnemyTurn.Add(player);

        if ((_playersReadyToBeginEnemyTurn.Count == _state!.Players.Count && _state.CurrentSide == CombatSide.Player) ||
            // This is a hack to allow simulating multiplayer in singleplayer by adding multiple players through
            // BootstrapSettings
            ClimbManager.Instance.NetService.Type == NetGameType.Singleplayer)
        {
            Log.LogMessage(LogLevel.Debug, LogType.GameSync, "All players ready to begin enemy turn, switching sides");
            TaskHelper.RunSafely(AfterAllPlayersReadyToBeginEnemyTurn());
        }
    }

    /// <returns>True if the passed player has hit the end turn button, and the next player turn has not yet begun.</returns>
    public bool IsPlayerReadyToEndTurn(Player player)
    {
        return _playersReadyToEndTurn.Contains(player);
    }

    public bool AllPlayersReadyToEndTurn()
    {
        bool allPlayersReady = _playersReadyToEndTurn.Count == _state!.Players.Count;

        // The IsSinglePlayer check is a hack to allow simulating multiplayer in singleplayer by adding multiple players
        // through BootstrapSettings and skipping the "everyone ready" check if that is the case.
        return ClimbManager.Instance.IsSinglePlayerOrFakeMultiplayer || (allPlayersReady && _state!.CurrentSide == CombatSide.Player);
    }

    private async Task EndEnemyTurn()
    {
        if (_state!.CurrentSide != CombatSide.Enemy)
        {
            throw new InvalidOperationException($"EndPlayerTurn called while the current side is {_state.CurrentSide}!");
        }

        await WaitForUnpause();
        await EndEnemyTurnInternal();

        _playerTakingExtraTurn = false;

        // Check in case anything killed the enemies at the end of their turn (i.e. Doom).
        await CheckWinCondition();

        if (!IsAboutToEnd)
        {
            SwitchSides();

            await WaitForUnpause();
            await StartTurn();
        }
    }

    // TODO: Maybe make this a private method that runs in response to a CombatState event.
    public void AddCreature(Creature creature)
    {
        if (!_state!.ContainsCreature(creature))
        {
            throw new InvalidOperationException("CombatState must already contain creature.");
        }

        creature.Monster?.SetUpForCombat();

        if (creature.SlotName != null)
        {
            _state.SortEnemiesBySlotName();
        }

        StateTracker.Subscribe(creature);
        CreaturesChanged?.Invoke(_state);
    }

    /// <summary>
    /// Called after both the Creature has been added to the room _and_ the NCreature is spawned.
    /// </summary>
    /// <param name="creature"></param>
    public async Task AfterCreatureAdded(Creature creature)
    {
        await creature.AfterAddedToRoom();

        // If a monster is spawned during the player turn, roll its move immediately.
        // If it's spawned during the enemy turn, wait until the player's next turn to roll its move.
        if (creature.IsEnemy && _state!.CurrentSide == CombatSide.Player)
        {
            creature.Monster!.RollMove(_state.Players.Select(p => p.Creature));
        }
    }

    /// <summary>
    /// Check for the player's hand to be empty and run the appropriate triggers if it is.
    ///
    /// We can't just do this check every time the hand size changes, because sometimes we're in the middle of a
    /// sequence of effects and we want to wait to check until they're all done.
    ///
    /// For example, if we have the Unceasing Top relic and the last card in our hand is Pommel Strike and we play it,
    /// we have to wait to check hand size until Pommel Strike is done being played, otherwise we'll draw two cards
    /// (one when your hand becomes "empty" immediately after Pommel Strike moves to the Play pile, and another after
    /// Pommel Strike's draw command executes).
    /// </summary>
    /// <param name="choiceContext">Object that keeps context of the action this is called from.</param>
    /// <param name="player">Player whose hand we want to check.</param>
    public async Task CheckForEmptyHand(PlayerChoiceContext choiceContext, Player player)
    {
        if (!IsInProgress) return;
        if (CardPileTarget.Hand.GetPile(player).Cards.Any()) return;

        await HookBus.Instance.AfterHandEmptied(choiceContext, player);
    }

    public void Reset()
    {
        if (_state != null)
        {
            foreach (Creature creature in _state.Creatures.ToList())
            {
                creature.Reset();
                RemoveCreature(creature);
                _state.RemoveCreature(creature);
            }

            _state = null;
        }

        IsInProgress = false;
        History.Clear();
    }

    public async Task HandlePlayerDeath(Player player)
    {
        // TODO: This is temporary until we know what death handling for multiple players actually looks like
        if (!IsInProgress) return;

        await CardPileCmd.RemoveFromCombat(
            ((CardPile[])
            [
                player.PlayerCombatState!.Hand,
                player.PlayerCombatState.DrawPile,
                player.PlayerCombatState.DiscardPile,
                player.PlayerCombatState.ExhaustPile,
                player.PlayerCombatState.PlayPile
            ]).SelectMany(p => p.Cards)
            .ToArray());

        await PlayerCmd.SetEnergy(0, player);
        await PlayerCmd.SetStars(0, player);
    }

    public void LoseCombat()
    {
        IsInProgress = false;
        ClimbManager.Instance.OnCombatLost(_state!);
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// </summary>
    public async Task EndCombatInternal()
    {
        Player localPlayer = LocalContext.GetMe(_state)!;
        IClimbState climbState = _state!.ClimbState;
        CombatRoom room = (CombatRoom)climbState.CurrentRoom!;

        IsInProgress = false;
        PlayerActionsDisabled = false;

        foreach (Player player in _state.Players)
        {
            await player.ReviveBeforeCombatEnd();
        }

        await HookBus.Instance.AfterCombatEnd(room);
        History.Clear();
        room.OnCombatEnded();
        CombatEnded?.Invoke(room);

        if (ClimbManager.Instance.NetService.Type != NetGameType.Replay)
        {
            ClimbManager.Instance.CombatReplayWriter.WriteReplay("user://replays/latest.mcr", true);
        }

        foreach (Player player in _state!.Players)
        {
            player.AfterCombatEnd();
        }

        await HookBus.Instance.AfterCombatVictory(room);

        NHoverTipSet.Clear();

        if (climbState.CurrentMapPointHistoryEntry != null)
        {
            climbState.CurrentMapPointHistoryEntry!.TurnsTaken = _state!.RoundNumber;
        }

        if (climbState.CurrentRoomCount == 1)
        {
            // Only save if this combat is the only room in the stack.
            // This avoids weird situations where we save in the middle of an event that has a combat in it, like Dense
            // Vegetation or Battleworn Dummy.
            room.MarkPreFinished();
            await SaveManager.Instance.SaveClimb(room);
            NMapScreen.Instance?.SetTravelEnabled(true);
        }

        SaveManager.Instance.UpdateProgressSaveAfterCombatWon(room);

        // Casey says, maybe encounters is simpler...
        AchievementsHelper.CheckForDefeatedAllEnemiesAchievement(climbState.Act, localPlayer);

        SaveManager.Instance.SaveProgressFile();

        if (room.RoomType == RoomType.Boss)
        {
            AchievementsHelper.AfterBossDefeated(localPlayer);
        }

        CombatWon?.Invoke(room);

        // Unpause the action executor in case it was paused.
        ClimbManager.Instance.ActionExecutor.Unpause();

        // Unpause all player action queues in case they were paused.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotInCombat);

        NClimbMusicController.Instance?.UpdateTrack();
    }

    public void RemoveCreature(Creature creature)
    {
        if (creature.IsMonster)
        {
            // Note: If we ever need this to be async/awaited, it should probably be moved to CreatureCmd.Remove
            creature.Monster!.BeforeRemovedFromRoom();
        }

        StateTracker.Unsubscribe(creature);
        CreaturesChanged?.Invoke(_state!);
    }

    public async Task CheckWinCondition()
    {
        if (IsAboutToEnd)
        {
            await EndCombatInternal();
        }
    }

    private async Task ExecuteEnemyTurn(Func<Task>? actionDuringEnemyTurn = null)
    {
        // Need this check here in case there are no enemies
        if (!IsInProgress) return;

        if (actionDuringEnemyTurn != null)
        {
            await actionDuringEnemyTurn.Invoke();
        }

        foreach (Creature enemy in _state!.Enemies.ToList())
        {
            // The enemy may have been removed from combat during a previous enemy's move (for example, it may have died
            // to the Bent Nail relic), so make sure it's still in combat.
            // We can't just check if the enemy is dead here, because some enemies (like Decimillipede) perform moves
            // while dead.
            if (!_state.ContainsCreature(enemy)) continue;

            NCreature? node = NCombatRoom.Instance?.GetCreatureNode(enemy);

            if (node != null)
            {
                await node.PerformIntent();
            }

            await enemy.TakeTurn();

            await WaitForUnpause();
            await CheckWinCondition();

            if (!IsInProgress) return;
        }

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After enemy turn end", null);

        await EndEnemyTurn();
    }

    private async Task AfterAllPlayersReadyToEndTurn()
    {
        EndingPlayerTurnPhaseOne = true;

        // This causes all player-driven actions to be cancelled until the next player turn. It is vital that this occurs
        // before any hooks are executed, otherwise timing issues can occur when executing non-hook actions interleaved
        // with hooks.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.EndTurnPhaseOne);

        await EndPlayerTurnPhaseOneInternal();

        // Certain hooks in phase one can end combat, e.g. The Bomb. If combat has ended, we do not need this action.
        // If we are in replay mode, the replay enqueues this, not us.
        if (IsInProgress && ClimbManager.Instance.NetService.Type != NetGameType.Replay)
        {
            ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(
                new ReadyToBeginEnemyTurnAction(LocalContext.GetMe(_state!)!)
            );
        }

        EndingPlayerTurnPhaseOne = false;

        // Once ready actions are received from all players, this flow continues in AfterAllPlayersReadyToBeginEnemyTurn
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This calls all end-of-turn hooks that could require player choices to be made.
    /// </summary>
    public async Task EndPlayerTurnPhaseOneInternal()
    {
        if (_state!.CurrentSide != CombatSide.Player)
        {
            throw new InvalidOperationException($"EndPlayerTurn called while the current side is {_state.CurrentSide}!");
        }

        await WaitForUnpause();

        await HookBus.Instance.BeforeTurnEnd(_state.CurrentSide, _state);

        foreach (Player player in _state.Players)
        {
            await player.PlayerCombatState!.OrbQueue.BeforeTurnEnd();

            CardPile hand = CardPileTarget.Hand.GetPile(player);
            CardPile discardPile = CardPileTarget.Discard.GetPile(player);
            List<CardModel> turnEndCards = [];
            List<CardModel> etherealCards = [];

            foreach (CardModel card in hand.Cards)
            {
                if (card.HasTurnEndInHandEffect)
                {
                    turnEndCards.Add(card);
                }
                else if (card.HasEtherealThisTurn && HookBus.Instance.ShouldEtherealTrigger(card))
                {
                    etherealCards.Add(card);
                }
            }

            List<Task> etherealTasks = [];

            foreach (CardModel etherealCard in etherealCards)
            {
                HookPlayerChoiceContext playerChoiceContext = new(etherealCard.Owner, LocalContext.NetId!.Value);
                Task task = CardCmd.Exhaust(playerChoiceContext, etherealCard, true);
                await playerChoiceContext.AssignTaskAndWaitForPauseOrCompletion(task);

                if (!task.IsCompleted)
                {
                    etherealTasks.Add(task);
                }
            }

            await Task.WhenAll(etherealTasks);

            foreach (CardModel card in turnEndCards)
            {
                await CardPileCmd.Add(card, CardPileTarget.Play);

                if (LocalContext.IsMe(player))
                {
                    await Cmd.CustomScaledWait(0.3f, 0.6f, 0.6f);
                }

                await card.OnTurnEndInHand();

                // Make sure that if the end of turn card has ethereal, that it goes to the Ethereal pile
                // rather than the discard pile
                if (card.HasEtherealThisTurn)
                {
                    HookPlayerChoiceContext playerChoiceContext = new(card.Owner, LocalContext.NetId!.Value);
                    await CardCmd.Exhaust(playerChoiceContext, card, true);
                }
                else
                {
                    await CardPileCmd.Add(card, discardPile);
                }
            }
        }

        foreach (Player player in _state.Players)
        {
            await HookBus.Instance.BeforeFlush(player);
        }

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("After player turn phase one end", null);

        // Check if combat is over, e.g. from The Bomb.
        await CheckWinCondition();
    }

    private async Task EndEnemyTurnInternal()
    {
        await HookBus.Instance.BeforeTurnEnd(_state!.CurrentSide, _state);
        await HookBus.Instance.AfterTurnEnd(_state.CurrentSide, _state);
    }

    private async Task AfterAllPlayersReadyToBeginEnemyTurn()
    {
        EndingPlayerTurnPhaseTwo = true;

        // This causes all player queues to become paused, and stops non-hook actions from being cancelled.
        // Player queues must be paused during this time, but still allow receiving actions from other players, because
        // of different execution speeds. If client 1 begins the player turn before client 2 (because 1 is on fast and 2
        // is on slow), then client 1 may begin playing cards while 2 is still in the enemy turn. Client 2 should start
        // executing those card plays as soon as their player turn begins.
        ClimbManager.Instance.ActionQueueSynchronizer.SetCombatState(ActionSynchronizerCombatState.NotPlayPhase);

        AboutToSwitchToEnemyTurn?.Invoke(_state!);

        // This finishes the ReadyToBeginEnemyTurnAction, in case combat is about to be finished
        await Task.Yield();

        await EndPlayerTurnPhaseTwoInternal();
        await SwitchFromPlayerToEnemySide();
        EndingPlayerTurnPhaseTwo = false;
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This does all the player state cleanup for the end of their turn. It must not call any hooks that might cause
    /// player choices to occur.
    /// </summary>
    public async Task EndPlayerTurnPhaseTwoInternal()
    {
        if (_state!.CurrentSide != CombatSide.Player)
        {
            throw new InvalidOperationException($"EndPlayerTurnPhaseTwo called while the current side is {_state.CurrentSide}!");
        }

        foreach (Player player in _state.Players)
        {
            CardPile hand = CardPileTarget.Hand.GetPile(player);
            List<CardModel> cardsToFlush = [];
            List<CardModel> cardsToRetain = [];

            foreach (CardModel card in hand.Cards)
            {
                if (card.ShouldRetainThisTurn)
                {
                    cardsToRetain.Add(card);
                }
                else
                {
                    cardsToFlush.Add(card);
                }
            }

            if (HookBus.Instance.ShouldFlush(player))
            {
                // Flush hand.
                // We use Add instead of Discard here to avoid triggering "when a card is discarded" effects.
                await CardPileCmd.Add(cardsToFlush, CardPileTarget.Discard.GetPile(player));
            }

            foreach (CardModel card in cardsToRetain)
            {
                await HookBus.Instance.AfterCardRetained(card);
            }

            player.PlayerCombatState!.EndOfPlayerTurnCleanup();
        }

        await HookBus.Instance.AfterTurnEnd(_state.CurrentSide, _state);

        ClimbManager.Instance.ChecksumTracker.GenerateChecksum("after player turn phase two end", null);
    }

    /// <summary>
    /// DO NOT CALL THIS unless you're in this class or ModelTest.
    /// This switches from the player side to the enemy side, handling extra player turns if necessary.
    /// </summary>
    /// <param name="actionDuringEnemyTurn">Optional action to execute during the enemy turn. This is useful for tests.</param>
    public async Task SwitchFromPlayerToEnemySide(Func<Task>? actionDuringEnemyTurn = null)
    {
        _playerTakingExtraTurn = false;

        foreach (Player player in _state!.Players)
        {
            _playerTakingExtraTurn = _playerTakingExtraTurn || HookBus.Instance.ShouldTakeExtraTurn(player);
        }

        SwitchSides();

        if (_playerTakingExtraTurn)
        {
            foreach (Player player in _state.Players)
            {
                await HookBus.Instance.AfterTakingExtraTurn(player);
            }
        }

        await WaitForUnpause();
        await StartTurn(actionDuringEnemyTurn);
    }

    private void SwitchSides()
    {
        if (_state!.CurrentSide == CombatSide.Player && !_playerTakingExtraTurn)
        {
            _state.CurrentSide = CombatSide.Enemy;
        }
        else
        {
            _state.CurrentSide = CombatSide.Player;
            _state.RoundNumber++;
        }

        foreach (Creature creature in _state!.Creatures)
        {
            creature.OnSideSwitch();
        }
    }

    /// <summary>
    /// Pause combat.
    /// </summary>
    public void Pause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        // No pausing outside of combat.
        if (!IsInProgress) return;

        IsPaused = true;
    }

    /// <summary>
    /// Un-pause combat.
    /// </summary>
    public void Unpause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        IsPaused = false;
    }

    public async Task WaitForUnpause()
    {
        // No pausing in tests.
        if (TestMode.IsOn) return;

        while (IsPaused && IsInProgress)
        {
            await NGame.Instance!.ToSignal(NGame.Instance.GetTree(), SceneTree.SignalName.ProcessFrame);
        }
    }
}
