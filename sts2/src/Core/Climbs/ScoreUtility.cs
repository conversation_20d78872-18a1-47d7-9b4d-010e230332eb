using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Climbs;

public static class ScoreUtility
{
    /// <summary>
    /// A sentinel value for clients in a multiplayer climb to submit when uploading a daily climb score.
    /// For more information on why this is needed, see DailyClimbUtility.ShouldUploadScore.
    /// </summary>
    public const int clientScore = -99999;

    /// <summary>
    /// Calculates the score for a given climb.
    /// </summary>
    /// <param name="climb">The climb that was just completed.</param>
    /// <param name="playerId">The ID of the local player.</param>
    /// <param name="won">True if the climb ended in a victory.</param>
    /// <returns>The final score for the climb.</returns>
    public static int CalculateScore(SerializableClimb climb, ulong playerId, bool won)
    {
        // This implementation is temporary! Later we will need to:
        // - Adjust scores based on new calculations (we're using STS1's calculations)
        // - Return a breakdown of what score was obtained where to display it in the final screen

        // Not yet implemented:
        // - Overkill
        // - Heartbreaker
        // - C-c-c-combo
        // - On My Own Terms

        // Do a bunch of setup of local vars for various calculations
        List<MapPointHistoryEntry> flattenedEntries = climb.MapPointHistory.SelectMany(p => p).ToList();
        SerializablePlayer player = climb.Players.First(p => p.NetId == playerId);
        CharacterModel character = ModelDb.GetById<CharacterModel>(player.CharacterId!);

        Dictionary<ModelId, int> cardCounts = [];
        foreach (SerializableCard card in player.Deck)
        {
            cardCounts.TryGetValue(card.Id!, out int count);
            cardCounts[card.Id!] = count + 1;
        }

        int score = 0;

        // 5 points per floor
        score += flattenedEntries.Count() * 5;

        // 2 points per enemy encounter
        score += flattenedEntries.SelectMany(e => e.RoomTypes).Count(t => t == RoomType.Monster) * 2;

        // 10/20/30 points for each elite slain in the respective acts
        for (int i = 0; i < climb.MapPointHistory.Count; i++)
        {
            score += climb.MapPointHistory[i].SelectMany(e => e.RoomTypes).Count(t => t == RoomType.Elite) * 10 * i;
        }

        // 50/150/300/500/750 points for the respective boss count killed
        int bossesSlain = flattenedEntries.SelectMany(e => e.RoomTypes).Count(t => t == RoomType.Boss);

        score += bossesSlain switch
        {
            0 => 0,
            1 => 50,
            2 => 150,
            3 => 300,
            4 => 500,
            _ => 750
        };

        // 25 points per elite without taking damage
        score += flattenedEntries.Where(e => e.RoomTypes.Contains(RoomType.Elite)).Count(e => e.GetEntry(playerId).DamageTaken == 0) * 25;

        if (flattenedEntries.Where(e => e.RoomTypes.Contains(RoomType.Boss)).Count(e => e.GetEntry(playerId).DamageTaken == 0) >= 3)
        {
            // 200 points if at least 3 bosses killed without taking damage
            score += 200;
        }
        else
        {
            // 50 points per boss without taking damage
            score += flattenedEntries.Where(e => e.RoomTypes.Contains(RoomType.Boss)).Count(e => e.GetEntry(playerId).DamageTaken == 0) * 50;
        }

        // 50 points per 4 copies of any non-starter card
        score += cardCounts.Count(p => p.Value >= 4 && ModelDb.GetById<CardModel>(p.Key).Rarity != CardRarity.Basic) * 25;

        // 50 points for deck size greater than 50, 25 points for deck size greater than 35
        score += player.Deck.Count >= 50 ? 50 : player.Deck.Count >= 35 ? 25 : 0;

        // 25 points for traveling to 15+ unknown rooms
        score += flattenedEntries.Count(e => e.MapPointType == MapPointType.Unknown) > 15 ? 25 : 0;

        // 50 points for 25+ relics
        score += player.Relics.Count > 25 ? 50 : 0;

        // 50 points for 30+ max HP increase, 25 for 15+ increase
        int maxHpDiff = player.MaxHp - character.StartingHp;
        score += maxHpDiff >= 50 ? 30 : maxHpDiff >= 25 ? 15 : 0;

        // 75 points for 3000 gold, 50 points for 2000 gold, 25 points for 1000 gold
        int totalGold = flattenedEntries.Sum(e => e.GetEntry(playerId).Gold) + character.StartingGold;
        score += totalGold >= 3000 ? 75 : totalGold >= 2000 ? 50 : totalGold > 1000 ? 25 : 0;

        // 50 points for zero rare cards
        score += player.Deck.Any(c => ModelDb.GetById<CardModel>(c.Id!).Rarity == CardRarity.Rare) ? 0 : 50;

        // 50 points for victory in under 45 min, 25 for under 60 min
        if (won)
        {
            score += climb.ClimbTime < 45 * 60 ? 50 : climb.ClimbTime < 60 * 60 ? 25 : 0;
        }

        // 100 points for 5 curses in deck
        score += player.Deck.Count(c => ModelDb.GetById<CardModel>(c.Id!).Rarity == CardRarity.Curse) > 5 ? 100 : 0;

        // 100 points for no duplicates in deck (excluding starter
        score += cardCounts.Any(p => p.Value > 1 && ModelDb.GetById<CardModel>(p.Key).Rarity != CardRarity.Basic) ? 0 : 100;

        // 5% score per ascension level
        score = (int)Math.Round(score * (1 + 0.05 * climb.Ascension));

        return score;
    }
}
