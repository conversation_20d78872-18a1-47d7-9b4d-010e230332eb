using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.RelicPools;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Climbs;

public class RelicGrabBag
{
    private static readonly HashSet<RelicRarity> _rarities =
    [
        RelicRarity.Common,
        RelicRarity.Uncommon,
        RelicRarity.Rare,
        RelicRarity.Shop
    ];

    /// <summary>
    /// "Deque" is short for Double-Ended Queue.
    /// https://en.wikipedia.org/wiki/Double-ended_queue
    /// </summary>
    private readonly Dictionary<RelicRarity, List<RelicModel>> _deques = new();

    public bool IsPopulated => _deques.Count > 0;

    private readonly bool _refreshAllowed;
    private List<RelicModel>? _originalRelics;

    public RelicGrabBag()
    {
    }

    public RelicGrabBag(bool refreshAllowed)
    {
        _refreshAllowed = refreshAllowed;
    }

    public void Populate(CharacterModel character, Rng rng)
    {
        if (IsPopulated) throw new InvalidOperationException("Grab bag was already populated.");

        // The grab bag should contain all shared relics and all relics for the specified
        // character.
        List<RelicModel> validRelics = ModelDb.RelicPool<SharedRelicPool>().Relics.ToList();
        validRelics.AddRange(character.RelicPool.Relics);
        validRelics.RemoveAll(r => !_rarities.Contains(r.Rarity));
        _originalRelics = validRelics;

        foreach (RelicModel relic in validRelics)
        {
            if (!_deques.TryGetValue(relic.Rarity, out List<RelicModel>? value))
            {
                value = new List<RelicModel>();
                _deques[relic.Rarity] = value;
            }

            value.Add(relic);
        }

        foreach (List<RelicModel> bucket in _deques.Values)
        {
            bucket.UnstableShuffle(rng);
        }
    }

    public void Populate(IEnumerable<RelicModel> relics, Rng rng)
    {
        if (IsPopulated) throw new InvalidOperationException("Grab bag was already populated.");

        _originalRelics = relics.ToList();

        foreach (RelicModel relic in _originalRelics)
        {
            if (!_deques.TryGetValue(relic.Rarity, out List<RelicModel>? value))
            {
                value = new List<RelicModel>();
                _deques[relic.Rarity] = value;
            }

            value.Add(relic);
        }

        foreach (List<RelicModel> bucket in _deques.Values)
        {
            bucket.UnstableShuffle(rng);
        }
    }

    /// <summary>
    /// Get the front relic from the bucket of the specified rarity and remove it from the bucket.
    /// Used by most relic reward sources (elite combat rewards etc).
    /// </summary>
    /// <param name="rarity">Rarity of the relic we want.</param>
    /// <returns>RelicModel</returns>
    public RelicModel? PullFromFront(RelicRarity rarity)
    {
        List<RelicModel>? deque = GetAvailableDeque(rarity);
        if (deque == null || deque.Count == 0) return null;

        RelicModel relic = deque[0];

        while (!relic.IsAllowed)
        {
            // if it isn't allowed, remove it from the possible list of relics we can get.
            deque.RemoveAt(0);

            if (deque.Count == 0)
            {
                if (!_refreshAllowed) return null;
                RefreshRarity(rarity);
            }

            relic = deque[0];
        }

        deque.RemoveAt(0);
        return relic;
    }

    /// <summary>
    /// Get the back relic from the bucket of the specified rarity and remove it from the bucket.
    /// Used in shops.
    /// </summary>
    /// <param name="rarity">Rarity of the relic we want.</param>
    /// <param name="blacklist">Relics that this shouldn't be able to return.</param>
    /// <returns>RelicModel</returns>
    public RelicModel? PullFromBack(RelicRarity rarity, IEnumerable<RelicModel> blacklist)
    {
        // Make sure we're blacklisting canonical relics here, otherwise we could end up accidentally allowing some
        // because they're different instances.
        foreach (RelicModel blacklistedRelic in blacklist)
        {
            blacklistedRelic.AssertCanonical();
        }

        List<RelicModel>? deque = GetAvailableDeque(rarity);
        if (deque == null || deque.Count == 0) return null;

        int index = deque.Count - 1;

        // Skip past blacklisted relics and relics that aren't allowed.
        while (blacklist.Contains(deque[index]) || !deque[index].IsAllowed)
        {
            index--;
            if (index < 0) return null;
        }

        RelicModel relic = deque[index];
        deque.RemoveAt(index);

        return relic;
    }

    /// <summary>
    /// Removes the specified relic from the bucket of the appropriate rarity so that it is never rolled.
    /// If it's already not there, then nothing happens.
    /// </summary>
    public void Remove<T>() where T : RelicModel => Remove(ModelDb.Relic<T>());

    public void Remove(RelicModel relic)
    {
        List<RelicModel>? deque = GetAvailableDeque(relic.Rarity);
        if (deque == null || deque.Count == 0) return;

        deque.RemoveAll(r => r.Id == relic.Id);
    }

    private List<RelicModel>? GetAvailableDeque(RelicRarity rarity)
    {
        List<RelicModel>? deque = GetDeque(rarity);

        if (deque.Count == 0 && _refreshAllowed)
        {
            RefreshRarity(rarity);
        }

        // If a lower-rarity deque is empty, fall "forward" to a higher-rarity deque.
        while (deque is { Count: 0 })
        {
            rarity = rarity switch
            {
                RelicRarity.Shop => RelicRarity.Common,
                RelicRarity.Common => RelicRarity.Uncommon,
                RelicRarity.Uncommon => RelicRarity.Rare,
                _ => RelicRarity.None
            };

            deque = rarity == RelicRarity.None ? null : GetDeque(rarity);
        }

        return deque;
    }

    public SerializableRelicGrabBag ToSerializable()
    {
        SerializableRelicGrabBag save = new();

        foreach ((RelicRarity key, List<RelicModel> deque) in _deques)
        {
            List<ModelId> ids = deque.Select(r => r.Id).ToList();
            save.RelicIdLists[key] = ids;
        }

        return save;
    }

    public static RelicGrabBag FromSerializable(SerializableRelicGrabBag save)
    {
        RelicGrabBag bag = new();
        bag.LoadFromSerializable(save);
        return bag;
    }

    public void LoadFromSerializable(SerializableRelicGrabBag save)
    {
        foreach ((RelicRarity key, List<ModelId> ids) in save.RelicIdLists)
        {
            if (!_deques.TryGetValue(key, out List<RelicModel>? relics))
            {
                relics = [];
                _deques[key] = relics;
            }

            relics.Clear();

            foreach (ModelId id in ids)
            {
                relics.Add(ModelDb.GetById<RelicModel>(id));
            }
        }
    }

    private List<RelicModel> GetDeque(RelicRarity rarity)
    {
        return _deques.TryGetValue(rarity, out List<RelicModel>? value) ? value : [];
    }

    private void RefreshRarity(RelicRarity rarity)
    {
        if (_originalRelics == null) throw new InvalidOperationException("Tried to refresh relics but original list is null");

        foreach (RelicModel relic in _originalRelics)
        {
            if (relic.Rarity != rarity) continue;
            if (!_deques.TryGetValue(relic.Rarity, out List<RelicModel>? value))
            {
                value = new List<RelicModel>();
                _deques[relic.Rarity] = value;
            }

            value.Add(relic);
        }
    }
}
