using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Climbs.History;

public struct CardEnchantmentHistoryEntry(ModelId card, ModelId enchantment) : IPacketSerializable
{
    [JsonPropertyName("card")]
    public ModelId Card { get; set; } = card;

    [JsonPropertyName("enchantment")]
    public ModelId Enchantment { get; set; } = enchantment;

    public void Serialize(PacketWriter writer)
    {
        writer.WriteModelEntry(Card);
        writer.WriteModelEntry(Enchantment);
    }

    public void Deserialize(PacketReader reader)
    {
        Card = reader.ReadModelIdAssumingType<CardModel>();
        Enchantment = reader.ReadModelIdAssumingType<EnchantmentModel>();
    }
}
