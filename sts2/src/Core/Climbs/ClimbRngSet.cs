using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Rngs;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Saves.Climbs;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Climbs;

public class ClimbRngSet
{
    private static readonly ClimbRngSet _mockInstance = new(string.Empty);

    public static ClimbRngSet GetMockInstance()
    {
        // If we ever need to run an actual combat with no containing climb (like for debugging or in a special
        // one-off game mode), rethink this.
        if (TestMode.IsOff)
        {
            throw new InvalidOperationException("Cannot get ClimbRng when not in a climb outside of tests!");
        }

        return _mockInstance;
    }

    /// <summary>
    /// We generate a string for the seed that gets hashed to a uint for the actual thing passed to the RNGs.
    /// This is the original string that was input, for display purposes.
    /// </summary>
    public string StringSeed { get; }

    /// <summary>
    /// The seed that was hashed from the InputSeed.
    /// </summary>
    public uint Seed { get; }

    private readonly Dictionary<ClimbRngType, Rng> _rngs = new();

    /// <summary>
    /// Determines everything that's generated upfront when a climb first starts. This includes:
    /// * Which monsters you'll fight.
    /// * Which events you'll run into.
    /// * Which relics you'll be offered.
    /// </summary>
    public Rng UpFront => GetRng(ClimbRngType.UpFront);

    /// <summary>
    /// Determines how your draw pile gets shuffled, both at the start of combat and when you
    /// run out of cards in it.
    /// </summary>
    public Rng Shuffle => GetRng(ClimbRngType.Shuffle);

    /// <summary>
    /// Determines what types of room is rolled when visiting an unknown map point.
    /// </summary>
    public Rng UnknownMapPoint => GetRng(ClimbRngType.UnknownMapPoint);

    /// <summary>
    /// Determines what cards are generated during combat by things like Attack Potion.
    /// Distinct from CardRewards because we don't want Attack Potion usage to impact card rewards.
    /// </summary>
    public Rng CombatCardGeneration => GetRng(ClimbRngType.CombatCardGeneration);

    /// <summary>
    /// Determines what potions are generated during combat by things like Alchemize.
    /// Distinct from PotionRewards because these can't generate healing potions.
    /// </summary>
    public Rng CombatPotionGeneration => GetRng(ClimbRngType.CombatPotionGeneration);

    /// <summary>
    /// Determines what cards are randomly chosen during combat by things like True Grit.
    /// </summary>
    public Rng CombatCardSelection => GetRng(ClimbRngType.CombatCardSelection);

    /// <summary>
    /// Determines random in-combat energy costs for things like Confusion and Snecko Eye.
    /// </summary>
    public Rng CombatEnergyCosts => GetRng(ClimbRngType.CombatEnergyCosts);

    /// <summary>
    /// Determines the results of random targeting during combat (Bouncing Flask, Sword Boomerang, etc.).
    /// </summary>
    public Rng CombatTargets => GetRng(ClimbRngType.CombatTargets);

    /// <summary>
    /// Determines what moves each monster makes whenever there's randomness involved.
    /// </summary>
    public Rng MonsterAi => GetRng(ClimbRngType.MonsterAi);

    /// <summary>
    /// Determines some niche one-off RNG stuff that we don't care about interacting, like
    /// Strange Spoon.
    /// </summary>
    public Rng Niche => GetRng(ClimbRngType.Niche);

    /// <summary>
    /// Determines what orbs are randomly chosen during combat by things like Chaos.
    /// </summary>
    public Rng CombatOrbGeneration => GetRng(ClimbRngType.CombatOrbs);

    /// <summary>
    /// Determines who gets treasure when multiple players pick the same relic at the treasure room.
    /// </summary>
    public Rng TreasureRoomRelics => GetRng(ClimbRngType.TreasureRoomRelics);

    public ClimbRngSet(string seed)
    {
        StringSeed = seed;
        Seed = (uint)StringHelper.GetDeterministicHashCode(seed);

        foreach (ClimbRngType rngType in Enum.GetValues<ClimbRngType>())
        {
            _rngs[rngType] = CreateRng(rngType);
        }
    }

    private Rng CreateRng(ClimbRngType rngType)
    {
        string snakeCased = StringHelper.SnakeCase(rngType.ToString());
        return new Rng(Seed, snakeCased);
    }

    public SerializableClimbRngSet ToSerializable()
    {
        SerializableClimbRngSet save = new() { Seed = StringSeed };

        foreach ((ClimbRngType key, Rng rng) in _rngs)
        {
            save.Counters[key] = rng.Counter;
        }

        return save;
    }

    public static ClimbRngSet FromSave(SerializableClimbRngSet save)
    {
        ClimbRngSet set = new(save.Seed!);

        foreach ((ClimbRngType key, int counter) in save.Counters)
        {
            Rng rng = set.CreateRng(key);
            rng.FastForwardCounter(counter);
            set._rngs[key] = rng;
        }

        return set;
    }

    public void LoadFromSerializable(SerializableClimbRngSet save)
    {
        if (StringSeed != save.Seed) throw new NotImplementedException("RngSet seed should not change during the climb!");

        foreach ((ClimbRngType key, int counter) in save.Counters)
        {
            Rng rng = _rngs[key];

            if (counter < rng.Counter)
            {
                rng = CreateRng(key);
                rng.FastForwardCounter(counter);
                _rngs[key] = rng;
            }
            else
            {
                _rngs[key].FastForwardCounter(counter);
            }
        }
    }

    private Rng GetRng(ClimbRngType rngType) => _rngs[rngType];
}
