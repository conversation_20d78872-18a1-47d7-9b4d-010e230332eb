using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;

namespace MegaCrit.Sts2.Core.Climbs;

public class ClimbHistory : ISaveSchema
{
    [JsonPropertyName("schema_version")]
    public int SchemaVersion { get; set; }

    #region Info

    public required PlatformType PlatformType { get; init; }
    public required GameMode GameMode { get; init; }
    public required bool Win { get; init; }
    public required string Seed { get; init; }
    public required long StartTime { get; init; }
    public required float ClimbTime { get; init; }

    public required int Ascension { get; init; }
    public required string BuildId { get; init; } = "pre-v0.42";

    #endregion

    #region Stats

    public bool WasAbandoned { get; init; }
    public ModelId KilledByEncounter { get; init; } = ModelId.none;
    public ModelId KilledByEvent { get; init; } = ModelId.none;

    #endregion

    #region Equipment

    public List<ClimbHistoryPlayer> Players { get; init; } = [];

    // It'd be nice to be able to key MapPointHistory by ActModel.Id, but that doesn't play nice with serialization.
    public List<ModelId> Acts { get; init; } = [];
    public List<SerializableModifier> Modifiers { get; init; } = [];
    public List<List<MapPointHistoryEntry>> MapPointHistory { get; init; } = [];

    #endregion

    public static readonly JsonSerializerOptions defaultSerializerSettings = new()
    {
        WriteIndented = true,
        IncludeFields = true,
        PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
        TypeInfoResolver = new DefaultJsonTypeInfoResolver { Modifiers = { JsonSerializeConditionAttribute.CheckJsonSerializeConditionsModifier } },
        Converters = { new ModelIdClimbSaveConverter() },
        UnmappedMemberHandling = JsonUnmappedMemberHandling.Disallow
    };
}
