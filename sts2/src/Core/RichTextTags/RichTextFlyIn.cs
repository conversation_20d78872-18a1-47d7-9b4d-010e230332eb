using Godot;
using Godot.Collections;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.RichTextTags;

[GlobalClass]

[Tool]
public partial class RichTextFlyIn : AbstractMegaRichTextEffect
{
    private static readonly StringName _xOffsetKey = new StringName("offset_x");
    private static readonly StringName _yOffsetKey = new StringName("offset_y");

    public new string bbcode = "fly_in";
    protected override string Bbcode => bbcode;

    public override bool _ProcessCustomFX(CharFXTransform charFx)
    {
        Dictionary env = charFx.Env;

        // Reset the offset to avoid accumulation if other effects are applied
        charFx.Offset = Vector2.Zero;

        Vector2 originPosition = Vector2.Zero;

        if (env.TryGetValue(_xOffsetKey, out Variant xOffset))
        {
            originPosition.X = (float)xOffset.AsDouble();
        }

        if (env.TryGetValue(_yOffsetKey, out Variant yOffset))
        {
            originPosition.Y = (float)yOffset.AsDouble();
        }

        double fadePhase = charFx.ElapsedTime * 3f - charFx.RelativeIndex * 0.015f;

        Color targetColor = charFx.Color;
        targetColor.A = Mathf.Clamp((float)fadePhase, 0f, 1f);
        charFx.Color = targetColor;

        Vector2 originalPosition = new Vector2(charFx.Transform.X.X, charFx.Transform.Y.Y);
        Vector2 offsetPosition = originPosition.Lerp(originalPosition, Ease.QuadOut(targetColor.A));
        Vector2 offset = offsetPosition - originalPosition;
        charFx.Transform = charFx.Transform.TranslatedLocal(offset);
        charFx.Transform = charFx.Transform.RotatedLocal(Ease.QuadOut(1 - targetColor.A) * Mathf.DegToRad(20f) * (offset.X < 0 ? 1 : -1f));

        return true;
    }
}
