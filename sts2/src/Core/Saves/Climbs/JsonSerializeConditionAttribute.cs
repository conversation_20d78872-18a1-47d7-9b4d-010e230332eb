using System;
using System.Linq;
using System.Reflection;
using System.Text.Json.Serialization.Metadata;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

public class JsonSerializeConditionAttribute : Attribute
{
    public readonly SerializationCondition defaultBehaviour;

    public JsonSerializeConditionAttribute(SerializationCondition defaultBehaviour)
    {
        this.defaultBehaviour = defaultBehaviour;
    }

    public static void CheckJsonSerializeConditionsModifier(JsonTypeInfo typeInfo)
    {
        if (typeInfo.Kind != JsonTypeInfoKind.Object) return;

        foreach (JsonPropertyInfo propInfo in typeInfo.Properties)
        {
            JsonSerializeConditionAttribute? attr = propInfo.AttributeProvider?.GetCustomAttributes(true).OfType<JsonSerializeConditionAttribute>().FirstOrDefault();

            if (attr != null && propInfo.AttributeProvider is MemberInfo memberInfo)
            {
                propInfo.ShouldSerialize = (_, c) => attr.defaultBehaviour.ShouldSerialize(c, memberInfo);
            }
        }
    }
}
