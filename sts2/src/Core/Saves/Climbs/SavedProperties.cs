using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

[Serializable]
public class SavedProperties : ISaveSchema, IPacketSerializable
{
    [JsonPropertyName("schema_version")]
    public int SchemaVersion { get; set; }

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<int>>? ints;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<bool>>? bools;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<string>>? strings;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<int[]>>? intArrays;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<ModelId>>? modelIds;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<SerializableCard>>? cards;

    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public List<SavedProperty<SerializableCard[]>>? cardArrays;

    public struct SavedProperty<T>
    {
        // ReSharper disable once FieldCanBeMadeReadOnly.Global
        // REASON: Set implicitly during deserialization.
        public string name;

        // ReSharper disable once FieldCanBeMadeReadOnly.Global
        // REASON: Set implicitly during deserialization.
        public T value;

        public SavedProperty(string name, T value)
        {
            this.name = name;
            this.value = value;
        }
    }

    /// <summary>
    /// For JSON deserialization.
    /// </summary>
    public SavedProperties() { }

    /// <summary>
    /// For manual instantiation at the start of a climb or in tests.
    /// </summary>
    /// <param name="model">Abstract model to grab saved properties from.</param>
    public static SavedProperties? From(AbstractModel model)
    {
        return FromInternal(model, model.Id);
    }

    /// <summary>
    /// This should only be called internally or from tests.
    /// </summary>
    /// <param name="model">The object to serialize properties from.</param>
    /// <param name="id">Id of the AbstractModel passed, or null if it is not an abstract model.</param>
    public static SavedProperties? FromInternal(object model, ModelId? id)
    {
        SavedProperties props = new();

        foreach (PropertyInfo propertyInfo in SavedPropertiesTypeCache.GetJsonPropertiesForType(model.GetType()) ?? [])
        {
            string propertyName = propertyInfo.Name;
            object? propertyVal = propertyInfo.GetValue(model);
            SavedPropertyAttribute attr = propertyInfo.GetCustomAttribute<SavedPropertyAttribute>()!;

            if (!attr.defaultBehaviour.ShouldSerialize(propertyVal, propertyInfo))
            {
                continue;
            }

            switch (propertyVal)
            {
                case int intVal:
                    props.ints ??= [];
                    props.ints.Add(new SavedProperty<int>(propertyName, intVal));
                    break;
                case int[] intArrayVal:
                    props.intArrays ??= [];
                    props.intArrays.Add(new SavedProperty<int[]>(propertyName, intArrayVal));
                    break;
                case Enum enumVal:
                    props.ints ??= [];
                    props.ints.Add(new SavedProperty<int>(propertyName, Convert.ToInt32(enumVal)));
                    break;
                case Enum[] enumArrayVal:
                    props.intArrays ??= [];
                    props.intArrays.Add(new SavedProperty<int[]>(propertyName, enumArrayVal.Select(Convert.ToInt32).ToArray()));
                    break;
                case ModelId contentIdVal:
                    props.modelIds ??= [];
                    props.modelIds.Add(new SavedProperty<ModelId>(propertyName, contentIdVal));
                    break;
                case bool boolVal:
                    props.bools ??= [];
                    props.bools.Add(new SavedProperty<bool>(propertyName, boolVal));
                    break;
                case string stringVal:
                    props.strings ??= [];
                    props.strings.Add(new SavedProperty<string>(propertyName, stringVal));
                    break;
                case SerializableCard serializableCard:
                    props.cards ??= [];
                    props.cards.Add(new SavedProperty<SerializableCard>(propertyName, serializableCard));
                    break;
                case List<SerializableCard> serializableCardList:
                    props.cardArrays ??= [];
                    props.cardArrays.Add(new SavedProperty<SerializableCard[]>(propertyName, serializableCardList.ToArray()));
                    break;
                default:
                    throw new JsonException($"Property {propertyName} on {id} is not a valid type for [SavedProperty] (type {propertyVal?.GetType()}).");
            }
        }

        if (!props.Any()) return null;

        return props;
    }

    private bool Any() =>
        (ints != null && ints?.Count != 0) ||
        (bools != null && bools?.Count != 0) ||
        (intArrays != null && intArrays?.Count != 0) ||
        (strings != null && strings?.Count != 0) ||
        (modelIds != null && modelIds?.Count != 0) ||
        (cards != null && cards?.Count != 0) ||
        (cardArrays != null && cardArrays?.Count != 0);

    public void Fill(AbstractModel model)
    {
        FillInternal(model);
    }

    /// <summary>
    /// Only for use internally or in tests!
    /// </summary>
    /// <param name="model">Object to dump properties into.</param>
    public void FillInternal(object model)
    {
        Type modelType = model.GetType();

        if (ints != null)
        {
            foreach (SavedProperty<int> pair in ints)
            {
                PropertyInfo? propInfo = modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels);
                propInfo?.SetValue(model, propInfo.PropertyType.IsEnum ? Enum.ToObject(propInfo.PropertyType, pair.value) : pair.value);
            }
        }

        if (intArrays != null)
        {
            foreach (SavedProperty<int[]> pair in intArrays)
            {
                PropertyInfo? propInfo = modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels);
                if (propInfo == null) continue;
                Type elementType = propInfo.PropertyType.GetElementType()!;

                // Turns out that C# will let you assign an int[] to an Enum[], but it won't treat the values as equal
                // to the equivalent enum values, so tests fail
                if (elementType.IsEnum)
                {
                    Array enumArr = Array.CreateInstance(elementType, pair.value.Length);

                    for (int i = 0; i < pair.value.Length; i++)
                    {
                        enumArr.SetValue(Enum.ToObject(elementType, pair.value[i]), i);
                    }

                    propInfo.SetValue(model, enumArr);
                }
                else
                {
                    propInfo.SetValue(model, pair.value);
                }
            }
        }

        if (bools != null)
        {
            foreach (SavedProperty<bool> pair in bools)
            {
                modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels)?.SetValue(model, pair.value);
            }
        }

        if (modelIds != null)
        {
            foreach (SavedProperty<ModelId> pair in modelIds)
            {
                modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels)?.SetValue(model, pair.value);
            }
        }

        if (cards != null)
        {
            foreach (SavedProperty<SerializableCard> pair in cards)
            {
                modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels)?.SetValue(model, pair.value);
            }
        }

        if (cardArrays != null)
        {
            foreach (SavedProperty<SerializableCard[]> pair in cardArrays)
            {
                // Assume these are List<SerializableCard> properties.
                modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels)?.SetValue(model, pair.value.ToList());
            }
        }

        if (strings != null)
        {
            foreach (SavedProperty<string> pair in strings)
            {
                modelType.GetProperty(pair.name, ReflectionHelper.allAccessLevels)?.SetValue(model, pair.value);
            }
        }
    }

    private static void WritePropertyName(PacketWriter writer, string propertyName)
    {
        writer.WriteInt(SavedPropertiesTypeCache.GetNetIdForPropertyName(propertyName), SavedPropertiesTypeCache.NetIdBitSize);
    }

    private static string ReadPropertyName(PacketReader reader)
    {
        int propId = reader.ReadInt(SavedPropertiesTypeCache.NetIdBitSize);
        return SavedPropertiesTypeCache.GetPropertyNameForNetId(propId);
    }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteInt(SchemaVersion);
        writer.WriteBool(ints != null);
        if (ints != null)
        {
            writer.WriteInt(ints.Count, 8);
            foreach (SavedProperty<int> pair in ints)
            {
                WritePropertyName(writer, pair.name);
                writer.WriteInt(pair.value);
            }
        }

        writer.WriteBool(intArrays != null);
        if (intArrays != null)
        {
            writer.WriteInt(intArrays.Count, 8);
            foreach (SavedProperty<int[]> pair in intArrays)
            {
                WritePropertyName(writer, pair.name);

                writer.WriteInt(pair.value.Length);
                foreach (int val in pair.value)
                {
                    writer.WriteInt(val);
                }
            }
        }

        writer.WriteBool(bools != null);
        if (bools != null)
        {
            writer.WriteInt(bools.Count, 8);
            foreach (SavedProperty<bool> pair in bools)
            {
                WritePropertyName(writer, pair.name);
                writer.WriteBool(pair.value);
            }
        }

        writer.WriteBool(modelIds != null);
        if (modelIds != null)
        {
            writer.WriteInt(modelIds.Count, 8);
            foreach (SavedProperty<ModelId> pair in modelIds)
            {
                WritePropertyName(writer, pair.name);
                writer.WriteFullModelId(pair.value);
            }
        }

        writer.WriteBool(cards != null);
        if (cards != null)
        {
            writer.WriteInt(cards.Count, 8);
            foreach (SavedProperty<SerializableCard> pair in cards)
            {
                WritePropertyName(writer, pair.name);
                writer.Write(pair.value);
            }
        }

        writer.WriteBool(cardArrays != null);
        if (cardArrays != null)
        {
            writer.WriteInt(cardArrays.Count, 8);
            foreach (SavedProperty<SerializableCard[]> pair in cardArrays)
            {
                WritePropertyName(writer, pair.name);

                writer.WriteInt(pair.value.Length);
                foreach (SerializableCard card in pair.value)
                {
                    writer.Write(card);
                }
            }
        }

        writer.WriteBool(strings != null);
        if (strings != null)
        {
            writer.WriteInt(strings.Count, 8);
            foreach (SavedProperty<string> pair in strings)
            {
                WritePropertyName(writer, pair.name);
                writer.WriteString(pair.value);
            }
        }
    }

    public void Deserialize(PacketReader reader)
    {
        SchemaVersion = reader.ReadInt();
        bool hasInts = reader.ReadBool();
        if (hasInts)
        {
            ints = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                ints.Add(new SavedProperty<int>(ReadPropertyName(reader), reader.ReadInt()));
            }
        }

        bool hasIntArrays = reader.ReadBool();
        if (hasIntArrays)
        {
            intArrays = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                string propertyName = ReadPropertyName(reader);
                int intArrayCount = reader.ReadInt();
                int[] intArray = new int[intArrayCount];

                for (int j = 0; j < intArrayCount; j++)
                {
                    intArray[j] = reader.ReadInt();
                }

                intArrays.Add(new SavedProperty<int[]>(propertyName, intArray));
            }
        }

        bool hasBools = reader.ReadBool();

        if (hasBools)
        {
            bools = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                bools.Add(new SavedProperty<bool>(ReadPropertyName(reader), reader.ReadBool()));
            }
        }

        bool hasModelIds = reader.ReadBool();
        if (hasModelIds)
        {
            modelIds = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                modelIds.Add(new SavedProperty<ModelId>(ReadPropertyName(reader), reader.ReadFullModelId()));
            }
        }

        bool hasCards = reader.ReadBool();
        if (hasCards)
        {
            cards = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                cards.Add(new SavedProperty<SerializableCard>(ReadPropertyName(reader), reader.Read<SerializableCard>()));
            }
        }

        bool hasCardArrays = reader.ReadBool();
        if (hasCardArrays)
        {
            cardArrays = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                string propertyName = ReadPropertyName(reader);
                int cardArrayCount = reader.ReadInt();
                SerializableCard[] cardArray = new SerializableCard[cardArrayCount];

                for (int j = 0; j < cardArrayCount; j++)
                {
                    cardArray[j] = reader.Read<SerializableCard>();
                }

                cardArrays.Add(new SavedProperty<SerializableCard[]>(propertyName, cardArray));
            }
        }

        bool hasStrings = reader.ReadBool();
        if (hasStrings)
        {
            strings = [];
            int count = reader.ReadInt(8);
            for (int i = 0; i < count; i++)
            {
                strings.Add(new SavedProperty<string>(ReadPropertyName(reader), reader.ReadString()));
            }
        }
    }

    public override string ToString()
    {
        List<string> props = [];

        if (ints != null)
        {
            foreach (SavedProperty<int> prop in ints)
            {
                props.Add($"{prop.name}={prop.value}");
            }
        }

        if (bools != null)
        {
            foreach (SavedProperty<bool> prop in bools)
            {
                props.Add($"{prop.name}={prop.value}");
            }
        }

        if (strings != null)
        {
            foreach (SavedProperty<string> prop in strings)
            {
                props.Add($"{prop.name}={prop.value}");
            }
        }

        if (intArrays != null)
        {
            foreach (SavedProperty<int[]> prop in intArrays)
            {
                props.Add($"{prop.name}={string.Join(",", prop.value)}");
            }
        }

        if (modelIds != null)
        {
            foreach (SavedProperty<ModelId> prop in modelIds)
            {
                props.Add($"{prop.name}={prop.value}");
            }
        }

        if (cards != null)
        {
            foreach (SavedProperty<SerializableCard> prop in cards)
            {
                props.Add($"{prop.name}={prop.value}");
            }
        }

        if (cardArrays != null)
        {
            foreach (SavedProperty<SerializableCard[]> prop in cardArrays)
            {
                props.Add($"{prop.name}={string.Join(",", prop.value.Select(c => c.Id!.Entry))}");
            }
        }

        return string.Join(",", props);
    }
}
