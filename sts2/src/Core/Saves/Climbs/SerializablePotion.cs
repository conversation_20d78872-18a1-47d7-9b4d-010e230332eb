using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

public class SerializablePotion : IPacketSerializable
{
    [JsonPropertyName("id")]
    public ModelId? Id { get; set; }

    [JsonPropertyName("slot_index")]
    public int SlotIndex { get; set; }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteModelEntry(Id!);
        writer.WriteInt(SlotIndex, 4);
    }

    public void Deserialize(PacketReader reader)
    {
        Id = reader.ReadModelIdAssumingType<PotionModel>();
        SlotIndex = reader.ReadInt(4);
    }
}
