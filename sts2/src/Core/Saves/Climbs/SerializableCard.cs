using System;
using System.Diagnostics.CodeAnalysis;
using System.Text.Json.Serialization;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.Saves.Climbs;

/// <summary>
/// Represents a way to serialize the entire state of a card to save file or for sending to a multiplayer peer.
/// This should only be used in multiplayer if you need to recreate a card from scratch. If you are referencing an
/// already-existing card owned by a player, you should use NetCombatCard or NetDeckCard.
/// </summary>
public class SerializableCard : IPacketSerializable
{
    [JsonPropertyName("id")]
    public ModelId? Id { get; set; }

    [JsonPropertyName("current_upgrade_level")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public int CurrentUpgradeLevel { get; set; }

    [JsonPropertyName("enchantment")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public SerializableEnchantment? Enchantment { get; set; }

    [JsonPropertyName("props")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public SavedProperties? Props { get; set; }

    [JsonPropertyName("floor_added_to_deck")]
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingNull)]
    public int? FloorAddedToDeck { get; set; }

    public void Serialize(PacketWriter writer)
    {
        writer.WriteModelEntry(Id!);
        writer.WriteInt(CurrentUpgradeLevel, 8);

        writer.WriteBool(Enchantment != null);
        if (Enchantment != null)
        {
            writer.Write(Enchantment);
        }

        writer.WriteBool(Props != null);
        if (Props != null)
        {
            writer.Write(Props);
        }

        writer.WriteBool(FloorAddedToDeck != null);
        if (FloorAddedToDeck != null)
        {
            writer.WriteInt(FloorAddedToDeck.Value, 8);
        }
    }

    public void Deserialize(PacketReader reader)
    {
        Id = reader.ReadModelIdAssumingType<CardModel>();
        CurrentUpgradeLevel = reader.ReadInt(8);

        bool hasEnchantment = reader.ReadBool();
        if (hasEnchantment)
        {
            Enchantment = reader.Read<SerializableEnchantment>();
        }

        bool hasProps = reader.ReadBool();
        if (hasProps)
        {
            Props = reader.Read<SavedProperties>();
        }

        bool hasFloorAdded = reader.ReadBool();
        if (hasFloorAdded)
        {
            FloorAddedToDeck = reader.ReadInt(8);
        }
    }

    public override bool Equals(object? obj)
    {
        if (obj == null) return false;
        if (obj.GetType() != GetType()) return false;

        SerializableCard other = (SerializableCard)obj;
        return Id!.Equals(other.Id) && CurrentUpgradeLevel == other.CurrentUpgradeLevel && Equals(Enchantment, other.Enchantment);
    }

    [SuppressMessage(
        "ReSharper",
        "NonReadonlyMemberInGetHashCode",
        Justification = "These properties need setters for deserialization, but will never be updated after.")
    ]
    public override int GetHashCode() => HashCode.Combine(Id, CurrentUpgradeLevel, Enchantment);

    public override string ToString() => $"SerializableCard {Id}. Upgrades: {CurrentUpgradeLevel} Enchantment: {Enchantment} Props: {Props} Floor: {FloorAddedToDeck}";
}
