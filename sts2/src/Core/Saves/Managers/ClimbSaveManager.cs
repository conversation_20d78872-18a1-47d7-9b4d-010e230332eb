using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Rooms;

namespace MegaCrit.Sts2.Core.Saves.Managers;

public class ClimbSaveManager
{
    public const string climbSaveFileName = "current_climb.save";
    public const string multiplayerClimbSaveFileName = "current_climb_mp.save";

    private readonly ISaveStore _saveStore;
    private bool _forceSynchronous;

    private string CurrentClimbPath => _saveStore.GetFullPath(climbSaveFileName);
    private string CurrentMultiplayerClimbPath => _saveStore.GetFullPath(multiplayerClimbSaveFileName);

    public event Action? Saved;

    public bool HasClimbSave => _saveStore.FileExists(CurrentClimbPath);
    public bool HasMultiplayerClimbSave => _saveStore.FileExists(CurrentMultiplayerClimbPath);

    public ClimbSaveManager(ISaveStore saveStore, bool forceSynchronous = false)
    {
        _saveStore = saveStore;
        _forceSynchronous = forceSynchronous;
    }

    public async Task SaveClimb(AbstractRoom? preFinishedRoom)
    {
        if (!ClimbManager.Instance.ShouldSave) return;
        if (ClimbManager.Instance.NetService.Type != NetGameType.Singleplayer && ClimbManager.Instance.NetService.Type != NetGameType.Host) return;

        SerializableClimb save = ClimbManager.Instance.ToSave();
        save.PreFinishedRoom = preFinishedRoom?.ToSerializable();

        string savePath = ClimbManager.Instance.NetService.Type.IsMultiplayer() ? CurrentMultiplayerClimbPath : CurrentClimbPath;

        if (!_forceSynchronous)
        {
            using MemoryStream stream = new();
            await JsonSerializer.SerializeAsync(stream, save, JsonSerializationUtility.serializerOptions);
            stream.Position = 0;
            await _saveStore.WriteFileAsync(savePath, stream);
        }
        else
        {
            string json = JsonSerializer.Serialize(save, JsonSerializationUtility.serializerOptions);
            _saveStore.WriteFile(savePath, json);
        }

        Saved?.Invoke();
    }

    public ReadSaveResult<SerializableClimb> LoadClimbSave()
    {
        string? data = _saveStore.ReadFile(CurrentClimbPath);
        if (data == null)
        {
            Log.Warn($"The climb save file could not be read at '{CurrentClimbPath}'!.");
            return new ReadSaveResult<SerializableClimb>(ReadSaveStatus.FileNotFound);
        }

        if (string.IsNullOrWhiteSpace(data))
        {
            GD.PushError($"Empty climb save file='{CurrentClimbPath}'");
            return new ReadSaveResult<SerializableClimb>(ReadSaveStatus.FileEmpty);
        }

        return JsonSerializationUtility.FromJson<SerializableClimb>(data);
    }

    public ReadSaveResult<SerializableClimb> LoadMultiplayerClimbSave()
    {
        string? data = _saveStore.ReadFile(CurrentMultiplayerClimbPath);
        if (data == null)
        {
            Log.Warn($"The multiplayer climb save file could not be read at '{CurrentMultiplayerClimbPath}'!.");
            return new ReadSaveResult<SerializableClimb>(ReadSaveStatus.FileNotFound);
        }

        if (string.IsNullOrWhiteSpace(data))
        {
            GD.PushError($"Empty multiplayer climb save file='{CurrentMultiplayerClimbPath}'");
            return new ReadSaveResult<SerializableClimb>(ReadSaveStatus.FileEmpty);
        }

        return JsonSerializationUtility.FromJson<SerializableClimb>(data);
    }

    public void DeleteCurrentClimb()
    {
        _saveStore.DeleteFile(CurrentClimbPath);
    }

    public void DeleteCurrentMultiplayerClimb()
    {
        _saveStore.DeleteFile(CurrentMultiplayerClimbPath);
    }

    public void RenameBrokenClimbSave()
    {
        _saveStore.RenameFile(CurrentClimbPath, _saveStore.GetFullPath($"broken_climb_{DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()}.save"));
    }

    public void RenameBrokenMultiplayerClimbSave()
    {
        _saveStore.RenameFile(CurrentMultiplayerClimbPath, _saveStore.GetFullPath($"broken_mp_climb_{DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString()}.save"));
    }
}
