using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Climbs;

namespace MegaCrit.Sts2.Core.Saves.Managers;

public class ClimbHistorySaveManager
{
    private const string _historyDirName = "history";

    private readonly ISaveStore _saveStore;
    private string HistoryPath => _saveStore.GetFullPath(_historyDirName);

    public ClimbHistorySaveManager(ISaveStore saveStore)
    {
        _saveStore = saveStore;
        _saveStore.CreateDirectory(HistoryPath);
    }

    public void SaveHistory(ClimbHistory history)
    {
        string json = JsonSerializationUtility.ToJson(history, ClimbHistory.defaultSerializerSettings);
        string path = _saveStore.GetFullPath($"{_historyDirName}/{history.StartTime}.climb");
        _saveStore.WriteFile(path, json);
    }

    public int GetHistoryCount()
    {
        return _saveStore.GetFilesInDirectory(HistoryPath).Length;
    }

    public IEnumerable<string> GetHistories()
    {
        return _saveStore.GetFilesInDirectory(HistoryPath).Select(fileName => _saveStore.GetFullPath($"{_historyDirName}/{fileName}"));
    }
}
