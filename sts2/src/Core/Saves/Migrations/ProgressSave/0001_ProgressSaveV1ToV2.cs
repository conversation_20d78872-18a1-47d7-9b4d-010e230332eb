using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.Saves.Migrations.ProgressSave;

/// <summary>
/// Migration from schema v1 to v2 for ProgressSave.
/// </summary>
[Migration(typeof(Saves.ProgressSave), 1, 2)]
public class ProgressSaveV1ToV2 : MigrationBase<Saves.ProgressSave>
{
    protected override void ApplyMigration(MigratingData saveData)
    {
        Log.Info("Migration: removing 'max_shared_ascension'");
        saveData.Remove("max_shared_ascension");
    }
}
