using MegaCrit.Sts2.Core.Models;
using System.Text.Json.Serialization;

namespace MegaCrit.Sts2.Core.Saves;

/// <summary>
/// Holds progress data on a per character basis.
/// Used by ProgressSave
/// </summary>
public class CharacterStats
{
    [JsonPropertyName("id")]
    public ModelId? Id { get; init; }

    [JsonPropertyName("max_ascension")]
    public int MaxAscension { get; set; }

    // The player's preferred Ascension level.
    // Is set when the player chooses an Ascension level that isn't the max level during character select OR the max ascension level is incremented.
    [JsonPropertyName("preferred_ascension")]
    public int PreferredAscension { get; set; }

    [JsonPropertyName("total_wins")]
    public int TotalWins { get; set; }

    [JsonPropertyName("total_losses")]
    public int TotalLosses { get; set; }

    [JsonPropertyName("fastest_win_time")]
    public long FastestWinTime { get; set; } = -1;

    [JsonPropertyName("best_win_streak")]
    public long BestWinStreak { get; set; }

    [Json<PERSON>ropertyName("current_streak")]
    public long CurrentWinStreak { get; set; }

    // Cumulative time played for this specific character (in seconds)
    [JsonPropertyName("playtime")]
    public long Playtime { get; set; }
}
