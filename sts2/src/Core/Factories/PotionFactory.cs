using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.PotionPools;
using MegaCrit.Sts2.Core.Random;

namespace MegaCrit.Sts2.Core.Factories;

public static class PotionFactory
{
    private const float _rareChance = 0.1f;
    private const float _rareThreshold = _rareChance;

    private const float _uncommonChance = 0.25f;
    private const float _uncommonThreshold = _uncommonChance + _rareChance;

    /// <summary>
    /// Create a random potion while out of combat.
    /// This can get any potion from the valid potion pools, including potions that can do restricted things like heal
    /// the player.
    /// </summary>
    /// <param name="player">Player to create a potion for.</param>
    /// <param name="rng">RNG to use to determine what potion to create.</param>
    /// <param name="blacklist">Canonical potions that should be blocked from being generated.</param>
    /// <returns>Newly-created potion.</returns>
    public static PotionModel CreateRandomPotionOutOfCombat(Player player, Rng rng, IEnumerable<PotionModel>? blacklist = null)
    {
        return CreateRandomPotion(
            GetPotionOptions(player, blacklist ?? []),
            1,
            rng
        ).First();
    }

    /// <summary>
    /// Create random potions while out of combat.
    /// This can get any potion from the valid potion pools, including potions that can do restricted things like heal
    /// the player.
    /// </summary>
    /// <param name="player">Player to create a potion for.</param>
    /// <param name="count">How many potions you want to generate.</param>
    /// <param name="rng">RNG to use to determine what potion to create.</param>
    /// <param name="blacklist">Canonical potions that should be blocked from being generated.</param>
    /// <returns>Newly-created potion.</returns>
    public static List<PotionModel> CreateRandomPotionsOutOfCombat(Player player, int count, Rng rng, IEnumerable<PotionModel>? blacklist = null)
    {
        return CreateRandomPotion(
            GetPotionOptions(player, blacklist ?? []),
            count,
            rng
        );
    }

    /// <summary>
    /// Create a random potion while in combat.
    /// This can get any potion from the valid potion pools, except potions that cannot be generated in combat (like
    /// healing potions).
    /// </summary>
    /// <param name="player">Player to create a potion for.</param>
    /// <param name="rng">RNG to use to determine what potion to create.</param>
    /// <param name="blacklist">Canonical potions that should be blocked from being generated.</param>
    /// <returns>Newly-created potion.</returns>
    public static PotionModel CreateRandomPotionInCombat(Player player, Rng rng, IEnumerable<PotionModel>? blacklist = null)
    {
        return CreateRandomPotion(
            GetPotionOptions(player, blacklist ?? []).Where(p => p.CanBeGeneratedInCombat),
            1,
            rng
        ).First();
    }

    private static List<PotionModel> CreateRandomPotion(IEnumerable<PotionModel> options, int count, Rng rng)
    {
        List<PotionModel> validOptions = options.ToList();
        List<PotionModel> ret = [];

        for (int i = 0; i < count; i++)
        {
            float rand = rng.NextFloat();

            PotionRarity rarity = rand switch
            {
                <= _rareThreshold => PotionRarity.Rare,
                <= _uncommonThreshold => PotionRarity.Uncommon,
                _ => PotionRarity.Common
            };

            PotionModel potion = rng.NextItem(validOptions.Where(potion => potion.Rarity == rarity))!;
            ret.Add(potion);
            validOptions.Remove(potion);
        }

        return ret;
    }

    private static IEnumerable<PotionModel> GetPotionOptions(Player player, IEnumerable<PotionModel> blacklist)
    {
        return player.Character.PotionPool.Potions
            .Concat(ModelDb.PotionPool<SharedPotionPool>().Potions)
            .Where(p => !blacklist.Contains(p));
    }
}
