using System.Diagnostics;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Core.GameActions;

/// <summary>
/// This action indicates that the player is ready to proceed to phase two of the end of the turn, where player state
/// cleanup and switching to the next side occurs. It comes after EndPlayerTurnAction.
/// Note that this action is automatically enqueued without player intervention.
/// </summary>
public class ReadyToBeginEnemyTurnAction : GameAction
{
    private readonly Player _player;
    public override ulong OwnerId => _player.NetId;
    public override GameActionType ActionType => GameActionType.Combat;

    public ReadyToBeginEnemyTurnAction(Player player)
    {
        _player = player;
    }

    protected override Task ExecuteAction()
    {
        CombatManager.Instance.SetReadyToBeginEnemyTurn(_player);
        return Task.CompletedTask;
    }

    public override INetAction ToNetAction()
    {
        return new NetReadyToBeginEnemyTurnAction();
    }

    protected override void CancelAction()
    {
        Log.Debug($"Cancel\n{new StackTrace()}");
    }

    public override string ToString() => $"{nameof(ReadyToBeginEnemyTurnAction)} {_player.NetId}";
}

public struct NetReadyToBeginEnemyTurnAction : INetAction
{
    public GameAction ToGameAction(Player player)
    {
        return new ReadyToBeginEnemyTurnAction(player);
    }

    public void Serialize(PacketWriter serializer)
    {
    }

    public void Deserialize(PacketReader deserializer)
    {
    }

    public override string ToString() => nameof(NetReadyToBeginEnemyTurnAction);
}
