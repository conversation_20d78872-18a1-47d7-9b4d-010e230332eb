using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Context;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;
using MegaCrit.Sts2.Core.Nodes.Combat;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.GameActions;

// Note: This must be NetCombatCard and not a CardModel because, in multiplayer, the following may occur:
// * Player plays Blade Dance
// * Player queues the shiv generated by Blade Dance
// * These two actions are sent over the internet, and received together
// * Blade Dance begins playing
// * PlayCardAction for the shiv is placed on the queue, but the shiv CardModel has not yet been created on the receiving peer
// To resolve this, the enqueued action uses a NetCombatCard to refer to the target card even though it doesn't exist yet.
public sealed class PlayCardAction: GameAction
{
    public override ulong OwnerId => Player.NetId;
    public override GameActionType ActionType => GameActionType.CombatPlayPhaseOnly;

    public Player Player { get; }
    public NetCombatCard NetCombatCard { get; }
    public ModelId CardModelId { get; }
    public uint? TargetId { get; }

    // Only set after the action has begun execution
    public PlayerChoiceContext? PlayerChoiceContext { get; private set; }

    private CardModel? _card;

    // Constructor for the owning player's machine that is sending out the action to other players
    public PlayCardAction(CardModel cardModel, Creature? target)
    {
        if (target is { CombatId: null })
        {
            throw new InvalidOperationException($"Cannot target card against target {target} with no combat ID!");
        }

        Player = cardModel.Owner;
        NetCombatCard = NetCombatCard.FromModel(cardModel);
        CardModelId = cardModel.Id;
        TargetId = target?.CombatId;
    }

    // Constructor for remote player machines receiving a NetPlayCardAction
    public PlayCardAction(Player player, NetCombatCard netCombatCard, ModelId cardModelId, uint? targetId)
    {
        Player = player;
        NetCombatCard = netCombatCard;
        CardModelId = cardModelId;
        TargetId = targetId;
    }

    /// <summary>
    /// Get the creature that this action is targeting.
    /// Null for un-targeted cards.
    /// </summary>
    public Creature? Target => Player.Creature.CombatState?.GetCreature(TargetId);

    protected override async Task ExecuteAction()
    {
        // Time, in seconds, that we wait for monsters to spawn if the target ID points to a non-existent monster.
        const double monsterSpawnTimeout = 10;

        // Get the reference to the actual card that this action points to.
        // We cannot do this until we execute the action because the card may not have been created when the action is
        // enqueued. For example, if we are running slow and someone plays a Storm of Steel and then a generated Shiv
        // very quickly in sequence, we can receive the Shiv enqueue before we finish executing the Storm of Steel.
        _card = NetCombatCard.ToCardModel();

        // In multiplayer, if the owner's visual state is ahead of our local state, they can play a card on an enemy
        // that has yet to spawn for us. In that case, the monster ID sent in the target index may not yet exist, and we
        // need to block the action until it does.
        Creature? target = await Player.Creature.CombatState!.GetCreatureAsync(TargetId, monsterSpawnTimeout);

        // If you're playing very fast, it's possible to queue up a card play and then invalidate it.
        // This can happen in a few ways.

        // An effect from an earlier queued card could cause this card to leave your hand.
        //
        // For example, you may play Survivor, then quickly play Defend before Survivor starts executing, then choose
        // to discard that same Defend.
        //
        // In these cases, the card's already "gone", so just skip the play.
        if (_card.Pile?.Type != CardPileTarget.Hand) return;

        // If we are targeting inappropriately for the card, it's possible that we targeted a dead enemy, but this could
        // also be a multiplayer error. CanPlayTargeting below handles this case in the normal flow, but it's unusual
        // enough that we could use the extra log.
        if (target == null && _card.TargetEnemy == UiTargetEnemy.Any)
        {
            Log.Warn($"Attempted to play card {_card} with TargetEnemy of type {_card.TargetEnemy}, but no target was passed to the play card action!");
        }

        // Even if the card is still in your hand, something else could invalidate the play. For example:
        //
        // Imagine you have 3 energy and you make the following plays very fast:
        // 1. Defend (decreasing you to 2 energy)
        // 2. Strike (queued, _will_ decrease you to 1 energy when it's dequeued but not yet)
        // 3. Impervious (costs 2 energy)
        //
        // Since you attempted to play Impervious while you still had 2 energy (Strike hadn't been dequeued yet), it was
        // successfully queued. However, once Strike was dequeued and played, your energy was reduced to 1. Now, you
        // don't have enough energy to play the queued Impervious.
        //
        // There are other ways this can happen too, like if a queued card kills a target that a later-queued card is
        // targeting.
        //
        // In these cases, cancel the play and visually move the card back into your hand.
        if (!_card.CanPlayTargeting(target))
        {
            Cancel();
            return;
        }

        string ownerName = PlatformUtil.GetPlayerName(ClimbManager.Instance.NetService.Platform, _card.Owner.NetId);
        string targetLog = target != null ? $"targeting {target.Name}" : "no target";
        Log.Info($"Player {ownerName} playing card {_card.Id.Entry} ({targetLog})");

        await _card.SpendResources();

        PlayerChoiceContext = new GameActionPlayerChoiceContext(this);
        await _card.OnPlayWrapper(PlayerChoiceContext, target);
    }

    /// <summary>
    /// We override this to handle the case where some external action (like showing the hand selection screen) needs to
    /// cancel queued card plays.
    /// </summary>
    protected override void CancelAction()
    {
        // If we are in tests, there may be no climb in progress. In this case, there's nothing to cancel.
        if (TestMode.IsOn && !ClimbManager.Instance.IsInProgress) return;

        // This might return null if the card doesn't exist yet. That can only happen in multiplayer, and in that
        // case, we don't actually care about cancelling the card play anyway, because it won't be happening visually
        _card ??= NetCombatCard.ToCardModelOrNull();

        if (LocalContext.IsMe(Player) && _card != null)
        {
            NPlayerHand.Instance?.TryCancelCardPlay(_card);
        }
    }

    public override INetAction ToNetAction()
    {
        NetPlayCardAction netAction = new()
        {
            card = NetCombatCard,
            modelId = CardModelId,
            targetId = TargetId,
        };

        return netAction;
    }

    public override string ToString()
    {
        CardModel? card = NetCombatCard.ToCardModelOrNull();
        return $"{nameof(PlayCardAction)} {card} {NetCombatCard.CombatCardIndex} {TargetId}";
    }
}

public struct NetPlayCardAction : INetAction
{
    public NetCombatCard card;
    public ModelId modelId;
    public uint? targetId;

    public GameAction ToGameAction(Player player)
    {
        return new PlayCardAction(player, card, modelId, targetId);
    }

    public void Serialize(PacketWriter writer)
    {
        writer.Write(card);
        writer.WriteModelEntry(modelId);
        writer.WriteBool(targetId != null);

        if (targetId != null)
        {
            // Hopefully no more than 64 creatures in a single combat
            writer.WriteUInt(targetId ?? 0, 6);
        }
    }

    public void Deserialize(PacketReader reader)
    {
        card = reader.Read<NetCombatCard>();
        modelId = reader.ReadModelIdAssumingType<CardModel>();

        if (reader.ReadBool())
        {
            targetId = reader.ReadUInt(6);
        }
        else
        {
            targetId = null;
        }
    }

    public override string ToString()
    {
        return $"NetPlayCardAction ({card}) target: {targetId?.ToString() ?? "null"}";
    }
}
