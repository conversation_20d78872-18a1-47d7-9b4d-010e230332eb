using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Actions;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;

namespace MegaCrit.Sts2.Core.GameActions;

/// <summary>
/// A GameAction is a thin wrapper around an async task that should be run in response to player input.
/// THIS IS DIFFERENT from GameActions in STS1 (and the original Unity version of STS2).
///
/// In STS1, a GameAction represented a small unit of game logic, like dealing damage, gaining block, etc.
///
/// In STS2, these small units of logic are handled by Commands (see the MegaCrit.Sts2.Core.Commands namespace).
/// A GameAction WRAPS these commands, and should ONLY be used for player input.
///
/// Examples of things that SHOULD be wrapped in a game action:
/// * Playing a card.
/// * Drinking a potion.
/// * Clicking the "end player turn" button.
///
/// Examples of things that should NOT be wrapped in a game action:
/// * Dealing damage (this can happen as PART of a game action, but never directly from player input).
/// * Gaining block (same reason).
/// * Applying a power to a creature (same reason).
/// * A monster making a move (once the player's turn ends, monster moves are run in sequence. They wait on each other
///   to execute, but never on player input, so no GameActions are necessary).
/// </summary>
public abstract class GameAction
{
    private static readonly Logger _logger = new(nameof(GameAction), LogType.Actions);
    public GameActionState State { get; private set; }

    public abstract ulong OwnerId { get; }

    /// <summary>
    /// The type of game action, which dictates cancellation and enqueuing timings. See comments in the enum for
    /// details.
    /// </summary>
    public abstract GameActionType ActionType { get; }

    // Note that this is only assigned to actions that are enqueued to an action queue.
    // It will not be set in these instances:
    // - When a client constructs a GameAction to be sent to the host for enqueueing
    // - When a peer is executing a UsePotionAction outside of combat
    public uint? Id { get; private set; }

    // Completed when the GameAction is paused for player choice.
    private TaskCompletionSource? _pauseForPlayerChoiceTaskSource;

    // Completed when the GameAction is resumed after being paused for player choice.
    private TaskCompletionSource? _executeAfterResumptionTaskSource;

    // Completed when the GameAction and all callbacks are completely finished executing.
    private TaskCompletionSource _completionSource = new();

    // The task which the GameAction is currently executing.
    private Task? _executionTask;

    /// <summary>
    /// Called when the GameAction is fully run to completion.
    /// </summary>
    public event Action<GameAction>? AfterFinished;

    /// <summary>
    /// Called just before the GameAction begins execution for the first time.
    /// Not called when the GameAction is resumed.
    /// </summary>
    public event Action<GameAction>? BeforeExecuted;

    /// <summary>
    /// Called when the GameAction is cancelled.
    /// </summary>
    public event Action<GameAction>? BeforeCancelled;

    /// <summary>
    /// Called when the GameAction is paused for player choice.
    /// </summary>
    public event Action<GameAction>? BeforePausedForPlayerChoice;

    /// <summary>
    /// Called when the GameAction is ready to resume after player choice.
    /// </summary>
    public event Action<GameAction>? BeforeReadyToResumeAfterPlayerChoice;

    /// <summary>
    /// Called when the GameAction resumes execution after player choice.
    /// </summary>
    public event Action<GameAction>? BeforeResumedAfterPlayerChoice;

    /// <summary>
    /// Use this to wait for the GameAction to be fully completed.
    /// </summary>
    public Task CompletionTask => _completionSource.Task;

    public Exception? Exception => _executionTask?.Exception;
    public virtual bool RecordableToReplay => true;

    public void OnEnqueued(Action<GameAction> afterFinished, uint id)
    {
        if (State != GameActionState.None) throw new InvalidOperationException($"GameAction {this} was enqueued to the queue twice!");
        Log.VeryDebug($"Action {this} enqueued with id {id}");

        Id = id;
        AfterFinished += afterFinished;
        State = GameActionState.WaitingForExecution;
    }

    public async Task Execute()
    {
        _pauseForPlayerChoiceTaskSource = new TaskCompletionSource();

        switch (State)
        {
            case GameActionState.WaitingForExecution:
                _logger.VeryDebug($"Action {this} began executing");
                State = GameActionState.Executing;
                BeforeExecuted?.Invoke(this);
                _executionTask = TaskHelper.RunSafely(ExecuteAction());
                break;
            case GameActionState.ReadyToResumeExecuting:
                _logger.VeryDebug($"Action {this} resumed execution");
                State = GameActionState.Executing;
                _executeAfterResumptionTaskSource!.SetResult();
                break;
            default:
                throw new InvalidOperationException($"Attempted to execute GameAction {this} from invalid state {State}! Expected WaitingForExecution or ReadyToResumeExecuting");
        }

        // Without this try/finally, tests will run forever after an exception, because _afterFinished, which pops the
        // action off the queue, would never be run
        try
        {
            await TaskHelper.WhenAny(_executionTask!, _pauseForPlayerChoiceTaskSource.Task);
        }
        finally
        {
            if (_executionTask!.IsCompleted)
            {
                _logger.VeryDebug($"Action {this} finished execution");
                State = GameActionState.Finished;
                AfterFinished?.Invoke(this);
                _completionSource.SetResult();
            }
            else
            {
                _logger.VeryDebug($"Action {this} paused execution");
            }
        }
    }

    public void ResumeAfterGatheringPlayerChoice(uint newId)
    {
        if (State != GameActionState.GatheringPlayerChoice) throw new InvalidOperationException($"Tried setting GameAction {this} ready from invalid state {State}! Expected GatheringPlayerChoice");
        _logger.VeryDebug($"Action {this} finished gathering player choice, and is assigned new id {newId}");
        Id = newId;
        BeforeReadyToResumeAfterPlayerChoice?.Invoke(this);
        State = GameActionState.ReadyToResumeExecuting;
    }

    public async Task WaitForActionToResumeExecutingAfterPlayerChoice()
    {
        _logger.VeryDebug($"Action {this} waiting to resume execution after player choice");
        await _executeAfterResumptionTaskSource!.Task;
        _executeAfterResumptionTaskSource = null;
        BeforeResumedAfterPlayerChoice?.Invoke(this);
    }

    public void PauseForPlayerChoice()
    {
        if (State != GameActionState.Executing) throw new InvalidOperationException($"Tried to pause GameAction {this} from invalid state {State}! Expected Executing");
        _logger.VeryDebug($"Action {this} gathering player choice");
        _executeAfterResumptionTaskSource = new TaskCompletionSource();
        _pauseForPlayerChoiceTaskSource!.SetResult();
        BeforePausedForPlayerChoice?.Invoke(this);
        State = GameActionState.GatheringPlayerChoice;
    }

    protected abstract Task ExecuteAction();

    public void Cancel()
    {
        State = GameActionState.Canceled;
        BeforeCancelled?.Invoke(this);
        CancelAction();
    }

    // Usually a no-op (removing the action from the queue is enough to prevent its logic from being run), but some
    // actions might need to update the UI in response to being canceled. They can add that logic by overriding this.
    protected virtual void CancelAction() { }

    public abstract INetAction ToNetAction();
}
