using System;
using Godot;
using MegaCrit.Sts2.Core.Assets;

namespace MegaCrit.Sts2.Core.Entities.Multiplayer;

/// <summary>
/// Used in multiplayer to sync which screen the player is on.
/// </summary>
public enum NetScreenType
{
    None,

    /// <summary>
    /// Whichever room is the current room (e.g. the combat room, or the rest site).
    /// </summary>
    Room,

    /// <summary>
    /// Map screen.
    /// </summary>
    Map,

    /// <summary>
    /// Settings screen.
    /// </summary>
    Settings,

    /// <summary>
    /// Compendium screen.
    /// </summary>
    Compendium,

    /// <summary>
    /// Deck view screen.
    /// </summary>
    DeckView,

    /// <summary>
    /// Any combat card pile screen (exhaust, draw).
    /// </summary>
    CardPile,

    /// <summary>
    /// Simple card view screen, e.g. when viewing cards that were transformed by Rebirth
    /// </summary>
    SimpleCardsView,

    /// <summary>
    /// Any card selection screen (Survivor card selection, smith card selection, event card selection).
    /// </summary>
    CardSelection,

    /// <summary>
    /// Climb history screen shown at the end of a climb.
    /// </summary>
    GameOver,

    /// <summary>
    /// The Pause menu (ESC during gameplay).
    /// </summary>
    PauseMenu,

    /// <summary>
    /// Any reward offer screen.
    /// </summary>
    Rewards,

    /// <summary>
    /// The send feedback screen.
    /// </summary>
    Feedback,

    /// <summary>
    /// The shared relic picking screen at the treasure room.
    /// </summary>
    SharedRelicPicking,

    /// <summary>
    /// The expanded state displayed when a remote player is clicked.
    /// </summary>
    RemotePlayerExpandedState,
}

public static class NetScreenTypeExtensions
{
    public static Texture2D? GetLocationIcon(this NetScreenType screenType)
    {
        string? screenIconPath = screenType switch
        {
            NetScreenType.None => null,
            NetScreenType.Room => null,
            NetScreenType.Map => "res://images/atlases/ui_atlas.sprites/top_bar/top_bar_map.tres",
            NetScreenType.Settings => "res://images/atlases/ui_atlas.sprites/top_bar/top_bar_settings.tres",
            NetScreenType.PauseMenu => "res://images/atlases/ui_atlas.sprites/top_bar/top_bar_settings.tres",
            NetScreenType.Compendium => "res://images/atlases/ui_atlas.sprites/compendium.tres",
            NetScreenType.DeckView => "res://images/atlases/ui_atlas.sprites/top_bar/top_bar_deck.tres",
            NetScreenType.CardPile => "res://images/packed/combat_ui/discard_pile.png",
            NetScreenType.SimpleCardsView => "res://images/ui/reward_screen/reward_icon_card.png",
            NetScreenType.CardSelection => "res://images/ui/reward_screen/reward_icon_card.png",
            NetScreenType.GameOver => null,
            NetScreenType.Rewards => "res://images/ui/reward_screen/reward_icon_money.png",
            NetScreenType.Feedback => "res://images/atlases/ui_atlas.sprites/top_bar/top_bar_settings.tres",
            NetScreenType.SharedRelicPicking => "res://images/ui/reward_screen/reward_icon_shared_relic.png",
            NetScreenType.RemotePlayerExpandedState => null,
            _ => throw new ArgumentOutOfRangeException(nameof(screenType), screenType, null)
        };

        if (screenIconPath == null) return null;
        return PreloadManager.Cache.GetTexture2D(screenIconPath);
    }
}
