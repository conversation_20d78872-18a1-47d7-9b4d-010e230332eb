using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Helpers;

namespace MegaCrit.Sts2.Core.Entities.Potions;

public enum PotionBody
{
    None = 0,
    Anvil = 1,
    Bolt = 2,
    Card = 3,
    Cube = 4,
    <PERSON> = 5,
    <PERSON> = 6,
    <PERSON> = 7,
    Fat = 8,
    Fat<PERSON><PERSON><PERSON> = 9,
    <PERSON><PERSON>k = 10,
    <PERSON> = 11,
    <PERSON> = 12,
    <PERSON> = 13,
    <PERSON> = 14,
    <PERSON><PERSON><PERSON> = 15,
    <PERSON><PERSON> = 16,
    <PERSON><PERSON><PERSON> = 17,
    <PERSON> = 18
}

public static class PotionBodyExtensions
{
    public static string GetBodyPath(this PotionBody body)
    {
        string filename = body switch
        {
            PotionBody.None => throw new ArgumentOutOfRangeException(nameof(body), body, null),
            PotionBody.Anvil => "potion_anvil_body.png",
            PotionBody.Bolt => "potion_bolt_body.png",
            PotionBody.Card => "potion_card_body.png",
            PotionBody.Cube => "potion_cube_body.png",
            PotionBody.Diamond => "potion_diamond_body.png",
            PotionBody.Eye => "potion_eye_body.png",
            PotionBody.Fairy => "potion_fairy_body.png",
            PotionBody.Fat => "potion_fat_body.png",
            PotionBody.FatDiamond => "potion_fat_diamond_body.png",
            PotionBody.Flask => "potion_flask_body.png",
            PotionBody.Ghost => "potion_ghost_body.png",
            PotionBody.Heart => "potion_heart_body.png",
            PotionBody.Moon => "potion_moon_body.png",
            PotionBody.Shield => "potion_shield_body.png",
            PotionBody.Snecko => "potion_snecko_body.png",
            PotionBody.Sphere => "potion_sphere_body.png",
            PotionBody.Spiky => "potion_spiky_body.png",
            PotionBody.Thin => "potion_thin_body.png",
            _ => throw new ArgumentOutOfRangeException(nameof(body), body, null)
        };

        return ImageHelper.GetImagePath($"packed/potion/body/{filename}");
    }

    public static string? GetGradientPath(this PotionBody body)
    {
        string? filename = body switch
        {
            PotionBody.Anvil => "potion_anvil_gradient.png",
            PotionBody.Card => "potion_card_gradient.png",
            PotionBody.Sphere => "potion_sphere_gradient.png",
            _ => null
        };

        return filename == null ? null : ImageHelper.GetImagePath($"packed/potion/gradient/{filename}");
    }

    public static string GetJuicePath(this PotionBody body)
    {
        string filename = body switch
        {
            PotionBody.None => throw new ArgumentOutOfRangeException(nameof(body), body, null),
            PotionBody.Anvil => "potion_anvil_juice.png",
            PotionBody.Bolt => "potion_bolt_juice.png",
            PotionBody.Card => "potion_card_juice.png",
            PotionBody.Cube => "potion_cube_juice.png",
            PotionBody.Diamond => "potion_diamond_juice.png",
            PotionBody.Eye => "potion_eye_juice.png",
            PotionBody.Fairy => "potion_fairy_juice.png",
            PotionBody.Fat => "potion_fat_juice.png",
            PotionBody.FatDiamond => "potion_fat_diamond_juice.png",
            PotionBody.Flask => "potion_flask_juice.png",
            PotionBody.Ghost => "potion_ghost_juice.png",
            PotionBody.Heart => "potion_heart_juice.png",
            PotionBody.Moon => "potion_moon_juice.png",
            PotionBody.Shield => "potion_shield_juice.png",
            PotionBody.Snecko => "potion_snecko_juice.png",
            PotionBody.Sphere => "potion_sphere_juice.png",
            PotionBody.Spiky => "potion_spiky_juice.png",
            PotionBody.Thin => "potion_thin_juice.png",
            _ => throw new ArgumentOutOfRangeException(nameof(body), body, null)
        };

        return ImageHelper.GetImagePath($"packed/potion/juice/{filename}");
    }

    private static readonly Dictionary<(PotionBody, PotionOverlay), string> _potionOverlayMap = new()
    {
        [(PotionBody.Cube, PotionOverlay.Bubbles)] = "potion_cube_bubbles.png",
        [(PotionBody.Cube, PotionOverlay.Curve)] = "potion_cube_curve.png",
        [(PotionBody.Diamond, PotionOverlay.Bubbles)] = "potion_diamond_bubbles.png",
        [(PotionBody.Fairy, PotionOverlay.Sparkle)] = "potion_fairy_sparkle.png",
        [(PotionBody.FatDiamond, PotionOverlay.Curve)] = "potion_fat_diamond_curve.png",
        [(PotionBody.Heart, PotionOverlay.Curve)] = "potion_heart_curve.png",
        [(PotionBody.Spiky, PotionOverlay.Curve)] = "potion_spiky_curve.png"
    };

    public static string? GetOverlayPath(this PotionBody body, PotionOverlay overlay)
    {
        if (_potionOverlayMap.TryGetValue((body, overlay), out string? path))
        {
            return ImageHelper.GetImagePath($"packed/potion/overlay/{path}");
        }
        return null;
    }
}
