using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Extensions;
using MegaCrit.Sts2.Core.Models;

namespace MegaCrit.Sts2.Core.Entities.Models;

/// <summary>
/// An IComparer which sorts a collection of models into the same order.
/// The HookBus keeps a collection of models. In multiplayer, these models may be added or removed in different orders
/// based on the peer. However, we need these models to execute their hooks in the exact same order across all peers
/// to maintain determinism.
/// </summary>
public struct DeterministicModelComparer : IComparer<AbstractModel>
{
    public int Compare(AbstractModel? model1, AbstractModel? model2)
    {
        // First, compare model IDs
        if (model1 == null && model2 == null) return 0;
        if (ReferenceEquals(model1, model2)) return 0;
        if (model1 == null) return -1;
        if (model2 == null) return 1;

        if (model1.CategorySortingId != model2.CategorySortingId)
        {
            return model1.CategorySortingId.CompareTo(model2.CategorySortingId);
        }

        if (model1.EntrySortingId != model2.EntrySortingId)
        {
            return model1.EntrySortingId.CompareTo(model2.EntrySortingId);
        }

        // Then, compare owners
        Creature? modelOwner1 = GetOwner(model1);
        Creature? modelOwner2 = GetOwner(model2);

        if (modelOwner1 == null && modelOwner2 == null) return 0;
        if (modelOwner1 == null) return -1;
        if (modelOwner2 == null) return 1;

        if (!ReferenceEquals(modelOwner1, modelOwner2))
        {
            if (modelOwner1.IsPlayer && modelOwner2.IsPlayer)
            {
                return modelOwner1.Player!.NetId.CompareTo(modelOwner2.Player!.NetId);
            }
            else if (modelOwner1.CombatId.HasValue && modelOwner2.CombatId.HasValue)
            {
                return modelOwner1.CombatId.Value.CompareTo(modelOwner2.CombatId.Value);
            }
            else if (modelOwner1.CombatId.HasValue)
            {
                return 1;
            }
            else
            {
                return -1;
            }
        }

        // Finally, compare based on model-type-specific information
        return CompareModelsWithSameOwnerAndId(model1, model2);
    }

    private static Creature? GetOwner(AbstractModel model)
    {
        return model switch
        {
            CardModel cardModel => cardModel.Owner.Creature,
            RelicModel relicModel => relicModel.Owner.Creature,
            PotionModel potionModel => potionModel.Owner.Creature,
            PowerModel powerModel => powerModel.Owner,
            AfflictionModel afflictionModel => afflictionModel.Card.Owner.Creature,
            EnchantmentModel enchantmentModel => enchantmentModel.Card.Owner.Creature,
            _ => null
        };
    }

    private static int CompareModelsWithSameOwnerAndId(AbstractModel model1, AbstractModel model2)
    {
        // Cards are sorted first by pile then by index in that pile
        if (model1 is CardModel cardModel1 && model2 is CardModel cardModel2)
        {
            return CompareCardModelsWithSameOwnerAndId(cardModel1, cardModel2);
        }

        if (model1 is AfflictionModel afflictionModel1 && model2 is AfflictionModel afflictionModel2)
        {
            return CompareCardModelsWithSameOwnerAndId(afflictionModel1.Card, afflictionModel2.Card);
        }

        if (model1 is EnchantmentModel enchantmentModel1 && model2 is EnchantmentModel enchantmentModel2)
        {
            return CompareCardModelsWithSameOwnerAndId(enchantmentModel1.Card, enchantmentModel2.Card);
        }

        // Relics are sorted by index in player belt
        if (model1 is RelicModel relicModel1 && model2 is RelicModel relicModel2)
        {
            return relicModel1.Owner.Relics.IndexOf(relicModel1).CompareTo(relicModel2.Owner.Relics.IndexOf(relicModel2));
        }

        // Potions are sorted by index in player belt
        if (model1 is PotionModel potionModel1 && model2 is PotionModel potionModel2)
        {
            return potionModel1.Owner.PotionSlots.IndexOf(potionModel1).CompareTo(potionModel2.Owner.PotionSlots.IndexOf(potionModel2));
        }

        if (model1 is PowerModel powerModel1 && model2 is PowerModel powerModel2)
        {
            return powerModel1.Owner.Powers.IndexOf(powerModel1).CompareTo(powerModel2.Owner.Powers.IndexOf(powerModel2));
        }

        throw new InvalidOperationException($"Tried to compare equivalent models {model1} and {model2} but we can't map them to any know model types!");
    }

    private static int CompareCardModelsWithSameOwnerAndId(CardModel cardModel1, CardModel cardModel2)
    {
        // Compare card pile types
        if (cardModel1.Pile == null && cardModel2.Pile == null) return 0;
        if (cardModel1.Pile == null) return 1;
        if (cardModel2.Pile == null) return -1;

        if (cardModel1.Pile.Type != cardModel2.Pile.Type)
        {
            return cardModel1.Pile.Type.CompareTo(cardModel2.Pile.Type);
        }

        // Same pile, use index in pile
        return cardModel1.Pile.Cards.IndexOf(cardModel1).CompareTo(cardModel2.Pile.Cards.IndexOf(cardModel2));
    }
}
