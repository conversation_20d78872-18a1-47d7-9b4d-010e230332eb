using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;

namespace MegaCrit.Sts2.Core.Entities.Merchant;

/// <summary>
/// Manages a buy-able card in the merchant shop.
/// </summary>
public sealed class MerchantCardEntry : MerchantEntry
{
    private static int GetCost(CardModel card)
    {
        int cost = card.Rarity switch
        {
            CardRarity.Rare => 150,
            CardRarity.Uncommon => 75,
            _ => 50
        };

        if (card.Pool is ColorlessCardPool)
        {
            cost = Mathf.RoundToInt(cost * 1.15f);
        }

        return cost;
    }

    public CardCreationResult? CreationResult { get; private set; }

    public bool IsOnSale { get; private set; }

    public MerchantCardEntry(CardCreationResult creationResult, Player player) : base(player)
    {
        CreationResult = creationResult;
        HookBus.Instance.ModifyMerchantCardCreationResults(_player, [CreationResult]);
        CalcCost();
    }

    // This is to check if we need to update a card based on a purchase that was made
    // i.e. toxic egg should visually update all skills to be upgraded
    protected override void UpdateEntry()
    {
        if (CreationResult != null)
        {
            HookBus.Instance.ModifyMerchantCardCreationResults(_player, [CreationResult]);
        }
    }

    public void SetOnSale()
    {
        IsOnSale = true;
        CalcCost();
    }

    public override bool IsStocked => CreationResult != null;

    public override void CalcCost()
    {
        if (CreationResult == null) throw new InvalidOperationException("There is no item to purchase.");

        _cost = Mathf.RoundToInt(GetCost(CreationResult.Card) * _player.PlayerRng.Shops.NextFloat(0.95f, 1.05f));

        if (IsOnSale)
        {
            _cost /= 2;
        }
    }

    protected override async Task<bool> OnTryPurchase(MerchantInventory? inventory)
    {
        CardPileAddResult result = await CardPileCmd.Add(CreationResult!.Card, CardPileTarget.Deck);

        if (!result.success)
        {
            InvokePurchaseFailed(PurchaseStatus.FailureSpace);
            return false;
        }

        _player.Gold -= Cost;

        ClimbManager.Instance.RewardSynchronizer.SyncLocalGoldLost(Cost);
        ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedCard(CreationResult.Card);

        if (HookBus.Instance.ShouldRefillMerchantEntry(this, _player))
        {
            // Don't restock with a card that's already stocked in a different entry.
            HashSet<CardModel> existingCards = inventory?
                .CardEntries
                .Select(e => e.CreationResult?.Card.CanonicalInstance)
                .OfType<CardModel>() // Exclude nulls
                .ToHashSet() ?? [];

            IEnumerable<CardModel> refillOptions = CreationResult.Card.Pool.Cards.Where(c => !existingCards.Contains(c));
            CreationResult = CardFactory.CreateForMerchant(_player, refillOptions, [CreationResult.Card.Type]).First();
            CalcCost();
        }
        else
        {
            CreationResult = null;
        }

        return true;
    }
}
