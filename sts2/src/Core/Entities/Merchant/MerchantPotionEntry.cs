using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Potions;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.TestSupport;

namespace MegaCrit.Sts2.Core.Entities.Merchant;

/// <summary>
/// Manages a purchasable potion in the merchant shop.
///
/// </summary>
public sealed class MerchantPotionEntry : MerchantEntry
{
    public PotionModel? Model { get; private set; }
    public override bool IsStocked => Model != null;

    public MerchantPotionEntry(PotionModel potion, Player player) : base(player)
    {
        potion.AssertMutable();
        Model = potion;
        CalcCost();
        SaveManager.Instance.MarkPotionAsSeen(Model);
    }

    public MerchantPotionEntry(Player player) : base(player)
    {
        FillSlot([]);
    }

    private void FillSlot(IEnumerable<PotionModel> blacklist)
    {
        Model = PotionFactory.CreateRandomPotionOutOfCombat(_player, _player.PlayerRng.Shops, blacklist).ToMutable();
        CalcCost();
        SaveManager.Instance.MarkPotionAsSeen(Model);
    }

    private static int GetCost(PotionRarity rarity)
    {
        return rarity switch
        {
            PotionRarity.Rare => 100,
            PotionRarity.Uncommon => 75,
            _ => 50
        };
    }

    public override void CalcCost()
    {
        if (Model == null)
        {
            throw new InvalidOperationException("There is no item to purchase.");
        }

        _cost = GetCost(Model!.Rarity);

        if (TestMode.IsOff)
        {
            _cost = (int)Mathf.Round(_cost * _player.PlayerRng.Shops.NextFloat(0.95f, 1.05f));
        }
    }

    protected override async Task<bool> OnTryPurchase(MerchantInventory? inventory)
    {
        if (Model == null) throw new InvalidOperationException("There is no item to purchase.");

        PotionProcureResult results = await PotionCmd.TryToProcure(Model, _player);
        if (!results.success)
        {
            InvokePurchaseFailed(results.failureReason == PotionProcureFailureReason.NotAllowed ? PurchaseStatus.NotAllowed : PurchaseStatus.FailureSpace);
            return false;
        }

        _player.Gold -= Cost;
        _player.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(_player.NetId).BoughtPotions.Add(Model.Id);

        ClimbManager.Instance.RewardSynchronizer.SyncLocalGoldLost(Cost);
        ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedPotion(Model);

        if (HookBus.Instance.ShouldRefillMerchantEntry(this, _player))
        {
            // Don't restock with a potion that's already stocked in a different entry.
            HashSet<PotionModel> existingPotions = inventory?
                .PotionEntries
                .Select(e => e.Model?.CanonicalInstance)
                .OfType<PotionModel>() // Exclude nulls
                .ToHashSet() ?? [];

            FillSlot(existingPotions);
        }
        else
        {
            Model = null;
        }

        return true;
    }
}
