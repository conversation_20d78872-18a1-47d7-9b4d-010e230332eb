using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Factories;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Core.Entities.Merchant;

/// <summary>
/// Manages a purchasable relic in the merchant shop
/// </summary>
public sealed class MerchantRelicEntry : MerchantEntry
{
    /// <summary>
    /// These relics can never be offered by the merchant.
    /// </summary>
    private static readonly HashSet<RelicModel> _baseBlacklist =
    [
        ModelDb.Relic<TheCourier>(),
        ModelDb.Relic<OldCoin>()
    ];

    public RelicModel? Model { get; private set; }
    public override bool IsStocked => Model != null;

    public MerchantRelicEntry(RelicRarity rarity, Player player) : base(player)
    {
        FillSlot(rarity);
    }

    private void FillSlot(RelicRarity rarity, IEnumerable<RelicModel>? blacklist = null)
    {
        blacklist ??= [];

        Model = RelicFactory.PullNextRelicFromBack(
            _player,
            rarity,
            blacklist.Concat(_baseBlacklist)
        ).ToMutable();

        CalcCost();
        SaveManager.Instance.MarkRelicAsSeen(Model);
    }

    public override void CalcCost()
    {
        _cost = (int)Math.Round(Model!.MerchantCost * _player.PlayerRng.Shops.NextFloat(0.95f, 1.05f));
    }

    protected override async Task<bool> OnTryPurchase(MerchantInventory? inventory)
    {
        _player.Gold -= Cost;
        _player.ClimbState.CurrentMapPointHistoryEntry?.GetEntry(_player.NetId).BoughtRelics.Add(Model!.Id);
        await RelicCmd.Obtain(Model!, _player);

        ClimbManager.Instance.RewardSynchronizer.SyncLocalGoldLost(Cost);
        ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedRelic(Model!);

        if (HookBus.Instance.ShouldRefillMerchantEntry(this, _player))
        {
            // Don't restock with a relic that's already stocked in a different entry.
            HashSet<RelicModel> existingRelics = inventory?
                .RelicEntries
                .Select(e => e.Model?.CanonicalInstance)
                .OfType<RelicModel>() // Exclude nulls
                .ToHashSet() ?? [];

            FillSlot(RelicFactory.RollRarity(_player), existingRelics);
        }
        else
        {
            Model = null;
        }

        return true;
    }
}
