using System;
using System.Collections.Generic;
using System.Linq;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using NUnit.Framework.Constraints;

namespace MegaCrit.Sts2.Test.Constraints;

public class CardsConstraint : Constraint
{
    private readonly ModelId[] _ids;

    public override string Description => $"Cards to be {string.Join<ModelId>(",", _ids)}";

    public CardsConstraint()
    {
        _ids = [];
    }

    public CardsConstraint(params Type[] types)
    {
        _ids = types.Select(ModelDb.GetId).ToArray();
    }

    public override ConstraintResult ApplyTo<T>(T actual)
    {
        IEnumerable<ModelId> ids;

        if (actual is CardPile pile)
        {
            ids = pile.Cards.Select(c => c.Id);
        }
        else
        {
            ids = (IEnumerable<ModelId>)actual;
        }

        return Is.EquivalentTo(_ids).ApplyTo(ids);
    }
}
