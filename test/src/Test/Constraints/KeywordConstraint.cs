using System;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using NUnit.Framework.Constraints;

namespace MegaCrit.Sts2.Test.Constraints;

public class KeywordConstraint(CardKeyword keyword) : Constraint
{
    public override string Description => $"Card to have keyword {keyword}";

    public override ConstraintResult ApplyTo<T>(T actual)
    {
        if (actual is not CardModel card) throw new ArgumentException($"Argument must be of type {nameof(CardModel)}, but was of type {typeof(T)}");

        return NUnit.Framework.Contains.Item(keyword).ApplyTo(card.Keywords);
    }
}