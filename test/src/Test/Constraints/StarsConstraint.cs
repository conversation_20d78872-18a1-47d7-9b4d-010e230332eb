using System;
using MegaCrit.Sts2.Core.Entities.Players;
using NUnit.Framework.Constraints;

namespace MegaCrit.Sts2.Test.Constraints;

public class StarsConstraint(int extraStars) : Constraint
{
    public override string Description => $"Extra stars to be {extraStars}";

    public override ConstraintResult ApplyTo<T>(T actual)
    {
        if (actual is not Player player) throw new ArgumentException($"Argument must be of type {nameof(Player)}, but was of type {typeof(T)}");

        return Is.EqualTo(extraStars).ApplyTo(Math.Max(0, player.PlayerCombatState!.Stars));
    }
}