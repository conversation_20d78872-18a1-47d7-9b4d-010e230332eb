using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Test.Models;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class CharacterSkillAchievementsTest : AchievementTest
{
    [Test]
    public async Task TestPlayingFiveSlyCardsFromSingleCardUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill().MockKeyword(CardKeyword.Sly), CardPileTarget.Hand);
        }

        MockSkillCard discardingCard = MockSkill().MockDiscard(5);
        await CardPileCmd.Add(discardingCard, CardPileTarget.Hand);

        await ModelTest.Play(discardingCard);

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillSilent1);
    }

    [Test]
    public async Task TestRemotePlayerPlayingFiveSlyCardsFromSingleCardDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(players[1]), CardPileTarget.Hand);
        }

        MockSkillCard discardingCard = MockSkill(players[1]).MockDiscard(5);
        await CardPileCmd.Add(discardingCard, CardPileTarget.Hand);

        await ModelTest.Play(discardingCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillSilent1), Is.False);
    }

    [Test]
    public async Task TestPlayingFourSlyCardsFromSingleCardDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(MockSkill().MockKeyword(CardKeyword.Sly), CardPileTarget.Hand);
        }

        MockSkillCard discardingCard = MockSkill().MockDiscard(4);
        await CardPileCmd.Add(discardingCard, CardPileTarget.Hand);

        await ModelTest.Play(discardingCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillSilent1), Is.False);
    }

    [Test]
    public async Task TestApplying99PoisonToAnEnemyUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard poisonCard = MockSkill().MockPower<Poison>(50, ActionTarget.AllEnemies);
        await CardPileCmd.Add(poisonCard, CardPileTarget.Hand);

        for (int i = 0; i < 2; i++)
        {
            await ModelTest.Play(poisonCard);
        }

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillSilent2);
    }

    [Test]
    public async Task TestRemotePlayerApplying99PoisonToAnEnemyDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard poisonCard = MockSkill(players[1]).MockPower<Poison>(99, ActionTarget.AllEnemies);
        await CardPileCmd.Add(poisonCard, CardPileTarget.Hand);
        await ModelTest.Play(poisonCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillSilent2), Is.False);
    }

    [Test]
    public async Task TestApplying98PoisonToAnEnemyDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard poisonCard = MockSkill().MockPower<Poison>(98, ActionTarget.AllEnemies);
        await CardPileCmd.Add(poisonCard, CardPileTarget.Hand);

        await ModelTest.Play(poisonCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillSilent2), Is.False);
    }

    [Test]
    public async Task TestExhausting20CardsInASingleCombatUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 2; i++)
        {
            for (int j = 0; j < 10; j++)
            {
                CardModel card = MockSkill().MockKeyword(CardKeyword.Exhaust);
                await CardPileCmd.Add(card, CardPileTarget.Hand);

                await ModelTest.Play(card);
            }

            await CombatManager.Instance.EndPlayerTurnPhaseOneInternal();
            await CombatManager.Instance.EndPlayerTurnPhaseTwoInternal();
            await CombatManager.Instance.SwitchFromPlayerToEnemySide();
        }

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillIronclad1);
    }

    [Test]
    public async Task TestRemotePlayerExhausting20CardsInASingleCombatDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int j = 0; j < 20; j++)
        {
            CardModel card = MockSkill(players[1]).MockKeyword(CardKeyword.Exhaust);
            await CardPileCmd.Add(card, CardPileTarget.Hand);

            await ModelTest.Play(card);
        }

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad1), Is.False);
    }

    [Test]
    public async Task TestExhausting19CardsInASingleCombatDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 19; i++)
        {
            CardModel card = MockSkill().MockKeyword(CardKeyword.Exhaust);
            await CardPileCmd.Add(card, CardPileTarget.Hand);
            await ModelTest.Play(card);
        }

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad1), Is.False);
    }

    [Test]
    public async Task TestExhausting20CardsInMultipleCombatsDoesNotUnlockAchievement()
    {
        await SetupClimb();

        for (int i = 0; i < 3; i++)
        {
            await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

            for (int j = 0; j < 10; j++)
            {
                CardModel card = MockSkill().MockKeyword(CardKeyword.Exhaust);
                await CardPileCmd.Add(card, CardPileTarget.Hand);
                await ModelTest.Play(card);
            }
        }

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad1), Is.False);
    }

    [Test]
    public async Task TestDealing999DamageUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockAttackCard card = MockAttack().MockDamage(999);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        await ModelTest.Play(card, card.CombatState!.Enemies[0]);

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillIronclad2);
    }

    [Test]
    public async Task TestRemotePlayerDealing999DamageDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockAttackCard card = MockAttack(players[1]).MockDamage(999);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        await ModelTest.Play(card, card.CombatState!.Enemies[0]);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad2), Is.False);
    }

    [Test]
    public async Task TestDealing998DamageDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockAttackCard card = MockAttack().MockDamage(998);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        await ModelTest.Play(card, card.CombatState!.Enemies[0]);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad2), Is.False);
    }

    [Test]
    public async Task TestDealing999DamageAcrossMultipleHitsDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockAttackCard card = MockAttack().MockDamage(99).MockHitCount(10);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        await ModelTest.Play(card, card.CombatState!.Enemies[0]);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad2), Is.False);
    }

    [Test]
    public async Task TestApplying999DoomUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard doomCard = MockSkill().MockPower<Doom>(500, ActionTarget.AllEnemies);

        for (int i = 0; i < 2; i++)
        {
            await ModelTest.Play(doomCard);
        }

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillNecrobinder1);
    }

    [Test]
    public async Task TestRemotePlayerApplying999DoomDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        await ModelTest.Play(MockSkill(players[1]).MockPower<Doom>(999, ActionTarget.AllEnemies));

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad2), Is.False);
    }

    [Test]
    public async Task TestApplying998DoomDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard doomCard = MockSkill().MockPower<Doom>(998, ActionTarget.AllEnemies);
        await CardPileCmd.Add(doomCard, CardPileTarget.Hand);

        await ModelTest.Play(doomCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillIronclad2), Is.False);
    }

    [Test]
    public async Task TestApplying50StrengthToOstyUnlocksAchievement()
    {
        await SetupClimb(ModelDb.Character<Necrobinder>());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        // This is kind of hard to mock so just do it directly
        await PowerCmd.Apply<Strength>(GetPlayer().Osty!, 50, GetPlayer().Creature, null);

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillNecrobinder2);
    }

    [Test]
    public async Task TestRemotePlayerApplying50StrengthToOstyDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Necrobinder>(1), Player.CreateForNewClimb<Necrobinder>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        // This is kind of hard to mock so just do it directly
        await PowerCmd.Apply<Strength>(players[1].Osty!, 50, players[1].Creature, null);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillNecrobinder2), Is.False);
    }

    [Test]
    public async Task TestApplying49StrengthToOstyDoesNotUnlockAchievement()
    {
        await SetupClimb(ModelDb.Character<Necrobinder>());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        // This is kind of hard to mock so just do it directly
        await PowerCmd.Apply<Strength>(GetPlayer().Osty!, 49, GetPlayer().Creature, null);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillNecrobinder2), Is.False);
    }

    [Test]
    public async Task TestForging999DamageSovereignBladeUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard forgeCard = MockSkill().MockForge(500);
        await CardPileCmd.Add(forgeCard, CardPileTarget.Hand);

        for (int i = 0; i < 2; i++)
        {
            await ModelTest.Play(forgeCard);
        }

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillRegent1);
    }

    [Test]
    public async Task TestRemotePlayerForging999DamageSovereignBladeDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Necrobinder>(1), Player.CreateForNewClimb<Necrobinder>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard forgeCard = MockSkill(players[1]).MockForge(999);
        await CardPileCmd.Add(forgeCard, CardPileTarget.Hand);

        await ModelTest.Play(forgeCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillRegent1), Is.False);
    }

    [Test]
    public async Task TestForging998DamageSovereignBladeUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard forgeCard = MockSkill().MockForge(998 - ModelDb.Card<SovereignBlade>().DynamicVars.Damage.BaseValue);
        await CardPileCmd.Add(forgeCard, CardPileTarget.Hand);

        await ModelTest.Play(forgeCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillRegent1), Is.False);
    }

    [Test]
    public async Task TestGaining20StarsUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard starsCard = MockSkill().MockStarGain(10);
        await CardPileCmd.Add(starsCard, CardPileTarget.Hand);

        await ModelTest.Play(starsCard);
        await ModelTest.Play(starsCard);

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.CharacterSkillRegent2);
    }

    [Test]
    public async Task TestRemotePlayerGaining20StarsDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Necrobinder>(1), Player.CreateForNewClimb<Necrobinder>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard starsCard = MockSkill(players[1]).MockStarGain(20);
        await CardPileCmd.Add(starsCard, CardPileTarget.Hand);
        await ModelTest.Play(starsCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillRegent2), Is.False);
    }

    [Test]
    public async Task TestGaining19StarsDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        MockSkillCard starsCard = MockSkill().MockStarGain(19);
        await CardPileCmd.Add(starsCard, CardPileTarget.Hand);

        await ModelTest.Play(starsCard);

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.CharacterSkillRegent2), Is.False);
    }
}
