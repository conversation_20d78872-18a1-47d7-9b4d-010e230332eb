using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Platform;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class NoRelicsAchievementTest : AchievementTest
{
    [Test]
    public async Task TestWinningWithoutObtainingRelicsUnlocksAchievement()
    {
        await SetupClimb();
        await WinClimb();
        AssertThatOnlyOneAchievementIsUnlocked(Achievement.NoRelicWin);
    }

    [Test]
    public async Task TestLosingWithoutObtainingRelicsDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await LoseClimb();
        Assert.That(AchievementsUtil.IsUnlocked(Achievement.NoRelicWin), Is.False);
    }

    [Test]
    public async Task TestWinningAfterObtainingAnyRelicDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await RelicCmd.Obtain<Strawberry>(GetPlayer());
        await WinClimb();
        Assert.That(AchievementsUtil.IsUnlocked(Achievement.NoRelicWin), Is.False);
    }

    [Test]
    public async Task TestWinningWhenOtherPlayerHasNoRelicsDoesNotUnlockAchievement()
    {
        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        await SetupMultiplayerClimb(players);
        await RelicCmd.Obtain<Strawberry>(GetPlayer());
        await WinClimb();

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.AllCardsUpgraded), Is.False);
    }
}
