using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Achievements;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Platform;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Test.Models;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Achievements;

public class Play20CardsInSingleTurnAchievementTest : AchievementTest
{
    [Test]
    public async Task TestPlaying20CardsInSingleTurnUnlocksAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 20; i++)
        {
            MockSkillCard card = MockSkill();
            await CardPileCmd.Add(card, CardPileTarget.Hand);

            await ModelTest.Play(card);
        }

        AssertThatOnlyOneAchievementIsUnlocked(Achievement.Play20CardsSingleTurn);
    }

    [Test]
    public async Task TestPlaying20CardsAcrossMultipleTurnsDoesNotUnlockAchievement()
    {
        await SetupClimb();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 3; i++)
        {
            for (int j = 0; j < 10; j++)
            {
                CardModel card = MockSkill().MockKeyword(CardKeyword.Exhaust);
                await CardPileCmd.Add(card, CardPileTarget.Hand);

                await ModelTest.Play(card);
            }

            await CombatManager.Instance.EndPlayerTurnPhaseOneInternal();
            await CombatManager.Instance.EndPlayerTurnPhaseTwoInternal();
            await CombatManager.Instance.SwitchFromPlayerToEnemySide();
        }

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.Play20CardsSingleTurn), Is.False);
    }

    [Test]
    public async Task TestRemotePlayerPlaying20CardsInSingleTurnDoesNotUnlockAchievement()
    {
        List<Player> players = [Player.CreateForNewClimb<Necrobinder>(1), Player.CreateForNewClimb<Necrobinder>(1000)];
        await SetupMultiplayerClimb(players);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        for (int i = 0; i < 20; i++)
        {
            CardModel card = MockSkill(players[1]).MockKeyword(CardKeyword.Exhaust).MockEnergyCost(0);
            await CardPileCmd.Add(card, CardPileTarget.Hand);

            await ModelTest.Play(card);
        }

        Assert.That(AchievementsUtil.IsUnlocked(Achievement.Play20CardsSingleTurn), Is.False);
    }
}
