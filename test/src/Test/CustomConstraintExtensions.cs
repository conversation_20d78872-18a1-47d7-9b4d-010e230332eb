using System;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Test.Constraints;
using NUnit.Framework.Constraints;

namespace MegaCrit.Sts2.Test;

public static class CustomConstraintExtensions
{
    public static AliveConstraint Alive(this ConstraintExpression expression)
    {
        AliveConstraint constraint = new();
        expression.Append(constraint);
        return constraint;
    }

    public static BlockConstraint Block(this ConstraintExpression expression, int expected)
    {
        BlockConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static CardsConstraint Cards(this ConstraintExpression expression, params Type[] expected)
    {
        CardsConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static CardTypeConstraint CardType(this ConstraintExpression expression, CardType expected)
    {
        CardTypeConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static DeadConstraint Dead(this ConstraintExpression expression)
    {
        DeadConstraint constraint = new();
        expression.Append(constraint);
        return constraint;
    }

    public static EnergyCostConstraint EnergyCost(this ConstraintExpression expression, int expected)
    {
        EnergyCostConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static AfflictionConstraint Affliction(this ConstraintExpression expression, AfflictionModel expected)
    {
        AfflictionConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static EnergyCostGreaterThanConstraint EnergyCostGreaterThan(this ConstraintExpression expression, int expected)
    {
        EnergyCostGreaterThanConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static EnergyCostLessThanConstraint EnergyCostLessThan(this ConstraintExpression expression, int expected)
    {
        EnergyCostLessThanConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static EnergyCostFreeConstraint EnergyCostFree(this ConstraintExpression expression)
    {
        EnergyCostFreeConstraint constraint = new();
        expression.Append(constraint);
        return constraint;
    }

    public static EnergyCostXConstraint EnergyCostX(this ConstraintExpression expression)
    {
        EnergyCostXConstraint constraint = new();
        expression.Append(constraint);
        return constraint;
    }

    public static ExtraEnergyConstraint ExtraEnergy(this ConstraintExpression expression, int expected)
    {
        ExtraEnergyConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static InPileConstraint InPile(this ConstraintExpression expression, CardPileTarget expected)
    {
        InPileConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static CardPoolConstraint<T> InPool<T>(this ConstraintExpression expression) where T : CardPoolModel
    {
        CardPoolConstraint<T> constraint = new();
        expression.Append(constraint);
        return constraint;
    }

    public static KeywordConstraint Keyword(this ConstraintExpression expression, CardKeyword expected)
    {
        KeywordConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static LostHpConstraint LostHp(this ConstraintExpression expression, int expected)
    {
        LostHpConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static PowerAmountConstraint<T> PowerAmount<T>(this ConstraintExpression expression, int expectedAmount) where T : PowerModel
    {
        PowerAmountConstraint<T> constraint = new(expectedAmount);
        expression.Append(constraint);
        return constraint;
    }

    public static SpentEnergyConstraint SpentEnergy(this ConstraintExpression expression, int expected)
    {
        SpentEnergyConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static StarCostConstraint StarCost(this ConstraintExpression expression, int expected)
    {
        StarCostConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static StarCostGreaterThanConstraint StarCostGreaterThan(this ConstraintExpression expression, int expected)
    {
        StarCostGreaterThanConstraint constraint = new(expected);
        expression.Append(constraint);
        return constraint;
    }

    public static UpgradedConstraint Upgraded(this ConstraintExpression expression)
    {
        UpgradedConstraint constraint = new();
        expression.Append(constraint);
        return constraint;
    }
}
