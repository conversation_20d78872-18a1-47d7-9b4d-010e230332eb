using System.IO;
using Godot;

namespace MegaCrit.Sts2.Test.Helpers;

public static class PathHelper
{
    /// <summary>
    /// The root directory of the whole repository.
    /// </summary>
    public static string RepoRootDir => new DirectoryInfo(Sts2ProjectDir).Parent!.FullName;

    /// <summary>
    /// The directory that the STS2 project lives in.
    /// </summary>
    public static string Sts2ProjectDir => new DirectoryInfo(ProjectSettings.GlobalizePath("res://")).FullName;

    /// <summary>
    /// The directory that the STS2 project's C# source files live in.
    /// </summary>
    public static string SrcDir => new DirectoryInfo(ProjectSettings.GlobalizePath("res://src")).FullName;

    /// <summary>
    /// The directory that the STS2 project's localization files live in.
    /// </summary>
    public static string LocDir => new DirectoryInfo(ProjectSettings.GlobalizePath("res://localization")).FullName;
}