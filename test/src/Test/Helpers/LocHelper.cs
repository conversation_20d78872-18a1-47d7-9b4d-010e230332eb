using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;

namespace MegaCrit.Sts2.Test.Helpers;

public static partial class LocHelper
{
    public static List<string> GetAllTablePaths()
    {
        DirectoryInfo di = new(PathHelper.LocDir);
        return di.GetFiles("*.json", SearchOption.AllDirectories).Select(fi => fi.FullName).ToList();
    }

    /// <summary>
    /// Get the table key from a line of JSON loc.
    /// </summary>
    /// <example>
    /// "BASH.title": "Bash",
    /// should return:
    /// "BASH.title"
    /// </example>
    /// <param name="jsonLine">Line of JSON loc.</param>
    /// <param name="filename">Name of the file it's in. This is just used for the error message.</param>
    /// <returns>Table key.</returns>
    public static string GetKey(string jsonLine, string filename)
    {
        Match match = TableKeyRegex().Match(jsonLine);

        if (match.Success)
        {
            return match.Groups[1].Value;
        }
        else
        {
            throw new ArgumentException($"Line {jsonLine} in {filename} doesn't match format.", nameof(jsonLine));
        }
    }

    [GeneratedRegex(@"""([^""]+)"":")]
    private static partial Regex TableKeyRegex();
}