using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Cards;

[TestFixture]
public class GeneticAlgorithmSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        GeneticAlgorithm initialCard = (GeneticAlgorithm)ModelDb.Card<GeneticAlgorithm>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialCard.CurrentBlock += 10;
        initialSave.Players[0].Deck.Add(initialCard.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        GeneticAlgorithm deserializedCard = (GeneticAlgorithm)CardModel.FromSerializable(deserializedSave.Players[0].Deck[0]);

        Assert.That(deserializedCard.DynamicVars.Block.IntValue, Is.EqualTo(11));
    }
}
