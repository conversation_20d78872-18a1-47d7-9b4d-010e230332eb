using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Logging;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.SavedPropertyTests;

public class CheckSavedPropertyTypesTest
{
    private HashSet<Type> _allowedTypes =
    [
        typeof(int),
        typeof(bool),
        typeof(string),
        typeof(List<int>),
        typeof(ModelId),
        typeof(SerializableCard),
        typeof(List<SerializableCard>)
    ];

    [Test]
    public void TestAllSavedPropertyAttributesAreOnAllowedTypes()
    {
        PropertyInfo[] allPropertyInfos = ReflectionHelper.AllTypes.SelectMany(a => a.GetProperties()).ToArray();

        int propertiesWithNonAllowedSavedPropertyType = 0;

        foreach (PropertyInfo propertyInfo in allPropertyInfos)
        {
            Type propertyType = propertyInfo.PropertyType;
            foreach (object attribute in propertyInfo.GetCustomAttributes(true))
            {
                if (attribute is not SavedPropertyAttribute savedPropertyAttribute) continue;

                if (!_allowedTypes.Contains(propertyType) &&
                    !propertyType.IsEnum &&
                    (!propertyType.IsArray || !propertyType.GetElementType()!.IsEnum))
                {
                    propertiesWithNonAllowedSavedPropertyType++;
                    Log.Error(string.Format(
                        $"Property '{propertyInfo.DeclaringType}.{propertyInfo.Name}' has invalid type: '{propertyInfo.PropertyType}'!"));
                }
            }
        }

        Assert.That(propertiesWithNonAllowedSavedPropertyType, Is.EqualTo(0));
    }
}
