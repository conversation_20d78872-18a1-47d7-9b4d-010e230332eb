using System.Text.Json;
using JetBrains.Annotations;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.SavedPropertyTests;

[TestFixture]
public class SaveIfNotDefaultTypeBehaviourTest
{
    private static readonly JsonSerializerOptions _serializerOptions = new() { IncludeFields = true };

    private enum TestEnum
    {
        ValueOne,
        ValueTwo,
        ValueThree
    }

    private class TestClass
    {
        [SavedProperty(SerializationCondition.SaveIfNotTypeDefault)]
        public int SaveIfNotZeroInt { get; init; }

        [SavedProperty(SerializationCondition.SaveIfNotTypeDefault)]
        public TestEnum SaveIfNotValueOneEnum { get; init; }

        [SavedProperty(SerializationCondition.SaveIfNotTypeDefault)]
        public bool SaveIfNotFalseBool { get; init; }

        // [UsedImplicitly] because we check the serialized version.
        [SavedProperty(SerializationCondition.SaveIfNotTypeDefault)]
        public int DummyProperty { [UsedImplicitly] get; set; }
    }

    [SetUp]
    public void Setup()
    {
        SavedPropertiesTypeCache.InjectTypeIntoCache(typeof(TestClass));
    }

    [Test]
    public void TestNoPropertiesSetGeneratesNullSavedProperties()
    {
        SavedPropertiesTypeCache.InjectTypeIntoCache(typeof(TestClass));
        TestClass test = new();
        SavedProperties? props = SavedProperties.FromInternal(test, null);

        Assert.That(props, Is.Null);
    }

    [Test]
    public void TestDoesNotSaveAnyDefaultProperties()
    {
        TestClass test = new()
        {
            DummyProperty = 11
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        Assert.That(props, Is.Not.Null);
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotZeroInt)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotValueOneEnum)));
        Assert.That(json, Does.Not.Contain(nameof(TestClass.SaveIfNotFalseBool)));
    }

    [Test]
    public void TestDeserializesDefaultValuesCorrectly()
    {
        TestClass test = new()
        {
            DummyProperty = 11
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        SavedProperties deserializedProps = JsonSerializer.Deserialize<SavedProperties>(json, _serializerOptions)!;
        TestClass deserialized = new();
        deserializedProps.FillInternal(deserialized);

        Assert.That(deserialized.SaveIfNotZeroInt, Is.EqualTo(0));
        Assert.That(deserialized.SaveIfNotValueOneEnum, Is.EqualTo(TestEnum.ValueOne));
        Assert.That(deserialized.SaveIfNotFalseBool, Is.EqualTo(false));
    }

    [Test]
    public void TestSavesNonDefaultProperties()
    {
        SavedPropertiesTypeCache.InjectTypeIntoCache(typeof(TestClass));
        TestClass test = new()
        {
            SaveIfNotValueOneEnum = TestEnum.ValueTwo,
            SaveIfNotZeroInt = 123,
            SaveIfNotFalseBool = true
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        Assert.That(props, Is.Not.Null);
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotValueOneEnum)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotZeroInt)));
        Assert.That(json, Does.Contain(nameof(TestClass.SaveIfNotFalseBool)));
    }

    [Test]
    public void TestDeserializesNonDefaultPropertiesCorrectly()
    {
        SavedPropertiesTypeCache.InjectTypeIntoCache(typeof(TestClass));
        TestClass test = new()
        {
            SaveIfNotValueOneEnum = TestEnum.ValueTwo,
            SaveIfNotZeroInt = 123,
            SaveIfNotFalseBool = true
        };

        SavedProperties? props = SavedProperties.FromInternal(test, null);
        string json = JsonSerializer.Serialize(props, _serializerOptions);

        SavedProperties deserializedProps = JsonSerializer.Deserialize<SavedProperties>(json, _serializerOptions)!;
        TestClass deserialized = new();
        deserializedProps.FillInternal(deserialized);

        Assert.That(deserialized.SaveIfNotValueOneEnum, Is.EqualTo(TestEnum.ValueTwo));
        Assert.That(deserialized.SaveIfNotZeroInt, Is.EqualTo(123));
        Assert.That(deserialized.SaveIfNotFalseBool, Is.EqualTo(true));
    }
}
