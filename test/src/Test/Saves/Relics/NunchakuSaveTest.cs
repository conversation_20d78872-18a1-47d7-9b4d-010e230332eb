using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Relics;

[TestFixture]
public class NunchakuSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        Nunchaku initialRelic = (Nunchaku)ModelDb.Relic<Nunchaku>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialRelic.AttacksPlayed++;
        initialSave.Players[0].Relics.Add(initialRelic.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        Nunchaku deserializedRelic = (Nunchaku)RelicModel.FromSerializable(deserializedSave.Players[0].Relics[0]);

        Assert.That(deserializedRelic.AttacksPlayed, Is.EqualTo(1));
    }
}
