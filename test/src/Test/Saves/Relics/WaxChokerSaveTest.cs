using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Relics;

[TestFixture]
public class WaxChokerSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        WaxChoker initialRelic = (WaxChoker)ModelDb.Relic<WaxChoker>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialRelic.CombatsSeen += 3;
        initialSave.Players[0].Relics.Add(initialRelic.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        WaxChoker deserializedRelic = (WaxChoker)RelicModel.FromSerializable(deserializedSave.Players[0].Relics[0]);

        Assert.That(deserializedRelic.CombatsSeen, Is.EqualTo(3));
    }
}