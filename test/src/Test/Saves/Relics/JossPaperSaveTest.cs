using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Climbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves.Relics;

[TestFixture]
public class JossPaperSaveTest
{
    [Test]
    public void TestSerializesState()
    {
        JossPaper initialRelic = (JossPaper)ModelDb.Relic<JossPaper>().ToMutable();
        SerializableClimb initialSave = new() { Players = [new SerializablePlayer()] };

        initialRelic.CardsExhausted++;
        initialSave.Players[0].Relics.Add(initialRelic.ToSerializable());
        string json = SaveManager.ToJson(initialSave);

        SerializableClimb deserializedSave = SaveManager.FromJson<SerializableClimb>(json).SaveData!;
        JossPaper deserializedRelic = (JossPaper)RelicModel.FromSerializable(deserializedSave.Players[0].Relics[0]);

        Assert.That(deserializedRelic.CardsExhausted, Is.EqualTo(1));
    }
}
