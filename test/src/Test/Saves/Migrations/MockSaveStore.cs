using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Test.Saves.Migrations;

public class MockSaveStore : ISaveStore
{
    private readonly Dictionary<string, string> _fileContents = new();
    public Dictionary<string, string> RenamedFiles { get; } = new();

    public void SetFileContent(string path, string content)
    {
        _fileContents[path] = content;
    }

    public bool DeleteFile(string filePath) => _fileContents.Remove(filePath);

    public virtual void RenameFile(string sourcePath, string destinationPath)
    {
        string? content = ReadFile(sourcePath);
        if (content != null)
        {
            DeleteFile(sourcePath);
            WriteFile(destinationPath, content);
            RenamedFiles[sourcePath] = destinationPath;
        }
    }

    public string[] GetFilesInDirectory(string directoryPath) => throw new NotImplementedException();

    public void CreateDirectory(string directoryPath)
    {
        throw new NotImplementedException();
    }

    public void DeleteTemporaryFiles(string directoryPath)
    {
        throw new NotImplementedException();
    }

    public virtual string GetFullPath(string filename) => filename;

    public Task WriteFileAsync(string path, Stream contentStream) => throw new NotImplementedException();

    public bool FileExists(string filePath) => _fileContents.ContainsKey(filePath);

    void ISaveStore.DeleteFile(string path)
    {
        throw new NotImplementedException();
    }

    public IEnumerable<string> GetFiles(string directory, string searchPattern)
    {
        // Simple implementation just for testing
        // This doesn't properly implement wildcards but is sufficient for the tests
        return _fileContents.Keys
            .Where(path => path.StartsWith(directory));
    }

    public string? ReadFile(string filePath) => _fileContents.TryGetValue(filePath, out string? content) ? content : null;

    public void WriteFile(string filePath, string content)
    {
        _fileContents[filePath] = content;
    }
}
