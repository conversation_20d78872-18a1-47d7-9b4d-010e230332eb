using System;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.Saves;
using MegaCrit.Sts2.Core.Saves.Managers;
using MegaCrit.Sts2.Test.Multiplayer;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Saves;

public class CombatRoomSaveTest
{
    private MockGodotFileIo _fileIo = default!;

    [SetUp]
    public void Setup()
    {
        _fileIo = new MockGodotFileIo("user://test");
        SaveManager.MockInstanceForTesting(new SaveManager(_fileIo, true));
    }

    [TearDown]
    public void Teardown()
    {
        SaveManager.ClearInstanceForTesting();
        ClimbManager.Instance.CleanUp();
    }

    [Test]
    public async Task TestCombatRoomGeneratesSaveAfterCompletion()
    {
        await CreateClimbAndEnterRoom();
        await CombatManager.Instance.EndCombatInternal();

        Assert.That(_fileIo.FileExists($"user://test/{ClimbSaveManager.climbSaveFileName}"), Is.True);
    }

    [Test]
    public async Task TestLoadingIntoPreFinishedCombatRoom()
    {
        await CreateClimbAndEnterRoom();

        await CombatManager.Instance.EndCombatInternal();
        ClimbManager.Instance.CleanUp();

        ClimbState climbState = await LoadClimbAndEnterPreFinishedRoom();

        Assert.That(climbState.CurrentRoom!.IsPreFinished, Is.True);
        Assert.That(climbState.CurrentRoom, Is.TypeOf<CombatRoom>());
    }

    [Test]
    public async Task TestLoadingIntoPreFinishedCombatRoomWithEscapees()
    {
        await CreateClimbAndEnterRoom();

        await CreatureCmd.Escape(CombatManager.Instance.DebugOnlyGetState()!.Enemies.First());
        await CombatManager.Instance.CheckWinCondition();
        ClimbManager.Instance.CleanUp();

        ClimbState climbState = await LoadClimbAndEnterPreFinishedRoom();

        Assert.That(((CombatRoom)climbState.CurrentRoom!).GoldProportion, Is.Zero);
    }

    private async Task CreateClimbAndEnterRoom()
    {
        ClimbState climbState = ClimbState.CreateForTest();
        ClimbManager.Instance.SetUpTest(climbState, new TestGameService(1, NetGameType.Singleplayer), shouldSave: true);

        ClimbManager.Instance.Launch();
        ClimbManager.Instance.GenerateRooms();
        await ClimbManager.Instance.GenerateMap();

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());
    }

    private async Task<ClimbState> LoadClimbAndEnterPreFinishedRoom()
    {
        SerializableClimb loadedClimb = SaveManager.Instance.LoadClimbSave().SaveData!;
        ClimbState climbState = ClimbState.FromSerializable(loadedClimb);
        ClimbManager.Instance.SetUpSavedSinglePlayer(climbState, loadedClimb);
        ClimbManager.Instance.Launch();
        await ClimbManager.Instance.GenerateMap();
        await ClimbManager.Instance.EnterMapPointInternal(
            1,
            MapPointType.Monster,
            AbstractRoom.FromSerializable(loadedClimb.PreFinishedRoom, climbState),
            false
        );

        return climbState;
    }
}
