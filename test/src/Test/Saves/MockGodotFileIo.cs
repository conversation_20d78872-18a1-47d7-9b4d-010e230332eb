using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Saves;

namespace MegaCrit.Sts2.Test.Saves;

/// <summary>
/// A minimalist mock implementation of ISaveStore for testing.
/// Instead of relying on actual file system operations like the original implementation,
/// this mock keeps everything in memory and tracks method calls for verification.
/// This approach:
/// 1. Avoids duplicating the real implementation's logic
/// 2. Makes tests more predictable by not hitting the actual file system
/// 3. Allows verifying interactions through the Calls collection
/// 4. Provides customization points only where needed for specific tests
/// </summary>
public class MockGodotFileIo : ISaveStore
{
    /// <summary>
    /// Constants for method names to use when tracking or comparing method calls
    /// </summary>
    public static class Methods
    {
        public const string writeFile = "WriteFile";
        public const string writeFileAsync = "WriteFileAsync";
        public const string readFile = "ReadFile";
        public const string fileExists = "FileExists";
        public const string renameFile = "RenameFile";
        public const string deleteFile = "DeleteFile";
        public const string getFullPath = "GetFullPath";
        public const string getFilesInDirectory = "GetFilesInDirectory";
        public const string createDirectory = "CreateDirectory";
        public const string deleteTemporaryFiles = "DeleteTemporaryFiles";
    }

    /// <summary>
    /// In-memory storage of file contents, with path as the key and content as the value
    /// </summary>
    private readonly Dictionary<string, string> _fileContents = new();

    /// <summary>
    /// In-memory representation of directory structure
    /// </summary>
    private readonly Dictionary<string, List<string>> _directories = new();

    /// <summary>
    /// Tracks all method calls for verification in tests
    /// </summary>
    public List<(string Method, object[] Args)> Calls { get; } = [];

    /// <summary>
    /// Custom callback for RenameFile operation to allow tests to intercept and verify behavior
    /// </summary>
    public Action<string, string>? RenameFileAction { get; set; }

    /// <summary>
    /// Base directory for GetFullPath operations
    /// </summary>
    private readonly string _saveDir;

    /// <summary>
    /// Creates a new instance of the mock save store with the specified base directory
    /// </summary>
    /// <param name="saveDir">The base directory for file operations</param>
    public MockGodotFileIo(string saveDir)
    {
        _saveDir = saveDir;
        // Create the base directory in our virtual file system
        CreateDirectory(_saveDir);
    }

    /// <summary>
    /// Gets the full path for a filename relative to the base directory
    /// </summary>
    public string GetFullPath(string filename)
    {
        Calls.Add((Methods.getFullPath, [filename]));
        return $"{_saveDir}/{filename}";
    }

    /// <summary>
    /// Reads a file from the virtual file system
    /// </summary>
    public string? ReadFile(string path)
    {
        Calls.Add((Methods.readFile, [path]));
        return _fileContents.TryGetValue(path, out string? content) ? content : null;
    }

    /// <summary>
    /// Writes a file to the virtual file system
    /// </summary>
    public void WriteFile(string path, string content)
    {
        Calls.Add((Methods.writeFile, [path, content]));
        _fileContents[path] = content;
    }

    /// <summary>
    /// Asynchronously writes a file to the virtual file system
    /// </summary>
    public Task WriteFileAsync(string path, Stream contentStream)
    {
        Calls.Add((Methods.writeFileAsync, [path, contentStream]));

        // Read the content from the stream
        using StreamReader reader = new(contentStream, leaveOpen: true);
        contentStream.Position = 0;
        _fileContents[path] = reader.ReadToEnd();

        return Task.CompletedTask;
    }

    /// <summary>
    /// Checks if a file exists in the virtual file system
    /// </summary>
    public bool FileExists(string path)
    {
        Calls.Add((Methods.fileExists, [path]));
        return _fileContents.ContainsKey(path);
    }

    /// <summary>
    /// Deletes a file from the virtual file system
    /// </summary>
    public void DeleteFile(string path)
    {
        Calls.Add((Methods.deleteFile, [path]));
        _fileContents.Remove(path);
    }

    /// <summary>
    /// Renames a file in the virtual file system.
    /// If RenameFileAction is set, it will be called instead of performing the default behavior.
    /// </summary>
    public void RenameFile(string sourcePath, string destinationPath)
    {
        Calls.Add((Methods.renameFile, [sourcePath, destinationPath]));

        if (RenameFileAction != null)
        {
            // Allow test to handle the rename operation
            RenameFileAction(sourcePath, destinationPath);
            return;
        }

        // Default implementation - move the content from source to destination
        if (_fileContents.Remove(sourcePath, out string? content))
        {
            _fileContents[destinationPath] = content;
        }
    }

    /// <summary>
    /// Gets all files in a directory from the virtual file system
    /// </summary>
    public string[] GetFilesInDirectory(string directoryPath)
    {
        Calls.Add((Methods.getFilesInDirectory, [directoryPath]));

        // Extract just the filenames from paths that start with the directory
        string prefix = directoryPath.EndsWith('/') ? directoryPath : directoryPath + '/';
        return _fileContents.Keys
            .Where(path => path.StartsWith(prefix))
            .Select(path => Path.GetFileName(path))
            .ToArray();
    }

    /// <summary>
    /// Creates a directory in the virtual file system
    /// </summary>
    public void CreateDirectory(string directoryPath)
    {
        Calls.Add((Methods.createDirectory, [directoryPath]));
        if (!_directories.ContainsKey(directoryPath))
        {
            _directories[directoryPath] = [];
        }
    }

    /// <summary>
    /// Deletes temporary files (ending with .tmp) from a directory in the virtual file system
    /// </summary>
    public void DeleteTemporaryFiles(string directoryPath)
    {
        Calls.Add((Methods.deleteTemporaryFiles, [directoryPath]));

        // Find and remove all .tmp files
        string prefix = directoryPath.EndsWith('/') ? directoryPath : directoryPath + '/';
        List<string> tempFiles = _fileContents.Keys
            .Where(path => path.StartsWith(prefix) && path.EndsWith(".tmp"))
            .ToList();

        foreach (string file in tempFiles)
        {
            _fileContents.Remove(file);
        }
    }
}
