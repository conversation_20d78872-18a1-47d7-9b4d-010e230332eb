using System.Collections.Generic;
using System.IO;
using System.Linq;
using MegaCrit.Sts2.Test.Helpers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.CodeGotchas;

[TestFixture]
public class BinaryResourcesTest
{
    private static readonly HashSet<string> _filesToCheck = [".res", ".glb"];

    [Test]
    public void TestFoundBinaryFilesWithTextFileAlternatives()
    {
        string dir = PathHelper.Sts2ProjectDir;
        IEnumerable<string> allFiles = Directory.EnumerateFiles(dir, "*", SearchOption.AllDirectories).Where(file =>
        {
            // Ignore addons since they're doing their own thing.
            if (file.Contains("addons")) return false;

            // Ignore files in the .godot dir since we don't commit them anyways.
            if (file.Contains(".godot")) return false;

            // Only include files with the specified extensions.
            return _filesToCheck.Contains(Path.GetExtension(file).ToLowerInvariant());
        });

        List<string> filesToConvert = [];

        foreach (string file in allFiles)
        {
            string extension = Path.GetExtension(file).ToLowerInvariant();
            switch (extension)
            {
                case ".res":
                    filesToConvert.Add($"File: '{file}' is a '.res' file. Please export it as '.tres' instead.");
                    break;
                case ".glb":
                    filesToConvert.Add($"File: '{file}' is a '.glb' file. Please export it as '.gltf' instead.");
                    break;
            }
        }

        if (filesToConvert.Count != 0)
        {
            Assert.Fail("The following files need to be converted:\n" + string.Join("\n", filesToConvert));
        }
    }
}