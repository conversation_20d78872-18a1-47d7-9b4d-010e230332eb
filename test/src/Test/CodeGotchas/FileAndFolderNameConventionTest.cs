using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using MegaCrit.Sts2.Test.Helpers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.CodeGotchas;

[TestFixture]
public partial class FileAndFolderNameConventionTest
{
    [Test]
    public void TestFileAndFolderNamesFollowConvention()
    {
        string rootDirectory = PathHelper.Sts2ProjectDir;
        string[] includeFolders = ["images", "localization", "scenes"];
        string[] filesToIgnore = [".DS_Store"];

        IEnumerable<string> allPaths = Directory.GetFiles(rootDirectory, "*.*", SearchOption.AllDirectories)
            .Union(Directory.GetDirectories(rootDirectory, "*", SearchOption.AllDirectories))
            .Where(path => includeFolders.Any(folder => path.StartsWith(Path.Combine(rootDirectory, folder), StringComparison.OrdinalIgnoreCase)))
            .Where(file => filesToIgnore.All(ignore => !file.Contains(ignore)));


        Regex regex = ValidPathCharactersRegex();
        foreach (string path in allPaths)
        {
            string name = Path.GetFileName(path);
            Assert.That(name, Does.Match(regex), $"{path} does not follow the naming convention.");
        }
    }

    [GeneratedRegex("^[a-z0-9_.]*$")]
    private static partial Regex ValidPathCharactersRegex();
}