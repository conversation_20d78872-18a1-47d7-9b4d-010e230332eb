using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.TestSupport;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PaelsWingTest : ModelTest
{
    [Test]
    public async Task TestAddExtraOption()
    {
        await RelicCmd.Obtain<PaelsWing>(GetPlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetPlayer()));
        Assert.That(options.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task TestGetExtraRelic()
    {
        Player player = GetPlayer();
        int startingMaxHp = player.Creature.MaxHp;

        await RelicCmd.Obtain<PaelsWing>(player);

        // guarantee we roll the Strawberry when we get a nwe relic
        TestRngInjector.SetRelicOverride<Strawberry>();

        for (int i = 0; i < 3; i++)
        {
            IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetPlayer()));
            CardRewardAlternative sacrificeOption = options.First(o => o.OptionId == PaelsWing.sacrificeAlternativeKey);
            await sacrificeOption.OnSelect();
        }

        // Completing the pael wing requirement of 3 sacrifices
        // should result in obtaining a Strawberry, which should gain the player 7 HP.
        Assert.That(player.Creature.MaxHp, Is.EqualTo(startingMaxHp + 7));
    }
}
