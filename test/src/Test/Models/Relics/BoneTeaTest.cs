using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BoneTeaTest : ModelTest
{
    [Test]
    public async Task TestUpgradesHandOnCombat1()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), CardPileTarget.Deck);
        }

        await RelicCmd.Obtain<BoneTea>(player);
        await RestartCombat();

        Assert.That(CardPileTarget.Hand.GetPile(player), Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard)));
        Assert.That(CardPileTarget.Hand.GetPile(player).Cards, Is.All.Upgraded());
    }

    [Test]
    public async Task TestUpgradesHandOnCombat4()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), CardPileTarget.Deck);
        }

        await RelicCmd.Obtain<BoneTea>(player);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
        }

        Assert.That(CardPileTarget.Hand.GetPile(player), Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard)));
        Assert.That(CardPileTarget.Hand.GetPile(player).Cards, Is.All.Upgraded());
    }

    [Test]
    public async Task TestDoesNothingOnCombat6()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 6; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), CardPileTarget.Deck);
        }

        await RelicCmd.Obtain<BoneTea>(player);

        for (int i = 0; i < 6; i++)
        {
            await RestartCombat();
        }

        Assert.That(CardPileTarget.Hand.GetPile(player), Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard)));
        Assert.That(CardPileTarget.Hand.GetPile(player).Cards, Is.All.Not.Upgraded());
    }

    [Test]
    public async Task TestDoesNothingOnTurn2OfCombat()
    {
        Player player = GetPlayer();

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), CardPileTarget.Deck);
        }

        await RelicCmd.Obtain<BoneTea>(player);
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(CardPileTarget.Hand.GetPile(player), Has.Cards(typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard), typeof(MockAttackCard)));
        Assert.That(CardPileTarget.Hand.GetPile(player).Cards, Is.All.Not.Upgraded());
    }
}
