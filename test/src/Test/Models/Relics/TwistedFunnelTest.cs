using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class TwistedFunnelTest : ModelTest
{
    [Test]
    public async Task TestAddPoisonAtTheStartOfCombat()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TwistedFunnel>(player);
        await RestartCombat();

        Assert.That(GetEnemy(), Has.PowerAmount<Poison>(4));
    }

    [Test]
    public async Task TestOnlyAddPoisonAtTheStartOfCombat()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TwistedFunnel>(player);
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has<PERSON><Poison>(3));
    }
}
