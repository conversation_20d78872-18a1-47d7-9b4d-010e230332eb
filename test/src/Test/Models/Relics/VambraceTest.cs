using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class VambraceTest : ModelTest
{
    [Test]
    public async Task TestWhenBlockGained()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await Play<MockSkillCard>();

        Assert.That(GetPlayer().Creature, Has.Block(10));
    }

    [Test]
    public async Task TestWithDexterity()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, 2, null, null);
        await Play<MockSkillCard>();

        // Vambrace always triggers after Dexterity.
        // (5 + 2) * 2
        Assert.That(GetPlayer().Creature, Has.Block(14));
    }

    [Test]
    public async Task TestWhenTwoBlockCardsPlayed()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await Play<MockSkillCard>();
        await Play<MockSkillCard>();

        // First play: 5 * 2
        // Second play: 5 (Vambrace has been used up)
        Assert.That(GetPlayer().Creature, Has.Block(15));
    }

    [Test]
    public async Task TestWhenSingleCardGainsBlockMultipleTimes()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await Play(MockSkill().MockBlockCount(2));

        // 2 instances of block * 5 block per instance * 2 from Vambrace
        Assert.That(GetPlayer().Creature, Has.Block(20));
    }

    [Test]
    public async Task TestOnlyTriggersOnceOverMultipleTurns()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());

        await Play<MockSkillCard>();

        await PassToNextPlayerTurn();

        await Play<MockSkillCard>();

        // Playing MockSkillCard on turn 1 used up Vambrace, so playing MockSkillCard on turn 2 gains the normal amount of block.
        Assert.That(GetPlayer().Creature, Has.Block(5));
    }

    [Test]
    public async Task TestDoesNotTriggerForNonCardBlockGain()
    {
        await RelicCmd.Obtain<Vambrace>(GetPlayer());
        await UsePotion<BlockPotion>();

        // Would be 24 if Vambrace triggered.
        Assert.That(GetPlayer().Creature, Has.Block(12));
    }

    [Test]
    public async Task TestDoesNotTriggerWhenZeroBlockIsGained()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<Vambrace>(player);

        await Play(MockSkill().MockBlock(0));
        await Play(MockSkill());

        // The no-block skill shouldn't trigger Vambrace, so it should still double the block from the second skill.
        Assert.That(player.Creature, Has.Block(10));
    }
}
