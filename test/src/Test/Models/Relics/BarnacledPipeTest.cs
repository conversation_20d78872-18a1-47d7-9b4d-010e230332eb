using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BarnacledPipeTest : ModelTest
{
    [Test]
    public async Task TestIncreaseBlockForCards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BarnacledPipe>(player);
        await CreatureCmd.GainBlock(player.Creature, 5, BlockProps.card, null);

        // 5 + 1 (barnacled pipe)
        Assert.That(GetPlayer().Creature, Has.Block(6));
    }

    [Test]
    public async Task TestIncreaseBlockForNonCards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BarnacledPipe>(player);
        await CreatureCmd.GainBlock(player.Creature, 5, BlockProps.nonCardUnpowered, null);

        // 5 + 1 (barnacled pipe)
        Assert.That(GetPlayer().<PERSON><PERSON><PERSON>, <PERSON>.Block(6));
    }
}
