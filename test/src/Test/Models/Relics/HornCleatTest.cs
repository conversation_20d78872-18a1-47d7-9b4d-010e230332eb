using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class HornCleatTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingOnRound1()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<HornCleat>(player);
        await RestartCombat();

        Assert.That(player.Creature, Has.Block(0));
    }

    [Test]
    public async Task TestAddsBlockOnRound2()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<HornCleat>(player);
        await PassToNextPlayerTurn();

        Assert.That(player.Creature, <PERSON>.Block(14));
    }
}