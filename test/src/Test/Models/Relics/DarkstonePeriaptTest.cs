using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class DarkstonePeriaptTest : ModelTest
{
    [Test]
    public async Task TestGainMaxHpWhenCurseIsObtained()
    {
        Player player = GetPlayer();
        int startingMaxHp = player.Creature.MaxHp;

        await RelicCmd.Obtain<DarkstonePeriapt>(player);

        CardModel card = MockCurse(CardScope.Climb);
        await CardPileCmd.Add(card, CardPileTarget.Deck);

        Assert.That(player.Creature.MaxHp, Is.EqualTo(startingMaxHp + 6));
    }

    [Test]
    public async Task TestDoesNothingForNonCurse()
    {
        Player player = GetPlayer();
        int startingMaxHp = player.Creature.MaxHp;

        await RelicCmd.Obtain<DarkstonePeriapt>(player);

        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, CardPileTarget.Deck);

        Assert.That(player.Creature.MaxHp, Is.EqualTo(startingMaxHp));
    }
}
