using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PlanisphereTest : ModelTest
{
    [Test]
    public async Task TestHealsInUnknownMapPoints()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<Planisphere>(player);
        await CreatureCmd.Damage(player.Creature, 5, DamageProps.nonCardHpLoss, null, null);

        await ClimbManager.Instance.EnterMapCoordDebug(
            new MapCoord(),
            RoomType.Monster,
            pointType: MapPointType.Unknown,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );

        // Lost 5, then healed 2 from entering an unknown map point.
        Assert.That(player.Creature, Has.LostHp(2));
    }

    [Test]
    public async Task TestDoesNotHealInKnownMapPoints()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<Planisphere>(player);
        await CreatureCmd.Damage(player.Creature, 5, DamageProps.nonCardHpLoss, null, null);

        await ClimbManager.Instance.EnterMapCoordDebug(
            new MapCoord(),
            RoomType.Monster,
            pointType: MapPointType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );

        // Lost 5, did not heal any because point is known.
        Assert.That(player.Creature, Has.LostHp(5));
    }
}
