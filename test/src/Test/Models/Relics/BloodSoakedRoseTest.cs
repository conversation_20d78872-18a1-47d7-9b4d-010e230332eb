using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BloodSoakedRoseTest : ModelTest
{
    [Test]
    public async Task TestHas1ExtraEnergyOnTurn1()
    {
        Player player = GetPlayer();
        player.MaxEnergy = 3;

        await RelicCmd.Obtain<BloodSoakedRose>(player);
        await RestartCombat();

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestHas1ExtraEnergyOnTurn2()
    {
        Player player = GetPlayer();
        player.MaxEnergy = 3;

        await RelicCmd.Obtain<BloodSoakedRose>(player);
        await RestartCombat();

        await PassToNextPlayerTurn();

        Assert.That(player, Has.ExtraEnergy(1));
    }
}