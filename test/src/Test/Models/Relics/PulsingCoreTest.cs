using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PulsingCoreTest : ModelTest
{
    [Test]
    public async Task TestDrawsExtraCardsOnTurn3()
    {
        await RelicCmd.Obtain<PulsingCore>(GetPlayer());

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(MockSkillCard), 7).ToArray()));
    }

    [Test]
    public async Task TestDrawsExtraCardsOnTurn6()
    {
        await RelicCmd.Obtain<PulsingCore>(GetPlayer());

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();

        for (int i = 0; i < 5; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(MockSkillCard), 7).ToArray()));
    }

    [Test]
    public async Task TestDoesNothingOnTurn2()
    {
        await RelicCmd.Obtain<PulsingCore>(GetPlayer());

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();

        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(MockSkillCard), 5).ToArray()));
    }

    [Test]
    public async Task TestDoesNothingOnTurn4()
    {
        await RelicCmd.Obtain<PulsingCore>(GetPlayer());

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(MockSkillCard), 5).ToArray()));
    }
}
