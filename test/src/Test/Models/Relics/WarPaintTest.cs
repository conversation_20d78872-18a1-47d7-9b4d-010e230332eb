using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class WarPaintTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingToNonSkills()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), deck);
        }

        await RelicCmd.Obtain<WarPaint>(GetPlayer());

        Assert.That(deck.Cards, Has.None.Upgraded());
    }

    [Test]
    public async Task TestUpgrades2Skills()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        }

        await RelicCmd.Obtain<WarPaint>(GetPlayer());

        Assert.That(deck.Cards, Has.Exactly(2).Upgraded());
    }

    [Test]
    public async Task TestSkipUpgradedSkills()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        await CardPileCmd.Add(Upgrade(MockSkill(CardScope.Climb)), deck);

        await RelicCmd.Obtain<WarPaint>(GetPlayer());

        Assert.That(deck.Cards, Has.Exactly(2).Upgraded());
    }
}
