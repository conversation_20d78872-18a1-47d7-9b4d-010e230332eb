using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class TezcatarasCandleTest : ModelTest
{
    [Test]
    public async Task TestAddsEnergyOnTurn1OfCombat()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TezcatarasCandle>(player);
        await RestartCombat();

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestAddsEnergyOnTurn2OfCombat()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<TezcatarasCandle>(player);
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(player, Has.ExtraEnergy(1));
    }
}