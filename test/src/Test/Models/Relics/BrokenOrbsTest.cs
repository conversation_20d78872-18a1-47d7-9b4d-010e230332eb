using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BrokenOrbsTest : ModelTest
{
    [Test]
    public async Task TestAddsEnergyOnTurn1()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BrokenOrbs>(player);
        await RestartCombat();

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestDoesNothingOnTurn2()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BrokenOrbs>(player);
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(player, Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestsAddsEnergyOnCombat3()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BrokenOrbs>(player);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
        }

        Assert.That(player, Has.ExtraEnergy(1));
    }


    [Test]
    public async Task TestDoesNothingOnCombat4()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BrokenOrbs>(player);

        for (int i = 0; i < 4; i++)
        {
            await RestartCombat();
        }

        Assert.That(player, Has.ExtraEnergy(0));
    }
}
