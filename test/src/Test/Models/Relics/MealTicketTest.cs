using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class MealTicketTest : ModelTest
{
    [Test]
    public async Task TestHealIfEnterShop()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();

        Creature player = GetPlayer().Creature;
        await CreatureCmd.SetCurrentHp(player, 1);
        await RelicCmd.Obtain<MealTicket>(GetPlayer());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);

        Assert.That(player.CurrentHp, Is.EqualTo(16));
    }

    [Test]
    public async Task TestDontHealIfEnterOtherRooms()
    {
        Creature player = GetPlayer().Creature;
        await CreatureCmd.SetCurrentHp(player, 1);
        await RelicCmd.Obtain<MealTicket>(GetPlayer());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);

        Assert.That(player.CurrentHp, Is.EqualTo(1));
    }
}
