using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class EctoplasmTest : ModelTest
{
    [Test]
    public async Task TestIncreaseMaxEnergy()
    {
        Player player = GetPlayer();
        player.MaxEnergy = 3;

        await RelicCmd.Obtain<Ectoplasm>(player);
        await RestartCombat();

        Assert.That(player.PlayerCombatState!.MaxEnergy, Is.EqualTo(4));
    }

    [Test]
    public async Task TestBlockAddGoldAfterObtain()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<Ectoplasm>(player);
        await PlayerCmd.GainGold(1000, player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(0));
    }
}
