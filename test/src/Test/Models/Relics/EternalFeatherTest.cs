using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class EternalFeatherTest : ModelTest
{
    [Test]
    public async Task TestHealWhenEnteringRestSite()
    {
        await FillDeck(25);
        Creature player = GetPlayer().Creature;

        // curHealth = 1
        await CreatureCmd.Damage(player, 99, DamageProps.nonCardHpLoss, null, null);
        await RelicCmd.Obtain<EternalFeather>(GetPlayer());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.RestSite);

        // 1 (curHealth) + 25 / 5 * 3 = 16
        Assert.That(player.CurrentHp, Is.EqualTo(16));
    }

    [Test]
    public async Task TestNoHealIfNoCards()
    {
        Creature player = GetPlayer().Creature;
        await CreatureCmd.Damage(player, 99, DamageProps.nonCardHpLoss, null, null);
        await RelicCmd.Obtain<EternalFeather>(GetPlayer());
        await ClimbManager.Instance.EnterRoomDebug(RoomType.RestSite);

        // 1 (curHealth) + 0 / 5 * 3 = 1
        Assert.That(player.CurrentHp, Is.EqualTo(1));
    }


    [Test]
    public async Task TestNoHealWhenEnteringOtherRooms()
    {
        await FillDeck(25);
        Creature player = GetPlayer().Creature;
        await CreatureCmd.Damage(player, 99, DamageProps.nonCardHpLoss, null, null);
        await RelicCmd.Obtain<EternalFeather>(GetPlayer());

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);
        Assert.That(player.CurrentHp, Is.EqualTo(1));
    }

    private async Task FillDeck(int amount)
    {
        CardPile drawPile = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < amount; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), drawPile);
        }
    }
}
