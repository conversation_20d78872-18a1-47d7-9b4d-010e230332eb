using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class AmethystBangleTest : ModelTest
{
    [Test]
    public async Task TestModifiesCardRewardCardPool()
    {
        await RelicCmd.Obtain<AmethystBangle>(GetPlayer());
        IEnumerable<CardModel> cards = [ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()];
        cards = HookBus.Instance.ModifyCardRewardCardPool(GetPlayer(), cards, CardCreationSource.RegularEncounter);

        Assert.That(cards.Count(c => c.Pool is ColorlessCardPool), Is.EqualTo(ModelDb.CardPool<ColorlessCardPool>().Cards.Count()));
    }

    [Test]
    public async Task TestDoesNotDoubleUpColorless()
    {
        await RelicCmd.Obtain<AmethystBangle>(GetPlayer());

        // cards includes colorless card ultimate strike
        IEnumerable<CardModel> cards = [ModelDb.Card<UltimateStrike>(), ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()];
        cards = HookBus.Instance.ModifyCardRewardCardPool(GetPlayer(), cards, CardCreationSource.RegularEncounter);

        // shouldn't double count ultimate strike
        Assert.That(cards.Count(c => c.Pool is ColorlessCardPool), Is.EqualTo(ModelDb.CardPool<ColorlessCardPool>().Cards.Count()));
    }

    [Test]
    public async Task TestDoesNotModifyCustomRewards()
    {
        await RelicCmd.Obtain<AmethystBangle>(GetPlayer());

        IEnumerable<CardModel> cards = [ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()];
        cards = HookBus.Instance.ModifyCardRewardCardPool(GetPlayer(), cards, CardCreationSource.Custom);

        Assert.That(cards, Is.EquivalentTo((IEnumerable<CardModel>)[ModelDb.Card<Inflame>(), ModelDb.Card<Stomp>()]));
    }
}
