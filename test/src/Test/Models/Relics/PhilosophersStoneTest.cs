using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PhilosophersStoneTest : ModelTest
{
    [Test]
    public async Task TestGainsEnergyAndGivesEnemyStrength()
    {
        await RelicCmd.Obtain<PhilosophersStone>(GetPlayer());
        await RestartCombat();

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
        Assert.That(GetEnemy(), Has.PowerAmount<Strength>(1));
    }
}
