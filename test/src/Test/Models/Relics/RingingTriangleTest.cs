using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class RingingTriangleTest : ModelTest
{
    [Test]
    public async Task TestStopsHandFromDiscarding()
    {
        await RelicCmd.Obtain<RingingTriangle>(GetPlayer());
        await RestartCombat();

        
        CardPile draw = GetPile(CardPileTarget.Draw);
        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), draw);
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), hand);
        }
        
        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(StrikeSilent), 10).ToArray()));

        // No Strikes should be left in the draw pile, since 5 were added and 5 should have been drawn
        Assert.That(GetPile(CardPileTarget.Draw).Cards, Is.Empty);
    }
    
    [Test]
    public async Task TestHandDiscardsNextTurn()
    {
        await RelicCmd.Obtain<RingingTriangle>(GetPlayer());
        await RestartCombat();


        CardPile draw = GetPile(CardPileTarget.Draw);
        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), draw);
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), hand);
        }

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(Enumerable.Repeat(typeof(StrikeSilent), 5).ToArray()));

        // Draw pile should have been reshuffled
        Assert.That(GetPile(CardPileTarget.Draw).Cards, Has.Exactly(5).Items);
    }
}