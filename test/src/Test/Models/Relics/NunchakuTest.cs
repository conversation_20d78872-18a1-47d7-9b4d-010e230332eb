using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class NunchakuTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingAfterAttack9()
    {
        await RelicCmd.Obtain<Nunchaku>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<StrikeIronclad>(GetEnemy());
        }

        Assert.That(GetPlayer(), Has.SpentEnergy(9));
    }

    [Test]
    public async Task TestAddsEnergyAfterAttack10()
    {
        await RelicCmd.Obtain<Nunchaku>(GetPlayer());

        for (int i = 0; i < 10; i++)
        {
            await Play<StrikeIronclad>(GetEnemy());
        }

        // Spent 10, gained 1.
        Assert.That(GetPlayer(), Has.SpentEnergy(9));
    }

    [Test]
    public async Task TestCountPersistsBetweenCombats()
    {
        await RelicCmd.Obtain<Nunchaku>(GetPlayer());

        // Play 9 attacks.
        for (int i = 0; i < 9; i++)
        {
            await Play<StrikeIronclad>(GetEnemy());
        }

        // Restart combat, then play a 10th attack.
        await RestartCombat();
        await Play<StrikeIronclad>(GetEnemy());

        // 10th attack gains 1 energy.
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
