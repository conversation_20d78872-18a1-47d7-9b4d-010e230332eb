using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PocketwatchTest : ModelTest
{
    [Test]
    public async Task TestTurn1()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetPlayer());

        // Add some cards to draw
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();
        Assert.AreEqual(GetPile(CardPileTarget.Hand).Cards.Count, 5);
    }

    [Test]
    public async Task TestCardDrawOnThreePlays()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetPlayer());

        // Add some cards to draw
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockAttack(), CardPileTarget.Draw);
        }

        Creature enemy = GetEnemy();

        // Play three cards
        for (int i = 0; i < 3; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        // Pass to next turn, we should draw three extra cards
        await PassToNextPlayerTurn();

        Assert.AreEqual(GetPile(CardPileTarget.Hand).Cards.Count, 8);
    }

    [Test]
    public async Task TestNoCardDrawOnMoreThanThreePlays()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetPlayer());

        // Add some cards to draw
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockAttack(), CardPileTarget.Draw);
        }

        Creature enemy = GetEnemy();

        // Play four cards
        for (int i = 0; i < 4; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        // Pass to next turn, we should draw no extra cards
        await PassToNextPlayerTurn();

        Assert.AreEqual(GetPile(CardPileTarget.Hand).Cards.Count, 5);
    }

    [Test]
    public async Task TestHellraiserCardsDoNotCountThisTurn()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetPlayer());
        await Play<Hellraiser>();

        // Add some Strikes to draw.
        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        }

        // Add some non-Strike cards to draw.
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockSkill(), CardPileTarget.Draw);
        }

        // Pass to next turn. Hellraiser will play the 3 Strikes.
        await PassToNextPlayerTurn();

        // Draw 8 cards - the 3 Hellraisers that were played.
        Assert.AreEqual(GetPile(CardPileTarget.Hand).Cards.Count, 5);
    }

    [Test]
    public async Task TestHellraiserCardsCountNextTurn()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetPlayer());
        await Play<Hellraiser>();

        // Add some Strikes to draw.
        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        }

        // Add some non-Strike cards to draw.
        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(MockSkill(), CardPileTarget.Draw);
        }

        // Pass to next turn. Hellraiser will play the 3 Strikes.
        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        // Because those 3 Strikes were played last turn, Pocketwatch shouldn't trigger this turn.
        Assert.AreEqual(GetPile(CardPileTarget.Hand).Cards.Count, 5);
    }
}
