using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BuriedSeedTest : ModelTest
{
    [Test]
    public async Task TestAddsEnergyOnCombat1()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BuriedSeed>(player);
        await RestartCombat();

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestAddsEnergyOnCombat3()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BuriedSeed>(player);

        for (int i = 0; i < 3; i++)
        {
            await RestartCombat();
        }

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestDoesNothingOnCombat8()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BuriedSeed>(player);

        for (int i = 0; i < 8; i++)
        {
            await RestartCombat();
        }

        Assert.That(player, Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestDoesNothingOnTurn2OfCombat()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<BuriedSeed>(player);
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(player, Has.ExtraEnergy(0));
    }
}
