using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BookOfFiveRingsTest : ModelTest
{
    [Test]
    public async Task TestDoesNotTriggerAfterAdding4Cards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BookOfFiveRings>(player);
        await CreatureCmd.Damage(player.Creature, 30, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        Assert.That(player.Creature, Has.LostHp(30));
    }

    [Test]
    public async Task TestTriggersAfterAdding5Cards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BookOfFiveRings>(player);
        await CreatureCmd.Damage(player.Creature, 30, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        // 30 - 15
        Assert.That(player.Creature, Has.LostHp(15));
    }

    [Test]
    public async Task TestTriggersOnceAfterAdding9Cards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BookOfFiveRings>(player);
        await CreatureCmd.Damage(player.Creature, 30, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 9; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        // 30 - 15
        Assert.That(player.Creature, Has.LostHp(15));
    }

    [Test]
    public async Task TestTriggersTwiceAfterAdding10Cards()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BookOfFiveRings>(player);
        await CreatureCmd.Damage(player.Creature, 30, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        // 30 - 30
        Assert.That(player.Creature, Has.LostHp(0));
    }

    [Test]
    public async Task TestDoesNotTriggerAfterAddingCardsToOtherPile()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BookOfFiveRings>(player);
        await CreatureCmd.Damage(player.Creature, 30, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(), CardPileTarget.Draw);
        }

        Assert.That(player.Creature, Has.LostHp(30));
    }
}
