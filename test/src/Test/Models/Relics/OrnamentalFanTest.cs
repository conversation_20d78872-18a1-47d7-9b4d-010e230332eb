using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class OrnamentalFanTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingWhenPlayingNonAttacks()
    {
        await RelicCmd.Obtain<OrnamentalFan>(GetPlayer());

        for (int i = 0; i < 3; i++)
        {
            await Play<Inflame>();
        }

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestAddsBlockWhenPlaying3AttacksIn1Turn()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<OrnamentalFan>(GetPlayer());

        for (int i = 0; i < 3; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestAddsBlockMultipleTimes()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<OrnamentalFan>(GetPlayer());

        for (int i = 0; i < 6; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        Assert.That(GetPlayer().Creature, Has.Block(8));
    }

    [Test]
    public async Task TestDoesNothingWhenPlaying3AttacksOverMultipleTurns()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<OrnamentalFan>(GetPlayer());

        await Play<StrikeIronclad>(enemy);
        await Play<StrikeIronclad>(enemy);

        await PassToNextPlayerTurn();

        await Play<StrikeIronclad>(enemy);

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestInteractsWithInitialDrawAutoplays()
    {
        // Auto-plays on draws that occur before the player's turn officially starts should proc Ornamental Fan
        await RelicCmd.Obtain<OrnamentalFan>(GetPlayer());

        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await Play<Hellraiser>();

        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.Block(4));
    }
}
