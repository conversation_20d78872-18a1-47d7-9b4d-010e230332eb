using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BigMushroomTest : ModelTest
{
    [Test]
    public async Task TestDrawsFewerCardsOnTurn1()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        }

        await RelicCmd.Obtain<BigMushroom>(GetPlayer());
        await RestartCombat();

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(3));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task TestDoesNothingOnTurn2()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        }

        await RelicCmd.Obtain<BigMushroom>(GetPlayer());
        await RestartCombat();
        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(5));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count, Is.EqualTo(0));
    }
}
