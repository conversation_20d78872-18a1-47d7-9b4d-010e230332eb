using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class OrnateReliquaryTest : ModelTest
{
    [Test]
    public async Task TestHealWhenDoomTriggers()
    {
        Player player = GetPlayer();
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<OrnateReliquary>(player);
        await CreatureCmd.Damage(player.Creature, 8, DamageProps.nonCardUnpowered, GetEnemy(), null);

        await PowerCmd.Apply<Doom>(enemy, enemy.CurrentHp, null, null);
        await PassToNextPlayerTurn();
        Assert.That(enemy, Is.Dead());

        Assert.That(player.Creature, Has.LostHp(2));
    }

    [Test]
    public async Task TestDoesntHealOffOfMinions()
    {
        Player player = GetPlayer();
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Minion>(enemy, 1, null, null);

        await RelicCmd.Obtain<OrnateReliquary>(player);
        await CreatureCmd.Damage(player.Creature, 8, DamageProps.nonCardUnpowered, GetEnemy(), null);

        await PowerCmd.Apply<Doom>(enemy, enemy.CurrentHp, null, null);
        await PassToNextPlayerTurn();
        Assert.That(enemy, Is.Dead());

        Assert.That(player.Creature, Has.LostHp(8));
    }


    [Test]
    public async Task TestHealOffOfEndOfDays()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<OrnateReliquary>(player);
        await CreatureCmd.Damage(player.Creature, 8, DamageProps.nonCardUnpowered, GetEnemy(), null);

        Creature enemy = GetEnemy();
        await CreatureCmd.SetMaxHp(enemy, EndOfDays.doomAmount - 1);
        await Play<EndOfDays>();
        Assert.That(enemy, Is.Dead());

        Assert.That(player.Creature, Has.LostHp(2));
    }
}
