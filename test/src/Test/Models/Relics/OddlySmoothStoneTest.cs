using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class OddlySmoothStoneTest : ModelTest
{
    [Test]
    public async Task TestAddsDexterity()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<OddlySmoothStone>(player);
        await RestartCombat();

        Assert.That(player.Creature, Has.PowerAmount<Dexterity>(1));
    }
}