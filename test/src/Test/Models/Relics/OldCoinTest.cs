using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class OldCoinTest : ModelTest
{
    [Test]
    public async Task TestAddsGold()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<OldCoin>(player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(300));
    }
}