using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class SealOfGoldTest : ModelTest
{
    [Test]
    public async Task TestAddsGold()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<SealOfGold>(player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(777));
    }
    
    [Test]
    public async Task TestBlockAddGoldAfterObtain()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<SealOfGold>(player);
        await PlayerCmd.GainGold(1000, player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(777));
    }
    
    [Test]
    public async Task TestLoseGoldAfterObtain()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<SealOfGold>(player);
        await PlayerCmd.LoseGold(10, player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(767));
    }
}