using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class PerpetualCoinTest : ModelTest
{
    [Test]
    public async Task TestAddsGold()
    {
        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<PerpetualCoin>(player);

        Assert.That(player.Gold - originalGold, Is.EqualTo(999));
    }

    [Test]
    public async Task TestPreventsShopsInUnknownMapPoints()
    {
        await RelicCmd.Obtain<PerpetualCoin>(GetPlayer());

        HashSet<RoomType> seenRoomTypes = [];

        for (int i = 0; i < 100; i++)
        {
            seenRoomTypes.Add(GetPlayer().ClimbState.Odds.UnknownMapPoint.Roll([]));
        }

        Assert.That(seenRoomTypes, Does.Not.Contain(RoomType.Shop));
    }

    [Test]
    public async Task TestAllowsShopsInUnknownMapPointsAfterRemoval()
    {
        PerpetualCoin relic = await RelicCmd.Obtain<PerpetualCoin>(GetPlayer());
        await RelicCmd.Remove(relic);

        HashSet<RoomType> seenRoomTypes = [];

        // Note: if this fails intermittently, it's probably safe to increase to 1000.
        for (int i = 0; i < 100; i++)
        {
            seenRoomTypes.Add(GetPlayer().ClimbState.Odds.UnknownMapPoint.Roll([]));
        }

        Assert.That(seenRoomTypes, Does.Contain(RoomType.Shop));
    }
}
