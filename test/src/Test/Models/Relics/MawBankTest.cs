using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class MawBankTest : ModelTest
{
    [Test]
    public async Task TestGainGoldWhenEnteringANewRoom()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();

        Player player = GetPlayer();
        int originalGold = player.Gold;

        await RelicCmd.Obtain<MawBank>(player);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);

        Assert.That(player.Gold - originalGold, Is.EqualTo(24));
    }

    [Test]
    public async Task TestNoGoldGainedAfterPurchase()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();

        Player player = GetPlayer();
        await RelicCmd.Obtain<MawBank>(player);

        await PlayerCmd.GainGold(1000, GetPlayer());
        MerchantPotionEntry potionEntry = new(GetPlayer());
        await potionEntry.OnTryPurchaseWrapper(null);

        int originalGold = player.Gold;

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);

        Assert.That(player.Gold - originalGold, Is.EqualTo(0));
    }
}
