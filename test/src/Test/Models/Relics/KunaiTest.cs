using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class KunaiTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingWhenPlayingNonAttacks()
    {
        await RelicCmd.Obtain<Kunai>(GetPlayer());

        for (int i = 0; i < 3; i++)
        {
            await Play<Inflame>();
        }

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(0));
    }

    [Test]
    public async Task TestAddsDexterityWhenPlaying3AttacksIn1Turn()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<Kunai>(GetPlayer());

        for (int i = 0; i < 3; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(1));
    }

    [Test]
    public async Task TestAddsDexterityMultipleTimes()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<Kunai>(GetPlayer());

        for (int i = 0; i < 6; i++)
        {
            await Play<StrikeIronclad>(enemy);
        }

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestDoesNothingWhenPlaying3AttacksOverMultipleTurns()
    {
        Creature enemy = GetEnemy();

        await RelicCmd.Obtain<Kunai>(GetPlayer());

        await Play<StrikeIronclad>(enemy);
        await Play<StrikeIronclad>(enemy);

        await PassToNextPlayerTurn();

        await Play<StrikeIronclad>(enemy);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(0));
    }

    [Test]
    public async Task TestInteractsWithInitialDrawAutoplays()
    {
        // Auto-plays on draws that occur before the player's turn officially starts should proc Kunai
        await RelicCmd.Obtain<Kunai>(GetPlayer());

        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        await Play<Hellraiser>();

        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(1));
    }
}
