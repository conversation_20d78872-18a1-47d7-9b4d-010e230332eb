using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class AncientTeaSetTest : ModelTest
{
    [Test]
    public async Task TestExtraEnergyAfterEnterRestSite()
    {
        Player player = GetPlayer();
        player.MaxEnergy = 3;

        await RelicCmd.Obtain<AncientTeaSet>(player);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.RestSite);
        await RestartCombat();

        Assert.That(player.PlayerCombatState!.Energy, Is.EqualTo(5));
    }

    [Test]
    public async Task TestNoExtraEnergyAfterEnteringOtherRooms()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();

        Player player = GetPlayer();
        player.MaxEnergy = 3;

        await RelicCmd.Obtain<AncientTeaSet>(player);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);
        await RestartCombat();

        Assert.That(player.PlayerCombatState!.Energy, Is.EqualTo(3));
    }
}
