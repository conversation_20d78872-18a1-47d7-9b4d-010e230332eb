using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class ReptileTrinketTest : ModelTest
{
    [Test]
    public async Task TestGrantsTemporaryStrengthOnPotionUse()
    {
        Player player = GetPlayer();

        await RelicCmd.Obtain<ReptileTrinket>(player);
        await UsePotion<RadiantTincture>();

        Assert.That(player.Creature, Has.PowerAmount<Strength>(3));
        Assert.That(player.Creature, Has.PowerAmount<StrengthDown>(3));
    }
}
