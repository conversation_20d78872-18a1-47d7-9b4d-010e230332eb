using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class SneckoEyeTest : ModelTest
{
    [Test]
    public async Task TestDrawsExtraCardsAndModifiesDrawnCardCost()
    {
        Player player = GetPlayer();
        SneckoEye relic = await RelicCmd.Obtain<SneckoEye>(player);
        relic.SetTestEnergyCostOverride(3);

        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(MockSkill(), CardPileTarget.Draw);
        }

        await PassToNextPlayerTurn();

        IReadOnlyList<CardModel> hand = GetPile(CardPileTarget.Hand).Cards;
        Assert.That(hand, Has.Count.EqualTo(7));
        Assert.That(hand, Has.All.EnergyCost(3));
    }

    [Test]
    public async Task TestWithRoyalPoisonAndCentennialPuzzle()
    {
        Player player = GetPlayer();
        SneckoEye relic = await RelicCmd.Obtain<SneckoEye>(player);
        relic.SetTestEnergyCostOverride(3);

        await RelicCmd.Obtain<RoyalPoison>(player);
        await RelicCmd.Obtain<CentennialPuzzle>(player);

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), CardPileTarget.Deck);
        }

        await RestartCombat();

        IReadOnlyList<CardModel> hand = GetPile(CardPileTarget.Hand).Cards;

        // 5 base, 2 from Snecko Eye, 3 from Royal Poison (triggering via Centennial Puzzle)
        Assert.That(hand, Has.Count.EqualTo(10));

        // All cards should have an energy cost of 3 due to Snecko Eye. If some cards don't, it's probably because they
        // were drawn before Snecko Eye applied Confusion to the player.
        Assert.That(hand, Has.All.EnergyCost(3));
    }
}
