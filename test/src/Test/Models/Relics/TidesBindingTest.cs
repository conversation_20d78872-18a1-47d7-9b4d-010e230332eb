using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class TidesBindingTest : ModelTest
{
    [Test]
    public async Task TestBlocksHeals()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await RelicCmd.Obtain<TidesBinding>(player);
        await CreatureCmd.Damage(playerCreature, 8, DamageProps.nonCardHpLoss, null, null);
        await CreatureCmd.Heal(playerCreature, 10);
        Assert.That(playerCreature, Has.LostHp(8));
    }

    [Test]
    public async Task TestDisabledAfter3Wins()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await RelicCmd.Obtain<TidesBinding>(player);
        await CreatureCmd.Damage(playerCreature, 8, DamageProps.nonCardHpLoss, null, null);

        for (int i = 0; i < 3; i++)
        {
            await WinCombat();
        }

        await CreatureCmd.Heal(playerCreature, 10);
        Assert.That(playerCreature, Has.LostHp(0));
    }

    [Test]
    public async Task TestInteractionWithFairyInABottle()
    {
        Player player = GetPlayer();
        Creature playerCreature = player.Creature;

        await PotionCmd.TryToProcure<FairyInABottle>(player);
        await RelicCmd.Obtain<TidesBinding>(player);
        await CreatureCmd.Damage(playerCreature, 999, DamageProps.nonCardHpLoss, null, null);

        Assert.That(playerCreature.IsDead, Is.True);
        Assert.That(CombatManager.Instance.IsInProgress, Is.False);
    }
}
