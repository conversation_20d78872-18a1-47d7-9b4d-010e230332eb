using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BlackStarTest : ModelTest
{
    [Test]
    public async Task TestAddsExtraRelicForEliteCombats()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BlackStar>(player);
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(
            player,
            new CombatRoom(GetEncounter(RoomType.Elite), player.ClimbState)
        );

        Assert.That(rewards, Has.Exactly(2).InstanceOf<RelicReward>());
    }

    [Test]
    public async Task TestsDoesNothingForNonEliteCombats()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BlackStar>(player);
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(
            player,
            new CombatRoom(GetEncounter(RoomType.Monster), player.ClimbState)
        );

        Assert.That(rewards, Has.Exactly(0).InstanceOf<RelicReward>());
    }
}
