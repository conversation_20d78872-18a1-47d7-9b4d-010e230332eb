using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class KusarigamaTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingOnSkill9()
    {
        await RelicCmd.Obtain<Kusarigama>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<Production>();
        }

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestDoesNothingOnCard10PlayedWhenOther9WereNotAllAttacks()
    {
        await RelicCmd.Obtain<Kusarigama>(GetPlayer());

        for (int i = 0; i < 9; i++)
        {
            await Play<StrikeIronclad>(GetEnemy());
        }

        await Play<Production>();

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestGivesBlockOnSkill10()
    {
        await RelicCmd.Obtain<Kusarigama>(GetPlayer());

        for (int i = 0; i < 10; i++)
        {
            await Play<Production>();
        }

        Assert.That(GetPlayer().Creature, Has.Block(7));
    }

    [Test]
    public async Task TestGivesBlockOnSkill10AfterCombatRestarted()
    {
        await RelicCmd.Obtain<Kusarigama>(GetPlayer());

        for (int i = 0; i < 6; i++)
        {
            await Play<Production>();
        }

        await RestartCombat();

        for (int i = 0; i < 4; i++)
        {
            await Play<Production>();
        }

        Assert.That(GetPlayer().Creature, Has.Block(7));
    }
}
