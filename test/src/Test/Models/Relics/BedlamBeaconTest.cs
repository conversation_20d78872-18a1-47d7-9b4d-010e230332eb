using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BedlamBeaconTest : ModelTest
{
    [Test]
    public async Task TestDamage()
    {
        await RelicCmd.Obtain<BedlamBeacon>(GetPlayer());
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(5));
    }
}
