using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Relics;

public class BagOfMarblesTest : ModelTest
{
    [Test]
    public async Task TestAddsVulnerable()
    {
        await RelicCmd.Obtain<BagOfMarbles>(GetPlayer());
        await RestartCombat();

        Assert.That(GetEnemy(), Has.PowerAmount<Vulnerable>(1));
    }
}
