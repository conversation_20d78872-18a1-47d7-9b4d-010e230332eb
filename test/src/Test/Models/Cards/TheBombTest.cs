using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Combat.History.Entries;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TheBombTest : ModelTest
{
    [Test]
    public async Task TestDoesNoDamage2TurnsAfterPlaying()
    {
        await Play<TheBomb>();

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetEnemy(), Has.LostHp(0));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<TheBombPower>(1));
    }

    [Test]
    public async Task TestDoesDamage3TurnsAfterPlaying()
    {
        await Play<TheBomb>();

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetEnemy(), Has.LostHp(40));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<TheBombPower>(0));
    }

    [Test]
    public async Task TestKeepsInstancesSeparate()
    {
        // Play the first instance of The Bomb.
        await Play<TheBomb>();

        // Pass the turn.
        await PassToNextPlayerTurn();

        // Play the second instance of the Bomb.
        await Play<TheBomb>();

        // Pass 2 more turns.
        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        // Now that 3 turns have passed since The Bomb Instance 1 was played, the enemy should've taken 40 damage.
        // If instancing wasn't working properly, this might be 0 or 80.
        Assert.That(GetEnemy(), Has.LostHp(40));

        // Now that 3 turns have passed since The Bomb Instance 1 was played, the player should have one instance of
        // The Bomb left with Amount=1.
        // Note: this is a little hacky, as PowerAmount assumes non-instanced powers (since 99% of powers are
        // non-instanced). If we run into trouble with this test, we should come up with a more robust solution.
        Assert.That(GetPlayer().Creature, Has.PowerAmount<TheBombPower>(1));
    }

    [Test]
    public async Task TestDamageIsTreatedAsOurs()
    {
        await Play<TheBomb>();

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        int ourDamage = CombatManager.Instance.History.Entries
            .OfType<DamageReceivedEntry>()
            .Where(e => e.Dealer == GetPlayer().Creature)
            .Sum(e => e.Result.UnblockedDamage);

        Assert.That(ourDamage, Is.EqualTo(40));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        await PlayUpgraded<TheBomb>();

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetEnemy(), Has.LostHp(50));
    }
}