using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FavoriteStickTest : ModelTest
{
    [Test]
    public async Task TestRetrievesFromDrawPile()
    {
        Creature enemy = GetEnemy();

        await CardPileCmd.Add(CreateCard<FavoriteStick>(), CardPileTarget.Draw);
        await Play<Bodyguard>(enemy);

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(FavoriteStick)));
        Assert.That(GetPile(CardPileTarget.Draw).Cards, Is.Empty);
    }
}
