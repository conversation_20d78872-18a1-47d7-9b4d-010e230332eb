using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AllForOneTest : ModelTest
{
    [Test]
    public async Task TestGetZeroCostCards()
    {
        await CardPileCmd.Add(CreateCard<Claw>(), CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<GoForTheEyes>(), CardPileTarget.Discard);
        await CardPileCmd.Add(Upgrade(CreateCard<Claw>()), CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<PerfectedStrike>(), CardPileTarget.Discard);

        await Play<AllForOne>(GetEnemy());

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(Claw), typeof(GoForTheEyes), typeof(Claw)));
        Assert.That(GetPile(CardPileTarget.Discard), Has.Cards(typeof(PerfectedStrike), typeof(AllForOne)));
    }

    [Test]
    public async Task TestNoZeroCostCards()
    {
        await CardPileCmd.Add(CreateCard<Barricade>(), CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<Normality>(), CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<Wound>(), CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<PerfectedStrike>(), CardPileTarget.Discard);

        await Play<AllForOne>(GetEnemy());

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(0));
        Assert.That(GetPile(CardPileTarget.Discard).Cards.Count, Is.EqualTo(5));
    }
}
