using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SeekingEdgeTest : ModelTest
{
    [Test]
    public async Task TestAttackAll()
    {
        await CreateEnemy<BigDummy>();

        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardPileCmd.Add(Upgrade(CreateCard<SovereignBlade>()), hand);

        await Play<SeekingEdge>();
        await Play(hand.Cards[0]);

        IReadOnlyList<Creature> enemies = GetEnemies();
        Assert.That(enemies[0], <PERSON>.LostHp(12));
        Assert.That(enemies[1], <PERSON><PERSON>Hp(12));
    }
}
