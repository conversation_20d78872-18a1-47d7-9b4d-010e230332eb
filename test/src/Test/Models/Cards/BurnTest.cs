using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BurnTest : ModelTest
{
    [Test]
    public void TestUnplayable()
    {
        Assert.That(async () => await Play<Burn>(), Throws.TypeOf<TestCardPlayException>());
    }

    [Test]
    public async Task TestDamageAtEndOfTurn()
    {
        await CardPileCmd.Add(CreateCard<Burn>(), CardPileTarget.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.LostHp(2));
    }

    [Test]
    public async Task TestWhenItHasEthereal()
    {
        CardModel card = CreateCard<Burn>();
        CardCmd.ApplyKeyword(card, CardKeyword.Ethereal);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Exhaust), Has.Cards(typeof(Burn)));
    }
}
