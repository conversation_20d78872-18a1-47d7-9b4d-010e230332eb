using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DefragmentTest : ModelTest
{
    [Test]
    public async Task TestBaseFocus()
    {
        await Play<Defragment>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Focus>(1));
    }

    [Test]
    public async Task TestUpgradedFocus()
    {
        await PlayUpgraded<Defragment>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Focus>(2));
    }
}