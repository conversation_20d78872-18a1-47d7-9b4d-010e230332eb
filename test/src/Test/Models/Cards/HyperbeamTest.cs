using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HyperbeamTest : ModelTest
{
    [Test]
    public async Task TestFocusLossAndDamage()
    {
        await Play<Hyperbeam>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Focus>(-3));
        Assert.That(GetEnemy(), <PERSON>.LostHp(30));
    }

    [Test]
    public async Task TestUpgradeDamage()
    {
        await PlayUpgraded<Hyperbeam>();
        Assert.That(Get<PERSON>nemy(), <PERSON>.LostHp(40));
    }
}
