using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ArtificialWinterTest : ModelTest
{
    [Test]
    public async Task TestAddOrb()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<ArtificialWinter>();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(orbQueue.Orbs, Is.All.TypeOf<FrostOrb>());
    }

    [Test]
    public async Task TestAddFrozenToHand()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        await Play<ArtificialWinter>();
        Assert.That(hand.Cards, Has.All.Affliction(ModelDb.Affliction<Frozen>()));
    }

    [Test]
    public async Task TestStacking()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        await Play<ArtificialWinter>();
        await Play<ArtificialWinter>();

        Assert.That(hand.Cards[0].Affliction!.Amount, Is.EqualTo(2));
    }
}
