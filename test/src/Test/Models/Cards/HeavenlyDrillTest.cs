using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HeavenlyDrillTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        await PlayerCmd.GainStars(5, GetPlayer());
        await Play<HeavenlyDrill>(GetEnemy());

        Assert.That(GetEnemy(), <PERSON>.LostHp(0));
    }
    
    
    [Test]
    public async Task TestSpentStars()
    {
        await PlayerCmd.GainStars(9, GetPlayer());
        await Play<DyingStar>();

        await Play<HeavenlyDrill>(GetEnemy());

        // 8 * 3 + Dying Star 9
        Assert.That(GetEnemy(), Has.LostHp(33));
    }


    [Test]
    public async Task TestSpentStarsBetweenTurns()
    {
        await PlayerCmd.GainStars(9, GetPlayer());
        await Play<DyingStar>();
        await PassToNextPlayerTurn();
        await Play<DyingStar>();

        await Play<HeavenlyDrill>(GetEnemy());

        // 8 * 3 + Dying Stars(9 + 9)
        Assert.That(GetEnemy(), Has.Lost<PERSON>p(42));
    }
}