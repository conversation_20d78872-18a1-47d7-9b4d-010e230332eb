using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DevourLifeTest : ModelTest
{
    [Test]
    public async Task TestBasePowers()
    {
        Creature player = GetPlayer().Creature;

        await Play<DevourLife>();

        Assert.That(player, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestPowersWithSoulsInHand()
    {
        Creature player = GetPlayer().Creature;

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<Soul>(), CardPileTarget.Hand);
        }

        await Play<DevourLife>();

        Assert.That(player, <PERSON><PERSON><Strength>(6));
        Assert.That(GetPile(CardPileTarget.Exhaust), <PERSON><PERSON>(typeof(Soul), typeof(Soul), typeof(Soul)));
    }

    [Test]
    public async Task TestPowersWithNonSoulsInHand()
    {
        Creature player = GetPlayer().Creature;

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendNecrobinder>(), CardPileTarget.Hand);
        }

        await Play<DevourLife>();

        Assert.That(player, Has.PowerAmount<Strength>(0));
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(DefendNecrobinder), typeof(DefendNecrobinder), typeof(DefendNecrobinder)));
    }

    [Test]
    public async Task TestPowersWithSoulsInOtherPiles()
    {
        Creature player = GetPlayer().Creature;

        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<Soul>(), CardPileTarget.Draw);
        }

        await Play<DevourLife>();

        Assert.That(player, Has.PowerAmount<Strength>(6));
    }
}