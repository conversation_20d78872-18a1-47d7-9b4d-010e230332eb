using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SentryModeTest : ModelTest
{
    [Test]
    public async Task TestAddsSweepingGazeNextTurn()
    {
        await Play<SentryMode>();
        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(SweepingGaze)));
    }

    [Test]
    public async Task TestTriggersBeforeHandDraw()
    {
        CardPile deck = GetPile(CardPileTarget.Deck);

        for (int i = 0; i < 11; i++)
        {
            await CardPileCmd.Add(MockSkill(CardScope.Climb), deck);
        }

        await RelicCmd.Obtain<RingOfTheDrake>(GetPlayer());
        await RelicCmd.Obtain<ZlatirsCape>(GetPlayer());
        await RestartCombat();

        // Discard a card to make room in hand for card to be played.
        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), hand.Cards[0]);
        await Play<SentryMode>();

        await PassToNextPlayerTurn();

        Assert.That(hand.Cards.Count(c => c is MockSkillCard), Is.EqualTo(9));
        Assert.That(hand.Cards.Count(c => c is SweepingGaze), Is.EqualTo(1));
    }
}
