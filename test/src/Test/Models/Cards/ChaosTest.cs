using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ChaosTest : ModelTest
{
    [Test]
    public async Task TestAddOrb()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Chaos>();
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestUpgrade()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await PlayUpgraded<Chaos>();
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(2));
    }
}
