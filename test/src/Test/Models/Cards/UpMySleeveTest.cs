using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class UpMySleeveTest : ModelTest
{
    [Test]
    public async Task TestBaseCost()
    {
        await Play<UpMySleeve>();
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestEnergyAfterPlayingTwice()
    {
        CardModel card = CreateCard<UpMySleeve>();

        for (int i = 0; i < 2; i++)
        {
            await Play(card);
        }

        // First play: cost 2
        // Second play: cost 1
        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public async Task TestEnergyAfterPlayingThreeTimes()
    {
        CardModel card = CreateCard<UpMySleeve>();

        for (int i = 0; i < 3; i++)
        {
            await Play(card);
        }

        // First play: cost 2
        // Second play: cost 1
        // Third play: cost 0
        Assert.That(GetPlayer(), <PERSON><PERSON>nergy(3));
    }
}
