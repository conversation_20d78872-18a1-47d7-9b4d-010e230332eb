using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class AutomationTest : ModelTest
{
    [Test]
    public async Task TestInstancedPower()
    {
        await Play<Automation>();
        await Play<Automation>();
        // we go to next turn so that we reset the cost of automation
        await PassToNextPlayerTurn();

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }
        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), 10, GetPlayer());
        Assert.That(GetPlayer(), Has.ExtraEnergy(2));
    }

    [Test]
    public async Task TestAfterDrawing9Cards()
    {
        await Play<Automation>();
        // we go to next turn so that we reset the cost of automation
        await PassToNextPlayerTurn();

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }

        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), 9, GetPlayer());
        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestAfterDrawing10Cards()
    {
        await Play<Automation>();
        // we go to next turn so that we reset the cost of automation
        await PassToNextPlayerTurn();

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }

        await CardPileCmd.Draw(new ThrowingPlayerChoiceContext(), 10, GetPlayer());
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }


    [Test]
    public async Task TestOffsetStacking()
    {
        await Play<Automation>();

        for (int i = 0; i < 20; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }

        // we go to next turn so that we reset the cost of automation
        await PassToNextPlayerTurn();
        await Play<Automation>();
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }
}
