using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ExecutionTest : ModelTest
{
    [Test]
    public async Task TestWithNoShivs()
    {
        await Play<Execution>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(11));
    }

    [Test]
    public async Task TestWith5Shivs()
    {
        CardPile exhaustPile = GetPile(CardPileTarget.Exhaust);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<Shiv>(), exhaustPile);
        }
        
        await Play<Execution>(GetEnemy());
        
        // 11 + 4 * 5
        Assert.That(GetEnemy(), Has.LostHp(31));
        Assert.That(GetPile(CardPileTarget.Exhaust), Has.Cards(typeof(Shiv), typeof(Shiv), typeof(Shiv), typeof(Shiv), typeof(Shiv), typeof(Execution)));
    }
    
    [Test]
    public async Task TestUpgrade()
    {
        CardPile exhaustPile = GetPile(CardPileTarget.Exhaust);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<Shiv>(), exhaustPile);
        }
        
        await PlayUpgraded<Execution>(GetEnemy());
        
        // 13 + 2 * 6
        Assert.That(GetEnemy(), Has.LostHp(25));
    }
}