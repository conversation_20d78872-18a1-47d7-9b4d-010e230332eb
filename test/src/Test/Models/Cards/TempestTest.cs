using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TempestTest : ModelTest
{
    [Test]
    public async Task TestAddsOrb()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await PlayerCmd.SetEnergy(3, player);

        await Play<Tempest>();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task TestUpgrade()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 4);
        await PlayerCmd.SetEnergy(3, player);

        await PlayUpgraded<Tempest>();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(4));
    }

    [Test]
    public async Task TestCausesOrbEvoke()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Zap>();

        await PlayerCmd.SetEnergy(3, player);
        await Play<Tempest>();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestNoOrbSlots()
    {
        await Play<Tempest>();

        Assert.That(GetPlayer().PlayerCombatState!.OrbQueue.Orbs.Count, Is.Zero);
        Assert.That(GetEnemy(), Has.LostHp(0));
    }
}
