using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ImperviousTest : ModelTest
{
    [Test]
    public async Task TestBaseBlockAndExhaust()
    {
        await Play<Impervious>();

        Assert.That(GetPlayer().Creature, Has.Block(30));
        Assert.That(GetPile(CardPileTarget.Exhaust).Cards.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestUpgradedBlock()
    {
        await PlayUpgraded<Impervious>();

        Assert.That(GetPlayer().Creature, Has.Block(40));
    }
}
