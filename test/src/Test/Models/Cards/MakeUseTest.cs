using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class MakeUseTest : ModelTest
{
    [Test]
    public async Task TestNoSummonWithoutFatal()
    {
        await Play<MakeUse>(GetEnemy());
        Assert.That(GetPlayer().IsOstyPresent, Is.False);
    }

    [Test]
    public async Task TestSummonWithFatal()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();
        await Play<MakeUse>(enemy);

        Assert.That(GetPlayer().IsOstyPresent, Is.True);
    }
}
