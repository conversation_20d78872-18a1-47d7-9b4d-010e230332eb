using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DiscoveryTest : ModelTest
{
    [Test]
    public async Task TestAddsA0CostCardToHand()
    {
        PrepareToSelectAtIndices(0);
        await Play<Discovery>();

        Assert.That(GetPile(CardPileTarget.Hand).Cards[0], Has.EnergyCostFree());
        Assert.That(GetPile(CardPileTarget.Exhaust), <PERSON>.Cards(typeof(Discovery)));
    }

    [Test]
    public async Task TestUpgradedDoesNotExhaust()
    {
        PrepareToSelectAtIndices(0);
        await PlayUpgraded<Discovery>();

        Assert.That(GetPile(CardPileTarget.Discard), <PERSON><PERSON>Card<PERSON>(typeof(Discovery)));
    }
}
