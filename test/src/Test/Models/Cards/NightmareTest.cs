using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class NightmareTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyHand()
    {
        await Play<Nightmare>();
        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand).Cards, Is.Empty);
    }

    [Test]
    public async Task TestWith1CardHand()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), hand);

        await Play<Nightmare>();
        await PassToNextPlayerTurn();


        // Original cards + 3 copies of Defend
        Assert.That(hand, Has.Cards(typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent)));
    }

    [Test]
    public async Task TestWithFullHandNextTurn()
    {
        await CardPileCmd.Add(CreateCard<Survivor>(), GetPile(CardPileTarget.Hand));

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await PowerCmd.Apply<DrawCardsNextTurn>(GetPlayer().Creature, 5, GetPlayer().Creature, null);

        await Play<Nightmare>();
        await PassToNextPlayerTurn();

        // the nightmare cards should spawn in first before the survivor cards
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(Survivor), typeof(Survivor), typeof(Survivor), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent), typeof(DefendSilent)));
        Assert.That(GetPile(CardPileTarget.Discard), Has.Cards(typeof(Survivor)));
    }

    [Test]
    public async Task TestWith2CardHand()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        CardModel card = CreateCard<StrikeSilent>();
        await CardPileCmd.Add(card, hand);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), hand);

        PrepareToSelect(card);
        await Play<Nightmare>();
        await PassToNextPlayerTurn();

        // Original cards + 3 copies of Strike
        Assert.That(hand, Has.Cards(typeof(DefendSilent), typeof(StrikeSilent), typeof(StrikeSilent), typeof(StrikeSilent), typeof(StrikeSilent)));
    }

    [Test]
    public async Task TestWithFullHand()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        CardModel card = CreateCard<StrikeSilent>();
        await CardPileCmd.Add(card, hand);

        await Play<Nightmare>();
        await Play<Nightmare>();
        await Play<Nightmare>();
        await Play<Nightmare>();

        await PassToNextPlayerTurn();

        // 10 of the cloned cards
        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(10));

        // 2 cloned cards + original strike
        Assert.That(GetPile(CardPileTarget.Discard).Cards.Count, Is.EqualTo(3));
    }

    [Test]
    public async Task TestBaseEnergyCost()
    {
        await Play<Nightmare>();
        Assert.That(GetPlayer(), Has.SpentEnergy(3));
    }

    [Test]
    public async Task TestUpgradedEnergyCost()
    {
        await PlayUpgraded<Nightmare>();
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestCopyAtSelection()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        CardModel defend = CreateCard<DefendSilent>();
        await CardPileCmd.Add(defend, hand);

        await Play<Nightmare>();
        CardCmd.ApplyKeyword(defend, CardKeyword.Sly);
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), defend); // get the defend out of the way
        await PassToNextPlayerTurn();


        // Original cards + 3 copies of Defend
        Assert.That(hand.Cards, Has.None.Keyword(CardKeyword.Sly));
    }
}
