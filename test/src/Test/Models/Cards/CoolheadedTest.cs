using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CoolheadedTest : ModelTest
{
    [Test]
    public async Task TestBaseOrbAndCardDraw()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        }

        await Play<Coolheaded>();

        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs[0], Is.TypeOf<FrostOrb>());
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestUpgradeCardDraw()
    {
        await OrbCmd.AddSlots(GetPlayer(), 3);
        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), CardPileTarget.Draw);
        }

        await PlayUpgraded<Coolheaded>();
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad)));
    }
}
