using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RadiateTest : ModelTest
{
    [Test]
    public async Task TestNoStars()
    {
        await Play<Radiate>();

        Assert.That(GetEnemy(), Has.LostHp(0));
    }

    [Test]
    public async Task TestWith1Star()
    {
        await PlayerCmd.GainStars(1, GetPlayer());
        await Play<Radiate>();

        Assert.That(GetEnemy(), Has.LostHp(3));
    }

    [Test]
    public async Task TestWithManyStars()
    {
        await PlayerCmd.GainStars(12, GetPlayer());
        await Play<Radiate>();

        // 3 * 12
        Assert.That(GetEnemy(), Has.LostHp(36));
    }
}