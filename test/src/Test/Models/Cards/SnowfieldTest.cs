using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SnowfieldTest : ModelTest
{
    [Test]
    public async Task TestGainBlockWhenChannelingFrostOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Snowfield>();
        await OrbCmd.Channel<FrostOrb>(new NullPlayerChoiceContext(), player);

        Assert.That(GetPlayer().Creature, Has.Block(4));
    }

    [Test]
    public async Task TestGainBlockWhenChannelingNonFrostOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Snowfield>();
        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await OrbCmd.Channel<ScrapOrb>(new NullPlayerChoiceContext(), player);

        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestStacking()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Snowfield>();
        await Play<Snowfield>();

        await OrbCmd.Channel<FrostOrb>(new NullPlayerChoiceContext(), player);

        Assert.That(GetPlayer().Creature, Has.Block(8));
    }
}
