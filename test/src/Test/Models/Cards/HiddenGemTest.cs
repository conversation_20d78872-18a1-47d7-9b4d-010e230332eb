using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class HiddenGemTest : ModelTest
{
    [Test]
    public async Task TestShuffle()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Hand));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await Play<HiddenGem>();

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(5));
        Assert.That(GetPile(CardPileTarget.Discard).Cards.Count, Is.EqualTo(1));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count, Is.EqualTo(10));
    }

    [Test]
    public async Task TestReplayGain()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await Play<HiddenGem>();
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count(c => c.ReplayCount == 2), Is.EqualTo(1));
    }

    [Test]
    public async Task TestUpgradedReplayGain()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await PlayUpgraded<HiddenGem>();
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count(c => c.ReplayCount == 2), Is.EqualTo(0));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count(c => c.ReplayCount == 3), Is.EqualTo(1));
    }

    [Test]
    public async Task TestDoesntWorkOnStatusesOrCurses()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<Regret>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<Slimed>(), GetPile(CardPileTarget.Draw));
        }

        await Play<HiddenGem>();
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count(c => c.ReplayCount == 2), Is.EqualTo(0));
    }
}
