using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Powers.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EnvenomTest : ModelTest
{
    [Test]
    public async Task Test1UnblockedAttack()
    {
        Creature enemy = GetEnemy();

        await Play<Envenom>();

        await CreatureCmd.Damage(enemy, 1, DamageProps.card, GetPlayer().Creature, null);
        Assert.That(enemy, Has.PowerAmount<Poison>(1));
    }

    [Test]
    public async Task Test2UnblockedAttacks()
    {
        Creature enemy = GetEnemy();

        await Play<Envenom>();

        for (int i = 0; i < 2; i++)
        {
            await CreatureCmd.Damage(enemy, 1, DamageProps.card, GetPlayer().Creature, null);
        }

        Assert.That(enemy, Has.PowerAmount<Poison>(2));
    }

    [Test]
    public async Task TestNoPoisonFromBlockedAttack()
    {
        Creature enemy = GetEnemy();

        await CreatureCmd.GainBlock(enemy, 10, BlockProps.monsterMove, null);

        await Play<Envenom>();
        await CreatureCmd.Damage(enemy, 1, DamageProps.card, GetPlayer().Creature, null);
        Assert.That(enemy, Has.PowerAmount<Poison>(0));
    }

    [Test]
    public async Task TestNoPoisonFromNonAttackDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Envenom>();
        await CreatureCmd.Damage(enemy, 1, DamageProps.cardUnpowered, GetPlayer().Creature, null);
        Assert.That(enemy, Has.PowerAmount<Poison>(0));
    }

    [Test]
    public async Task TestWithPreventDeathPower()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();
        await PowerCmd.Apply<MockPreventDeathPower>(enemy, 1, null, null);

        await Play<Envenom>();
        await CreatureCmd.Damage(enemy, 1, DamageProps.card, GetPlayer().Creature, null);

        Assert.That(enemy, Has.PowerAmount<Poison>(1));
    }

    [Test]
    public async Task TestTriggersOnPoisonEffects()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<SneckoSkull>(player);

        Creature enemy = GetEnemy();

        await Play<Envenom>();
        await CreatureCmd.Damage(enemy, 1, DamageProps.card, player.Creature, null);

        // 1 from Envenom, 1 from Snecko Skull.
        Assert.That(enemy, Has.PowerAmount<Poison>(2));
    }
}
