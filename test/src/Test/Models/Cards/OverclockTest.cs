using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class OverclockTest : ModelTest
{
    [Test]
    public async Task TestDrawAndStatus()
    {
        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }


        await Play<Overclock>();
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad)));
        Assert.That(GetPile(CardPileTarget.Discard), Has.Cards(typeof(Burn), typeof(Overclock)));
    }

    [Test]
    public async Task TestUpgradedDraw()
    {
        for (int i = 0; i < 4; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), GetPile(CardPileTarget.Draw));
        }

        await PlayUpgraded<Overclock>();
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(StrikeIronclad), typeof(StrikeIronclad), typeof(StrikeIronclad)));
    }
}
