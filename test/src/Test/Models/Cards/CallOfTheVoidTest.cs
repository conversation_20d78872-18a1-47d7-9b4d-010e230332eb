using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CallOfTheVoidTest : ModelTest
{
    [Test]
    public async Task TestCreatesEtherealCard()
    {
        AddToMockCardPool(MockSkill().MockKeyword(CardKeyword.Ethereal));

        await Play<CallOfTheVoid>();
        await PassToNextPlayerTurn();

        CardPile hand = GetPile(CardPileTarget.Hand);
        Assert.That(hand.Cards.Count, Is.EqualTo(1));
        Assert.That(hand.Cards[0], <PERSON><PERSON>Keyword(CardKeyword.Ethereal));
    }

    [Test]
    public async Task TestStacks()
    {
        AddToMockCardPool(MockSkill().MockKeyword(CardKeyword.Ethereal));

        for (int i = 0; i < 2; i++)
        {
            await Play<CallOfTheVoid>();
        }

        await PassToNextPlayerTurn();

        CardPile hand = GetPile(CardPileTarget.Hand);
        Assert.That(hand.Cards.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task TestTriggersBeforeHandDraw()
    {
        AddToMockCardPool(MockSkill().MockKeyword(CardKeyword.Ethereal));

        CardPile deck = GetPile(CardPileTarget.Deck);
        Player player = GetPlayer();

        for (int i = 0; i < 11; i++)
        {
            await CardPileCmd.Add(MockAttack(CardScope.Climb), deck);
        }

        // These 3 relics should cause the player to draw 6 extra cards (11 total, floored to 10 due to max hand size).
        for (int i = 0; i < 3; i++)
        {
            await RelicCmd.Obtain<ZlatirsCape>(player);
        }

        await RestartCombat();

        // Discard a card to make room in hand for card to be played.
        // After this, the player should have 8 cards in hand.
        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), hand.Cards[0]);

        await Play<CallOfTheVoid>();

        await PassToNextPlayerTurn();

        // Call of the Void should generate its Ethereal card first, then we should try to draw 11 more cards (see
        // above) but we should only be allowed to draw 9 more due to max hand size.
        // So, our hand should contain 9 MockAttackCards and 1 Ethereal card.
        Assert.That(hand.Cards.Count(c => c is MockAttackCard), Is.EqualTo(9));
        Assert.That(hand.Cards.Count(c => c.Keywords.Contains(CardKeyword.Ethereal)), Is.EqualTo(1));
    }
}
