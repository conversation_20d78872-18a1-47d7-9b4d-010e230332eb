using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SunderTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();
        await Play<Sunder>(enemy);
        Assert.That(enemy, Has.LostHp(24));
    }

    [Test]
    public async Task TestGainEnergyWithFatal()
    {
        Creature enemy = await CreateEnemy<OneHpMonster>();
        await Play<Sunder>(enemy);

        Assert.That(enemy, Is.Dead());
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }


    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();
        await PlayUpgraded<Sunder>(enemy);
        Assert.That(enemy, <PERSON>.<PERSON>Hp(32));
    }
}
