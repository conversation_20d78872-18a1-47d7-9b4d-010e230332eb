using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DarknessTest : ModelTest
{
    [Test]
    public async Task TestAddOrb()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Darkness>();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(1));
        Assert.That(orbQueue.Orbs[0], Is.TypeOf<DarkOrb>());
    }

    [Test]
    public async Task TestUpgraded()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);
        await PlayUpgraded<Darkness>();

        for (int i = 0; i < 2; i++)
        {
            await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);
        }

        Assert.That(GetEnemy(), Has.LostHp(24));
    }

    [Test]
    public async Task TestAddMultipleOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        for (int i = 0; i < 3; i++)
        {
            await Play<Darkness>();
        }

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(3));
        Assert.That(orbQueue.Orbs[0], Is.TypeOf<DarkOrb>());
    }

    [Test]
    public async Task TestNoOrbSlots()
    {
        await Play<Darkness>();
        Assert.That(GetPlayer().PlayerCombatState!.OrbQueue.Orbs.Count, Is.Zero);
    }
}
