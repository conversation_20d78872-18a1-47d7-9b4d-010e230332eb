using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RebootTest : ModelTest
{
    [Test]
    public async Task TestShuffle()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Hand));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await Play<Reboot>();

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(4));
        Assert.That(GetPile(CardPileTarget.Discard).Cards.Count, Is.EqualTo(0));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count, Is.EqualTo(11));
        Assert.That(GetPile(CardPileTarget.Exhaust).Cards.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task TestUpgradeDraw()
    {
        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Hand));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Draw));
        }

        await PlayUpgraded<Reboot>();

        Assert.That(GetPile(CardPileTarget.Hand).Cards.Count, Is.EqualTo(6));
        Assert.That(GetPile(CardPileTarget.Discard).Cards.Count, Is.EqualTo(0));
        Assert.That(GetPile(CardPileTarget.Draw).Cards.Count, Is.EqualTo(9));
        Assert.That(GetPile(CardPileTarget.Exhaust).Cards.Count, Is.EqualTo(1));
    }

    // Shuffle used to have a bug where it wouldn't shuffle the discard and the draw together, it would shuffle only
    // the discard and put it at the bottom of the draw. Ensure that isn't the case
    [Test]
    [Retry(25)]
    public async Task TestShufflesTogetherDrawAndDiscard()
    {
        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), GetPile(CardPileTarget.Hand));
        }

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), GetPile(CardPileTarget.Discard));
        }

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<Neutralize>(), GetPile(CardPileTarget.Draw));
        }

        await PlayUpgraded<Reboot>();

        // Check that the first three cards are of different types (i.e. that the piles have been mixed together).
        // This won't always be the case because of randomness, but it should pass enough times that the retry should get us there.
        // If it ends up failing intermittently, re-think the approach.
        Assert.That(GetPile(CardPileTarget.Hand).Cards[0], Is.Not.TypeOf(GetPile(CardPileTarget.Hand).Cards[1].GetType()));
        Assert.That(GetPile(CardPileTarget.Hand).Cards[1], Is.Not.TypeOf(GetPile(CardPileTarget.Hand).Cards[2].GetType()));
        Assert.That(GetPile(CardPileTarget.Hand).Cards[0], Is.Not.TypeOf(GetPile(CardPileTarget.Hand).Cards[2].GetType()));
    }
}
