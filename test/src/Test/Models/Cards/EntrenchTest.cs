using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EntrenchTest : ModelTest
{
    [Test]
    public async Task TestDoublesYourBlock()
    {
        await CreatureCmd.GainBlock(GetPlayer().Creature, 8, BlockProps.nonCardUnpowered, null);
        await Play<Entrench>();

        Assert.That(GetPlayer().Creature, Has.Block(16));
    }

    [Test]
    public async Task TestDoesNothingIfYouHaveNoBlock()
    {
        await Play<Entrench>();
        Assert.That(GetPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestNotAffectedByFrail()
    {
        await CreatureCmd.GainBlock(GetPlayer().Creature, 8, BlockProps.nonCardUnpowered, null);
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 1, null, null);
        await Play<Entrench>();

        Assert.That(GetPlayer().Creature, Has.Block(16));
    }
}
