using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CatalystTest : ModelTest
{
    [Test]
    public async Task TestDoublingWithNoPoison()
    {
        Creature enemy = GetEnemy();

        await Play<Catalyst>(enemy);

        Assert.That(enemy, Has.PowerAmount<Poison>(0));
    }

    [Test]
    public async Task TestDoublingWithPoison()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Poison>(enemy, 3, null, null);
        await Play<Catalyst>(enemy);

        Assert.That(enemy, Has.PowerAmount<Poison>(6));
    }
}
