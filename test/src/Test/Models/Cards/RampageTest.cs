using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RampageTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<Rampage>(enemy);

        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestDamageAfterPlayingTwice()
    {
        Creature enemy = GetEnemy();
        CardModel card = CreateCard<Rampage>();

        for (int i = 0; i < 2; i++)
        {
            await Play(card, enemy);
        }

        // First time: 9
        // Second time: 14
        Assert.That(enemy, Has.LostHp(23));
    }

    [Test]
    public async Task TestDamageAfterPlayingThreeTimes()
    {
        Creature enemy = GetEnemy();
        CardModel card = CreateCard<Rampage>();

        for (int i = 0; i < 3; i++)
        {
            await Play(card, enemy);
        }

        // First time: 9
        // Second time: 14
        // Third time: 19
        Assert.That(enemy, <PERSON>.LostHp(42));
    }

    [Test]
    public async Task TestUpgradedDamageAfterPlayingTwice()
    {
        Creature enemy = GetEnemy();
        CardModel card = Upgrade(CreateCard<Rampage>());

        for (int i = 0; i < 2; i++)
        {
            await Play(card, enemy);
        }

        // First time: 9
        // Second time: 18
        Assert.That(enemy, Has.LostHp(27));
    }
}
