using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BullyTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageOnCreatureWithoutVulnerable()
    {
        Creature enemy = GetEnemy();

        await Play<Bully>(enemy);

        Assert.That(enemy, Has.LostHp(4));
    }

    [Test]
    public async Task TestDamageIncreaseOnCreatureWithVulnerable()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Vulnerable>(enemy, 1, null, null);
        await Play<Bully>(enemy);

        // (4 + 1 stack of Vulnerable) * 1.5 for Vulnerable = 7.
        Assert.That(enemy, <PERSON>.LostHp(7));
    }

    [Test]
    public async Task TestWith2Vulnerable()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Vulnerable>(enemy, 2, null, null);
        await Play<Bully>(enemy);

        // (4 + 2 stacks of Vulnerable) * 1.5 for Vulnerable = 9.
        Assert.That(enemy, Has.LostHp(9));
    }

    [Test]
    public async Task TestWithVulnerableAndSharp()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Vulnerable>(enemy, 1, null, null);

        CardModel card = CreateCard<Bully>();
        CardCmd.Enchant<Sharp>(card, 2);
        await Play(card, enemy);

        // (4 base + 2 Sharp + 1 stack of Vulnerable) * 1.5 for Vulnerable = 10.
        Assert.That(enemy, Has.LostHp(10));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Vulnerable>(enemy, 3, null, null);
        await PlayUpgraded<Bully>(enemy);

        // (4 + 2 * 3) * 1.5 for Vulnerable = 15.
        Assert.That(enemy, Has.LostHp(15));
    }
}
