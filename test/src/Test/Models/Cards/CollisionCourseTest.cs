using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CollisionCourseTest : ModelTest
{
    [Test]
    public async Task TestGainDebris()
    {
        await Play<CollisionCourse>(GetEnemy());
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(Debris)));
    }
}