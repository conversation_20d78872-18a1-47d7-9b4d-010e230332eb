using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CosmicIndifferenceTest : ModelTest
{
    [Test]
    public async Task TestWithEmptyDiscardPile()
    {
        await Play<CosmicIndifference>();

        Assert.That(GetPile(CardPileTarget.Draw).Cards, Is.Empty);
        Assert.That(GetPile(CardPileTarget.Discard), Has.Cards(typeof(CosmicIndifference)));
    }

    [Test]
    public async Task TestWithSingleCardDiscardPile()
    {
        CardPile discardPile = GetPile(CardPileTarget.Discard);
        await CardPileCmd.Add(CreateCard<StrikeRegent>(), discardPile);

        await Play<CosmicIndifference>();

        Assert.That(GetPile(CardPileTarget.Draw), Has.Cards(typeof(StrikeRegent)));
        Assert.That(discardPile, Has.Cards(typeof(CosmicIndifference)));
    }

    [Test]
    public async Task TestWithMultiCardDiscardPile()
    {
        CardPile discardPile = GetPile(CardPileTarget.Discard);
        CardModel strike = CreateCard<StrikeRegent>();
        await CardPileCmd.Add(strike, discardPile);
        CardModel defend = CreateCard<DefendRegent>();
        await CardPileCmd.Add(defend, discardPile);

        PrepareToSelect(defend);
        await Play<CosmicIndifference>();

        Assert.That(GetPile(CardPileTarget.Draw), Has.Cards(typeof(DefendRegent)));
        Assert.That(discardPile, Has.Cards(typeof(StrikeRegent), typeof(CosmicIndifference)));
    }
}