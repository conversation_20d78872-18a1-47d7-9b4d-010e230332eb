using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SweepingBeamTest : ModelTest
{
    [Test]
    public async Task TestBaseDamageAndCardDraw()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);

        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), drawPile);
        }
        await Play<SweepingBeam>();

        Assert.That(GetEnemy(), Has.LostHp(6));
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(StrikeIronclad)));
    }

    [Test]
    public async Task TestUpgradedDamage()
    {
        await PlayUpgraded<SweepingBeam>();
        Assert.That(GetEnemy(), <PERSON>.LostHp(9));
    }
}
