using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class WaterloggedTest : ModelTest
{
    [Test]
    public async Task TestDamageBlockWhenPlayed()
    {
        await Play<Waterlogged>();
        Assert.That(GetPlayer().Creature, Has.LostHp(0));
    }

    [Test]
    public async Task TestTakeDamageWhenExhaustedViaEthereal()
    {
        await CardPileCmd.Add(CreateCard<Waterlogged>(), CardPileTarget.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }

    [Test]
    public async Task TestGainsBlockWhenExhaustedManually()
    {
        await CardCmd.Exhaust(new ThrowingPlayerChoiceContext(), CreateCard<Waterlogged>());
        Assert.That(GetPlayer().Creature, Has.LostHp(1));
    }
}
