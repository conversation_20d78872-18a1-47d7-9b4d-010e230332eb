using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FootworkTest : ModelTest
{
    [Test]
    public async Task TestBaseDexterity()
    {
        await Play<Footwork>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(2));
    }

    [Test]
    public async Task TestUpgradedDexterity()
    {
        await PlayUpgraded<Footwork>();
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Dexterity>(3));
    }
}