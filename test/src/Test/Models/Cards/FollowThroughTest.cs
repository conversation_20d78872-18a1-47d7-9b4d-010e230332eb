using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class FollowThroughTest : ModelTest
{
    [Test]
    public async Task TestPlayability()
    {
        await Play<MockSkillCard>();
        await Play<FollowThrough>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestBetweenTurns()
    {
        await Play<MockSkillCard>();

        await PassToNextPlayerTurn();

        await Play<FollowThrough>(GetEnemy());
        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestSkillThenAttack()
    {
        Creature enemy = GetEnemy();

        await Play<MockSkillCard>();
        await Play<MockAttackCard>(enemy);
        Assert.That(async () => await Play<FollowThrough>(enemy), Throws.TypeOf<TestCardPlayException>());

        Assert.That(enemy, Has.LostHp(6));
    }

    [Test]
    public async Task TestAttackThenSkill()
    {
        Creature enemy = GetEnemy();

        await Play<MockAttackCard>(enemy);
        await Play<MockSkillCard>();
        await Play<FollowThrough>(enemy);

        Assert.That(enemy, Has.LostHp(18));
    }

    [Test]
    public void TestFirstCard()
    {
        Creature enemy = GetEnemy();
        Assert.That(async () => await Play<FollowThrough>(enemy), Throws.TypeOf<TestCardPlayException>());
    }

    [Test]
    public async Task TestNoSkill()
    {
        Creature enemy = GetEnemy();

        await Play<MockAttackCard>(enemy);

        Assert.That(async () => await Play<FollowThrough>(enemy), Throws.TypeOf<TestCardPlayException>());
    }

    [Test]
    public async Task TestWithDiscardedSlySkill()
    {
        Creature enemy = GetEnemy();
        await CardPileCmd.Add(MockSkill().MockKeyword(CardKeyword.Sly), CardPileTarget.Hand);

        // Playing Dagger Throw should cause the Sly skill in the player's hand to be discarded and played.
        await Play<DaggerThrow>(GetEnemy());

        // The Sly skill in the player's hand that was discarded and played should count as the "last card played", so
        // it should be ok to play Follow Through.
        // If this fails, it's probably an ordering issue where Dagger Throw is being incorrectly counted as the last
        // card played.
        Assert.That(async () => await Play<FollowThrough>(enemy), Throws.Nothing);
    }
}
