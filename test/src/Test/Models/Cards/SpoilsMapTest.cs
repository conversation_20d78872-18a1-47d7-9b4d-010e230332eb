using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Relics;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SpoilsMapTest : ModelTest
{
    [Test]
    public async Task TestMarksExactlyOneMapNodeInNextAct()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        await ClimbManager.Instance.EnterNextAct();
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);

        Assert.That(spoilsMap.SpoilsActIndex, Is.EqualTo(climbState.CurrentActIndex));
        Assert.That(spoilsMap.SpoilsCoord, Is.Not.Null);
        Assert.That(climbState.Map.GetPoint(spoilsMap.SpoilsCoord!.Value)!.QuestCards, Is.EquivalentTo(new CardModel[] { spoilsMap }));
        Assert.That(climbState.Map.GetAllMapPoints().Count(p => p.QuestCards.Contains(spoilsMap)), Is.EqualTo(1));
    }

    [Test]
    public async Task TestMarksNoMapNodesInTwoActs()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        for (int i = 0; i < 2; i++)
        {
            await ClimbManager.Instance.EnterNextAct();
        }

        Assert.That(spoilsMap.SpoilsActIndex, Is.Not.EqualTo(climbState.CurrentActIndex));
        Assert.That(spoilsMap.SpoilsCoord, Is.Not.Null);
        Assert.That(climbState.Map.GetAllMapPoints().Count(p => p.QuestCards.Contains(spoilsMap)), Is.EqualTo(0));
    }

    [Test]
    public async Task TestGeneratesExtraRewardsInMarkedRoom()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        await ClimbManager.Instance.EnterNextAct();
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);
        await ClimbManager.Instance.EnterMapCoord(spoilsMap.SpoilsCoord!.Value);

        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(
            GetPlayer(),
            new CombatRoom(ModelDb.Encounter<MockMonsterEncounter>().ToMutable(), climbState)
        );

        Assert.That(rewards, Has.Exactly(2).InstanceOf<RelicReward>());
        Assert.That(rewards, Has.Exactly(1).Matches<Reward>(r => r is RelicReward { Rarity: RelicRarity.Rare }));
        Assert.That(rewards, Has.Exactly(1).Matches<Reward>(r => r is RelicReward { Rarity: RelicRarity.Uncommon }));

        // Note that these values are x2 on the hardcoded amounts in Reward.GenerateFor
        Assert.That(rewards, Has.Exactly(1).Matches<Reward>(r => r is GoldReward { Amount: >= 20 and <= 40 }));
    }

    [Test]
    public async Task TestRemovesCardWhenEnterRoom()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        await ClimbManager.Instance.EnterNextAct();
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);
        await ClimbManager.Instance.EnterMapCoord(spoilsMap.SpoilsCoord!.Value);

        await RewardsCmd.GenerateForRoom(
            GetPlayer(),
            new CombatRoom(ModelDb.Encounter<MockMonsterEncounter>().ToMutable(), climbState)
        );
        Assert.That(GetPile(CardPileTarget.Deck).Cards, Is.Empty);
    }

    [Test]
    public async Task TestGeneratesNoExtraRewardsInNonMarkedRoom()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        await ClimbManager.Instance.EnterNextAct();
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);
        MapPoint nonMarkedEncounterRoom = climbState.Map
            .GetAllMapPoints()
            .First(p => p.coord != spoilsMap.SpoilsCoord!.Value && p.PointType == MapPointType.Monster);

        await ClimbManager.Instance.EnterMapCoord(nonMarkedEncounterRoom.coord);

        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), climbState.CurrentRoom!);

        Assert.That(rewards, Has.Exactly(0).InstanceOf<RelicReward>());

        // Note that these values are hardcoded amounts in Reward.GenerateFor
        Assert.That(rewards, Has.Exactly(1).Matches<Reward>(r => r is GoldReward { Amount: >= 10 and <= 20 }));
    }

    [Test]
    public async Task TestGeneratesNoExtraRewardsAfterRemoved()
    {
        SpoilsMap spoilsMap = CreateCard<SpoilsMap>(CardScope.Climb);
        IClimbState climbState = spoilsMap.ClimbState!;
        await CardPileCmd.Add(spoilsMap, CardPileTarget.Deck);

        await ClimbManager.Instance.EnterNextAct();
        climbState.Act.GenerateRooms(climbState.Rng.UpFront);
        MapPoint markedTreasurePoint = climbState.Map.GetPoint(spoilsMap.SpoilsCoord!.Value)!;
        await CardPileCmd.RemoveFromDeck(spoilsMap);
        await ClimbManager.Instance.EnterMapCoord(spoilsMap.SpoilsCoord.Value);
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), climbState.CurrentRoom!);

        Assert.That(rewards, Has.Exactly(0).InstanceOf<RelicReward>());

        // Note that these values are hardcoded amounts in Reward.GenerateFor
        Assert.That(rewards, Has.Exactly(1).Matches<Reward>(r => r is GoldReward { Amount: >= 10 and <= 20 }));
        Assert.That(markedTreasurePoint.QuestCards, Is.Empty);
    }
}
