using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ShameTest : ModelTest
{
    [Test]
    public Task TestUnplayable()
    {
        Assert.That(async () => await Play<Shame>(), Throws.TypeOf<TestCardPlayException>());
        return Task.CompletedTask;
    }

    [Test]
    public async Task TestFrailAtEndOfTurnWhenInHand()
    {
        await CardPileCmd.Add(CreateCard<Shame>(), CardPileTarget.Hand);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Frail>(1));
    }

    [Test]
    public async Task TestDoesNothingWhenNotInHand()
    {
        await CardPileCmd.Add(CreateCard<Shame>(), CardPileTarget.Draw);
        await PassToNextPlayerTurn();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Frail>(0));
    }

    [Test]
    public async Task TestFrailIfYouAlreadyHaveIt()
    {
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 1, null, null);
        await PassToNextPlayerTurn();
        await CardPileCmd.Add(CreateCard<Shame>(), CardPileTarget.Hand);
        await PassToNextPlayerTurn();

        // shouldn't block the tick down because frail was already applied on a previous turn
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Frail>(1));
    }
}
