using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class InfernalBladeTest : ModelTest
{
    [Test]
    public async Task TestGeneratedCard()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);

        await Play<InfernalBlade>();

        CardModel card = hand.Cards.Last();

        Assert.That(card, Has.CardType(CardType.Attack));
        Assert.That(card, Has.EnergyCostFree());
        Assert.That(hand.Cards.Count, Is.EqualTo(1));
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestResetCost()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);

        await Play<InfernalBlade>();

        CardModel card = hand.Cards.Last();

        if (card.BaseEnergyCost == 0)
        {
            // we got unlucky and pulled a zero cost card. if so skip test
            return;
        }

        await PassToNextPlayerTurn();

        Assert.That(card, Has.CardType(CardType.Attack));
        Assert.That(card, Has.EnergyCostGreaterThan(0).Or.EnergyCostX());
    }

    [Test]
    public async Task TestUpgradeDecreasesCost()
    {
        await PlayUpgraded<InfernalBlade>();
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
