using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BladeOfInkTest : ModelTest
{
    [Test]
    public async Task TestStrengthFromShivCreation()
    {
        await Play<BladeOfInk>();
        await Play<CloakAndDagger>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(1));
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(Shiv)));
    }

    [Test]
    public async Task TestMultipleShivCreation()
    {
        await Play<BladeOfInk>();
        await Play<BladeDance>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(3));
    }

    [Test]
    public async Task TestStacking()
    {
        for (int i = 0; i < 2; i++)
        {
            await Play<BladeOfInk>();
        }

        await Play<CloakAndDagger>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestWearsOffNextTurn()
    {
        // Play 2 to prove that stacking doesn't increase duration.
        for (int i = 0; i < 2; i++)
        {
            await Play<BladeOfInk>();
        }

        await PassToNextPlayerTurn();

        await Play<CloakAndDagger>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Strength>(0));
    }
}
