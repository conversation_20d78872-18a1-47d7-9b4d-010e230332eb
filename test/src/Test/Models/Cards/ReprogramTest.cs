using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ReprogramTest : ModelTest
{
    [Test]
    public async Task TestBasePowers()
    {
        await Play<Reprogram>();

        Creature player = GetPlayer().Creature;
        Assert.That(player, Has.Power<PERSON>mount<Focus>(-1));
        Assert.That(player, Has.<PERSON>mount<Strength>(1));
        Assert.That(player, Has.PowerAmount<Dexterity>(1));
    }

    [Test]
    public async Task TestUpgradedPowers()
    {
        await PlayUpgraded<Reprogram>();

        Creature player = GetPlayer().Creature;
        Assert.That(player, Has.PowerAmount<Focus>(-2));
        Assert.That(player, Has.PowerAmount<Strength>(2));
        Assert.That(player, <PERSON><PERSON><Dexterity>(2));
    }
}