using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class UnmovableTest : ModelTest
{
    [Test]
    public async Task TestGainBlockThisTurn()
    {
        await Play<Unmovable>();
        await Play<DefendIronclad>();

        // 5 (defend) + 6 (unmovable)
        Assert.That(GetPlayer().Creature, Has.Block(11));
    }

    [Test]
    public async Task TestPlayMultipleBlocks()
    {
        await Play<Unmovable>();
        await Play<DefendIronclad>();
        await Play<DefendIronclad>();

        // 5 (defend) + 5 (defend) + 6 (unmovable)
        Assert.That(GetPlayer().Creature, Has.Block(16));
    }

    [Test]
    public async Task TestPlayBeforeUnmovable()
    {
        await Play<DefendIronclad>();
        await Play<Unmovable>();
        await Play<DefendIronclad>();

        // 5 (defend) + 5 (defend)
        Assert.That(GetPlayer().Creature, <PERSON>.Block(10));
    }

    [Test]
    public async Task TestResetsOnNextTurn()
    {
        await Play<Unmovable>();
        await Play<DefendIronclad>();
        await PassToNextPlayerTurn();
        await Play<DefendIronclad>();

        // 5 (defend) + 6
        Assert.That(GetPlayer().Creature, Has.Block(11));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await PlayUpgraded<Unmovable>();
        await Play<DefendIronclad>();

        // 5 (defend) + 8 (unmovable)
        Assert.That(GetPlayer().Creature, Has.Block(13));
    }
}
