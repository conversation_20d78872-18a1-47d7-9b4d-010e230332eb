using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SpectralTest : ModelTest
{
    [Test]
    public async Task TestClearFrailAndWeak()
    {
        await PowerCmd.Apply<Weak>(GetPlayer().Creature, 10,  null, null);
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 10,  null, null);

        await Play<Spectral>();

        Assert.That(GetPlayer().<PERSON>reature, Has.PowerAmount<Weak>(0));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Frail>(0));
    }
}