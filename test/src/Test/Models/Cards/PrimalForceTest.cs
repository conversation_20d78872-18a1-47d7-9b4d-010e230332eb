using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PrimalForceTest : ModelTest
{
    [Test]
    public async Task TestReplacesAttacksWithGiantRocksInDrawAndDiscardPiles()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);
        CardPile discardPile = GetPile(CardPileTarget.Discard);
        CardPile hand = GetPile(CardPileTarget.Hand);

        await CardPileCmd.Add(CreateCard<StrikeSilent>(), drawPile);
        await CardPileCmd.Add(CreateCard<Bash>(), drawPile);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), drawPile);

        await CardPileCmd.Add(CreateCard<StrikeSilent>(), discardPile);
        await CardPileCmd.Add(CreateCard<Bash>(), discardPile);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), discardPile);

        await CardPileCmd.Add(CreateCard<StrikeSilent>(), hand);
        await CardPileCmd.Add(CreateCard<Bash>(), hand);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), hand);

        await Play<PrimalForce>();

        // Attacks in draw/discard piles should be left alone
        Assert.That(drawPile, Has.Cards(typeof(StrikeSilent), typeof(Bash), typeof(DefendSilent)));
        Assert.That(discardPile, Has.Cards(typeof(StrikeSilent), typeof(Bash), typeof(DefendSilent), typeof(PrimalForce)));

        // Attacks in hand should be giant rocks
        Assert.That(hand, Has.Cards(typeof(GiantRock), typeof(GiantRock), typeof(DefendSilent)));
    }

    [Test]
    public async Task TestReplacesAttacksWithUpgradedGiantRocksWhenUpgraded()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);

        await CardPileCmd.Add(CreateCard<StrikeSilent>(), hand);
        await PlayUpgraded<PrimalForce>();

        Assert.That(hand, Has.Cards(typeof(GiantRock)));
        Assert.That(hand.Cards.First(), Is.Upgraded());
    }
}
