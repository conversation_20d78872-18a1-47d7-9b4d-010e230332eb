using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TearAsunderTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<TearAsunder>(enemy);

        Assert.That(enemy, Has.LostHp(5));
    }

    [Test]
    public async Task TestDamageAfterBeingDamaged()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 2; i++)
        {
            await CreatureCmd.Damage(GetPlayer().Creature, 1, DamageProps.nonCardUnpowered, null, null);
        }

        await Play<TearAsunder>(enemy);

        Assert.That(enemy, Has.LostHp(15));
    }

    [Test]
    public async Task TestUpgradedDamageAfterBeingDamaged()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 2; i++)
        {
            await CreatureCmd.Damage(GetPlayer().Creature, 1, DamageProps.nonCardUnpowered, null, null);
        }

        await PlayUpgraded<TearAsunder>(enemy);

        Assert.That(enemy, Has.LostHp(21));
    }
}
