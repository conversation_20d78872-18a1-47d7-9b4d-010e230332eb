using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SporeMindTest : ModelTest
{
    [Test]
    public async Task TestAfterPlayingPower()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardPileCmd.Add(CreateCard<SporeMind>(), hand);

        await Play<MockPowerCard>();
        await Play<MockPowerCard>();

        // 2 + 2
        Assert.That(GetPlayer().Creature, Has.LostHp(4));
    }

    [Test]
    public async Task TestAfterPlayingNonPower()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        await CardPileCmd.Add(CreateCard<SporeMind>(), hand);

        await Play<MockAttackCard>(GetEnemy());
        Assert.That(GetPlayer().Creature, Has.LostHp(0));
    }

    [Test]
    public async Task TestAfterDiscardingSporeMind()
    {
        CardPile hand = GetPile(CardPileTarget.Hand);
        CardModel sporeMind = CreateCard<SporeMind>();
        await CardPileCmd.Add(sporeMind, hand);

        await Play<MockPowerCard>();
        await CardCmd.Discard(new ThrowingPlayerChoiceContext(), sporeMind);
        await Play<MockPowerCard>();

        Assert.That(GetPlayer().Creature, Has.LostHp(2));
    }
}
