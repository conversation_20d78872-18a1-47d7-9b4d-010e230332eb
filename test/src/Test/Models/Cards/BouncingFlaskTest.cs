using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class BouncingFlaskTest : ModelTest
{
    [Test]
    public async Task TestBasePoison()
    {
        await Play<BouncingFlask>();
        Assert.That(GetEnemy(), Has.PowerAmount<Poison>(9));
    }

    [Test]
    public async Task TestUpgradedPoison()
    {
        await PlayUpgraded<BouncingFlask>();
        Assert.That(GetEnemy(), Has.PowerAmount<Poison>(12));
    }
}