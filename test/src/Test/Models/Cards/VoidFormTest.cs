using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class VoidFormTest : ModelTest
{
    [Test]
    public async Task TestPlaysFirstTwoCardsForFree()
    {
        await Play<VoidForm>();
        await WaitTillPlayerNextTurn();

        for (int i = 0; i < 2; i++)
        {
            await Play<MockAttackCard>(GetEnemy());
        }

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }

    [Test]
    public async Task TestPlaysThirdCardCostsEnergy()
    {
        await Play<VoidForm>();
        await WaitTillPlayerNextTurn();

        for (int i = 0; i < 3; i++)
        {
            await Play<MockAttackCard>(GetEnemy());
        }

        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }

    [Test]
    public async Task TestResetsBetweenTurns()
    {
        await Play<VoidForm>();
        await WaitTillPlayerNextTurn();

        await Play<MockAttackCard>(GetEnemy());
        await Play<MockAttackCard>(GetEnemy());
        await PassToNextPlayerTurn();
        await Play<MockAttackCard>(GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }

    //HACKY... wait till the start of the next turn triggered by void form
    private async Task WaitTillPlayerNextTurn()
    {
        do
        {
            await Task.Delay(100);
        } while (GetCombatState().CurrentSide != CombatSide.Player);
    }

}
