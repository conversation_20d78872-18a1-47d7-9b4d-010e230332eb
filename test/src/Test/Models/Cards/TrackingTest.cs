using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class TrackingTest : ModelTest
{
    [Test]
    public async Task TestAttackDamageWithWeak()
    {
        await Play<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 1, null, null);
        await CreatureCmd.Damage(GetEnemy(), 10, DamageProps.card, GetPlayer().Creature, MockAttack());

        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestAttackDamageWithNoWeak()
    {
        await Play<Tracking>();
        await CreatureCmd.Damage(GetEnemy(), 10, DamageProps.card, GetPlayer().Creature, MockAttack());

        Assert.That(GetEnemy(), Has.LostHp(10));
    }

    [Test]
    public async Task TestPoisonDamageWithWeak()
    {
        await Play<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 2, null, null);
        await PowerCmd.Apply<Poison>(GetEnemy(), 10, null, null);
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(12));
    }

    [Test]
    public async Task TestPoisonDamageWithNoWeak()
    {
        await Play<Tracking>();
        await PowerCmd.Apply<Poison>(GetEnemy(), 10, null, null);
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(10));
    }

    [Test]
    public async Task TestUpgradeAttackDamageWithWeak()
    {
        await PlayUpgraded<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 1, null, null);
        await CreatureCmd.Damage(GetEnemy(), 20, DamageProps.card, GetPlayer().Creature, MockAttack());

        Assert.That(GetEnemy(), Has.LostHp(25));
    }

    [Test]
    public async Task TestUpgradePoisonDamageWithWeak()
    {
        await PlayUpgraded<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 1, null, null);
        await PowerCmd.Apply<Poison>(GetEnemy(), 20, null, null);
        await PassToNextPlayerTurn();
        Assert.That(GetEnemy(), Has.LostHp(25));
    }

    [Test]
    public async Task TestStackingAttackDamage()
    {
        await Play<Tracking>();
        await Play<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 1, null, null);
        await CreatureCmd.Damage(GetEnemy(), 10, DamageProps.card, GetPlayer().Creature, MockAttack());

        Assert.That(GetEnemy(), Has.LostHp(14));
    }


    [Test]
    public async Task TestStackingPoisonDamage()
    {
        await Play<Tracking>();
        await Play<Tracking>();
        await PowerCmd.Apply<Weak>(GetEnemy(), 1, null, null);
        await PowerCmd.Apply<Poison>(GetEnemy(), 10, null, null);
        await PassToNextPlayerTurn();

        Assert.That(GetEnemy(), Has.LostHp(14));
    }
}
