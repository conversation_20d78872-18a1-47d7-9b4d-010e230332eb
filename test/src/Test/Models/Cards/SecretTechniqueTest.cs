using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class SecretTechniqueTest : ModelTest
{
    [Test]
    public async Task TestSelectSkill()
    {
        CardModel strike = CreateCard<StrikeIronclad>();
        CardModel defend = CreateCard<DefendIronclad>();

        await CardPileCmd.Add(strike, CardPileTarget.Draw);
        await CardPileCmd.Add(defend, CardPileTarget.Draw);

        PrepareToSelectAtIndices(0);
        await Play<SecretTechnique>();

        Assert.That(GetPile(CardPileTarget.Draw), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(typeof(DefendIronclad)));
    }
}
