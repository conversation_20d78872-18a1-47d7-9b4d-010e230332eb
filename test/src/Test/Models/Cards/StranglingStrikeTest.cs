using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class StranglingStrikeTest : ModelTest
{
    [Test]
    public async Task TestDamageFromAttacks()
    {
        Creature enemy = GetEnemy();

        await Play<StranglingStrike>(enemy);
        await Play<StrikeSilent>(enemy);

        // 7 (Strangling Strike card) + 8 (Strike + Strangling Strike power)
        Assert.That(enemy, Has.LostHp(15));
    }

    [Test]
    public async Task TestWearsOffNextTurn()
    {
        Creature enemy = GetEnemy();

        await Play<StranglingStrike>(enemy);
        await PassToNextPlayerTurn();
        await Play<StrikeSilent>(enemy);

        // 7 (Strangling Strike card) + 6 (Strike)
        Assert.That(enemy, Has.LostHp(13));
    }

    [Test]
    public async Task TestStacking()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 2; i++)
        {
            await Play<StranglingStrike>(enemy);
        }

        await Play<StrikeSilent>(enemy);

        // 7 (first Strangling Strike card) +
        // 9 (second Strangling Strike card + 1x Strangling Strike power) +
        // 10 (Strike + 2x Strangling Strike power)
        Assert.That(enemy, Has.LostHp(26));
    }
}
