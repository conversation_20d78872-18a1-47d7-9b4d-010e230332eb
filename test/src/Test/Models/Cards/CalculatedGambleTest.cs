using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CalculatedGambleTest : ModelTest
{
    [Test]
    public async Task TestBaseDraws()
    {
        await Play<CalculatedGamble>();

        // After playing Calculated Gamble, your draw pile, hand, and discard pile should still be empty.
        Assert.That(GetPile(CardPileTarget.Draw).Cards, Is.Empty);
        Assert.That(GetPile(CardPileTarget.Hand).Cards, Is.Empty);
        Assert.That(GetPile(CardPileTarget.Discard).Cards, Is.Empty);
        Assert.That(GetPile(CardPileTarget.Exhaust), <PERSON>.Cards(typeof(CalculatedGamble)));
    }

    [Test]
    public async Task TestDrawsWithCardsInDrawPileAndHand()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);
        CardPile discardPile = GetPile(CardPileTarget.Discard);
        CardPile hand = GetPile(CardPileTarget.Hand);

        // Add 3 Strikes to your draw pile
        for (int i = 0; i < 3; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeSilent>(), drawPile);
        }

        // Add 2 Defends to your hand
        for (int i = 0; i < 2; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), hand);
        }

        await Play<CalculatedGamble>();

        // After playing Calculated Gamble:
        //
        // * Your draw pile should have 1 Strike left in it.
        // * Your hand should have 2 Strikes in it (drawn from your draw pile).
        // * Your discard pile should have 2 Defends in it (discarded from your hand).
        Assert.That(drawPile, Has.Cards(typeof(StrikeSilent)));
        Assert.That(hand, Has.Cards(typeof(StrikeSilent), typeof(StrikeSilent)));
        Assert.That(discardPile, Has.Cards(typeof(DefendSilent), typeof(DefendSilent)));
    }

    /// <summary>
    /// This test is for the scenario where multiple cards can be queued up for autoplay, and a later-queued card's
    /// target gets killed by an earlier-queued card.
    /// This test is not specific to Calculated Gamble, but it's one of the few ways to trigger this situation.
    /// </summary>
    [Test]
    public async Task TestDiscardingMultipleSlyAttacksWhenRevivingTargetDies()
    {
        // Set it up so we have a single one-HP enemy.
        Creature oldEnemy = GetEnemy();
        Creature enemy = await CreateEnemy<OneHpMonster>();
        await CreatureCmd.Escape(oldEnemy);

        // Give the one-HP enemy a power that revives it after it dies.
        await PowerCmd.Apply<MockRevivePower>(enemy, 1, null, null);

        // Add targeted attack cards with Sly to your hand.
        for (int i = 0; i < 3; i++)
        {
            CardModel card = CreateCard<StrikeSilent>();
            card.AddKeyword(CardKeyword.Sly);
            await CardPileCmd.Add(card, CardPileTarget.Hand);
        }

        // Fill your draw pile with other cards so you don't just re-draw the attacks.
        for (int i = 0; i < 5; i++)
        {
            CardModel card = CreateCard<DefendSilent>();
            await CardPileCmd.Add(card, CardPileTarget.Draw);
        }

        // Discard your hand. All cards are Sly attacks, so this will kill the enemy and cause them to go into a
        // reviving state.
        await Play<CalculatedGamble>();

        // Only the first card should've gotten played (since the target would be dead-but-reviving for the other 2),
        // but all 3 should end up in the discard pile. The other 2 fizzle out.
        Assert.That(GetPile(CardPileTarget.Discard), Has.Cards(typeof(StrikeSilent), typeof(StrikeSilent), typeof(StrikeSilent)));
    }

    /// <summary>
    /// This test is for the scenario where you have a Sly card in your hand that selects another card in your hand when
    /// played (in this case, Survivor).
    /// It should end up selecting from the cards that Calculated Gamble draws _after_ discarding, not the cards that
    /// are in your hand _before_ discarding.
    /// </summary>
    [Test]
    public async Task TestDiscardingSlyCardThatSelectsCardInHand()
    {
        Survivor slyCard = CreateCard<Survivor>();
        slyCard.AddKeyword(CardKeyword.Sly);
        await CardPileCmd.Add(slyCard, CardPileTarget.Hand);

        CardModel selectedCard = MockSkill();
        await CardPileCmd.Add(selectedCard, CardPileTarget.Draw);

        await Play<CalculatedGamble>();

        // Playing Calculated Gamble should:
        // 1. Discard Sly Survivor (but _not_ trigger its Sly keyword yet).
        // 2. Draw MockSkillCard.
        // 3. Survivor's Sly keyword should be triggered, causing MockSkillCard to be selected and discarded.
        Assert.That(selectedCard, Is.InPile(CardPileTarget.Discard));
    }
}
