using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ReinforcedBodyTest : ModelTest
{
    [Test]
    public async Task TestBaseBlock()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await Play<ReinforcedBody>();
        Assert.That(GetPlayer().<PERSON>reature, Has.Block(21));
    }

    [Test]
    public async Task TestUpgradedBlock()
    {
        await PlayerCmd.SetEnergy(3, GetPlayer());
        await PlayUpgraded<ReinforcedBody>();
        Assert.That(GetPlayer().<PERSON><PERSON>ture, <PERSON>.Block(27));
    }

    [Test]
    public async Task TestBlockWithNoEnergy()
    {
        await PlayerCmd.SetEnergy(0, GetPlayer());
        await Play<ReinforcedBody>();
        Assert.That(GetPlayer().<PERSON><PERSON><PERSON>, <PERSON><PERSON>Block(0));
    }
}
