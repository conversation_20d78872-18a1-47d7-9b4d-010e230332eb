using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class CrushingDepthsTest : ModelTest
{
    [Test]
    public async Task TestNoDamageWhenNoCardsPlayedThisTurn()
    {
        Creature enemy = GetEnemy();
        await Play<CrushingDepths>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestDamageWhen3CardsPlayedThisTurn()
    {
        for (int i = 0; i < 3; i++)
        {
            await Play<DefendRegent>();
        }

        Creature enemy = GetEnemy();
        await Play<CrushingDepths>(enemy);

        // 8 * 3
        Assert.That(enemy, Has.LostHp(24));
    }

    [Test]
    public async Task TestNoDamageWhenCardsPlayedLastTurn()
    {
        await Play<DefendRegent>();
        await PassToNextPlayerTurn();

        Creature enemy = GetEnemy();
        await Play<CrushingDepths>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }
}
