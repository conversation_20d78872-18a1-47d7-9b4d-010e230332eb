using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class GracefulTest : ModelTest
{
    [Test]
    public async Task TestEnergyGain()
    {
        await Play<Graceful>();
        await PassToNextPlayerTurn();

        for (int i = 0; i < 3; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestInstancedPower()
    {
        await Play<Graceful>();
        await Play<Graceful>();
        await PassToNextPlayerTurn();

        for (int i = 0; i < 3; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(2));
    }

    [Test]
    public async Task TestAfterPlaying2Cards()
    {
        await Play<Graceful>();
        await PassToNextPlayerTurn();

        for (int i = 0; i < 2; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestAfterPlaying6Cards()
    {
        await Play<Graceful>();
        await PassToNextPlayerTurn();

        for (int i = 0; i < 6; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }


        Assert.That(GetPlayer(), Has.ExtraEnergy(2));
    }

    [Test]
    public async Task TestAfterPlaying3CardsOver2Turns()
    {
        await Play<Graceful>();
        await PassToNextPlayerTurn();

        for (int i = 0; i < 2; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }

        await PassToNextPlayerTurn();

        await Play(MockSkill().MockEnergyCost(0));

        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }

    [Test]
    public async Task TestAfterPlayingBetween5Cards()
    {
        for (int i = 0; i < 4; i++)
        {
            await Play(MockSkill().MockEnergyCost(0));
        }

        await Play<Graceful>();
        await Play(MockSkill().MockEnergyCost(0));

        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }
}
