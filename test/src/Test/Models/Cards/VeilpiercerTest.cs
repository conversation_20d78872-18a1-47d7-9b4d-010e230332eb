using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class VeilpiercerTest : ModelTest
{
    [Test]
    public async Task TestZeroCostWhenNextCardIsEthereal()
    {
        Creature enemy = GetEnemy();
        await Play<Veilpiercer>(enemy);
        await Play<Fear>(enemy);

        // Veilpiecer: 1
        // Fear: 0 (reduced from 1 by Veilpiecer)
        // Total: 1
        Assert.That(GetPlayer(), Has.SpentEnergy(1));
    }


    [Test]
    public async Task TestNormalCostWhenNextCardIsNotEthereal()
    {
        Creature enemy = GetEnemy();

        await Play<Veilpiercer>(enemy);
        await Play<StrikeIronclad>(enemy);

        // Veilpiecer: 1
        // Strike: 1 (no reduction from Veilpiecer since it's not a ethereal)
        // Total: 2
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestZeroCostWhenPlayingSkillAfterNonSEthereal()
    {
        Creature enemy = GetEnemy();

        await Play<Veilpiercer>(enemy);
        await Play<StrikeIronclad>(enemy);
        await Play<Fear>(enemy);

        // Veilpiecer: 1
        // Strike: 1 (no reduction from Veilpiecer since it's not a ethereal)
        // Fear: 0 (reduced from 1 by Veilpiecer)
        // Total: 2
        Assert.That(GetPlayer(), Has.SpentEnergy(2));
    }

    [Test]
    public async Task TestZeroCostWhenPlayingAnEtherealCardNextTurn()
    {
        await Play<Veilpiercer>(GetEnemy());
        await PassToNextPlayerTurn();
        await Play<Fear>(GetEnemy());

        // Energy resets next turn, and Veilpiercer still reduces Fear's cost to 0.
        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
