using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EquilibriumTest : ModelTest
{
    [Test]
    public async Task TestBaseBlock()
    {
        await Play<Equilibrium>();

        Assert.That(GetPlayer().Creature, Has.Block(13));
    }

    [Test]
    public async Task TestRetainHand()
    {
        await Play<Equilibrium>();

        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        await PassToNextPlayerTurn();

        Assert.That(hand.Cards.Count, Is.EqualTo(10));
    }

    [Test]
    public async Task TestWithExhaustCards()
    {
        await Play<Equilibrium>();

        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<Dazed>(), hand);
        }

        await PassToNextPlayerTurn();

        Assert.That(hand.Cards.Count, Is.EqualTo(1));
        Assert.That(GetPile(CardPileTarget.Exhaust).Cards.Count, Is.EqualTo(10));
    }


    [Test]
    public async Task TestAfterTwoTurns()
    {
        await Play<Equilibrium>();

        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 15; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(), hand);
        }

        await PassToNextPlayerTurn();
        await PassToNextPlayerTurn();

        Assert.That(hand.Cards.Count, Is.EqualTo(5));
    }

    [Test]
    public async Task TestUpgradedBlock()
    {
        await PlayUpgraded<Equilibrium>();

        Assert.That(GetPlayer().Creature, Has.Block(16));
    }
}
