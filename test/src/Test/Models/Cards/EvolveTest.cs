using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class EvolveTest : ModelTest
{
    [Test]
    public async Task TestDrawExtraCardWhenStatusIsDrawn()
    {
        await Play<Evolve>();

        await CardPileCmd.Add(CreateCard<Slimed>(), GetPile(CardPileTarget.Draw));
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(CardPileTarget.Draw));
        }

        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand).Cards, Has.Count.EqualTo(6));
    }

    [Test]
    public async Task TestNoStatus()
    {
        await Play<Evolve>();

        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(CardPileTarget.Draw));
        }

        await PassToNextPlayerTurn();
        Assert.That(GetPile(CardPileTarget.Hand).Cards, Has.Count.EqualTo(5));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await PlayUpgraded<Evolve>();

        await CardPileCmd.Add(CreateCard<Slimed>(), GetPile(CardPileTarget.Draw));
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<MockAttackCard>(), GetPile(CardPileTarget.Draw));
        }

        await PassToNextPlayerTurn();

        Assert.That(GetPile(CardPileTarget.Hand).Cards, Has.Count.EqualTo(7));
    }
}
