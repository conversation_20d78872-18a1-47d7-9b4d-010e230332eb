using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class PreciseCutTest : ModelTest
{
    [Test]
    public async Task TestBaseDamage()
    {
        Creature enemy = GetEnemy();

        await Play<PreciseCut>(enemy);

        Assert.That(enemy, <PERSON>.LostHp(13));
    }

    [Test]
    public async Task TestDamageWith3CardsInHand()
    {
        Creature enemy = GetEnemy();
        await CardPileCmd.Add(CreateCard<DefendSilent>(), CardPileTarget.Hand);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), CardPileTarget.Hand);
        await CardPileCmd.Add(CreateCard<DefendSilent>(), CardPileTarget.Hand);

        await Play<PreciseCut>(enemy);
        Assert.That(enemy, Has.LostHp(7));
    }

    [Test]
    public async Task TestDamageWith6CardsInHand()
    {
        Creature enemy = GetEnemy();

        for (int i = 0; i < 6; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendSilent>(), CardPileTarget.Hand);
        }

        await Play<PreciseCut>(enemy);
        Assert.That(enemy, Has.LostHp(1));
    }
    
    [Test]
    public async Task PlayFromHand()
    {
        Creature enemy = GetEnemy();
        CardModel card = CreateCard<PreciseCut>();
        await CardPileCmd.Add(card, CardPileTarget.Hand);

        await Play(card, enemy);
        Assert.That(enemy, Has.LostHp(13));
    }
}