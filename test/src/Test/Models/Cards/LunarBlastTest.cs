using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class LunarBlastTest : ModelTest
{
    [Test]
    public async Task TestNoDamageWhenNonSkillsPlayedThisTurn()
    {
        await Play<Barricade>();

        Creature enemy = GetEnemy();
        await Play<LunarBlast>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }

    [Test]
    public async Task TestDamageWhenSkillsPlayedThisTurn()
    {
        for (int i = 0; i < 3; i++)
        {
            await Play<DefendRegent>();
        }

        Creature enemy = GetEnemy();
        await Play<LunarBlast>(enemy);

        Assert.That(enemy, Has.LostHp(12));
    }

    [Test]
    public async Task TestNoDamageWhenSkillsPlayedLastTurn()
    {
        await Play<DefendRegent>();
        await PassToNextPlayerTurn();

        Creature enemy = GetEnemy();
        await Play<LunarBlast>(enemy);

        Assert.That(enemy, Has.LostHp(0));
    }
}
