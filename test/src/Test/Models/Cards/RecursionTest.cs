using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Orbs;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class RecursionTest : ModelTest
{
    [Test]
    public async Task TestEvokeOrb()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await Play<Recursion>();

        OrbQueue orbQueue = player.PlayerCombatState!.OrbQueue;
        Assert.That(orbQueue.Orbs.Count, Is.EqualTo(1));
        Assert.That(orbQueue.Orbs[0], Is.TypeOf<LightningOrb>());
        Assert.That(GetEnemy(), Has.LostHp(8));
    }

    [Test]
    public async Task TestDarkOrbRetainsValue()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<DarkOrb>(new NullPlayerChoiceContext(), player);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        await Play<Recursion>();
        await OrbCmd.EvokeNext(new NullPlayerChoiceContext(), player);

        // 24 + 24
        Assert.That(GetEnemy(), Has.LostHp(48));
    }

    [Test]
    public async Task TestNoOrbs()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);
        await Play<Recursion>();

        Assert.That(GetEnemy(), Has.LostHp(0));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.EqualTo(0));
    }
}
