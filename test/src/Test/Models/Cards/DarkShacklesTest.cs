using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DarkShacklesTest : ModelTest
{
    [Test]
    public async Task TestBaseStrengthLossThisTurn()
    {
        Creature enemy = GetEnemy();
        await Play<DarkShackles>(enemy);

        Assert.That(enemy, Has.PowerAmount<Strength>(-9));
        Assert.That(enemy, Has.PowerAmount<RestoreStrength>(9));
    }

    [Test]
    public async Task TestUpgradedStrengthLossThisTurn()
    {
        Creature enemy = GetEnemy();
        await PlayUpgraded<DarkShackles>(enemy);

        Assert.That(enemy, Has.PowerAmount<Strength>(-15));
        Assert.That(enemy, Has.PowerAmount<RestoreStrength>(15));
    }

    [Test]
    public async Task TestRestoresStrengthNextTurn()
    {
        Creature enemy = GetEnemy();
        await Play<DarkShackles>(enemy);
        await PassToNextPlayerTurn();

        Assert.That(enemy, Has.PowerAmount<Strength>(0));
        Assert.That(enemy, Has.PowerAmount<RestoreStrength>(0));
    }

    [Test]
    public async Task TestArtifact()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Artifact>(enemy, 1, enemy, null);

        await Play<DarkShackles>(enemy);

        Assert.That(enemy, Has.PowerAmount<Artifact>(0));
        Assert.That(enemy, Has.PowerAmount<Strength>(0));
        Assert.That(enemy, Has.PowerAmount<RestoreStrength>(0));
    }

    [Test]
    public async Task TestArtifactWithPreexistingStrength()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Artifact>(enemy, 1, enemy, null);
        await PowerCmd.Apply<Strength>(enemy, 10, enemy, null);


        await Play<DarkShackles>(enemy);

        Assert.That(enemy, Has.PowerAmount<Artifact>(0));
        Assert.That(enemy, Has.PowerAmount<Strength>(10));
        Assert.That(enemy, Has.PowerAmount<RestoreStrength>(0));
    }
}
