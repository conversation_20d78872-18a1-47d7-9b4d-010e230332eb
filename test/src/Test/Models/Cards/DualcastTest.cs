using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class DualcastTest : ModelTest
{
    [Test]
    public async Task TestEvokesOrbTwice()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await OrbCmd.Channel<LightningOrb>(new NullPlayerChoiceContext(), player);
        await Play<Dualcast>();

        Assert.That(GetEnemy(), <PERSON>.LostHp(16));
        Assert.That(player.PlayerCombatState!.OrbQueue.Orbs.Count, Is.Zero);
    }

    [Test]
    public async Task TestWithoutOrbs()
    {
        await OrbCmd.AddSlots(GetPlayer(), 3);
        await Play<Dualcast>();

        Assert.That(<PERSON><PERSON><PERSON><PERSON>(), <PERSON><PERSON>LostHp(0));
    }
}
