using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Orbs;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Cards;

public class ShredderTest : ModelTest
{
    [Test]
    public async Task TestGainScrapForOneEnemy()
    {
        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        await Play<Shredder>();

        IReadOnlyList<OrbModel> orbs = player.PlayerCombatState!.OrbQueue.Orbs;
        Assert.That(orbs.Count, Is.EqualTo(1));
        Assert.That(orbs[0], Is.TypeOf<ScrapOrb>());
    }

    [Test]
    public async Task TestGainScrapFor3Enemies()
    {
        for (int i = 0; i < 4; i++)
        {
            await <PERSON><PERSON><PERSON>ne<PERSON><BigDummy>();
        }

        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 3);

        Shredder card = CreateCard<Shredder>();
        card.TestAllowDuplicates = false;
        await Play(card);

        IReadOnlyList<OrbModel> orbs = player.PlayerCombatState!.OrbQueue.Orbs;
        Assert.That(orbs.Count, Is.EqualTo(3));
        Assert.That(orbs, Is.All.TypeOf<ScrapOrb>());
    }

    [Test]
    public async Task TestMoreEnemiesThanHits()
    {
        for (int i = 0; i < 4; i++)
        {
            await CreateEnemy<BigDummy>();
        }

        Player player = GetPlayer();
        await OrbCmd.AddSlots(player, 4);

        Shredder card = CreateCard<Shredder>();
        card.TestAllowDuplicates = false;
        await Play(card);

        IReadOnlyList<OrbModel> orbs = player.PlayerCombatState!.OrbQueue.Orbs;
        Assert.That(orbs.Count, Is.EqualTo(3));
        Assert.That(orbs, Is.All.TypeOf<ScrapOrb>());
    }
}
