using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Modifiers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Modifiers;

public class VintageTest : ModelTest
{
    protected override ClimbState ConstructClimb(CharacterModel character)
    {
        return ClimbState.CreateForTest(
            players: [Player.CreateForNewClimb(character, NetSingleplayerGameService.defaultNetId)],
            modifiers: [ModelDb.Modifier<Vintage>().ToMutable()]
        );
    }

    [Test]
    public async Task TestThatNormalEncounterDropsRelics()
    {
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), new CombatRoom(GetEncounter(RoomType.Monster), GetPlayer().ClimbState));
        Assert.That(rewards, Has.Exactly(0).InstanceOf<CardReward>());
        Assert.That(rewards, Has.Exactly(1).InstanceOf<RelicReward>());
    }

    [Test]
    public async Task TestThatEliteEncounterDropsCards()
    {
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), new CombatRoom(GetEncounter(RoomType.Elite), GetPlayer().ClimbState));
        Assert.That(rewards, Has.Exactly(1).InstanceOf<CardReward>());
    }

    [Test]
    public async Task TestThatBossEncounterDropsCards()
    {
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), new CombatRoom(GetEncounter(RoomType.Boss), GetPlayer().ClimbState));
        Assert.That(rewards, Has.Exactly(1).InstanceOf<CardReward>());
    }

    [Test]
    public async Task TestThatPrayerWheelDropsRelic()
    {
        await RelicCmd.Obtain<PrayerWheel>(GetPlayer());
        IEnumerable<Reward> rewards = await RewardsCmd.GenerateForRoom(GetPlayer(), new CombatRoom(GetEncounter(RoomType.Monster), GetPlayer().ClimbState));

        // Note, this is not necessarily what we want, just a guess
        Assert.That(rewards, Has.Exactly(0).InstanceOf<CardReward>());
        Assert.That(rewards, Has.Exactly(2).InstanceOf<RelicReward>());
    }
}
