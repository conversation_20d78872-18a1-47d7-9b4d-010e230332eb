using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Afflictions;

public class GalvanizedTest : ModelTest
{
    [Test]
    public async Task TestTakeDamageFromPowers()
    {
        CardModel card = CreateCard<DemonForm>();

        await CardCmd.Afflict<Galvanized>(card, 6);
        await Play(card);

        Player player = GetPlayer();
        Assert.That(player.Creature, Has.LostHp(6));
    }

    [Test]
    public async Task TestTryToAfflictNonPower()
    {
        Creature enemy = GetEnemy();
        CardModel card = CreateCard<DefendIronclad>();

        await CardCmd.Afflict<Galvanized>(card, 6);
        await Play(card);

        Player player = GetPlayer();
        Assert.That(player.Creature, Has.LostHp(0));
    }
}
