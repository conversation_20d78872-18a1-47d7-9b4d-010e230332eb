using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Afflictions;

public class RingingTest : ModelTest
{
    [Test]
    public async Task TestReducesDamageAndRemovesOnPlay()
    {
        Creature enemy = GetEnemy();
        CardModel card = CreateCard<Bash>();

        await CardCmd.Afflict<Ringing>(card, 1);
        await Play(card, enemy);

        Player player = GetPlayer();
        Assert.That(player, Has.SpentEnergy(3));
        Assert.That(card.Affliction, Is.Null);
    }
}
