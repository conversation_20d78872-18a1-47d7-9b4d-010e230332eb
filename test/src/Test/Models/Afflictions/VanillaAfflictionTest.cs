using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Afflictions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Afflictions;

public class VanillaAfflictionTest : ModelTest
{
    [Test]
    public async Task TestNormalAfflictionsCanAfflictUnplayableCards()
    {
        CardModel card = MockSkill();
        card.AddKeyword(CardKeyword.Unplayable);
        await CardCmd.Afflict<TestUselessAffliction>(card, 1);

        Assert.That(card.Affliction, Is.TypeOf<TestUselessAffliction>());
    }

    [Test]
    public async Task TestSpecialAfflictionsCanAfflictUnplayableCards()
    {
        CardModel card = MockSkill();
        card.AddKeyword(CardKeyword.Unplayable);
        await CardCmd.Afflict<TestNoUnplayableAffliction>(card, 1);

        Assert.That(card.Affliction, Is.Null);
    }
}
