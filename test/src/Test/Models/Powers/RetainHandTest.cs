using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class RetainHandTest : ModelTest
{
    [Test]
    public async Task TestStopsHandFromDiscarding()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);
        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), hand);
        }

        await PowerCmd.Apply<RetainHand>(GetPlayer().Creature, 2, null, null);

        await PassToNextPlayerTurn();

        Assert.That(hand, Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 10).ToArray()));

        // No Defends should be left in the draw pile, since 10 were added, 5 were drawn at start of the last turn, and
        // 5 were drawn at the start of this turn.
        Assert.That(drawPile.Cards, Is.Empty);

        // The discard pile should remain empty (it would have Defend in it if we didn't have Retain Hand).
        Assert.That(GetPile(CardPileTarget.Discard).Cards, Is.Empty);
    }

    [Test]
    public async Task TestWearsOffAfterDurationEnds()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);
        CardPile hand = GetPile(CardPileTarget.Hand);

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        for (int i = 0; i < 5; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), hand);
        }

        await PowerCmd.Apply<RetainHand>(GetPlayer().Creature, 2, null, null);

        for (int i = 0; i < 3; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(hand, Has.Cards(Enumerable.Repeat(typeof(DefendIronclad), 5).ToArray()));
    }
}
