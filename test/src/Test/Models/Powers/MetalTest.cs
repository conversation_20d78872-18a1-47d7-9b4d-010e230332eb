using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class MetalTest : ModelTest
{
    [Test]
    public async Task TestAddsBlockNextTurn()
    {
        Creature player = GetPlayer().Creature;
        await PowerCmd.Apply<Metal>(player, 3, null, null);

        await EndTurn();

        Assert.That(player, Has.Block(3));
    }

    [Test]
    public async Task TestWithDexterity()
    {
        Creature player = GetPlayer().Creature;
        await PowerCmd.Apply<Metal>(player, 3, null, null);
        await PowerCmd.Apply<Dexterity>(player, 1, null, null);

        await EndTurn();
        Assert.That(player, Has.Block(3));
    }

    [Test]
    public async Task TestAddsBlockTheTurnAfterNext()
    {
        Creature player = GetPlayer().Creature;
        await PowerCmd.Apply<Metal>(player, 3, null, null);

        await PassToNextPlayerTurn();
        await EndTurn();

        Assert.That(GetPlayer().Creature, Has.Block(3));
    }
}
