using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class DampenTest : ModelTest
{
    [Test]
    public async Task TestDowngradesCards()
    {
        CardModel card = CreateCard<StrikeIronclad>();
        CardCmd.Upgrade(card);
        await CardPileCmd.Add(card, CardPileTarget.Draw);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        dampen.AddCaster(GetEnemy());

        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);

        Assert.That(card, Is.Not.Upgraded());
    }

    [Test]
    public async Task TestUpgradesCardsAfterDeath()
    {
        Creature oneHpEnemy = await CreateEnemy<OneHpMonster>();

        CardModel upgradedCard = CreateCard<StrikeIronclad>();
        CardCmd.Upgrade(upgradedCard);
        await CardPileCmd.Add(upgradedCard, CardPileTarget.Draw);

        CardModel unUpgradedCard = CreateCard<DefendIronclad>();
        await CardPileCmd.Add(unUpgradedCard, CardPileTarget.Draw);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        dampen.AddCaster(oneHpEnemy);

        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);
        await CreatureCmd.Kill(oneHpEnemy);

        Assert.That(upgradedCard, Is.Upgraded());
        Assert.That(unUpgradedCard, Is.Not.Upgraded());
    }

    [Test]
    public async Task TestDoesNotUpgradeCardsAfterOnlyOneDeath()
    {
        Creature oneHpEnemy = await CreateEnemy<OneHpMonster>();

        CardModel card = CreateCard<StrikeIronclad>();
        CardCmd.Upgrade(card);
        await CardPileCmd.Add(card, CardPileTarget.Draw);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        dampen.AddCaster(oneHpEnemy);

        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);

        dampen.AddCaster(GetEnemy());

        await CreatureCmd.Kill(oneHpEnemy);

        Assert.That(card, Is.Not.Upgraded());
    }

    [Test]
    public async Task TestDowngradeWithEthereal()
    {
        CardModel card = CreateCard<EchoForm>();
        CardCmd.Upgrade(card);
        await CardPileCmd.Add(card, CardPileTarget.Draw);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);
        dampen.AddCaster(GetEnemy());

        Assert.That(card, Is.Not.Upgraded());
        Assert.That(card, Has.Keyword(CardKeyword.Ethereal));
    }

    [Test]
    public async Task TestCardWithHex()
    {
        CardModel card = CreateCard<EchoForm>();
        CardCmd.Upgrade(card);
        await CardPileCmd.Add(card, CardPileTarget.Draw);

        Hex hex = (Hex)ModelDb.Power<Hex>().ToMutable();
        await PowerCmd.Apply(hex, GetPlayer().Creature, 1, GetEnemy(), null);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);
        dampen.AddCaster(GetEnemy());

        Assert.That(card, Is.Not.Upgraded());
        Assert.That(card, Has.Keyword(CardKeyword.Ethereal));
    }

    [Test]
    public async Task TestCardHexAppliedAfter()
    {
        CardModel card = CreateCard<EchoForm>();
        CardCmd.Upgrade(card);
        await CardPileCmd.Add(card, CardPileTarget.Draw);

        Dampen dampen = (Dampen)ModelDb.Power<Dampen>().ToMutable();
        await PowerCmd.Apply(dampen, GetPlayer().Creature, 1, null, null);
        dampen.AddCaster(GetEnemy());

        Hex hex = (Hex)ModelDb.Power<Hex>().ToMutable();
        await PowerCmd.Apply(hex, GetPlayer().Creature, 1, GetEnemy(), null);

        Assert.That(card, Is.Not.Upgraded());
        Assert.That(card, Has.Keyword(CardKeyword.Ethereal));
    }
}
