using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class DoomTest : ModelTest
{
    [Test]
    public async Task TestDoesNothingWhenHpAboveThreshold()
    {
        Creature enemy = GetEnemy();

        int doomAmount = enemy.CurrentHp - 1;
        await PowerCmd.Apply<Doom>(enemy, doomAmount, null, null);
        await PassToNextPlayerTurn();

        Assert.That(enemy, Is.Not.Dead());
    }

    [Test]
    public async Task TestDoesNothingThisTurn()
    {
        Creature enemy = await CreateEnemy<TenHpMonster>();

        int doomAmount = enemy.CurrentHp - 5;
        await PowerCmd.Apply<Doom>(enemy, doomAmount, null, null);
        await CreatureCmd.Damage(enemy, doomAmount + 1, DamageProps.nonCardHpLoss, null, null);

        Assert.That(enemy, Is.Not.Dead());
    }

    [Test]
    public async Task TestKillsEnemyAfterTheirTurn()
    {
        Creature enemy = await CreateEnemy<TenHpMonster>();

        int doomAmount = enemy.CurrentHp - 5;
        await PowerCmd.Apply<Doom>(enemy, doomAmount, null, null);
        await CreatureCmd.Damage(enemy, doomAmount + 1, DamageProps.nonCardHpLoss, null, null);
        await PassToNextPlayerTurn();

        Assert.That(enemy, Is.Dead());
    }

    [Test]
    public async Task TestKillsPlayerAfterTheirTurn()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Doom>(player, player.CurrentHp, null, null);
        await EndTurn();

        Assert.That(player, Is.Dead());
    }
}
