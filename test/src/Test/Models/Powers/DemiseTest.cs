using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class DemiseTest : ModelTest
{
    [Test]
    public async Task TestDamageNextTurn()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Demise>(enemy, 2, null, null);
        await PassToNextPlayerTurn();
        Assert.That(enemy, Has.LostHp(2));
    }

    [Test]
    public async Task TestDamageGoesThroughBlock()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Demise>(enemy, 2, null, null);
        await CreatureCmd.GainBlock(enemy, 10, BlockProps.monsterMove, null);
        await PassToNextPlayerTurn();
        Assert.That(enemy, Has.LostHp(2));
    }

    [Test]
    public async Task TestDamageAndCountdownAfter2Turns()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Demise>(enemy, 4, null, null);

        for (int i = 0; i < 2; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(enemy, Has.LostHp(8));
        Assert.That(enemy, Has.PowerAmount<Demise>(4));
    }
}
