using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class PlowTest : ModelTest
{
    [Test]
    public async Task TestDamageDecrementsAmount()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Plow>(enemy, 10, enemy, null);
        await CreatureCmd.Damage(enemy, 3, DamageProps.monsterMove, GetPlayer().Creature, null);

        Assert.That(enemy, Has.PowerAmount<Plow>(7));
    }

    [Test]
    public async Task TestDecrementWorksWithPoison()
    {
        await PowerCmd.Apply<Plow>(GetEnemy(), 40, GetEnemy(), null);
        await PowerCmd.Apply<Poison>(GetEnemy(), 3, GetEne<PERSON>(), null);

        await PassToNextPlayerTurn();
        await CreatureCmd.Damage(GetPlayer().Creature, 0, DamageProps.monsterMove, GetEnemy(), null);
        Assert.That(GetPlayer().Creature, Has.LostHp(37));
    }

    [Test]
    public async Task TestStunWhenAmountReducedTo0()
    {
        Creature ceremonialBeast = await CreateEnemy<CeremonialBeast>();

        await PowerCmd.Apply<Plow>(ceremonialBeast, 1, ceremonialBeast, null);
        await CreatureCmd.Damage(ceremonialBeast, 2, DamageProps.monsterMove, GetPlayer().Creature, null);

        Assert.That(ceremonialBeast.Monster!.NextMove.Id, Is.EqualTo("STUN_MOVE"));
    }


    [Test]
    public async Task TestWithNegativeStrength()
    {
        Creature ceremonialBeast = await CreateEnemy<CeremonialBeast>();

        await PowerCmd.Apply<Strength>(ceremonialBeast, -10, ceremonialBeast, null);
        await PowerCmd.Apply<Plow>(ceremonialBeast, 40, ceremonialBeast, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 0, DamageProps.monsterMove, ceremonialBeast, null);

        Assert.That(GetPlayer().Creature, Has.LostHp(30));
    }
}
