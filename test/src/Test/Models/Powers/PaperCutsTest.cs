using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class PaperCutsTest : ModelTest
{
    [Test]
    public async Task TestPlayerLosesMaxHp()
    {
        int startingMaxHp = GetPlayer().Creature.MaxHp;
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<PaperCuts>(enemy, 1, null, null);
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());

        Assert.That(GetPlayer().Creature.MaxHp, Is.EqualTo(startingMaxHp - 3));
    }
    
    [Test]
    public async Task TestWhenBlocking()
    {
        int startingMaxHp = GetPlayer().Creature.MaxHp;
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<PaperCuts>(enemy, 1, null, null);

        await CreatureCmd.GainBlock(GetPlayer().Creature, 3, ValueProp.Move, null);
        
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());
        await CreatureCmd.Damage(GetPlayer().Creature, 1, ValueProp.Move, GetEnemy());

        Assert.That(GetPlayer().Creature.MaxHp, Is.EqualTo(startingMaxHp));
    }
}