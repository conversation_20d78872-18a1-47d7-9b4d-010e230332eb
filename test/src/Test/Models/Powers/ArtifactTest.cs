using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class ArtifactTest : ModelTest
{
    [Test]
    public async Task TestBlocksDebuffs()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Vulnerable>(player, 1, null, null);

        // Player should have 1 Artifact left (started with 2) and 0 Vulnerable (since Artifact negated it).
        Assert.That(player, Has.Power<PERSON>mount<Artifact>(1));
        Assert.That(player, Has.Power<PERSON>mount<Vulnerable>(0));
    }

    [Test]
    public async Task TestDoesNotBlockDebuffsOnOtherCreatures()
    {
        Creature player = GetPlayer().Creature;
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Vulnerable>(enemy, 1, null, null);

        Assert.That(player, Has.Power<PERSON>mount<Artifact>(2));
        Assert.That(player, Has.PowerAmount<Vulnerable>(0));
        Assert.That(enemy, Has.PowerAmount<Vulnerable>(1));
    }

    [Test]
    public async Task TestDoesNotBlockNonNegatableDebuffs()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<ChainsOfBinding>(player, 1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(2));
        Assert.That(player, Has.PowerAmount<ChainsOfBinding>(1));
    }

    [Test]
    public async Task TestDoesNotBlockNonDebuffPowers()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Strength>(player, 1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(2));
        Assert.That(player, Has.PowerAmount<Strength>(1));
    }

    [Test]
    public async Task TestBlocksNegativeStrength()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Strength>(player, -1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(1));
        Assert.That(player, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestDoesNotBlockPositiveStrengthWhenCurrentStrengthIsNegative()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Strength>(player, -3, null, null);
        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Strength>(player, 1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(2));
        Assert.That(player, Has.PowerAmount<Strength>(-2));
    }

    [Test]
    public async Task TestBlocksNegativeStrengthWhenCurrentStrengthIsPositive()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Strength>(player, 3, null, null);
        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Strength>(player, -1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(1));
        Assert.That(player, Has.PowerAmount<Strength>(3));
    }

    [Test]
    public async Task TestDoesNotBlockNegativeDebuff()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Vulnerable>(player, 2, null, null);
        await PowerCmd.Apply<Artifact>(player, 2, null, null);
        await PowerCmd.Apply<Vulnerable>(player, -1, null, null);

        Assert.That(player, Has.PowerAmount<Artifact>(2));
        Assert.That(player, Has.PowerAmount<Vulnerable>(1));
    }
}
