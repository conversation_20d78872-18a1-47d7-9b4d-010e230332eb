using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class SuckTest : ModelTest
{
    [Test]
    public async Task TestGainsStrengthWhenHittingPlayer()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Suck>(enemy, 2, null, null);

        await DamageCmd.Attack(1, 1)
            .FromMonster(enemy.Monster!)
            .Targeting(GetPlayer().Creature)
            .Execute();

        Assert.That(enemy, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestGainsStrengthWhenHittingOsty()
    {
        Creature osty = (await OstyCmd.Summon(new ThrowingPlayerChoiceContext(), GetPlayer(), 5, null)).Creature!;
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Suck>(enemy, 2, null, null);

        await DamageCmd.Attack(1, 1)
            .FromMonster(enemy.Monster!)
            .Targeting(osty)
            .Execute();

        Assert.That(enemy, Has.PowerAmount<Strength>(2));
    }

    [Test]
    public async Task TestGainsNoStrengthWhenHittingTeammate()
    {
        Creature enemy = GetEnemy();
        Creature teammate = await CreateEnemy<BigDummy>();
        await PowerCmd.Apply<Suck>(enemy, 2, null, null);

        await DamageCmd.Attack(1, 1)
            .FromMonster(enemy.Monster!)
            .Targeting(teammate)
            .Execute();

        Assert.That(enemy, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestGainsNoStrengthWhenDamageIsBlocked()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Suck>(enemy, 2, null, null);

        Creature player = GetPlayer().Creature;
        await CreatureCmd.GainBlock(player, 2, BlockProps.nonCardUnpowered, null);

        await DamageCmd.Attack(1, 1)
            .FromMonster(enemy.Monster!)
            .Targeting(player)
            .Execute();

        Assert.That(enemy, Has.PowerAmount<Strength>(0));
    }

    [Test]
    public async Task TestGainsStrengthAtEndOfMultiHit()
    {
        Creature enemy = GetEnemy();
        await PowerCmd.Apply<Suck>(enemy, 2, null, null);

        Creature player = GetPlayer().Creature;
        await DamageCmd.Attack(1, 3)
            .FromMonster(enemy.Monster!)
            .Targeting(player)
            .Execute();

        // Player should only lose 3 HP, because Suck shouldn't apply until the end of the multi-hit attack.
        // However, Suck should apply 6 Strength, because it applies for each instance of damage dealt.
        Assert.That(player, Has.LostHp(3));
        Assert.That(enemy, Has.PowerAmount<Strength>(6));
    }
}
