using System;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class DrawCardsNextTurnTest : ModelTest
{
    [Test]
    public async Task TestDrawsCardsNextTurn()
    {
        CardPile drawPile = GetPile(CardPileTarget.Draw);

        // We should be drawing 7 cards next turn (5 default + 2 from the power), so make sure
        // we have more than that in our draw pile so the tests will fail if we overdraw.
        for (int i = 0; i < 8; i++)
        {
            await CardPileCmd.Add(CreateCard<DefendIronclad>(), drawPile);
        }

        await PowerCmd.Apply<DrawCardsNextTurn>(GetPlayer().Creature, 2, null, null);
        await PassToNextPlayerTurn();

        Type[] names = Enumerable.Repeat(typeof(DefendIronclad), 7).ToArray();
        Assert.That(GetPile(CardPileTarget.Hand), Has.Cards(names));
    }
}
