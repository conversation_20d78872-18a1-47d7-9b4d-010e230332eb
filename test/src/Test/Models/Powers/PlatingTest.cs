using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Powers;

public class PlatingTest : ModelTest
{
    [Test]
    public async Task TestBlock()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Plating>(player, 3, null, null);
        await EndTurn();

        Assert.That(player, Has.Block(3));
    }

    [Test]
    public async Task TestDecrementsEveryTurn()
    {
        Creature player = GetPlayer().Creature;

        await PowerCmd.Apply<Plating>(player, 3, null, null);
        await PassToNextPlayerTurn();

        Assert.That(player, Has.PowerAmount<Plating>(2));
    }

    [Test]
    public async Task TestEnemiesThatStartWithPlatingAlsoStartWithBlock()
    {
        await <PERSON>art<PERSON>omb<PERSON>(encounter: ModelDb.Encounter<MockPlatingEncounter>().ToMutable());
        Assert.That(GetEnemy(), Has.Block(1));
    }
}
