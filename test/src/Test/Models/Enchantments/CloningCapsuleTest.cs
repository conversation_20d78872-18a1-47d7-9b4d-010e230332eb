using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class CloningCapsuleTest : ModelTest
{
    [Test]
    public async Task TestClonesAfter4Combats()
    {
        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, CardPileTarget.Deck);
        CardCmd.Enchant<CloningCapsule>(card, 4);
        await RestartCombat();

        for (int i = 0; i < 4; i++)
        {
            await WinCombat();
        }

        Assert.That(GetPile(CardPileTarget.Deck), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard)));
    }

    [Test]
    public async Task TestClonesAfter8Combats()
    {
        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, CardPileTarget.Deck);
        CardCmd.Enchant<CloningCapsule>(card, 4);
        await RestartCombat();

        for (int i = 0; i < 8; i++)
        {
            await WinCombat();
        }

        // after 6 combats, the original and the clone clone again for a total of 4 clones
        Assert.That(GetPile(CardPileTarget.Deck), Has.Cards(typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard), typeof(MockSkillCard)));
    }

    [Test]
    public async Task TestDoNotCloneEarly()
    {
        CardModel card = MockSkill(CardScope.Climb);
        await CardPileCmd.Add(card, CardPileTarget.Deck);
        CardCmd.Enchant<CloningCapsule>(card, 4);
        await RestartCombat();

        for (int i = 0; i < 2; i++)
        {
            await WinCombat();
        }

        Assert.That(GetPile(CardPileTarget.Deck), Has.Cards(typeof(MockSkillCard)));
    }
}
