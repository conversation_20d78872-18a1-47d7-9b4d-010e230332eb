using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class ToothedTest : ModelTest
{
    [Test]
    public async Task TestAddsArtifact()
    {
        CardModel card = CreateCard<FanOfKnives>();
        CardCmd.Enchant<Toothed>(card, 2);
        await Play(card);

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Artifact>(2));
    }
}