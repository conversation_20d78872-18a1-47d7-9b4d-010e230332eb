using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class CorruptedTest : ModelTest
{
    [Test]
    public async Task TestDamagesEnemyAndSelf()
    {
        CardModel card = CreateCard<StrikeIronclad>();
        Creature enemy = GetEnemy();

        CardCmd.Enchant<Corrupted>(card, 1);
        await Play(card, enemy);

        // 6 * 1.5
        Assert.That(enemy, Has.LostHp(9));
        Assert.That(GetPlayer().Creature, Has.LostHp(2));
    }

    [Test]
    public async Task TestApplicationOrderWithUpgrade()
    {
        CardModel card = CreateCard<Bash>();
        Creature enemy = GetEnemy();

        CardCmd.Enchant<Corrupted>(card, 1);
        Upgrade(card);
        await Play(card, enemy);

        // (8 + 2) * 1.5
        Assert.That(enemy, Has.LostHp(15));
        Assert.That(GetPlayer().Creature, Has.LostHp(2));
    }

    [Test]
    public async Task TestWithBurningBloodWhenFatal()
    {
        Player player = GetPlayer();
        await RelicCmd.Obtain<BurningBlood>(player);

        // Bring enemy to 1 HP.
        Creature enemy = GetEnemy();
        await CreatureCmd.Damage(enemy, enemy.CurrentHp - 1, DamageProps.nonCardHpLoss, null, null);

        // Kill enemy with Corrupted Strike.
        CardModel card = CreateCard<StrikeIronclad>();
        CardCmd.Enchant<Corrupted>(card, 1);
        await Play(card, enemy);

        // Should be at full HP, since you first lost 3 HP from Corrupted, then healed 6 from Burning Blood.
        // If this ends up saying "expected 0 but was 3", it probably means Corrupted is accidentally triggering after
        // Burning Blood.
        Assert.That(player.Creature, Has.LostHp(0));
    }
}
