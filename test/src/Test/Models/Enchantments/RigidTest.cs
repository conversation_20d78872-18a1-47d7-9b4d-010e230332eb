using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class RigidTest : ModelTest
{
    [Test]
    public async Task TestAddsBlock()
    {
        CardModel card = CreateCard<DefendIronclad>();

        CardCmd.Enchant<Rigid>(card, 3);
        await Play(card);

        // 5 + 3
        Assert.That(GetPlayer().Creature, Has.Block(8));
    }

    [Test]
    public async Task TestWorksOnNonBlockCard()
    {
        CardModel card = CreateCard<StrikeIronclad>();
        Creature enemy = GetEnemy();

        CardCmd.Enchant<Rigid>(card, 3);
        await Play(card, enemy);

        Assert.That(GetPlayer().Creature, Has.Block(3));
    }

    [Test]
    public async Task TestDexterityAppliesSeparately()
    {
        CardModel card = CreateCard<DefendIronclad>();

        await PowerCmd.Apply<Dexterity>(GetPlayer().Creature, 2, null, null);
        CardCmd.Enchant<Rigid>(card, 3);
        await Play(card);

        // 5 + 2 from Defend + Dexterity
        // 3 + 2 from Rigid + Dexterity
        Assert.That(GetPlayer().Creature, Has.Block(12));
    }
}
