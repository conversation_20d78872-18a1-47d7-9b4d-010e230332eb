using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class NimbleTest : ModelTest
{
    [Test]
    public async Task TestIncreasesBlock()
    {
        CardModel card = MockSkill();
        CardCmd.Enchant<Nimble>(card, 3);

        await Play(card);

        // 5 + 3
        Assert.That(GetPlayer().Creature, Has.Block(8));
    }

    [Test]
    public async Task TestWorksOnBlockingAttacks()
    {
        MockCardModel card = MockAttack().MockBlock(1);
        CardCmd.Enchant<Nimble>(card, 3);
        await Play(card, GetEnemy());

        // 1 + 3
        Assert.That(GetPlayer().<PERSON><PERSON>ture, Has.Block(4));
        Assert.That(GetEnemy(), <PERSON>.LostHp(6));
    }

    [Test]
    public void TestDoesNotWorkOnNonBlockCards()
    {
        Assert.That(ModelDb.Enchantment<Nimble>().CanEnchant(ModelDb.Card<MockAttackCard>()), Is.False);
    }

    [Test]
    public void TestCanEnchantMadScienceSkill()
    {
        MadScience madScienceSkill = CreateCard<MadScience>();
        madScienceSkill.TinkerTimeType = CardType.Skill;

        Assert.That(ModelDb.Enchantment<Nimble>().CanEnchant(madScienceSkill), Is.True);
    }

    [Test]
    public void TestCannotEnchantMadScienceAttack()
    {
        MadScience madScienceAttack = CreateCard<MadScience>();
        madScienceAttack.TinkerTimeType = CardType.Attack;

        Assert.That(ModelDb.Enchantment<Nimble>().CanEnchant(madScienceAttack), Is.False);
    }
}
