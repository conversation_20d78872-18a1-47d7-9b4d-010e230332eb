using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Enchantments;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Enchantments;

public class TezcatarasEmberTest : ModelTest
{
    [Test]
    public async Task TestCostZeroAndEternal()
    {
        CardModel card = CreateCard<StrikeIronclad>();
        CardCmd.Enchant<TezcatarasEmber>(card, 1);
        await Play(card, GetEnemy());

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
        Assert.That(card, Has.Keyword(CardKeyword.Eternal));
    }
}
