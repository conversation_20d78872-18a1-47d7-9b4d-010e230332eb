using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards.Mocks;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Test.Exceptions;
using MegaCrit.Sts2.Test.Multiplayer;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models;

public abstract class ModelTest
{
    private ClimbState? _climbState;

    // ReSharper disable once UnusedMember.Global
    // Reason: Used by tests.
    [SetUp]
    public async Task SetUp()
    {
        await StartClimbAs<Deprived>();
    }

    /// <summary>
    /// Override this in a test if the test must change the parameters passed to the ClimbState constructor.
    /// </summary>
    protected virtual ClimbState ConstructClimb(CharacterModel character)
    {
        return ClimbState.CreateForTest(
            players: [Player.CreateForNewClimb(character, NetSingleplayerGameService.defaultNetId)]
        );
    }

    // ReSharper disable once UnusedMember.Global
    // Reason: Used by tests.
    [TearDown]
    public void Cleanup()
    {
        HookBus.Instance.UnsubscribeAll();
        TestRngInjector.Cleanup();
        TestCardSelector.Instance?.Cleanup();
        ModelDb.Character<Deprived>().ResetMockCardPool();
        ClimbManager.Instance.CleanUp();
        _climbState = null;
    }

    protected async Task RestartClimbAs<T>() where T : CharacterModel
    {
        Cleanup();
        await StartClimbAs<T>();
    }

    private async Task StartClimbAs<T>() where T : CharacterModel
    {
        _climbState = ConstructClimb(ModelDb.Character<T>());
        ClimbManager.Instance.SetUpTest(_climbState, new TestGameService(1, NetGameType.Singleplayer));
        ClimbManager.Instance.Launch();
        _climbState.Map = new MockActMap();

        await RestartCombat();
    }

    #region Cards

    protected T CreateCard<T>(CardScope scope = CardScope.Combat) where T : CardModel
    {
        return ICardScope.DebugOnlyGet(scope).CreateCard<T>(GetPlayer());
    }

    protected CardModel CloneCard(CardModel card) => GetCombatState().CloneCard(card);

    protected MockAttackCard MockAttack(CardScope scope = CardScope.Combat) => CreateCard<MockAttackCard>(scope);
    protected MockSkillCard MockSkill(CardScope scope = CardScope.Combat) => CreateCard<MockSkillCard>(scope);
    protected MockPowerCard MockPower(CardScope scope = CardScope.Combat) => CreateCard<MockPowerCard>(scope);
    protected MockCurseCard MockCurse(CardScope scope = CardScope.Combat) => CreateCard<MockCurseCard>(scope);

    protected async Task<T> Play<T>(Creature? target = null) where T : CardModel
    {
        return (T)await Play(CreateCard<T>(), target);
    }

    protected async Task<T> PlayUpgraded<T>(Creature? target = null) where T : CardModel
    {
        return (T)await Play(Upgrade(CreateCard<T>()), target);
    }

    public static async Task<CardModel> Play(CardModel card, Creature? target = null)
    {
        if (card.TargetEnemy == UiTargetEnemy.Any)
        {
            if (target == null)
            {
                throw new ArgumentException($"{card.Id} requires a target.");
            }
        }
        else if (target != null)
        {
            throw new ArgumentException($"{card.Id} should not have a target.");
        }

        if (card.Pile is { IsCombatPile: false })
        {
            throw new ArgumentException(
                $"{card.Id} is in a non-combat pile. " +
                "Cards can only be played in tests if they're in a combat pile or no pile."
            );
        }

        if (card.Pile is not { Type: CardPileTarget.Hand })
        {
            CardPileAddResult result = await CardPileCmd.Add(card, CardPileTarget.Hand);

            if (!result.success || card.Pile is not { Type: CardPileTarget.Hand })
            {
                throw new InvalidOperationException($"Failed to add {card.Id.Entry} to hand before play!");
            }
        }

        bool played = card.TryManualPlay(target);

        await ClimbManager.Instance.ActionQueueSet.BecameEmpty();

        if (!played)
        {
            StringBuilder message = new($"Playing card {card.Id.Entry} failed unexpectedly.");

            if (card.Owner.PlayerCombatState!.Stars < card.CurrentStarCost)
            {
                message.Append(" Likely cause: the player doesn't have enough Stars to play it.");
            }

            if (card.Keywords.Contains(CardKeyword.Unplayable))
            {
                message.Append(" Likely cause: the card has the 'Unplayable' keyword.");
            }

            throw new TestCardPlayException(message.ToString());
        }

        return card;
    }

    protected static CardModel Upgrade(CardModel card)
    {
        card.UpgradeInternal();
        return card;
    }

    #endregion

    #region Card Piles

    protected CardPile GetPile(CardPileTarget pileType) => pileType.GetPile(GetPlayer());

    #endregion

    #region Card Pools

    protected void AddToMockCardPool(MockCardModel card)
    {
        card.MockCanonical();
        ((Deprived)GetPlayer().Character).AddToPool(card);
    }

    #endregion

    #region Card Selection

    protected void PrepareToSelect(params CardModel[] cards)
    {
        TestCardSelector.Instance!.PrepareToSelect(cards);
    }

    protected void PrepareToSelectAtIndices(params int[] indices)
    {
        TestCardSelector.Instance!.PrepareToSelect(indices);
    }

    protected void PrepareToSkipSelection()
    {
        PrepareToSelect();
    }

    #endregion

    #region Creatures

    protected IReadOnlyList<Creature> GetEnemies() => CombatManager.Instance.DebugOnlyGetState()!.Enemies.ToList();
    protected Creature GetEnemy() => GetEnemies()[0];
    protected Player GetPlayer() => _climbState!.Players[0];

    protected async Task<Creature> CreateEnemy<T>() where T : MonsterModel
    {
        return await CreatureCmd.Add<T>(GetCombatState());
    }

    #endregion

    #region Energy

    protected void ResetEnergy()
    {
        GetPlayer().PlayerCombatState!.ResetEnergy();
    }

    #endregion

    #region Potions

    protected async Task UsePotion<T>(Creature? target = null) where T : PotionModel
    {
        PotionModel potion = (await PotionCmd.TryToProcure<T>(GetPlayer())).potion;
        await UsePotion(potion, target);
    }

    protected async Task UsePotion(PotionModel potion, Creature? target = null)
    {
        // This replicates the behavior of NPotionHolder.UsePotion
        if (potion.Target == ActionTarget.Self || (potion.Target == ActionTarget.AnyPlayer && _climbState!.Players.Count <= 1))
        {
            target ??= potion.Owner.Creature;
        }

        if (potion.Target is ActionTarget.AnyEnemy or ActionTarget.AnyPlayer && target == null)
            throw new InvalidOperationException("Target must be supplied when potion is targeted");

        potion.EnqueueManualUse(target);
        await ClimbManager.Instance.ActionExecutor.FinishedExecutingActions();
    }

    #endregion

    #region Turn/Combat Management

    protected async Task EndTurn()
    {
        await CombatManager.Instance.EndPlayerTurnPhaseOneInternal();
        await CombatManager.Instance.EndPlayerTurnPhaseTwoInternal();
    }

    /// <summary>
    /// Ends the turn, runs the enemies turn, and resumes on the player's next turn.
    /// </summary>
    protected async Task PassToNextPlayerTurn(Func<Task>? actionDuringEnemyTurn = null)
    {
        await EndTurn();
        await CombatManager.Instance.SwitchFromPlayerToEnemySide(actionDuringEnemyTurn);
    }

    protected async Task RestartCombat(RoomType roomType = RoomType.Monster, EncounterModel? encounter = null)
    {
        encounter?.AssertMutable();
        CombatManager.Instance.Reset();
        await ClimbManager.Instance.EnterRoomDebug(roomType, model: encounter ?? GetEncounter(roomType));
    }

    protected EncounterModel GetEncounter(RoomType roomType)
    {
        EncounterModel encounter = roomType switch
        {
            RoomType.Monster => ModelDb.Encounter<MockMonsterEncounter>(),
            RoomType.Elite => ModelDb.Encounter<MockEliteEncounter>(),
            RoomType.Boss => ModelDb.Encounter<MockBossEncounter>(),
            _ => throw new ArgumentOutOfRangeException(nameof(roomType), roomType, null)
        };

        return encounter.ToMutable();
    }

    // TODO: Remove singleton access
    protected CombatState GetCombatState() => CombatManager.Instance.DebugOnlyGetState()!;

    protected async Task WinCombat() => await CombatManager.Instance.EndCombatInternal();

    #endregion
}
