using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class RadiantTinctureTest : ModelTest
{
    [Test]
    public async Task Test1EnergyThisTurn()
    {
        await UsePotion<RadiantTincture>();
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestEnergyGainNextTurn()
    {
        await UsePotion<RadiantTincture>();
        await PassToNextPlayerTurn();
        Assert.That(GetPlayer(), Has.ExtraEnergy(1));
    }

    [Test]
    public async Task TestEnergyGainAfterFiveTurns()
    {
        await UsePotion<RadiantTincture>();

        for (int i = 0; i < 4; i++)
        {
            await PassToNextPlayerTurn();
        }

        Assert.That(GetPlayer(), Has.ExtraEnergy(0));
    }
}
