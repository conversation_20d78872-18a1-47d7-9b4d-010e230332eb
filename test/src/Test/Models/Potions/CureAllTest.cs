using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class CureAllTest : ModelTest
{
    [Test]
    public async Task TestClearDebuffs()
    {
        await PowerCmd.Apply<Vulnerable>(GetPlayer().Creature, 999, null, null);
        await PowerCmd.Apply<Weak>(GetPlayer().Creature, 999, null, null);
        await PowerCmd.Apply<Frail>(GetPlayer().Creature, 999, null, null);

        await UsePotion<CureAll>();

        Assert.That(GetPlayer().Creature, Has.PowerAmount<Vulnerable>(0));
        Assert.That(GetPlayer().Creature, Has.PowerAmount<Weak>(0));
        Assert.That(GetPlayer().<PERSON>reature, Has.PowerAmount<Frail>(0));
    }
}
