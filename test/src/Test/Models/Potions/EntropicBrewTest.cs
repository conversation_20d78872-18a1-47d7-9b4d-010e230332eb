using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class EntropicBrewTest : ModelTest
{
    [Test]
    public async Task TestFillsPotionBar()
    {
        await UsePotion<EntropicBrew>();

        Assert.That(GetPlayer().Potions.Count(), Is.EqualTo(3));
    }

    [Test]
    public async Task TestPreservesExistingPotions()
    {
        Player player = GetPlayer();
        PotionModel potion = (await PotionCmd.TryToProcure<AttackPotion>(player)).potion;

        await UsePotion<EntropicBrew>();

        Assert.That(player.Potions.Count(), Is.EqualTo(3));
        Assert.That(player.Potions, Has.Member(potion));
    }
}
