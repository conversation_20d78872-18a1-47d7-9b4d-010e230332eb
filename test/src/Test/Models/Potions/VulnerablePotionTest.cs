using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class VulnerablePotionTest : ModelTest
{
    [Test]
    public async Task TestAddsVulnerable()
    {
        Creature enemy = GetEnemy();

        await UsePotion<VulnerablePotion>(enemy);

        Assert.That(enemy, Has.PowerAmount<Vulnerable>(3));
    }
}