using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class SkillPotionTest : ModelTest
{
    [Test]
    public async Task TestAddsZeroCostSkill()
    {
        PrepareToSelectAtIndices(0);
        await UsePotion<SkillPotion>();

        CardModel card = GetPile(CardPileTarget.Hand).Cards.First();
        Assert.That(card, Has.CardType(CardType.Skill));
        Assert.That(card, Has.EnergyCostFree());
    }
}
