using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class FoulPotionTest : ModelTest
{
    [Test]
    public async Task TestDealsDamageToEveryone()
    {
        await UsePotion<FoulPotion>();

        Assert.That(GetEnemy(), Has.LostHp(12));
        Assert.That(GetPlayer().Creature, Has.LostHp(12));
    }

    [Test]
    public async Task TestGrantsGoldAtMerchant()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);

        await UsePotion<FoulPotion>();

        Assert.That(GetPlayer().Gold, Is.EqualTo(GetPlayer().Character.StartingGold + 100));
    }

    [Test]
    public async Task TestGrantsGoldAtMerchantMultipleTimes()
    {
        // Use Ironclad so the shop can be populated from a real card pool.
        await RestartClimbAs<Ironclad>();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Shop);

        await UsePotion<FoulPotion>();
        await UsePotion<FoulPotion>();

        Assert.That(GetPlayer().Gold, Is.EqualTo(GetPlayer().Character.StartingGold + 200));
    }

    [Test]
    public async Task TestDoesNotGrantGoldAnywhereElse()
    {
        await UsePotion<FoulPotion>();

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Treasure);
        await UsePotion<FoulPotion>();

        Assert.That(GetPlayer().Gold, Is.EqualTo(GetPlayer().Character.StartingGold));
    }
}
