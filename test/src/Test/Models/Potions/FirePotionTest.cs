using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Powers;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class FirePotionTest : ModelTest
{
    [Test]
    public async Task TestDealsDamage()
    {
        Creature enemy = GetEnemy();

        await UsePotion<FirePotion>(enemy);

        Assert.That(enemy, Has.LostHp(20));
    }

    [Test]
    public async Task TestUnaffectedByStrength()
    {
        Creature enemy = GetEnemy();

        await PowerCmd.Apply<Strength>(enemy, 1, GetPlayer().Creature, null);
        await UsePotion<FirePotion>(enemy);

        Assert.That(enemy, Has.LostHp(20));
    }
}
