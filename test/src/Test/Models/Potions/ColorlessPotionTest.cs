using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class ColorlessPotionTest : ModelTest
{
    [Test]
    public async Task TestAddsZeroColorlessCard()
    {
        PrepareToSelectAtIndices(0);
        await UsePotion<ColorlessPotion>();

        CardModel card = GetPile(CardPileTarget.Hand).Cards.First();
        Assert.That(card, Is.InPool<ColorlessCardPool>());
        Assert.That(card, Has.EnergyCostFree());
    }
}
