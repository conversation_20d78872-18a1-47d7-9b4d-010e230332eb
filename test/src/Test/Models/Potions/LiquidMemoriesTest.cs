using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Potions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.Potions;

public class LiquidMemoriesTest : ModelTest
{
    [Test]
    public async Task TestZeroCost()
    {
        CardModel card = CreateCard<DefendIronclad>();

        await CardPileCmd.Add(card, CardPileTarget.Discard);
        await UsePotion<LiquidMemories>();

        // Card should be in hand after using Liquid Memories.
        await Play(card);

        Assert.That(GetPlayer(), Has.SpentEnergy(0));
    }
}
