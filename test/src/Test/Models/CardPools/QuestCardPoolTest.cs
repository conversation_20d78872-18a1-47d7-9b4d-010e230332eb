using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.CardPools;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Models.CardPools;

[TestFixture]
public class QuestCardPoolTest
{
    [Test]
    public void TestThatAllQuestCardPoolCardsAreQuestType()
    {
        foreach (CardModel card in ModelDb.CardPool<QuestCardPool>().Cards)
        {
            // Note that this can change later, but for now this should be the case because otherwise quest cards
            // will have a wrong-looking card frame
            Assert.That(card, Has.CardType(CardType.Quest));
        }
    }
}
