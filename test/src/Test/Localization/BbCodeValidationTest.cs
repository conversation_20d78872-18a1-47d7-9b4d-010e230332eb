using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using MegaCrit.Sts2.Test.Helpers;
using Newtonsoft.Json.Linq;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Localization;

[TestFixture]
public partial class BbCodeValidationTest
{
    [Test]
    public void TestEnsureBbCodeTagIsNotMalformed()
    {
        List<string> tablePaths = LocHelper.GetAllTablePaths();

        string[] validBbCodes = ["blue", "fade_in", "fly_in", "gold", "green", "jitter", "orange", "purple", "red", "sine"];
        string[] builtInBbCodes =
        [
            "alm", "b", "bgcolor", "cell", "center", "code", "color", "custom_fx", "dropcap", "fade", "fgcolor", "fill",
            "font_size", "font", "fsi", "ghost", "hint", "i", "img", "indent", "lb", "left", "lre", "lri", "lrm", "lro",
            "matrix", "ol", "opentype_features", "outline_color", "outline_size", "p", "pdf", "pdi", "pulse", "rainbow",
            "rb", "right", "rle", "rli", "rlm", "rlo", "s", "shake", "shy", "table", "tag", "tornado", "u", "ul", "url",
            "wave", "wj", "zwj", "zwnj"
        ];

        List<string> failMessages = [];

        foreach (string tablePath in tablePaths)
        {
            foreach (string bbCodeTag in ExtractBbCodeTags(tablePath))
            {
                string tagWithoutSpacesOrEquals = bbCodeTag.Split(" ")[0].Split("=")[0];

                if (builtInBbCodes.Contains(tagWithoutSpacesOrEquals))
                {
                    // skip built-in BBCode tags
                    continue;
                }

                if (bbCodeTag.StartsWith('{') && bbCodeTag.EndsWith('}'))
                {
                    // If it's templated, it could be anything
                    continue;
                }

                if (!validBbCodes.Contains(bbCodeTag))
                {
                    failMessages.Add(
                        $"BBCode tag '{bbCodeTag}' in file '{tablePath}' is not in the list of valid BBCodes."
                    );
                }
            }
        }

        Assert.That(failMessages, Is.Empty, string.Join("\n", failMessages));
    }

    private static IEnumerable<string> ParseJsonFile(string jsonString)
    {
        JObject jObject = JObject.Parse(jsonString);

        foreach (JProperty property in jObject.Properties())
        {
            yield return property.Value.ToObject<string>()!;
        }
    }

    private static IEnumerable<string> ExtractBbCodeTags(string jsonFilePath)
    {
        string fileContent = File.ReadAllText(jsonFilePath);

        foreach (string locString in ParseJsonFile(fileContent))
        {
            MatchCollection matches = BbCodeRegex().Matches(locString);

            foreach (Match match in matches)
            {
                string tag = match.Value;
                tag = tag.Replace("[", "").Replace("]", "").Replace("/", "");
                yield return tag;
            }
        }
    }

    [GeneratedRegex(@"\[(?:\\?.)*?\]")]
    private static partial Regex BbCodeRegex();
}
