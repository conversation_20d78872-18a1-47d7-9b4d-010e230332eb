using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Climbs.History;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;
using MegaCrit.Sts2.Core.Rewards;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer;

public class RewardSynchronizerTest
{
    private const ulong _localPlayerId = 1;
    private const ulong _remotePlayerId = 1000;
    private static readonly int _startingDeckCount = ModelDb.Character<Ironclad>().StartingDeck.Count();
    private static readonly int _startingRelicsCount = ModelDb.Character<Ironclad>().StartingRelics.Count;
    private static readonly int _startingHp = ModelDb.Character<Ironclad>().StartingHp;
    private static readonly int _strawberryHpBonus = 7; // Strawberry relic HP bonus

    private static IEnumerable<string> GenerateTestSeeds(int count)
    {
        for (int i = 1; i <= count; i++)
        {
            yield return $"TESTSEED{i:D2}";
        }
    }

    private static IEnumerable<string> GenerateFastModeTestSeeds()
    {
        return GenerateTestSeeds(5);
    }

    private static IEnumerable<string> GenerateSlowModeTestSeeds()
    {
        return GenerateTestSeeds(100);
    }

    private static IEnumerable<string> GenerateTestSeedsBasedOnMode()
    {
        string testMode = Environment.GetEnvironmentVariable("STS2_TEST_MODE") ?? "fast";
        return testMode.ToLower() == "slow" ? GenerateSlowModeTestSeeds() : GenerateFastModeTestSeeds();
    }

    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    private async Task<IClimbState> StartClimb(TestGameService gameService, string seed)
    {
        // We use Ironclad instead of the test character because its card pool is compatible with merchant card
        // generation.
        List<Player> players =
        [
            Player.CreateForNewClimb<Ironclad>(_localPlayerId),
            Player.CreateForNewClimb<Ironclad>(_remotePlayerId)
        ];

        ClimbState climbState = ClimbState.CreateForTest(players, seed: seed);
        ClimbManager.Instance.SetUpTest(climbState, gameService);
        ClimbManager.Instance.Launch();
        ClimbManager.Instance.GenerateRooms();
        await ClimbManager.Instance.GenerateMap();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Map);

        return climbState;
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestCardRewardMessageIsHandled(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Card, cardModel = ModelDb.Card<Pummel>().ToMutable(), wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Deck.Cards.Count, Is.EqualTo(_startingDeckCount + 1));
        Assert.That(player.Deck.Cards, Has.Exactly(1).TypeOf<Pummel>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestRelicRewardMessageIsHandled(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Relic, relicModel = ModelDb.Relic<Strawberry>().ToMutable(), wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Relics.Count, Is.EqualTo(_startingRelicsCount + 1));
        Assert.That(player.Relics, Has.Exactly(1).TypeOf<Strawberry>());
        Assert.That(player.Creature.MaxHp, Is.EqualTo(_startingHp + _strawberryHpBonus));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestPotionRewardMessageIsHandled(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Potion, potionModel = ModelDb.Potion<PowderedDemise>(), wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Potions, Has.Exactly(1).Items);
        Assert.That(player.Potions, Has.Exactly(1).TypeOf<PowderedDemise>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestGoldRewardMessageIsHandled(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Gold, goldAmount = 20, wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Gold, Is.EqualTo(119));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestGoldLostMessageIsHandled(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new GoldLostMessage { goldLost = 19, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Gold, Is.EqualTo(80));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestRewardMessageIsNotHandledInCombat(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Card, cardModel = ModelDb.Card<Pummel>().ToMutable(), wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Deck.Cards.Count, Is.EqualTo(_startingDeckCount));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestRewardMessageIsHandledAfterCombat(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Card, cardModel = ModelDb.Card<Pummel>().ToMutable(), wasSkipped = false, location = climbState.CurrentLocation }, 1000);
        await CombatManager.Instance.EndCombatInternal();
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Deck.Cards.Count, Is.EqualTo(_startingDeckCount + 1));
        Assert.That(player.Deck.Cards, Has.Exactly(1).TypeOf<Pummel>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestGoldLostMessageIsNotHandledInCombat(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );
        gameService.ReceiveMessage(new GoldLostMessage { goldLost = 80, location = climbState.CurrentLocation }, 1000);
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Gold, Is.EqualTo(99));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestGoldLostMessageIsHandledAfterCombat(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );
        gameService.ReceiveMessage(new GoldLostMessage { goldLost = 80, location = climbState.CurrentLocation }, 1000);
        await CombatManager.Instance.EndCombatInternal();
        Player player = climbState.GetPlayer(_remotePlayerId)!;

        Assert.That(player.Gold, Is.EqualTo(19));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestCannotObtainRewardInCombat(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );

        Assert.That(() =>
                ClimbManager.Instance.RewardSynchronizer.SyncLocalObtainedCard(ModelDb.Card<Pummel>().ToMutable()),
            Throws.TypeOf<InvalidOperationException>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestSkipRewardMessage(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Card, cardModel = ModelDb.Card<Pummel>().ToMutable(), wasSkipped = true, location = climbState.CurrentLocation }, 1000);
        ModelChoiceHistoryEntry? choiceEntry = climbState.MapPointHistory[0][0].GetEntry(1000).CardChoices.FirstOrDefault(e => e.choice == ModelDb.Card<Pummel>().Id);

        Assert.That(choiceEntry, Is.Not.Null);
        Assert.That(choiceEntry.Value.wasPicked, Is.False);
        Assert.That(climbState.GetPlayer(_remotePlayerId)!.Deck.Cards, Has.Exactly(0).TypeOf<Pummel>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestSkipCardRewardMessageToNonCurrentLocation(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.StartingMapPoint.coord);
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.GetPointsInRow(1).First().coord);
        ClimbLocation previousMapLocation = climbState.CurrentLocation;
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.GetPointsInRow(2).First().coord);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Card, cardModel = ModelDb.Card<Pummel>().ToMutable(), wasSkipped = true, location = previousMapLocation }, 1000);
        ModelChoiceHistoryEntry? choiceEntry = climbState.MapPointHistory[0][1].GetEntry(1000).CardChoices.FirstOrDefault(e => e.choice == ModelDb.Card<Pummel>().Id);

        Assert.That(choiceEntry, Is.Not.Null);
        Assert.That(choiceEntry.Value.wasPicked, Is.False);
        Assert.That(climbState.GetPlayer(_remotePlayerId)!.Deck.Cards, Has.Exactly(0).TypeOf<Pummel>());
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public async Task TestSkipRelicMessage(string seed)
    {
        TestGameService gameService = new(_localPlayerId, NetGameType.Host);
        IClimbState climbState = await StartClimb(gameService, seed);
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.StartingMapPoint.coord);
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.GetPointsInRow(1).First().coord);
        ClimbLocation previousMapLocation = climbState.CurrentLocation;
        await ClimbManager.Instance.EnterMapCoord(climbState.Map.GetPointsInRow(2).First().coord);
        gameService.ReceiveMessage(new RewardObtainedMessage { rewardType = RewardType.Relic, relicModel = ModelDb.Relic<Strawberry>().ToMutable(), wasSkipped = true, location = previousMapLocation }, 1000);
        ModelChoiceHistoryEntry? choiceEntry = climbState.MapPointHistory[0][1].GetEntry(1000).CardChoices.FirstOrDefault(e => e.choice == ModelDb.Relic<Strawberry>().Id);

        Assert.That(choiceEntry, Is.Not.Null);
        Assert.That(choiceEntry.Value.wasPicked, Is.False);
        Assert.That(climbState.GetPlayer(_remotePlayerId)!.Relics, Has.Exactly(0).TypeOf<Strawberry>());
    }
}
