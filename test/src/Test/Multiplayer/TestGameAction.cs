using System;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Multiplayer.Serialization;

namespace MegaCrit.Sts2.Test.Multiplayer;

public class TestGameAction : GameAction
{
    private readonly Player _player;

    public static event Action? OnGameActionExecuted;
    public override GameActionType ActionType => actionType;
    public GameActionType actionType = GameActionType.Combat;

    public override ulong OwnerId => _player.NetId;
    private readonly Func<TestGameAction, Task>? _execute;

    public TestGameAction(Player player, Func<TestGameAction, Task>? execute = null)
    {
        _player = player;
        _execute = execute;
    }

    protected override async Task ExecuteAction()
    {
        OnGameActionExecuted?.Invoke();
        if (_execute != null)
        {
            await _execute.Invoke(this);
        }
    }

    public override INetAction ToNetAction() => new NetTestGameAction();
}

public struct NetTestGameAction : INetAction
{
    public void Serialize(PacketWriter writer) { }
    public void Deserialize(PacketReader reader) { }
    public GameAction ToGameAction(Player player) => new TestGameAction(player);
}
