using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;
using Byrdpip = MegaCrit.Sts2.Core.Models.Relics.Byrdpip;

namespace MegaCrit.Sts2.Test.Multiplayer;

public class HookBusOrderingTest
{
    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    [Test]
    public async Task TestThatHookBusAlwaysExecutesModelsInTheSameOrder()
    {
        INetGameService gameService = new TestGameService(1, NetGameType.Host);

        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        // Launch climb, give one player both osty and byrdpip, and save which order they were in when in combat
        ClimbState climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(climbState, gameService);
        ClimbManager.Instance.Launch();

        Byrdpip byrdpip = (Byrdpip)ModelDb.Relic<Byrdpip>().ToMutable();
        BoundPhylactery boundPhylactery = (BoundPhylactery)ModelDb.Relic<BoundPhylactery>().ToMutable();
        await RelicCmd.Obtain(byrdpip, climbState.Players[0]);
        await RelicCmd.Obtain(boundPhylactery, climbState.Players[0]);

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        List<Creature> creatures = CombatManager.Instance.DebugOnlyGetState()!.Allies.ToList();

        ClimbManager.Instance.CleanUp();

        // Recreate players so that their relic lists are clean
        players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        // Launch climb again, give one player both osty and byrdpip but in the opposite order, and save which order they
        // were in when in combat
        climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(climbState, gameService);
        ClimbManager.Instance.Launch();

        BoundPhylactery boundPhylactery2 = (BoundPhylactery)ModelDb.Relic<BoundPhylactery>().ToMutable();
        Byrdpip byrdpip2 = (Byrdpip)ModelDb.Relic<Byrdpip>().ToMutable();
        await RelicCmd.Obtain(boundPhylactery2, climbState.Players[0]);
        await RelicCmd.Obtain(byrdpip2, climbState.Players[0]);

        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        List<Creature> creatures2 = CombatManager.Instance.DebugOnlyGetState()!.Allies.ToList();

        // Regardless of which order the relics were received in, Osty and Byrdpip should be spawned in the same order
        IEnumerable<MonsterModel?> monsters = creatures.Select(c => c.Monster);
        Assert.That(monsters, Has.Exactly(1).TypeOf<Osty>());
        Assert.That(monsters, Has.Exactly(1).TypeOf<Core.Models.Monsters.Byrdpip>());
        Assert.That(creatures.Count, Is.EqualTo(creatures2.Count));

        for (int i = 0; i < creatures.Count; i++)
        {
            Assert.That(creatures[i].IsMonster, Is.EqualTo(creatures2[i].IsMonster));
            Assert.That(creatures[i].Monster?.Id, Is.EqualTo(creatures2[i].Monster?.Id!));
        }
    }
}
