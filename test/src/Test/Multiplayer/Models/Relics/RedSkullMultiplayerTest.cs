using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class RedSkullMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestTriggersNextCombatAfterRemotePlayerRevived()
    {
        await RelicCmd.Obtain<RedSkull>(GetRemotePlayer());
        await CreatureCmd.Damage(GetRemotePlayer().Creature, 999, DamageProps.nonCardUnpowered, GetEnemy(), null);

        await WinCombat();
        await RestartCombat();

        Assert.That(GetRemotePlayer().Creature, Has.PowerAmount<Strength>(3));
    }
}
