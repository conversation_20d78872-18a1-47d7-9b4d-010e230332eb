using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.CardRewardAlternatives;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Rewards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class PaelsWingMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestAddExtraOptionForLocalWhenOwnedByLocal()
    {
        await RelicCmd.Obtain<PaelsWing>(GetLocalPlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetLocalPlayer()));

        Assert.That(options.Count, Is.EqualTo(2));
    }

    [Test]
    public async Task TestDoesNotAddExtraOptionForLocalWhenOwnedByRemote()
    {
        await RelicCmd.Obtain<PaelsWing>(GetRemotePlayer());
        IReadOnlyList<CardRewardAlternative> options = CardRewardAlternative.Generate(new CardReward(CardCreationSource.RegularEncounter, 3, GetLocalPlayer()));

        Assert.That(options.Count, Is.EqualTo(1));
    }
}
