using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class PocketwatchMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestDoesNotTriggerForNonOwningPlayer()
    {
        await RelicCmd.Obtain<Pocketwatch>(GetRemotePlayer());

        // Add some cards to draw
        for (int i = 0; i < 10; i++)
        {
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(GetLocalPlayer()), CardPileTarget.Draw);
            await CardPileCmd.Add(CreateCard<StrikeIronclad>(GetRemotePlayer()), CardPileTarget.Draw);
        }

        // Play three cards
        for (int i = 0; i < 3; i++)
        {
            await Play<StrikeIronclad>(GetRemotePlayer(), GetEnemy());
        }

        // Pass to next turn, remote player should draw three extra cards but local should not
        await PassToNextPlayerTurn();

        Assert.AreEqual(8, CardPileTarget.Hand.GetPile(GetRemotePlayer()).Cards.Count);
        Assert.AreEqual(5, CardPileTarget.Hand.GetPile(GetLocalPlayer()).Cards.Count);
    }
}
