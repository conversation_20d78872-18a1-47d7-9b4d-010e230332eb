using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Merchant;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class MembershipCardMultiplayerTest : MultiplayerModelTest
{
    // Membership card is a little weird because merchant logic runs only for the local player.

    [Test]
    public async Task TestItemsAreDiscountedForLocalOwningPlayer()
    {
        MerchantPotionEntry potionEntry = new(ModelDb.Potion<WeakPotion>().ToMutable(), GetLocalPlayer());
        await RelicCmd.Obtain<MembershipCard>(GetLocalPlayer());

        Assert.That(potionEntry.Cost, Is.EqualTo(25));
    }

    [Test]
    public async Task TestItemsAreNotDiscountedIfOwnedByRemotePlayer()
    {
        MerchantPotionEntry potionEntry = new(ModelDb.Potion<WeakPotion>().ToMutable(), GetLocalPlayer());
        await RelicCmd.Obtain<MembershipCard>(GetRemotePlayer());

        Assert.That(potionEntry.Cost, Is.EqualTo(50));
    }
}
