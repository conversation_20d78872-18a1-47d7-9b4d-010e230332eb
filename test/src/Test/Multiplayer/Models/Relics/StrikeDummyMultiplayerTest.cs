using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Relics;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Relics;

public class StrikeDummyMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestDoesNotTriggerForNonOwningPlayer()
    {
        await RelicCmd.Obtain<StrikeDummy>(GetRemotePlayer());

        await Play<StrikeIronclad>(GetLocalPlayer(), GetEnemy());

        Assert.That(GetEnemy(), <PERSON><PERSON>Hp(6));
    }
}
