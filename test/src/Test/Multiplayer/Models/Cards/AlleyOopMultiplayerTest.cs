using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Models.Cards;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Cards;

public class AlleyOopMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestDoesNotTriggerForApplier()
    {
        await Play<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 10
        await Play<StrikeIronclad>(GetLocalPlayer(), GetEnemy()); // 6
        Assert.That(GetEnemy(), Has.LostHp(16));
    }

    [Test]
    public async Task TestTriggersForRemotePlayer()
    {
        await Play<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 10
        await Play<StrikeIronclad>(GetRemotePlayer(), GetEnemy()); // 6 * 2(alley oop)
        Assert.That(GetEnemy(), Has.LostHp(22));
    }

    [Test]
    public async Task TestSamePlayerStacking()
    {
        await Play<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 10
        await Play<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 10

        await Play<StrikeIronclad>(GetRemotePlayer(), GetEnemy()); // 6 * 2(first alley oop) * 2(first alley oop)
        Assert.That(GetEnemy(), Has.LostHp(44));
    }

    [Test]
    public async Task TestMultipleStacksFromDifferentPlayers()
    {
        await Play<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 10
        await Play<AlleyOop>(GetRemotePlayer(), GetEnemy()); // 10 * 2(first alley oop)

        await Play<StrikeIronclad>(GetRemotePlayer(), GetEnemy()); // 6 * 2(first alley oop)
        Assert.That(GetEnemy(), Has.LostHp(42));
    }

    [Test]
    public async Task TestUpgrade()
    {
        await PlayUpgraded<AlleyOop>(GetLocalPlayer(), GetEnemy()); // 14

        await Play<StrikeIronclad>(GetRemotePlayer(), GetEnemy()); // 6 * 3(alley oop)
        Assert.That(GetEnemy(), Has.LostHp(32));
    }
}
