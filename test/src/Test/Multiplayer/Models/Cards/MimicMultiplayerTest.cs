using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Powers;
using MegaCrit.Sts2.Core.ValueProps;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models.Cards;

public class MimicMultiplayerTest : MultiplayerModelTest
{
    [Test]
    public async Task TestWhenTargetHasNoBlock()
    {
        await Play<Mimic>(GetLocalPlayer(), GetRemotePlayer().Creature);
        Assert.That(GetLocalPlayer().Creature, Has.Block(0));
    }

    [Test]
    public async Task TestWhenTargetHasBlock()
    {
        await CreatureCmd.GainBlock(GetRemotePlayer().Creature, 10, BlockProps.nonCardUnpowered, null);

        await Play<Mimic>(GetLocalPlayer(), GetRemotePlayer().Creature);
        Assert.That(GetLocalPlayer().<PERSON><PERSON><PERSON>, <PERSON><PERSON>Block(10));
    }
}
