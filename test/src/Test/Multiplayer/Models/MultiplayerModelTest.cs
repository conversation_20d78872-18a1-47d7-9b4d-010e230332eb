using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Helpers;
using MegaCrit.Sts2.Core.Hooks;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Rooms;
using MegaCrit.Sts2.Core.TestSupport;
using MegaCrit.Sts2.Test.Exceptions;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Models;

public abstract class MultiplayerModelTest
{
    protected const ulong _localPlayerId = 1;
    protected const ulong _remotePlayerId = 1000;

    protected TestGameService _gameService = default!;
    protected IClimbState _climbState = NullClimbState.Instance;

    // ReSharper disable once UnusedMember.Global
    // Reason: Used by tests.
    [SetUp]
    public async Task SetUp()
    {
        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(_localPlayerId),
            Player.CreateForNewClimb<Deprived>(_remotePlayerId)
        ];

        await CreateAndStartClimb(players, null);
    }

    protected async Task CreateAndStartClimb(IReadOnlyList<Player> players, string? seed)
    {
        _gameService = new TestGameService(players[0].NetId, NetGameType.Host);

        ClimbState climbState = ClimbState.CreateForTest(players, seed: seed);
        _climbState = climbState;
        ClimbManager.Instance.SetUpTest(climbState, _gameService);
        ClimbManager.Instance.Launch();
        await RestartCombat();
    }

    // ReSharper disable once UnusedMember.Global
    // Reason: Used by tests.
    [TearDown]
    public void Cleanup()
    {
        HookBus.Instance.UnsubscribeAll();
        TestRngInjector.Cleanup();
        TestCardSelector.Instance?.Cleanup();
        ClimbManager.Instance.CleanUp();
        _climbState = NullClimbState.Instance;
    }

    #region Card Selection

    protected void PrepareToSelect(params CardModel[] cards)
    {
        TestCardSelector.Instance!.PrepareToSelect(cards);
    }

    protected void PrepareToSelectAtIndices(params int[] indices)
    {
        TestCardSelector.Instance!.PrepareToSelect(indices);
    }

    protected void PrepareToSkipSelection()
    {
        PrepareToSelect();
    }

    #endregion

    #region Cards

    protected static T CreateCard<T>(Player owner, CardScope scope = CardScope.Combat) where T : CardModel
    {
        return ICardScope.DebugOnlyGet(scope).CreateCard<T>(owner);
    }

    protected static async Task Play<T>(Player owner, Creature? target = null) where T : CardModel
    {
        await Play(CreateCard<T>(owner), target);
    }

    protected static async Task PlayUpgraded<T>(Player owner, Creature? target = null) where T : CardModel
    {
        await Play(Upgrade(CreateCard<T>(owner)), target);
    }

    protected static async Task Play(CardModel card, Creature? target = null)
    {
        if (card.TargetEnemy == UiTargetEnemy.Any || card.TargetPlayer == UiTargetPlayer.Ally)
        {
            if (target == null)
            {
                throw new ArgumentException($"{card.Id} requires a target.");
            }
        }
        else if (target != null)
        {
            throw new ArgumentException($"{card.Id} should not have a target.");
        }

        if (card.Pile is { IsCombatPile: false })
        {
            throw new ArgumentException(
                $"{card.Id} is in a non-combat pile. " +
                "Cards can only be played in tests if they're in a combat pile or no pile."
            );
        }

        if (card.Pile is not { Type: CardPileTarget.Hand })
        {
            await CardPileCmd.Add(card, CardPileTarget.Hand);
        }

        bool played = card.TryManualPlay(target);

        await ClimbManager.Instance.ActionQueueSet.BecameEmpty();

        if (!played)
        {
            StringBuilder message = new($"Playing card {card.Id.Entry} failed unexpectedly.");

            if (card.Owner.PlayerCombatState!.Stars < card.CurrentStarCost)
            {
                message.Append(" Likely cause: the player doesn't have enough Stars to play it.");
            }

            if (card.Keywords.Contains(CardKeyword.Unplayable))
            {
                message.Append(" Likely cause: the card has the 'Unplayable' keyword.");
            }

            throw new TestCardPlayException(message.ToString());
        }
    }

    protected static CardModel Upgrade(CardModel card)
    {
        card.UpgradeInternal();
        return card;
    }

    #endregion

    #region Creatures

    protected IReadOnlyList<Creature> GetEnemies() => CombatManager.Instance.DebugOnlyGetState()!.Enemies.ToList();
    protected Creature GetEnemy() => GetEnemies()[0];
    protected Player GetLocalPlayer() => _climbState.GetPlayer(_localPlayerId)!;
    protected Player GetRemotePlayer() => _climbState.GetPlayer(_remotePlayerId)!;

    protected IReadOnlyList<Creature> GetPlayersCreatures() => _climbState.Players.Select(p => p.Creature).ToList();

    #endregion

    #region Turn/Combat Management

    protected async Task EndTurn()
    {
        await CombatManager.Instance.EndPlayerTurnPhaseOneInternal();
        await CombatManager.Instance.EndPlayerTurnPhaseTwoInternal();
    }

    /// <summary>
    /// Ends the turn, runs the enemies turn, and resumes on the player's next turn.
    /// </summary>
    protected async Task PassToNextPlayerTurn()
    {
        await EndTurn();
        await CombatManager.Instance.SwitchFromPlayerToEnemySide();
    }

    protected async Task RestartCombat(RoomType roomType = RoomType.Monster)
    {
        CombatManager.Instance.Reset();
        EncounterModel encounter = roomType switch
        {
            RoomType.Monster => ModelDb.Encounter<MockMonsterEncounter>(),
            RoomType.Elite => ModelDb.Encounter<MockEliteEncounter>(),
            RoomType.Boss => ModelDb.Encounter<MockBossEncounter>(),
            _ => throw new ArgumentOutOfRangeException(nameof(roomType), roomType, null)
        };

        await ClimbManager.Instance.EnterRoomDebug(roomType, model: encounter.ToMutable());
    }

    protected async Task WinCombat() => await CombatManager.Instance.EndCombatInternal();

    #endregion
}
