using System.Collections.Generic;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Models.Relics;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Game.PeerInput;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Sync;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer;

public class HoveredModelTrackerTest
{
    [SetUp]
    public void SetUp()
    {
        NetCombatCardDb.Instance.ClearCardsForTesting();
    }

    [Test]
    public void TestHoveringCardThatExists()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        NetCombatCardDb.Instance.StartCombat(players);
        CardModel card = ModelDb.Card<StrikeIronclad>().ToMutable();
        card.Owner = players[1];
        NetCombatCardDb.Instance.IdCardForTesting(card);

        HoveredModelData hoverData = new() { hoveredCombatCard = NetCombatCard.FromModel(card), type = HoveredModelType.Card, hoveredModelId = card.Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(card));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }

    [Test]
    public void TestHoveringCardThatDoesNotExist()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        HoveredModelData hoverData = new() { hoveredCombatCard = NetCombatCard.ForTesting(100), type = HoveredModelType.Card, hoveredModelId = ModelDb.Card<StrikeIronclad>().Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        // We received a hover message referencing a combat card that does not exist. This can happen if a card was created
        // on the remote that has yet to be created locally (e.g. Shiv). In this case, we fall back to the canonical card.
        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(ModelDb.Card<StrikeIronclad>()));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }

    [Test]
    public void TestHoveringPotionThatExists()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        ColorlessPotion potion = (ColorlessPotion)ModelDb.Potion<ColorlessPotion>().ToMutable();
        players[1].AddPotionInternal(potion, 0);

        HoveredModelData hoverData = new() { hoveredPotionIndex = 0, type = HoveredModelType.Potion, hoveredModelId = potion.Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(potion));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }

    [Test]
    public void TestHoveringPotionThatDoesNotExist()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        HoveredModelData hoverData = new() { hoveredPotionIndex = 0, type = HoveredModelType.Potion, hoveredModelId = ModelDb.Potion<ColorlessPotion>().Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        // We received a hover message referencing a potion that does not exist. This can happen if a potion was created
        // on the remote that has yet to be created locally (i.e. by Alchemize).
        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(ModelDb.Potion<ColorlessPotion>()));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }

    [Test]
    public void TestHoveringRelicThatExists()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        Strawberry relic = (Strawberry)ModelDb.Relic<Strawberry>().ToMutable();
        players[1].AddRelicInternal(relic, 0);

        HoveredModelData hoverData = new() { hoveredRelicIndex = 0, type = HoveredModelType.Relic, hoveredModelId = relic.Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(relic));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }

    [Test]
    public void TestHoveringRelicThatDoesNotExist()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        List<Player> players = [Player.CreateForNewClimb<Deprived>(1), Player.CreateForNewClimb<Deprived>(1000)];
        TestPlayerCollection playerCollection = new(players, 1);
        PeerInputSynchronizer peerInputSynchronizer = new(gameService);
        HoveredModelTracker tracker = new(peerInputSynchronizer, playerCollection);

        HoveredModelData hoverData = new() { hoveredRelicIndex = 0, type = HoveredModelType.Relic, hoveredModelId = ModelDb.Relic<Strawberry>().Id };
        PeerInputMessage message = new() { hoveredModelData = hoverData, mouseDown = false, netMousePos = null, screenType = NetScreenType.None };
        gameService.ReceiveMessage(message, 1000);

        // Actually, there's no situation in which this can legitimately happen yet... but it could eventually!
        Assert.That(tracker.GetHoveredModel(1000), Is.EqualTo(ModelDb.Relic<Strawberry>()));
        Assert.That(tracker.GetHoveredModel(1), Is.Null);
    }
}
