using System.Linq;
using System.Threading.Tasks;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game.Checksums;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer;

public class ChecksumTrackerTest
{
    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    private static async Task StartClimb(TestGameService gameService)
    {
        List<Player> players = [Player.CreateForNewClimb<Deprived>(gameService.NetId)];
        ClimbState climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(climbState, gameService);
        ClimbManager.Instance.Launch();
        await ClimbManager.Instance.EnterRoomDebug(
            RoomType.Monster,
            model: ModelDb.Encounter<MockMonsterEncounter>().ToMutable()
        );
        await CombatManager.Instance.StartCombatInternal();
    }

    [Test]
    public async Task TestHostDoesNotSendDivergenceMessageWhenChecksumMatches()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        await StartClimb(gameService);

        NetChecksumData checksumData = ClimbManager.Instance.ChecksumTracker.GenerateChecksum("Test", null);
        gameService.ReceiveMessage(new ChecksumDataMessage { checksumData = checksumData }, 1000);

        Assert.That(gameService.SentMessages.OfType<StateDivergenceMessage>(), Is.Empty);
    }

    [Test]
    public async Task TestHostSendsDivergenceMessageWhenChecksumMismatches()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        await StartClimb(gameService);

        NetChecksumData checksumData = ClimbManager.Instance.ChecksumTracker.GenerateChecksum("Test", null);
        checksumData.checksum += 1;
        gameService.ReceiveMessage(new ChecksumDataMessage { checksumData = checksumData }, 1000);

        Assert.That(gameService.SentMessages.OfType<StateDivergenceMessage>(), Is.Not.Empty);
    }

    [Test]
    public async Task TestClientEchoesDivergenceMessageWhenReceivingDivergenceMessage()
    {
        TestGameService gameService = new(1000, NetGameType.Client);
        await StartClimb(gameService);

        NetChecksumData checksumData = ClimbManager.Instance.ChecksumTracker.GenerateChecksum("Test", null);
        checksumData.checksum += 1;
        gameService.ReceiveMessage(new StateDivergenceMessage { senderChecksum = checksumData }, 1);

        Assert.That(gameService.SentMessages.OfType<StateDivergenceMessage>(), Is.Not.Empty);
    }

    [Test]
    public async Task TestHostDoesNotEchoDivergenceMessageWhenReceivingDivergenceMessage()
    {
        TestGameService gameService = new(1, NetGameType.Host);
        await StartClimb(gameService);

        NetChecksumData checksumData = ClimbManager.Instance.ChecksumTracker.GenerateChecksum("Test", null);
        checksumData.checksum += 1;
        gameService.ReceiveMessage(new StateDivergenceMessage { senderChecksum = checksumData }, 1000);

        Assert.That(gameService.SentMessages.OfType<StateDivergenceMessage>(), Is.Empty);
    }
}
