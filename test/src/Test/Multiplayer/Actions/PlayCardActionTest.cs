using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Cards;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Multiplayer;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.GameActions.Multiplayer;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Cards;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Actions;

public class PlayCardActionTest
{
    [SetUp]
    public void SetUp()
    {
        NetCombatCardDb.Instance.ClearCardsForTesting();
    }

    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    [Test]
    public async Task TestThatActionTargetingNotYetSpawnedMonsterIsDeferred()
    {
        TestGameService netService = new(1, NetGameType.Host);

        // In multiplayer, in the phrog parasite fight, one player may play a card against a spawned wriggler before
        // peers have spawned the wriggler. In this case, the card should wait until the wrigglers are spawned locally
        // before executing.
        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        ClimbState climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(climbState, netService);
        ClimbManager.Instance.Launch();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        CombatState combatState = players[0].Creature.CombatState!;
        StrikeIronclad card = combatState.CreateCard<StrikeIronclad>(players[1]);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        NetCombatCardDb.Instance.IdCardForTesting(card);

        // 0 is host, 1 is client, 2 is already-spawned enemy, 3 is the enemy that is about to be spawned
        NetPlayCardAction action = new() { card = NetCombatCard.FromModel(card), targetId = 3 };
        RequestEnqueueActionMessage message = new() { action = action, location = climbState.CurrentLocation };

        netService.ReceiveMessage(message, players[1].NetId);
        Creature creature = await CreatureCmd.Add<BigDummy>(combatState);

        Assert.That(creature, Has.LostHp(6));
    }

    [Test]
    public async Task TestThatActionTargetingDeadMonsterIsCanceled()
    {
        TestGameService netService = new(1, NetGameType.Host);

        // In multiplayer, in the phrog parasite fight, one player may play a card against a spawned wriggler before
        // peers have spawned the wriggler. In this case, the card should wait until the wrigglers are spawned locally
        // before executing.
        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        ClimbState climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(climbState, netService);
        ClimbManager.Instance.Launch();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        CombatState combatState = players[0].Creature.CombatState!;
        Creature creature2 = await CreatureCmd.Add<BigDummy>(combatState);
        await CreatureCmd.Kill(combatState.Enemies[0]);

        StrikeIronclad card = combatState.CreateCard<StrikeIronclad>(players[1]);
        await CardPileCmd.Add(card, CardPileTarget.Hand);
        NetCombatCardDb.Instance.IdCardForTesting(card);

        // 0 is host, 1 is client, 2 and 3 are the enemies. 2 has died
        NetPlayCardAction action = new() { card = NetCombatCard.FromModel(card), targetId = 2 };
        RequestEnqueueActionMessage message = new() { action = action, location = climbState.CurrentLocation };

        netService.ReceiveMessage(message, players[1].NetId);

        Assert.That(CardPileTarget.Hand.GetPile(players[1]), Has.Cards(typeof(StrikeIronclad)));
        Assert.That(creature2, Has.LostHp(0));
    }
}
