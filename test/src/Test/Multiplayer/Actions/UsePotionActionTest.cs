using System.Collections.Generic;
using System.Threading.Tasks;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Creatures;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Models.Events;
using MegaCrit.Sts2.Core.Models.Monsters;
using MegaCrit.Sts2.Core.Models.Potions;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Actions;

public class UsePotionActionTest
{
    private TestGameService _netService = default!;
    private ClimbState _climbState = default!;

    [SetUp]
    public void SetUp()
    {
        _netService = new TestGameService(1, NetGameType.Host);

        List<Player> players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        _climbState = ClimbState.CreateForTest(players);
        ClimbManager.Instance.SetUpTest(_climbState, _netService);
        ClimbManager.Instance.Launch();
    }

    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    /// <summary>
    /// In multiplayer, in the phrog parasite fight, one player may play a card against a spawned wriggler before peers
    /// have spawned the wriggler. In this case, the card should wait until the wrigglers are spawned locally before
    /// executing.
    /// </summary>
    [Test]
    public async Task TestThatActionTargetingNotYetSpawnedMonsterIsDeferred()
    {
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());

        await PotionCmd.TryToProcure<FirePotion>(_climbState.Players[1]);

        // 0 is host, 1 is client, 2 is already-spawned enemy, 3 is the enemy that is about to be spawned
        NetUsePotionAction action = new() { potionIndex = 0, targetId = 3 };
        RequestEnqueueActionMessage message = new() { action = action, location = _climbState.CurrentLocation };

        _netService.ReceiveMessage(message, _climbState.Players[1].NetId);
        Creature creature = await CreatureCmd.Add<BigDummy>(CombatManager.Instance.DebugOnlyGetState()!);

        Assert.That(creature, Has.LostHp(20));
    }

    [Test]
    public async Task TestThatActionEnqueuedJustBeforeCombatEndsIsCanceled()
    {
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());
        CombatState combatState = _climbState.Players[0].Creature.CombatState!;

        await CreatureCmd.SetMaxHp(_climbState.Players[0].Creature, 100);
        await CreatureCmd.SetCurrentHp(_climbState.Players[0].Creature, 50);
        await CreatureCmd.SetCurrentHp(combatState.Enemies[0], 1);

        PotionModel firePotion = (await PotionCmd.TryToProcure<FirePotion>(_climbState.Players[0])).potion;
        PotionModel bloodPotion = (await PotionCmd.TryToProcure<BloodPotion>(_climbState.Players[0])).potion;

        ClimbManager.Instance.ActionQueueSet.PauseAllPlayerQueues();
        firePotion.EnqueueManualUse(combatState.Enemies[0]);
        bloodPotion.EnqueueManualUse(_climbState.Players[0].Creature);
        ClimbManager.Instance.ActionQueueSet.UnpauseAllPlayerQueues();

        Assert.That(CombatManager.Instance.IsInProgress, Is.False);
        Assert.That(_climbState.Players[0].Creature.CurrentHp, Is.EqualTo(50));
        Assert.That(_climbState.Players[0].Potions, Has.Exactly(1).TypeOf<BloodPotion>());
        Assert.That(bloodPotion.IsQueued, Is.False);
    }

    [Test]
    public async Task TestThatActionEnqueuedAfterCombatIsExecuted()
    {
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Event, MapPointType.Unknown, ModelDb.Event<TheLegendsWereTrue>());

        await CreatureCmd.SetMaxHp(_climbState.Players[0].Creature, 100);
        await CreatureCmd.SetCurrentHp(_climbState.Players[0].Creature, 50);

        PotionModel bloodPotion = (await PotionCmd.TryToProcure<BloodPotion>(_climbState.Players[0])).potion;
        bloodPotion.EnqueueManualUse(_climbState.Players[0].Creature);

        Assert.That(_climbState.Players[0].Creature.CurrentHp, Is.GreaterThan(50));
        Assert.That(_climbState.Players[0].Potions, Has.Exactly(0).TypeOf<BloodPotion>());
    }
}
