using System.Collections.Generic;
using System.Threading.Tasks;
using Godot;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Combat;
using MegaCrit.Sts2.Core.Commands;
using MegaCrit.Sts2.Core.Entities.Actions;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.GameActions;
using MegaCrit.Sts2.Core.Map;
using MegaCrit.Sts2.Core.Models;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Models.Encounters;
using MegaCrit.Sts2.Core.Models.Encounters.Mocks;
using MegaCrit.Sts2.Core.Multiplayer.Game;
using MegaCrit.Sts2.Core.Multiplayer.Messages.Game;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Multiplayer.Actions;

public class EndTurnActionTest
{
    private List<Player> _players = default!;
    private TestGameService _netService = default!;
    private ClimbState _climbState = default!;

    [SetUp]
    public async Task Setup()
    {
        _netService = new TestGameService(1, NetGameType.Host);

        _players =
        [
            Player.CreateForNewClimb<Deprived>(1),
            Player.CreateForNewClimb<Deprived>(1000)
        ];

        _climbState = ClimbState.CreateForTest(_players);
        ClimbManager.Instance.SetUpTest(_climbState, _netService);
        ClimbManager.Instance.Launch();
        await ClimbManager.Instance.EnterRoomDebug(RoomType.Monster, MapPointType.Monster, ModelDb.Encounter<MockMonsterEncounter>().ToMutable());
    }

    [TearDown]
    public void Teardown()
    {
        ClimbManager.Instance.CleanUp();
    }

    [Test]
    public void TestThatEndTurnActionMarksPlayerAsReady()
    {
        NetEndPlayerTurnAction action = new() { combatRound = 1 };
        RequestEnqueueActionMessage message = new() { action = action, location = _climbState.CurrentLocation };

        _netService.ReceiveMessage(message, _players[1].NetId);

        Assert.That(CombatManager.Instance.IsPlayerReadyToEndTurn(_players[1]), Is.True);
    }

    [Test]
    public async Task TestThatEveryoneEndingTurnAdvancesRound()
    {
        NetEndPlayerTurnAction action = new() { combatRound = 1 };
        RequestEnqueueActionMessage message = new() { action = action, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(message, _players[1].NetId);

        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(new EndPlayerTurnAction(_players[0], 1));

        NetReadyToBeginEnemyTurnAction switchAction = new();
        RequestEnqueueActionMessage switchMessage = new() { action = switchAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(switchMessage, _players[1].NetId);

        await WaitForRoundNumberOrTimeout(2);
        Assert.That(CombatManager.Instance.DebugOnlyGetState()!.RoundNumber, Is.EqualTo(2));
    }

    [Test]
    public async Task TestThatEndTurnActionForPreviousRoundIsIgnored()
    {
        // Pass one turn
        await CombatManager.Instance.EndPlayerTurnPhaseOneInternal();
        await CombatManager.Instance.EndPlayerTurnPhaseTwoInternal();
        await CombatManager.Instance.SwitchFromPlayerToEnemySide();

        // Receive end turn action from client for previous round
        NetEndPlayerTurnAction action = new() { combatRound = 1 };
        RequestEnqueueActionMessage message = new() { action = action, location = _climbState.CurrentLocation };

        _netService.ReceiveMessage(message, _players[1].NetId);

        // Player should still be unready
        Assert.That(CombatManager.Instance.IsPlayerReadyToEndTurn(_players[1]), Is.False);
    }

    [Test]
    public void TestThatEndTurnCanBeUndone()
    {
        // Receive end turn action and undo end turn action from client
        NetEndPlayerTurnAction endAction = new() { combatRound = 1 };
        RequestEnqueueActionMessage endMessage = new() { action = endAction, location = _climbState.CurrentLocation };

        NetUndoEndPlayerTurnAction undoAction = new() { combatRound = 1 };
        RequestEnqueueActionMessage undoMessage = new() { action = undoAction, location = _climbState.CurrentLocation };

        _netService.ReceiveMessage(endMessage, _players[1].NetId);
        _netService.ReceiveMessage(undoMessage, _players[1].NetId);

        // Player should be unready
        Assert.That(CombatManager.Instance.IsPlayerReadyToEndTurn(_players[1]), Is.False);
    }

    [Test]
    public async Task TestThatEndingTurnDoesNotCancelNonCombatActions()
    {
        // Pause action executor
        ClimbManager.Instance.ActionExecutor.Pause();

        // Receive end turn action, undo end turn action, and end turn action from client and us
        NetEndPlayerTurnAction endAction = new() { combatRound = 1 };
        RequestEnqueueActionMessage endMessage = new() { action = endAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(endMessage, _players[1].NetId);

        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(new EndPlayerTurnAction(_players[0], 1));

        // We both ended turn. Imagine that the enemy somehow dies to an end-of-turn hook, and the remote player has already
        // seen this, zoomed through the rewards screen, and placed a map vote.
        MapCoord votedCoord = new() { col = 1, row = 5 };
        MapVote vote = new()
        {
            coord = votedCoord,
            mapGenerationCount = ClimbManager.Instance.MapSelectionSynchronizer.MapGenerationCount
        };
        NetVoteForMapCoordAction mapCoordAction = new() { source = _climbState.CurrentLocation, destination = vote };
        RequestEnqueueActionMessage mapCoordMessage = new() { action = mapCoordAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(mapCoordMessage, _players[1].NetId);

        // Start the action executor
        ClimbManager.Instance.ActionExecutor.Unpause();

        // Wait for some time for the combat manager to finish end of turn
        await Task.Delay(10);

        // Win combat
        await CreatureCmd.Kill(_players[0].Creature.CombatState!.Enemies);

        // The map vote should have been executed
        Assert.That(ClimbManager.Instance.MapSelectionSynchronizer.GetVote(_players[1]), Is.EqualTo(vote));
    }

    [Test]
    public async Task TestThatEndingTurnWithQueuedUndoAndEndTurnActionsDoesNotAdvanceRoundTwice()
    {
        // Pause action executor
        ClimbManager.Instance.ActionExecutor.Pause();

        // Receive end turn action, undo end turn action, and end turn action from client and us
        NetEndPlayerTurnAction endAction = new() { combatRound = 1 };
        RequestEnqueueActionMessage endMessage = new() { action = endAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(endMessage, _players[1].NetId);

        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(new EndPlayerTurnAction(_players[0], 1));

        NetUndoEndPlayerTurnAction undoAction = new() { combatRound = 1 };
        RequestEnqueueActionMessage undoMessage = new() { action = undoAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(undoMessage, _players[1].NetId);

        UndoEndPlayerTurnAction undoEndTurnActionReceivedAfterEndOfTurn = new UndoEndPlayerTurnAction(_players[0], 1);
        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(undoEndTurnActionReceivedAfterEndOfTurn);

        endAction = new NetEndPlayerTurnAction { combatRound = 1 };
        endMessage = new RequestEnqueueActionMessage { action = endAction, location = _climbState.CurrentLocation };
        _netService.ReceiveMessage(endMessage, _players[1].NetId);

        EndPlayerTurnAction endTurnActionReceivedAfterEndOfTurn = new EndPlayerTurnAction(_players[0], 1);
        ClimbManager.Instance.ActionQueueSynchronizer.RequestEnqueue(endTurnActionReceivedAfterEndOfTurn);
        CombatManager.Instance.SetReadyToBeginEnemyTurn(_players[1]);

        // Start the action executor
        ClimbManager.Instance.ActionExecutor.Unpause();

        await WaitForRoundNumberOrTimeout(2);

        // We should be in round 2 (not 3) with both players unready
        Assert.That(CombatManager.Instance.DebugOnlyGetState()!.RoundNumber, Is.EqualTo(2));
        Assert.That(CombatManager.Instance.IsPlayerReadyToEndTurn(_players[0]), Is.False);
        Assert.That(CombatManager.Instance.IsPlayerReadyToEndTurn(_players[1]), Is.False);

        // Our last two actions should have been cancelled
        Assert.That(undoEndTurnActionReceivedAfterEndOfTurn.State, Is.EqualTo(GameActionState.Canceled));
        Assert.That(endTurnActionReceivedAfterEndOfTurn.State, Is.EqualTo(GameActionState.Canceled));
    }

    private async Task WaitForRoundNumberOrTimeout(int roundNumber)
    {
        ulong startTime = Time.GetTicksMsec();

        while (CombatManager.Instance.DebugOnlyGetState()!.RoundNumber != roundNumber)
        {
            await Task.Yield();
            if (Time.GetTicksMsec() - startTime > 100) break;
        }
    }
}
