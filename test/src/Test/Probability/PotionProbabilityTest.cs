using System;
using System.Collections.Generic;
using MegaCrit.Sts2.Core.Climbs;
using MegaCrit.Sts2.Core.Entities.Ascension;
using MegaCrit.Sts2.Core.Entities.Players;
using MegaCrit.Sts2.Core.Models.Characters;
using MegaCrit.Sts2.Core.Odds;
using MegaCrit.Sts2.Core.Random;
using MegaCrit.Sts2.Core.Rooms;
using NUnit.Framework;

namespace MegaCrit.Sts2.Test.Probability;

public class PotionProbabilityTest
{
    private const int _climbs = 10000;
    // Margin set to 1.2% to account for variance from the pity system which adjusts odds by ±10% after each roll
    private const int _margin = (int)(_climbs * 0.012);

    private static IEnumerable<uint> GenerateTestSeeds(int count)
    {
        for (int i = 1; i <= count; i++)
        {
            yield return (uint)(i * 98765 + 43210); // Deterministic seeds
        }
    }

    private static IEnumerable<uint> GenerateFastModeTestSeeds()
    {
        return GenerateTestSeeds(5);
    }

    private static IEnumerable<uint> GenerateSlowModeTestSeeds()
    {
        return GenerateTestSeeds(100);
    }

    private static IEnumerable<uint> GenerateTestSeedsBasedOnMode()
    {
        string testMode = Environment.GetEnvironmentVariable("STS2_TEST_MODE") ?? "fast";
        return testMode.ToLower() == "slow" ? GenerateSlowModeTestSeeds() : GenerateFastModeTestSeeds();
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public void TestMonsterPotionOdds(uint seed)
    {
        PotionRewardOdds odds = new(new Rng(seed));

        Console.WriteLine($"Rolling for potions {_climbs} times in {nameof(RoomType.Monster)} room...");

        int expected = (int)Math.Round(_climbs * PotionRewardOdds.targetOdds);
        int times = RollPotions(odds, AscensionLevel.None, RoomType.Monster);

        Console.WriteLine($"Got a potion {times} times, expected around {expected}.");
        Assert.That(times, Is.EqualTo(expected).Within(_margin));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public void TestElitePotionOdds(uint seed)
    {
        PotionRewardOdds odds = new(new Rng(seed));

        Console.WriteLine($"Rolling for potions {_climbs} times in {nameof(RoomType.Elite)} room...");

        // Elites give potions 25% more often.
        int expected = (int)Math.Round(
            _climbs * PotionRewardOdds.targetOdds *
            (1 + PotionRewardOdds.eliteBonus)
        );

        int times = RollPotions(odds, AscensionLevel.None, RoomType.Elite);

        Console.WriteLine($"Got a potion {times} times, expected around {expected}.");
        Assert.That(times, Is.EqualTo(expected).Within(_margin));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public void TestScarcityMonsterPotionOdds(uint seed)
    {
        PotionRewardOdds odds = new(new Rng(seed));

        Console.WriteLine(
            $"Rolling for potions {_climbs} times in {nameof(RoomType.Monster)} room with " +
            $"{nameof(AscensionLevel.Scarcity)} ascension..."
        );

        // Scarcity gives potions 25% less often.
        int expected = (int)Math.Round(
            _climbs * PotionRewardOdds.targetOdds *
            (1 - PotionRewardOdds.scarcityPenalty)
        );

        int times = RollPotions(odds, AscensionLevel.Scarcity, RoomType.Monster);

        Console.WriteLine($"Got a potion {times} times, expected around {expected}.");
        Assert.That(times, Is.EqualTo(expected).Within(_margin));
    }

    [Test]
    [TestCaseSource(nameof(GenerateTestSeedsBasedOnMode))]
    public void TestScarcityElitePotionOdds(uint seed)
    {
        PotionRewardOdds odds = new(new Rng(seed));

        Console.WriteLine(
            $"Rolling for potions {_climbs} times in {nameof(RoomType.Elite)} room with " +
            $"{nameof(AscensionLevel.Scarcity)} ascension..."
        );

        // Elites give potions 25% more often, but Scarcity gives potions 25% less often.
        int expected = (int)Math.Round(
            _climbs * PotionRewardOdds.targetOdds *
            (1 + PotionRewardOdds.eliteBonus) *
            (1 - PotionRewardOdds.scarcityPenalty)
        );

        int times = RollPotions(odds, AscensionLevel.Scarcity, RoomType.Elite);

        Console.WriteLine($"Got a potion {times} times, expected around {expected}.");
        Assert.That(times, Is.EqualTo(expected).Within(_margin));
    }

    private static int RollPotions(PotionRewardOdds odds, AscensionLevel level, RoomType roomType)
    {
        int times = 0;


        Player player = Player.CreateForNewClimb<Deprived>(1);

        for (int i = 0; i < _climbs; i++)
        {
            if (odds.Roll(player, new AscensionManager(level), roomType))
            {
                times++;
            }
        }

        return times;
    }
}
